<template>
  <el-dialog
      :visible.sync="visible"
      :title="`${baseline.name}`"
      width="960px"
      append-to-body
      v-loading="loading"
      @open="handleOpen"
  >
    <div class="dialog-container">
      <el-radio v-model="showHistory" :label="true" @input="resetDiffDetail(true)">选择历史版本</el-radio>
      <el-radio v-model="showHistory" :label="false" @input="resetDiffDetail(false)">选择其它基线</el-radio>

      <div class="select-item">
        <div v-show="showHistory">
          <el-form>
            <el-form-item>
              <el-select
                  v-model="history_id"
                  style="width: 300px;"
                  placeholder="请选择历史版本"
              >
                <el-option
                    v-for="item in histories"
                    :key="item.id"
                    :label="`${item.operator}在 ${item.created_at} 修改版本`"
                    :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div v-show="!showHistory">
          <el-form>
            <el-form-item>
              <el-select
                  v-model="other_baseline_id"
                  style="width: 300px;"
                  placeholder="请选择其它基线"
              >
                <el-option
                    v-for="item in system_baselines"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <hr />
      <div v-if="compareData && baselineDetail">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-descriptions :title="diffTitle" direction="horizontal" :column="1" border>
              <el-descriptions-item label="基线名称">{{ compareData.name }}</el-descriptions-item>
              <el-descriptions-item label="部门">{{ compareData.department_name }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{ compareData.comment }}</el-descriptions-item>
              <el-descriptions-item label="告警条件">{{ compareData.compare_rule_desc }}</el-descriptions-item>
              <el-descriptions-item label="告警维度">{{ compareData.output_schema_data_names.join("、") }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="12">
            <el-descriptions title="当前基线详情" direction="horizontal" :column="1" border>
              <el-descriptions-item label="基线名称">{{ baselineDetail.name }}</el-descriptions-item>
              <el-descriptions-item label="部门">{{ baselineDetail.department_name }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{ baselineDetail.comment }}</el-descriptions-item>
              <el-descriptions-item label="告警条件">{{ baselineDetail.compare_rule_desc }}</el-descriptions-item>
              <el-descriptions-item label="告警维度">{{ baselineDetail.output_schema_data_names.join("、") }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
        <div v-if="dataSchema.length > 0">
          <div class="title">{{diffName}}</div>
          <el-tabs
            v-model="activeName"
            v-loading="loading"
            type="border-card"
          >
            <template
              v-for="item in dataSchema"
            >
              <el-tab-pane
                v-if="diff_datas[item.data_key]"
                :key="item.data_key"
                :label="item.name"
                :name="item.data_key"
              >
                <diff-datas-table
                  :diff-datas="diff_datas[item.data_key]"
                  :data-schema="item.schema"
                  :compare-name="compareName"
                  :diff-mode="diffMode"
                  :other-account-code="otherAccount"
                  :system-id="systemId"
                  :baseline-name="baselineName"
                  :compare-rule="compareRule"
                  :get-response="getResponse"
                  :entityName="resourceName"
                />
              </el-tab-pane>
            </template>
          </el-tabs>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button
        v-if="history_id"
        type="primary"
        @click="confirmRestore"
      >
        从该版本还原
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import axios from "axios";
import DiffDatasTable from '@/components/AccountDetail/DiffDatasTable.vue';

export default {
  components: {
    DiffDatasTable,
  },
  props: {
    baseline: {
      type: Object,
      required: true
    },
    systemId: {
      type: Number,
      required: true
    }
  },
  watch: {
    history_id(val, oldVal) {
      if(val) {
        this.other_baseline_id = null
        this.initHistory()
      }
    },
    baseline() {
      this.resetField()
    },
    other_baseline_id (val, oldVal) {
      if(val) {
        this.history_id = null
        this.initDiffSystemBaseline()
      }
    }
  },
  computed: {
    diffTitle() {
      if(this.history_id) {
        if(this.compareData.format_datetime) {
          return `历史基线详情（${this.compareData.format_datetime}）`
        } else {
          return "历史基线详情"
        }
      } else {
        return `${this.otherBaselineName}详情`
      }
    },
    diffName() {
      if(this.history_id) {
        return "基线历史差异"
      } else {
        return `对比${this.otherBaselineName}差异`
      }
    },
    baselineName () {
      if(this.history_id) {
        return "历史"
      } else {
        return this.otherBaselineName
      }
    },
    compareName () {
      if(this.history_id) {
        return "历史"
      } else {
        return this.otherBaselineName
      }
    },
  },
  data() {
    return {
      visible: false,
      histories: [],
      history_id: null,
      loading: false,
      history: null,
      baselineDetail: null,
      dataSchema: [],
      diff_datas: {},
      diffMode: "compareBaseline",
      otherAccount: null,
      getResponse: false,
      compareRule: "all_contrast", // 比较默认都按权限不一致来
      activeName: '',
      resourceName: "",
      otherBaselineName: "",
      system_baselines: [],
      other_baseline_id: null,
      compareData: null,
      showHistory: true
    }
  },
  methods: {
    handleOpen() {
      this.initSystemBaseline()
      this.initHistories()
      this.initSystemBaselineList()
    },
    resetDiffDetail(selectHistory) {
      this.compareData = null
      if(selectHistory) {
        this.other_baseline_id = null
      } else {
        this.history_id = null
      }
    },
    initHistories() {
      this.loading = true
      this.$axios.get(`api/systems/${this.systemId}/baselines/${this.baseline.id}/histories`)
          .then((res) => {
            this.loading = false
            this.histories = res.data.baseline_histories
          })
          .catch((err) => {
            this.loading = false
            console.error(err)
          })
    },
    initSystemBaselineList() {
      this.loading = true
      this.$axios.get(`api/systems/${this.systemId}/baselines`)
          .then(res => {
            this.loading = false
            this.system_baselines = res.data.data.filter(item => item.id !== this.baseline.id)
          })
          .catch(err => {
            this.loading = false
            console.log(err)
          })
    },
    initHistory() {
      if (this.history_id === null) return
      this.loading = true
      this.$axios.get(`api/systems/${this.systemId}/baselines/${this.baseline.id}/histories/${this.history_id}`)
          .then((res) => {
            this.loading = false
            this.compareData = res.data.history
            this.getDiffDataWithBaseline("diff_history", this.history_id)
          })
          .catch((err) => {
            this.loading = false
            console.error(err)
          })
    },
    initDiffSystemBaseline() {
      if (this.other_baseline_id === null) return
      this.loading = true
      this.$axios.get(`api/systems/${this.systemId}/baselines/${this.other_baseline_id}`)
          .then(res => {
            this.loading = false
            this.otherBaselineName = res.data.name
            this.compareData = res.data
            this.getDiffDataWithBaseline("diff_baseline", this.other_baseline_id)
          })
          .catch(err => {
            this.loading = false
            console.log(err)
          })
    },
    initSystemBaseline() {
      this.loading = true
      this.$axios.get(`api/systems/${this.systemId}/baselines/${this.baseline.id}`)
          .then((res) => {
            this.loading = false
            this.baselineDetail = res.data
          })
          .catch((err) => {
            this.loading = false
            console.error(err)
          })
    },
    resetField() {
      this.histories = []
      this.history_id = null
      this.history = null
      this.baselineDetail = null
      this.other_baseline_id = null
      this.compareData = null
      this.system_baselines = []
    },
    confirmRestore() {
      if (!this.history_id) {
        this.$message.error("请选择历史版本")
        return
      }
      this.$confirm('确定从该版本还原吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            this.restoreWithVersion()
          })
          .catch(() => {
            this.$message.info('已取消操作')
          })
    },
    restoreWithVersion() {
      this.loading = true
      this.$axios.put(`/api/systems/${this.systemId}/baselines/${this.baseline.id}/histories/${this.history_id}/restore`)
          .then((res) => {
            this.loading = false
            if (res.data.success) {
              this.resetField()
              this.visible = false
              this.$emit('closeDialog')
              this.$message.success('从历史版本恢复成功')
            } else {
              this.$message.error(res.data.error_message)
            }
          })
          .catch((err) => {
            this.loading = false
            console.error(err)
          })

    },
    // 系统基线差异
    getDiffDataWithBaseline (diff_mode, diff_id) {
      this.resourceName  = `${this.baselineDetail.name}系统基线`
      this.dataSchema = this.baselineDetail.output_schema
      this.$set(this.dataSchema, this.replaceSchemaWithBaselineNotice(this.baselineDetail.output_schema_data))
      this.activeName = this.dataSchema[0].data_key
      this.diffDataRequest(diff_mode, diff_id)
    },
    // 根据返回数据添加不告警提示,用于替换dataSchema
    replaceSchemaWithBaselineNotice (outputSchemaData) {
      const dataKeys = outputSchemaData.filter(x => !x.is_notice).map(x => x.data_key)
      return this.dataSchema.map(x => {
        if (dataKeys.indexOf(x.data_key) > -1 && x.name.indexOf('不告警') < 0) {
          x.name = x.name + '【不告警】'
        }
        return x
      })
    },
    diffDataRequest (diff_mode, diff_id) {
      this.getResponse = false
      this.loading = true
      let theParams = { diff_mode: diff_mode, diff_id: diff_id }
      this.$axios.get(`/api/systems/${this.systemId}/baselines/${this.baseline.id}/diff_history_baseline`, { params: theParams })
        .then(response => {
          this.diff_datas = response.data
          this.loading = false
          this.getResponse = true
        })
        .catch(() => {
          this.loading = false
          this.getResponse = true
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.title {
  font-size: 16px;
  font-weight: 700;
  color: #303133;
  margin-top: 20px;
  margin-bottom: 20px;
}

.select-item {
  margin-top: 20px;
}
</style>
