# frozen_string_literal: true

# 岗位员工
class JobUsersController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!
  after_action :sync_user_position, only: [:create]

  def create
    @job = Job.find(params[:job_id])
    before_users = @job.users.to_a
    time = Time.now
    ActiveRecord::Base.transaction do
      @job.job_users.delete_all
      JobUser.bulk_insert(:job_id, :user_id, :created_at, :updated_at) do |obj|
        obj.set_size = 1000
        params[:user_ids].each do |user_id|
          obj.add [@job.id, user_id, time, time]
        end
      end
    end
    after_users = @job.reload.users
    @add_users = after_users - before_users
    @del_users = before_users - after_users

    audit_log! [@job, @add_users, @del_users]
    json_respond(success: true)
  end

  protected

  def authenticate_policy!
    authorize nil, policy_class: JobUserPolicy
  end

  # 同步员工职务
  def sync_user_position
    user_ids = (@add_users + @del_users).uniq.pluck(:id)
    UserPositionJob.perform_later(user_ids) if user_ids.present?
  end
end
