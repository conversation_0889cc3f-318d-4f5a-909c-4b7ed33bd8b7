import axios from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const updateBulk = (systemId, recordIds) => {
  return axios.post(`/api/systems/${systemId}/baselines/update_bulk`, { qc_ids: recordIds })
}
export const exportBaseline = (systemId, filter = {}) => {
  return axios.get(`/api/systems/${systemId}/baselines/export_baselines`, { responseType: 'blob', params: filter })
    .then(response => {
      const fileName = parseFileName(response, 'baseline-export.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

// 导出部分系统基线
export const exportSelectBaseline = (systemId, baselineIds = []) => {
  return axios.get(`/api/systems/${systemId}/baselines/export_select_baselines`, { responseType: 'blob', params: { baseline_ids: baselineIds } })
    .then(response => {
      const fileName = parseFileName(response, 'baseline-export.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

export const exportBaselineList = (systemId, filter = {}) => {
  return axios.get(`/api/systems/${systemId}/baselines/export_accounts`, { responseType: 'blob', params: filter })
    .then(response => {
      const fileName = parseFileName(response, 'baseline-export.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

export const exportSelectBaselineList = (systemId, baselineIds = []) => {
  return axios.get(`/api/systems/${systemId}/baselines/export_select_accounts`, { responseType: 'blob', params: { baseline_ids: baselineIds } })
    .then(response => {
      const fileName = parseFileName(response, 'baseline-export.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}


// 导入前的验证，第一次导入的接口
export const importValidation = (params) => {
  return axios.post(`/api/systems/${params.system_id}/baselines/upload_validation`, params)
}

// 直接导入，没有验证，二次调用的接口
export const importBaseline = (systemId) => {
  return axios.post(`/api/systems/${systemId}/baselines/upload`)
}

export const mergeBaselines = (system_id, params) => {
    return axios.post(`/api/systems/${system_id}/baselines/merge_baselines`, { baseline: params })
}

export const removeBaseline = (system_id, params) => {
    return axios.put(`/api/systems/${system_id}/baselines/remove_baseline`, params)
}
