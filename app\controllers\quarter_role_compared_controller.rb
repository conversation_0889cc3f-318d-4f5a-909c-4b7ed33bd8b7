# frozen_string_literal: true

# 角色差异控制器
class QuarterRoleComparedController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!, except: %i[system_tree]

  before_action :set_quarter, only: %i[index compared_status destroy create]
  before_action :set_system, only: %i[index history_difference compared_status destroy]
  before_action :check_date_range, only: %i[history_difference history_excel_difference_export]

  def index
    compared_quarter_id = params[:compared_quarter_id]
    page                = params[:page]&.to_i || 1
    page_size           = params[:page_size]&.to_i || 15

    data =
      QuarterRoleComparedDifference.with_role(
        current_admin:       current_admin,
        quarter_id:          @quarter.id,
        compared_quarter_id: compared_quarter_id,
        business_system:     @business_system,
        page:                page,
        page_size:           page_size,
        filter:              filter_params
      )

    json_respond data
  end

  def create
    compared_quarter_id = params[:compared_quarter_id]&.to_i
    business_system_ids = params[:business_system_ids].map(&:to_i)
    last_job_start_time = QuarterRoleCompared::Runner
                            .process_start_time(@quarter.id, compared_quarter_id, business_system_ids.last)
    # 防止用户多次点击，5 秒内多次点击不生效
    if last_job_start_time.nil? || Time.now - last_job_start_time > 5
      compared_quarter = Quarter.find(compared_quarter_id)
      # 查询系统名称写入到日志
      business_system_names = BusinessSystem.where(id: business_system_ids).pluck(:name)
      audit_log! [business_system_names, compared_quarter&.name, @quarter.name]

      business_system_ids.each do |business_system_id|
        import_state = QuarterBusinessSystemImportStatus
                         .quarter_and_compared_quarter_import_system_state?(@quarter.id, compared_quarter_id, business_system_id)

        return json_custom_respond(404, error_message: '两个时间点数据导入异常！') unless import_state

        CreateQuarterRoleComparedDataJob.perform_later(@quarter.id, compared_quarter_id, business_system_id)
      end
      json_respond(success: true)
    else
      json_respond(success: false)
    end
  end

  def destroy
    QuarterRoleComparedDifference.where(
      quarter_id:          params[:quarter_id],
      compared_quarter_id: params[:compared_quarter_id],
      business_system_id:  params[:system_id]
    ).delete_all

    QuarterRoleComparedStatus.where(
      quarter_id:          params[:quarter_id],
      compared_quarter_id: params[:compared_quarter_id],
      business_system_id:  params[:system_id]
    ).delete_all

    compared_quarter = Quarter.find_by(id: params[:compared_quarter_id])
    # TODO: 审计日志里需要记录系统了，迁移到 audit_log 里
    business_system_name = BusinessSystem.find_by(id: params[:system_id])&.name
    audit_log! [business_system_name, compared_quarter&.name, @quarter.name]

    json_respond(success: true)
  end

  def excel_difference_export
    options = params.to_unsafe_h.symbolize_keys
    system_id           = params[:system_id].to_i
    quarter_id          = params[:quarter_id].to_i
    quarter             = Quarter.find(quarter_id)
    the_system          = BusinessSystem.find(system_id)
    log = diff_log_params(the_system&.name, quarter&.name)
    options.merge!(
      filter_params: filter_params,
      quarter_id:    quarter_id,
      admin_id:      current_admin.id,
      system_id:     system_id,
      log:           log
    )
    DownloadExport::QuarterRoleComparedDifferenceExport.new(quarter, current_admin, the_system, options).async_export

    json_respond(status: true)
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! diff_log_params(the_system&.name, quarter&.name), action: :excel_difference_export_faild
    json_custom_respond(500, error_message: e.message)
  end

  # def history_excel_difference_export
  #   quarters                   = Quarter.ransack(id_gteq: params[:start_quarter_id], id_lteq: params[:end_quarter_id]).result.order('id ASC')
  #   the_system                 = BusinessSystem.find(params[:system_id].to_i)
  #   filter_params              = JSON.parse params[:filter], symbolize_names: true
  #   start_quarter, end_quarter = Quarter.get_start_and_end_quarter(filter_params[:date_range])
  #   report                     = ReportExport::QuarterRoleComparedBySystemReport.new(quarters.first)
  #   report.start_quarter       = start_quarter
  #   report.end_quarter         = end_quarter
  #   report.business_system     = the_system
  #   report.current_admin       = current_admin
  #   report.filter              = filter_params

  #   roles       = report.generate_data.map { |x| the_system.role_class.find(x[:role_id]) }
  #   excel_datas = roles.map { |x| x.export_excel(x.quarter.previous_quarter_id || 0) }
  #   unique = "_#{Time.now.to_i}"
  #   DataExport.batch_zip([report.create_export_file, excel_datas].flatten, unique)
  #   params[:unique] = unique

  #   audit_log! history_diff_log_params(the_system&.name, start_quarter&.name, end_quarter&.name), action: :history_excel_difference_export_success
  #   download_batch_zip
  # rescue StandardError => e
  #   logger.error { e.message }
  #   logger.error { e.backtrace.join("\n") }
  #   audit_log! history_diff_log_params(the_system&.name, start_quarter&.name, end_quarter&.name), action: :history_excel_difference_export_faild
  #   json_custom_respond(500, error_message: e.message)
  # end

  def compared_status
    compared_quarter_id = params[:compared_quarter_id]
    status              = QuarterRoleComparedStatus.status(@quarter.id, compared_quarter_id, params[:system_id])

    if status
      json_respond status.reload.output
    else
      json_respond(
        quarter_id:          @quarter.id,
        compared_quarter_id: compared_quarter_id,
        business_system_id:  params[:system_id],
        status:              'not_exist'
      )
    end
  end

  def system_tree
    has_query_bs_ids = current_admin.business_systems_in_query.ids
    tree_output = BusinessSystemCategory.arrange_serializable do |parent, children|
      bs_list = parent.business_systems.inservice.where(id: has_query_bs_ids)
                      .map { |bs| bs.role_differences_count_with_category_tree(current_admin, params[:quarter_id], params[:compared_quarter_id]) }
      {
        id: parent.id,
        name: parent.name,
        parent_id: parent.parent_id,
        children: children,
        business_systems: bs_list,
        category: 'category'
      }
    end
    no_category_bs_list = BusinessSystem.where(business_system_category_id: nil).inservice
                                        .where(id: has_query_bs_ids)
                                        .map { |bs| bs.role_differences_count_with_category_tree(current_admin, params[:quarter_id], params[:compared_quarter_id]) }
    no_category_bs_list.each { |x| tree_output << x }
    json_respond tree_output
  end

  # 指定时间点段内的历史变化
  def history_difference
    page      = params[:page]&.to_i || 1
    page_size = params[:page_size]&.to_i || 15

    start_quarter, end_quarter = Quarter.get_start_and_end_quarter(filter_params[:date_range])
    roles                      =
      QuarterRoleComparedDifference.with_role(
        current_admin:    current_admin,
        start_quarter_id: start_quarter&.id,
        end_quarter_id:   end_quarter&.id,
        business_system:  @business_system,
        page:             page,
        page_size:        page_size,
        filter:           filter_params
      )

    json_respond roles
  end

  private

  def check_date_range
    if filter_params[:date_range].blank? || filter_params[:date_range].compact.size < 2
      json_custom_respond(400, error_message: '请选择时间范围')
    end
  end

  def set_system
    @business_system = BusinessSystem.find(params[:system_id])
  rescue ActiveRecord::RecordNotFound => e
    json_custom_respond(404, error_message: e.message)
  end

  def filter_params
    JSON.parse(params[:filter], symbolize_names: true)
  end

  def authenticate_policy!
    authorize nil, policy_class: QuarterRoleComparedPolicy
  end

  def download_batch_zip
    send_file_compatible_with_msie "#{Setting.data_path['export']}/download_tmp/#{params[:unique]}.rar"
  end

  def diff_log_params(system_name, quarter_name)
    {
      system_name:  system_name,
      quarter_name: quarter_name
    }
  end

  def history_diff_log_params(system_name, start_quarter_name, end_quarter_name)
    {
      system_name:        system_name,
      start_quarter_name: start_quarter_name,
      end_quarter_name:   end_quarter_name
    }
  end
end
