<template>
  <div
    class="operation-upload"
  >
    <el-row>
      <el-col
        v-for="system in uploadSystems"
        v-show="isShowSystem(system.id)"
        :key="system.id"
        :span="8"
      >
        <system-uploader
          :quarter="quarter"
          :systemId="system.id"
          :systemName="systemName(system)"
          multiple
          :action="uploadUrl"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import SystemUploader from './SystemUploader.vue'
import API            from '@/api'

export default {
  components: {
    SystemUploader
  },
  props:      {
    quarter: {
      type:     Object,
      required: true
    }
  },
  data () {
    return {
      uploadSystems:     [],
      autoImportSystems: [],
      uploadUrl: API.tool.absoluteUrlFor('/admin_api/upload_system_data')
    }
  },
  created () {
    this.getSystems()
  },
  methods: {
    getSystems () {
      API.systems.uploadType()
        .then(response => { this.uploadSystems = response.data })
        .catch(() => {})
      API.systems.autoImportType()
        .then(response => { this.autoImportSystems = response.data })
        .catch(() => {})
    },
    isShowSystem (id) {
      return !(id === 6 || id === 7)
    },
    systemName (system) {
      if (system.id === 5) return '直销系统'
      return system.name
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/components/_variables";

.operation-upload {
  height: 450px;
}
</style>
