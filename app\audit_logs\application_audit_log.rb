# frozen_string_literal: true

# 审计日志的父类
class ApplicationAuditLog
  attr_reader :user, :params, :comment, :request, :download_url, :job_id,
              :operation_module, :operation_category, :operation

  def initialize(user, request, params)
    @user    = user
    @request = request
    @params  = params
  end

  protected

  def create_audit_log
    AdminAuditLog.create(
      admin_id:           user_id,
      operation:          operation,
      operation_time:     now,
      operation_module:   operation_module,
      operation_category: operation_category,
      download_url:       download_url,
      job_id:             job_id,
      comment:            comment,
      ip_address:         request&.ip,
      event_id:           request&.request_id,
      host:               request&.host,
      agent:              request&.user_agent
    )
  end

  def user_id
    user&.id
  end

  def now
    Time.now
  end
end
