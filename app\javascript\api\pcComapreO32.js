import axios from "@/settings/axios";
import { parseFileName, downloadBlob } from "./tool";

export const index = (data) => {
  return axios.get("/api/pc_o32", { params: data });
};
export const exportData = (data) => {
  return axios
    .get(`/api/pc_o32/export`, {
      responseType: "blob",
      params: data,
    })
    .then((response) => {
      const fileName = parseFileName(response, "pc-o32-export.xlsx");
      downloadBlob(response.data, fileName);
    })
    .catch(() => {});
};
