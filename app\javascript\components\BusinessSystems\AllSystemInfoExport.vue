<template>
  <el-button
    v-loading="loading"
    type="primary"
    size="small"
    @click="exportAllSystemInfo"
    :disabled="!$store.getters.hasPermission('all_system_info.system_info_export')"
  >
    导出
  </el-button>
</template>

<script>
import API from '@/api'
export default {
  props: {
    systemIds: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      loading: false
    }
  },
  methods: {
    exportAllSystemInfo () {
      if (this.systemIds.length === 0) {
        this.$message.warning("您没有选择要导出的系统，这将导出一个只包含表头的excel文件")
      }
      this.loading = true
      API.systems.exportAllSystemInfo(this.systemIds)
        .then(() => this.loading = false)
    }
  }
}
</script>
