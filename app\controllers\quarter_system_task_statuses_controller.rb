class QuarterSystemTaskStatusesController < ApplicationController
  before_action :authenticate_admin!

  def index
    quarter = Quarter.find(params[:quarter_id])
    bs = BusinessSystem.find(params[:bs_id])
    system_task_statuses = AfterImport
                             .enable_system_tasks(quarter, [bs])
                             .map { |task| task.output.merge(loading: false) }
    json_respond data: system_task_statuses, business_system_id: bs.id
  end
end