<template>
  <span>
    <el-input v-model="filterName" size="small" placeholder="请输入系统名称" style="width:240px;margin-top:10px;margin-left: 10px;"/>
    <hr class="system-search-hr"/>
    <el-menu
      mode="vertical"
      @select="selectItem"
      :default-openeds="openTreeIds"
    >
      <template v-for="item in showTree">
        <system-count-recursive-menu
          :item="item"
          :filterName="filterName"
          :showTreeIds="showTreeIds"
        />
      </template>
    </el-menu>
  </span>
</template>

<script>
import SystemCountRecursiveMenu from '@/components/SystemCountRecursiveMenu.vue'
import API from '@/api'

export default {
  components: {
    SystemCountRecursiveMenu
  },
  props: {
    defaultActive: {
      type: Number,
      required: true
    },
    quarterId:         {
      type:     Number,
      required: true
    },
    comparedQuarterId: {
      type:     Number,
      required: true
    },
    loadPage: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      tree: [],
      loading: false,
      selectSystem: '',
      showTree: [],
      showTreeIds: [],
      openTreeIds: [],
      tree: [],
      filterName: "",
      allChildren: {}
    }
  },
  computed: {
    defaultActiveIndex () {
      let QuarterDifferenceActive = Number(this.$store.state.default_system_id) || this.defaultActive
      return this.findSystem(QuarterDifferenceActive, 'index')
    }
  },
  watch: {
    filterName() {
      this.startFilter()
      this.openTreeIds = this.filterName == "" ? [] : this.showTreeIds.map(x=> 'category-'+x)
    },
    comparedQuarterId () {
      this.gettingSystems()
    },
    loadPage () {
      this.gettingSystems()
    }
  },
  created () {
    this.gettingSystems()
  },
  methods: {
    startFilter() {
      this.showTreeIds = []
      this.scanTree(this.tree)
      this.showTree = this.tree.filter(x=> this.showTreeIds.indexOf(x.id) > -1 || x.category === 'bs')
    },
    verifyTree (data) {
      if (data.category !== 'category') return

      if (this.filterName == "" || data.business_systems.filter(x => x.name.indexOf(this.filterName) > -1).length > 0) {
        this.showTreeIds.push(data.id)
        let parentTree = this.allChildren[data.parent_id]
        while(parentTree){
          this.showTreeIds.push(parentTree.id)
          parentTree = this.allChildren[parentTree.parent_id]
        }
      }
      if (data.children.length > 0){
        this.scanTree(data.children)
      }
    },
    scanTree (data) {
      data.forEach((item, index) => {
        this.allChildren[item.id] = { id : item.id, parent_id : item.parent_id }
        this.verifyTree(item)
      })
    },
    gettingSystems () {
      this.loading = true
      this.$axios.get(`/api/quarter_compared/${this.quarterId}/compared_detail/${this.comparedQuarterId}/bs_tree`)
        .then(response => {
          this.tree = response.data
          this.loading = false
          this.startFilter()
          if (this.selectSystem === '') {
            this.showTree = this.tree
            return
          }

          // 如果传了参树business_system_id，自动点击该系统
          const bsId = this.$route.query.business_system_id
          if (!API.tool.isBlank(bsId)) {
            const index = this.findSystem(bsId, 'index')
            this.selectItem(index)
          }
        })
        .catch(() => {
          this.loading = false
          this.tree = [{ name: 'Read data error' }]
        })
    },
    findSystem (systemIdAndNameString) {
      let [systemId, systemName] = systemIdAndNameString.split('-')
      let system = { id: Number(systemId), name: systemName }
      return system
    },
    selectItem (index) {
      if(!index.startsWith('bs-')) return

      let systemIdAndNameString = this.$lodash.trimStart(index, "bs-")
      let systemIdString = systemIdAndNameString.split("-")[0]
      this.$store.commit('storeCompareSystem', systemIdString)
      this.$emit('select', systemIdAndNameString)
    }
  }
}
</script>

<style lang="scss" scoped>
  .system-search-hr {
    margin-top: 15px;
    margin-bottom: 0px;
  }
</style>
