#!/usr/bin/env ruby

# 简单的关键字计数检查
def count_keywords(filename)
  content = File.read(filename)
  lines = content.split("\n")
  
  counts = {
    'module' => 0,
    'def' => 0,
    'class' => 0,
    'if' => 0,
    'unless' => 0,
    'case' => 0,
    'while' => 0,
    'until' => 0,
    'for' => 0,
    'begin' => 0,
    'end' => 0
  }
  
  lines.each_with_index do |line, index|
    line_num = index + 1
    stripped = line.strip
    
    # 跳过注释行
    next if stripped.start_with?('#')
    
    counts.each do |keyword, count|
      if keyword == 'end'
        if stripped == 'end'
          counts[keyword] += 1
          puts "第 #{line_num} 行: end"
        end
      else
        if stripped =~ /^#{keyword}\b/
          counts[keyword] += 1
          puts "第 #{line_num} 行: #{keyword}"
        end
      end
    end
  end
  
  puts "\n关键字统计:"
  counts.each do |keyword, count|
    puts "#{keyword}: #{count}"
  end
  
  # 计算需要 end 的关键字总数
  need_end = counts.values_at('module', 'def', 'class', 'if', 'unless', 'case', 'while', 'until', 'for', 'begin').sum
  actual_end = counts['end']
  
  puts "\n需要 end 的关键字总数: #{need_end}"
  puts "实际 end 的数量: #{actual_end}"
  
  if need_end == actual_end
    puts "✅ 关键字匹配正确！"
    return true
  else
    puts "❌ 关键字不匹配！差异: #{actual_end - need_end}"
    return false
  end
end

puts "检查关键字匹配..."
count_keywords('config/initializers/start_sidekiq.rb')
