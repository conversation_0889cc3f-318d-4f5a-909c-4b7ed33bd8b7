class FdfgGzyss45ComparisonAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '系统设置'
    super
  end

  def update_funds_settings
    set_login_category
    @operation = "#{I18n.t('aas.fdfg_gzyss45_comparison.abnormals.fund_abnormal')}设置更新"
    @comment   = "更新了 #{params}"
    create_audit_log
  end

  def commit_alert
    set_login_category
    @operation = "#{I18n.t("aas.fdfg_gzyss45_comparison.abnormals.#{params[0]}")}设置更新"
    @comment   = "更新了 #{params[1]}"
    create_audit_log
  end

  def recheck_comparison
    set_login_category
    @operation = '重新检查数据'
    @comment   = "重新执行「#{params.name}」的#{I18n.t 'aas.fdfg_gzyss45_comparison.name'}数据检查"
    create_audit_log
  end

  def export_success
    set_login_category
    @operation = '信披-估值系统权限差异导出'
    @comment   = "成功导出「#{params[:quarter_name]}」的「信披-估值系统权限差异」数据"
    create_audit_log
  end

  def export_faild
    set_login_category
    @operation = '信披-估值系统权限差异导出'
    @comment   = "导出「#{params[:quarter_name]}」的「信披-估值系统权限差异」数据失败"
    create_audit_log
  end

  private

  def set_login_category
    @operation_category = "#{I18n.t 'aas.fdfg_gzyss45_comparison.name'}设置"
  end
end
