import axios from '@/settings/axios'

export const getAdminRoles = (adminId) => {
  return axios.get(`/admin_api/admin_accounts/${adminId}/admin_roles`)
}

export const getAdminSystems = (adminId) => {
  return axios.get(`/admin_api/admin_accounts/${adminId}/admin_systems_permission`)
}

export const getAdminDepartments = (adminId) => {
  return axios.get(`/admin_api/admin_accounts/${adminId}/admin_departments_permission`)
}

export const updateAdminRoles = (adminId, params) => {
  return axios.post(`/admin_api/admin_accounts/${adminId}/update_roles`, params)
}

// 更新系统权限和部门权限
export const updateAdminPermissions = (adminId, systemData, departmentData) => {
  const params = { system_data: systemData, department_data: departmentData }
  return axios.post(`/admin_api/admin_accounts/${adminId}/update_admin_permissions`, params)
}