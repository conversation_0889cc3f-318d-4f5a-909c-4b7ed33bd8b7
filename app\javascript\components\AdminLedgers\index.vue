<template>
  <div class="container">
    <h2>台账数据管理</h2>
    <hr>

    <el-container>
      <el-aside width="180px">
        <el-scrollbar>
          <el-collapse-transition>
            <sidebar-ledger-systems
              ref="systemSelect"
              v-loading="loading"
              style="height: calc(100vh - 245px);overflow-y:auto;"
              :default-active="Number(activeIndex)"
              :systems="systems"
              @select="selectItem"
            />
          </el-collapse-transition>
        </el-scrollbar>
      </el-aside>
      <el-main>
        <ledger-system
          v-if="activeIndex"
          :key="activeIndex"
          :system-id="Number(activeIndex)"
          :systems="systems"
        />
      </el-main>
    </el-container>
  </div>
</template>

<script>
import LedgerSystem from './AdminLedgerSystem.vue'
import SidebarLedgerSystems from '@/components/SidebarLedgerSystems'
import API from '@/api'
export default {
  components: {
    LedgerSystem,
    SidebarLedgerSystems
  },
  data () {
    return {
      activeIndex: null,
      loading:     false,
      systems:     [],
    }
  },
  computed: {
    userTree () {
      return this.$store.state.user_tree
    }
  },
  created () {
    this.getSystems()
  },
  methods: {
    getSystems () {
      this.loading = true
      API.systems.maintainList()
        .then(response => {
          this.loading = false
          this.systems = response.data
        })
        .catch(() => {
          this.loading = false
        })
    },
    selectItem (systemId) {
      this.activeIndex = systemId
    }
  }
}
</script>

<style lang="scss" scoped>

.el-aside {
  border-right: 1px solid #EBEEF5;
}
.el-main{
  padding: 0 0 0 20px;
}

.container {
  padding:          2em;
  background-color: white;
  min-width:        950px;
}
</style>
