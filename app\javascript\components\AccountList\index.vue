<template>
  <el-dialog
    title="系统基线"
    :visible.sync="visible"
    append-to-body
    width="80%"
    @open="handleOpen"
  >
    <div>
      <search-bar
        :filter.sync="filter"
        :page.sync="page"
        @requestData="getAccountList"
      >
      </search-bar>
      <el-table
        v-loading="loading"
        :data="data"
        border
        class="baseline-table"
      >
        <el-table-column
          fixed
          property="code"
          label="账号编码"
          sortable="custom"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.code || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          property="name"
          label="账号名称"
          sortable="custom"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          property="status"
          label="账号状态"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.status ? '正常' : '禁用' }}
          </template>
        </el-table-column>
        <el-table-column
          property="account_department_name"
          label="账号部门名称"
          min-width="120"
        >
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      :page-size="pageSize"
      :total="count"
      :current-page.sync="page"
      background
      layout="total, prev, pager, next, jumper"
      class="pagination"
      @current-change="getAccountList"
    />
  </el-dialog>
</template>

<script>
import API           from '@/api'
import SearchBar     from './SearchBar.vue'

export default {
  components: {
    SearchBar
  },
  props:      {
    accountIds: {
      type: Array,
      required: true
    },
    businessSystemId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      visible: false,
      count: 0,
      page: 1,
      pageSize: 25,
      data: [],
      filter: {}
    }
  },
  computed: {
  },
  created () {
  },
  methods: {
    handleOpen () {
      this.getAccountList()
    },
    getAccountList () {
      this.loading = true
      const theParams = {
        page:        this.page,
        per_page:    this.pageSize,
        account_ids: this.accountIds,
        filter:      this.filter,
      }
      this.$axios.get(`/api/systems/${this.businessSystemId}/search_accounts`, { params: theParams })
        .then(response => {
          this.loading = false
          this.data = response.data.data
          this.count = response.data.count
        })
        .catch(() => {
          this.loading = false
        })
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.pagination {
  margin-top: 20px;
}
</style>
