<template>
  <el-container>
    <div>
    </div>
  </el-container>
</template>

<script>
import API from '@/api'

export default {
  props: {
    message: {
      type:    String,
      default: ''
    }
  },
  data () {
    return {
    }
  },
  computed: {
    isSsoLoginEnable () {
      return this.$settings.authMethod === 'sso'
    },
    ssoSetting() {
      return this.$settings.sso
    }
  },
  created () {
    if (this.message !== '') {
      this.$message.info(this.message)
    }
    this.redirectToSso()
  },
  methods: {
    redirectToSso(){
      if (this.ssoSetting.adapter == 'yiban'){
        let url = `${this.ssoSetting.login_url}${this.ssoSetting.get_code_uri}?appid=${this.ssoSetting.corpid}&redirect_uri=${encodeURIComponent(this.ssoSetting.back_url)}&response_type=code&scope=snsapi_base&agentid=${this.ssoSetting.agentid}&state=STATE#wechat_redirect`
        window.location.href = url
      }
      else if (this.isSsoLoginEnable) {
        window.location.href = API.tool.absoluteUrlFor('/sso_sessions/connect')
      }
    }
  }
}
</script>
