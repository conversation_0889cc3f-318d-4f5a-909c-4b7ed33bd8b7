<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    title="业务系统账号绑定"
    width="650px"
    append-to-body
    @close="handleClose"
  >
    <div>
      <el-form
        ref="roleForm"
      >
        <el-form-item
          label="选择系统"
          label-width="150px"
          multiple
          >
          <el-select multiple v-model="selectSystemsId" style="width: 100%" @change="selectSystem(selectSystemsId)">
            <el-option
              v-for="item in allSystems"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-divider />
        <el-form-item
          v-for="(domain, index) in dynamicValidateForm.domains"
          :key="index"
          :label="domain.system_name"
          label-width="150px"
        >
          <system-account-select
            v-model="domain.operator"
            :system-id="domain.system_id"
            placeholder="请选择账号"
          />
          <span style="color: red;" :id="`error_${domain.system_id}`"></span>
        </el-form-item>
        <el-form-item>
          <el-row>
            <el-col :span="12">
              <el-button
                type="primary"
                @click="handleSubmit('roleForm')"
                :disabled='!submit'
              >
                确认
              </el-button>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>
<script>
import SystemAccountSelect from '@/components/common/SystemAccountSelect.vue'

export default {
  components: {
    SystemAccountSelect
  },
  props: {
    user: {
      type:     Object,
      required: true
    }
  },
  data () {
    return {
      allSystems: [],
      dialogVisible: false,
      AllSystemsRoles: [],
      dynamicValidateForm: {},
      selectSystemsId: [],
      domainsSystemIds: [],
      checkOperatorOption: {},
      submit: true,
      errorMessage:[]
    }
  },
  computed: {
  },
  watch: {
    dialogVisible () {
      if (this.visible) {
        this.initialization()
      }
    },
    user() {
      this.getSystems()
    },
    allSystems () {
      this.getSystemOperotor()
    }
  },
  created () {
    this.initialization()
  },
  methods: {
    initialization () {
      this.selectSystemsId = []
      this.dynamicValidateForm = { domains: [] }
    },
    getSystems () {
      this.$axios.get(`admin_api/admin_accounts/${this.user.id}/admin_systems_in_query`)
        .then(response => {
          this.allSystems = response.data
        })
    },
    getSystemOperotor () {
      this.$axios.get(`admin_api/admin_accounts/${this.user.id}/get_operator_permission`)
        .then(response => {
          this.initialization()
          this.editOperator(response.data)
        })
    },
    editOperator (domains) {
      if(domains){
        for(let key in domains){
          this.selectSystemsId.push(Number(key))
          let select_system = this.allSystems.find(system => system.id === Number(key))
          if(select_system){
            this.dynamicValidateForm.domains.push(
              { system_id: select_system.id, system_name: select_system.name, operator: domains[key] }
            )
          }
        }
      }
    },
    selectSystem (selectSystemsId) {
      let selected_operators = this.dynamicValidateForm.domains
      let new_domains = []
      for(let key in selectSystemsId){
        let is_selected = selected_operators.find(operator => operator.system_id === selectSystemsId[key])
        let select_system = this.allSystems.find(system => system.id === selectSystemsId[key])
        if(is_selected){
          new_domains.push(is_selected)
        }else{
          new_domains.push({ system_id: select_system.id, system_name: select_system.name, operator: '' })
        }
      }
      this.dynamicValidateForm.domains = new_domains
    },
    checkOperator (index) {
      let domain = this.dynamicValidateForm.domains[index]
      let selectSystems = {}
      selectSystems[domain.system_id] = domain.operator
      this.$axios.post(`admin_api/admin_accounts/${this.user.id}/check_operator`, {
        operator_option: selectSystems
      }).then(response => {
        if(response.data.success){
          this.deleteErrorMessage(domain.system_id)
        }else{
          this.errorMessage.push({class: `error_${domain.system_id}`, message: response.data.message})
        }
        this.addMessage()
      })
    },
    deleteErrorMessage (system_id) {
      let this_span = this.errorMessage.find(span => span.class === `error_${system_id}`)
      var index = this.errorMessage.indexOf(this_span)
      this.errorMessage.splice(index, 1)
      let indexMessage = document.getElementById(`error_${system_id}`)
      indexMessage.innerText = ''
      this.submit = true
    },
    addMessage () {
      for(let key in this.errorMessage){
        let indexMessage = document.getElementById(this.errorMessage[key].class)
        indexMessage.innerText = this.errorMessage[key].message
      }
    },
    handleSubmit () {
      this.$confirm('确认权限设置吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        this.bundleOporetorPermission()
      })
    },
    handleClose () {
      this.dialogVisible = false
    },
    bundleOporetorPermission () {
      this.$axios.post(`admin_api/admin_accounts/${this.user.id}/set_operator_permission`, {
        operator_options: this.dynamicValidateForm
      })
        .then(response => {
          this.dialogVisible = false
          this.$message.success('设置成功')
        })
        .catch(() => {})
    }
  }
}
</script>

