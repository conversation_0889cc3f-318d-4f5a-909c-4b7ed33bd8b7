# frozen_string_literal: true
module Overrides
  # 覆写 SessionsController 修改数据及添加审计日志
  class SessionsController < DeviseTokenAuth::SessionsController
    include ActAsSsoApis
    before_action :password_decrypt
    before_action :audit_login_attempt, only: [:create]
    before_action :should_login!, only: [:create]
    after_action :audit_login, only: [:create]
    before_action :audit_logout, only: [:destroy]

    def create
      super do |resource|
        if resource.disabled_at
          return render_error(401, I18n.t('devise_token_auth.sessions.user_is_disabled'))
        end

        if resource.access_locked?
          return render_error(401, I18n.t('devise_token_auth.sessions.not_confirmed'))
        end
      end
    end

    protected

    def render_create_success
      render json: {
        data: resource_data(resource_json: @resource.token_validation_response.merge(
          need_change_password: @resource.need_change_password?,
          home_page: @resource.home_page
        ))
      }
    end

    def render_destroy_success
      logout_url = nil
      if Setting.frontendSettings&.[]('authMethod') == 'sso'
        case Setting.sso['adapter']
        when 'mszq'
          mszq_logout
        when 'zyfund'
          logout_url = Setting.sso&.[]('logout_url')
        when 'csfund'
          logout_url = Setting.sso&.[]('logout_url')
        else
          logout_url = Setting.sso&.[]('logout_url')
        end
      end
      render json: { success:true , logout_url: logout_url}, status: 200
    end

    private

    def password_decrypt
      params[:password] = aes_decrypt_data(params[:password]) if params[:password]
    end

    def audit_login_attempt
      audit_log! params[:email], action: :login_attempt
    end

    def audit_login
      return unless current_admin

      if current_admin.need_change_password?
        if current_admin.password_change_requested?
          audit_log! action: :password_change_requested
        else
          audit_log! current_admin.password_changed_at, action: :password_expired
        end
      end

      if current_admin.failed_attempts.zero?
        audit_log! action: :login_success
      elsif current_admin.failed_attempts >= Admin.maximum_attempts
        audit_log! action: :user_locked
      else
        audit_log! current_admin.failed_attempts, action: :login_failed
      end
    end

    def audit_logout
      audit_log! action: :logout
    end

  end
end
