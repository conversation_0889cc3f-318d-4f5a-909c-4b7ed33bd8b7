<template>
  <div
    v-loading="loading"
    class="container"
  >
    <h2>{{ title }}</h2>
    <hr>
    <div v-if="showCreateQuaterButton" style="text-align: right;">
      <el-button
        @click="showCreateData"
        :disabled="!$store.getters.hasPermission('quarters.import')"
        type="primary"
        size="small"
        style="margin-bottom:10px;margin-top:-10px;"
      >
        导入新数据
      </el-button>
    </div>
    <el-table
      :data="quarters"
      border
    >
      <el-table-column
        property="id"
        label="ID"
        width="80"
      />
      <el-table-column label="名称" min-width="240px">
        <template slot-scope="scope">
          <span
            v-if="scope.row.edit"
          >
            <el-input
              v-model="scope.row.name"
              size="small"
              style="width: 70%;"
            />
            <el-button
                size="small"
                icon="el-icon-close"
                @click="cancelEdit(scope.row)"
            >
            </el-button>
            <el-button
              size="small"
              icon="el-icon-check"
              type="primary"
              @click="quarterEdit(scope.row)"
            >
            </el-button>
          </span>
          <span v-else>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="import_time"
        label="导入开始时间"
        width="170"
      />
      <el-table-column
        property="imports_spend"
        label="系统导入时长"
        width="110"
      />
      <el-table-column
        property="rakes_spend"
        label="任务执行时长"
        width="110"
      />
      <el-table-column
        fixed="right"
        label="操作"
        width="220"
      >
        <template slot-scope="scope">
          <import-status-popover
            :quarter="scope.row"
            :buttonDisabled="scope.row.status === 'removing'"
          />
          <el-button
            size="small"
            @click="handleEdit(scope.row)"
            :disabled="!$store.getters.hasPermission('quarters.update') || scope.row.status === 'removing'"
          >
            重命名
          </el-button>
          <el-button
            v-loading="scope.row.status === 'removing'"
            size="small"
            type="danger"
            @click="quarterDelete(scope.$index, scope.row)"
            :disabled="!$store.getters.hasPermission('quarters.delete')"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-pagination
      :current-page.sync="currentPage"
      :page-size="pageSize"
      :total="quartersCount"
      background
      layout="total, prev, pager, next, jumper"
      class="quarters-pagination"
    />

    <el-dialog
      :visible.sync="createDialogVisible"
      :close-on-click-modal="false"
      title="导入新数据"
      width="370px"
      center
    >
      <el-form :inline="true" size="small">
        <el-form-item style="margin-left:15px;">
          <el-input v-model="createDataName" placeholder="请输入数据名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" style="margin-left:-5px;" @click="startCreateData">开始导入</el-button>
        </el-form-item>
      </el-form>

    </el-dialog>

    <!-- eslint-enable vue/attribute-hyphenation -->
  </div>
</template>

<script>
import ImportStatusPopover from '@/components/AdminQuarters/ImportStatusPopover'
export default {
  components: {
    ImportStatusPopover
  },
  props: {
    warnings: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      title: this.$t('module_name.quarters'),
      quarters: [],
      loading: false,
      deleteLoading: false,
      currentPage: 1,
      pageSize: 15,
      quartersCount: 0,
      createDialogVisible: false,
      createDataName: '',
      localRow: {},
      timer: 0
    }
  },
  computed: {
    showCreateQuaterButton () {
      return this.$settings.importFromImporter['enable']
    },
    removeQuarters () {
      return this.quarters.filter(x => x.status === 'removing')
    }
  },
  created () {
    for (var w of this.warnings) {
      this.$notify.warning({ title: '警告', message: w, duration: 0 })
    }
    this.quartersUpdate()
    this.getStatusInterval()
  },
  watch: {
    pageSize () {
      this.quartersUpdate()
    },
    currentPage () {
      this.quartersUpdate()
    }
  },
  methods: {
    startCreateData() {
      this.createDialogVisible = false
      this.loading = true
      let obj = {
        name: this.createDataName
      }
      this.$axios.post('/api/quarters', obj)
        .then(response => {
          this.loading = false
          this.quartersUpdate()
          this.$message.success('时间点创建成功，数据正在导入，请在状态中查看导入进度')
        })
        .catch(() => {
          this.loading = false
        })
      this.createDataName = ''
    },
    showCreateData() {
      this.createDialogVisible = true
      this.createDataName = this.getQuarterString()
    },
    // 格式化变化日期，如：2023年07月10日
    getQuarterString () {
      const time = new Date()
      const month = (time.getMonth() + 1).toString().padStart(2, '0')
      const day = (time.getDate()).toString().padStart(2, '0')
      const createdAtText = time.getFullYear() + "年" + month + "月" + day + "日"
      return createdAtText
    },
    quarterDelete (index, row) {
      this.$confirm(`此操作将删除${row.name}所有导入数据，无法恢复，是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          row.status = 'removing'
          this.deleteLoading = true
          this.$axios.delete('/api/quarters', { params: { quarter_id: row.id } })
            .then(response => {
              if (response.data.success) {
                this.quartersUpdate()
                this.deleteLoading = false
                this.$message.success('此时间点所有导入数据已删除')
              } else {
                this.$message.error(response.data.error_message)
              }
            })
            .catch(() => {
              this.deleteLoading = false
            })
        })
        .catch(() => {
          this.$message.info('已取消删除操作')
        })
    },
    quarterEdit (row) {
      this.$axios.put('/api/quarters', { quarter_id: row.id, name: row.name })
        .then(response => {
          if (response.data.success) {
            row.edit = false
          } else {
            this.$message.error(response.data.error_message)
          }
        })
    },
    handleEdit(row) {
      row.edit = true
      this.localRow = { ...row }
    },
    cancelEdit (row) {
      row.edit = false
      row.name = this.localRow.name
    },
    quartersUpdate () {
      if(!this.$store.getters.hasPermission('quarters.query')){ return this.$message.error('您没有权限操作此功能') }
      this.loading = true
      const params = {
        page: this.currentPage,
        per_page: this.pageSize,
      }
      this.$axios.get('/api/quarters/detail', { params: params })
        .then(response => {
          this.quarters = response.data.quarters.map(x => Object.assign(x, { edit: false }))
          this.quartersCount = response.data.count
          this.updateCurrentQuarter(response.data)
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    updateCurrentQuarter (quarters) {
      const quarter = quarters.find(x => x.id === this.$store.state.current_quarter.id)
      if (!quarter) {
        if (quartersCount !== 0) this.$store.commit('quarterChange', quarters[0])
      }
    },
    // 轮询获取状态
    getStatusInterval () {
      this.timer = setInterval(this.getStatus, 8000)
    },
    getStatus () {
      if (this.removeQuarters.length > 0) {
        this.getDataStatus()
      }
    },
    getDataStatus () {
      const removeIds = this.removeQuarters.map(x => x.id)
      if (removeIds.length === 0) return

      const params = {
        ids: removeIds
      }
      this.$axios.get('/api/quarters/detail', { params: params })
        .then(response => {
          const ids = this.quarters.map(x => x.id)
          const data = response.data.quarters
          const dataIds = data.map(x => x.id)
          const deleteIds = removeIds.filter(x => dataIds.indexOf(x) < 0)

          this.$lodash.each(deleteIds, (deleteId, i) => {
            const index = ids.indexOf(deleteId)
            if (index >= 0) {
              this.quarters.splice(index, 1)
            }
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .container{
    padding: 2em;
    background-color: white;
    min-width: 600px;

    .tip{
      color: gray;
    }
    .el-button{
      margin-left: 5px;
    }
    .quarters-pagination{
      margin-top: 20px;
    }
  }

</style>
