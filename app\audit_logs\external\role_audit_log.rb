# frozen_string_literal: true

# 外部系统角色日志
class External::RoleAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '外部系统角色管理'
    @operation_category = '外部系统角色管理'
    super
  end

  def update
    @operation = '更新外部系统角色'
    @comment = generate_update_log(params)
    create_audit_log if @comment.present?
  end

  def create
    @operation = '创建外部系统角色'
    @comment   = generate_create_log(params)
    create_audit_log if @comment
  end

  def destroy
    @operation = '删除外部系统角色'
    @comment   = "外部系统「#{params.external_business_system&.name}」删除角色：#{params.name} 成功，ID：#{params.id}，编码：#{params.code}"
    create_audit_log
  end

  protected

  # 生成创建日志
  def generate_create_log(params)
    strs = []
    bs = params.external_business_system
    output = params.output
    role_settings = bs.role_settings.select { |o| o['is_enable'] }
    code_display_name = bs.role_field_name('code')
    name_display_name = bs.role_field_name('name')
    status_display_name = bs.role_field_name('status')
    strs << "#{code_display_name}：#{params.code}" if output[:code].present?
    strs << "#{name_display_name}：#{params.name}" if output[:name].present?
    strs << "#{status_display_name}：#{output[:status_text]}" if output[:status].present?
    role_settings.each do |obj|
      field = obj['name']
      display_name = bs.role_field_name(field)
      value = params.send(field)
      strs << "#{display_name}：#{value}" if value.present?
    end
    return if strs.blank?

    "外部系统「#{bs&.name}」创建角色：#{params.name} 成功，ID：#{params.id}，#{strs.join('，')}"
  end

  # 生成更新日志
  def generate_update_log(params)
    data = params[1].to_a - params[0].to_a
    bs = params[2]
    strs = []
    data.each do |o|
      field_name = bs.role_field_name(o[0].to_s)
      strs << "#{field_name}: 「#{o[1]}」" if field_name.present?
    end
    return if strs.blank?

    "更新外部系统角色 ID：「#{params[0][:id]}」，#{strs.join('，')}"
  end
end
