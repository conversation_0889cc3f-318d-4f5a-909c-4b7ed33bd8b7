<template>
  <div class="page-container">
    <el-page-header
      :content="`编辑权限--${roleName}`"
      @back="goBack"
    />
    <el-tabs 
      v-model="activeName"
      type="card"
      style="margin-top:20px"
    >
      <el-tab-pane 
        label="菜单权限"
        name="first"
      >
        <menu-permissions-form
          ref="menuPermissionForm"
          v-show="activeName === 'first'" 
          :role-id="roleId"
          :role-name="roleName"
          :module-function-ids="moduleFunctionIds"
        />
        <!-- @update="updateRoleMenu" -->
      </el-tab-pane>
      <el-tab-pane 
        label="告警权限"
        name="second"
        v-if="alertRouters.length > 0 && globalAlertEnable"
      >
        <alert-permissions-form
          ref="alertPermissionForm"
          v-show="activeName === 'second'"
          :role-id="roleId"
          :role-name="roleName"
          :alert-permissions="alertPermissions"
        />
      </el-tab-pane>
    </el-tabs>
    <el-row style="margin-top: 20px;">
      <el-col :span="12">
        <el-button
          size="small"
          type="primary"
          @click="submit"
        >
          保  存
        </el-button>
        <el-button
          size="small"
          @click="goBack"
        >
          返  回
        </el-button>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import API from '@/api'
import MenuPermissionsForm from './MenuPermissionsForm'
import AlertPermissionsForm from './AlertPermissionsForm'

export default {
  components: {
    MenuPermissionsForm,
    AlertPermissionsForm
  },
  props: {
    roleId: {
      type: [String, Number],
      required: true
    },
    roleName: {
      type: String,
      required: true
    },
    moduleFunctionIds: {
      type: Array,
      required: true
    },
    alertPermissions: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      activeName: 'first'
    }
  },
  computed: {
    alertRouters () {
      return this.$store.state.alertRouters
    },
    globalAlertEnable () {
      const globalAlert = this.$store.state.settings.globalAlert
      return globalAlert && globalAlert.enable
    }
  },
  methods: {
    submit () {
      this.$refs.menuPermissionForm.handleUpdatePermissions()
      setTimeout(() => {
        this.$refs.alertPermissionForm.handleUpdatePermissions()
      }, 1000)
    },
    goBack () {
      this.$confirm('未提交的表单数据将不会保存，且无法恢复，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          this.$emit('update')
        })
        .catch(() => {
        })
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
