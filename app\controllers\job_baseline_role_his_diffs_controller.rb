class JobBaselineRoleHisDiffsController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!

  def create_diff_data
    quarter_id = params[:quarter_id]
    compared_quarter_id = params[:compared_quarter_id]
    last_job_start_time = JobBaselineHistoryServices::ComparedRunner.new(quarter_id, compared_quarter_id).process_start_time
    # 防止用户多次点击，5 秒内多次点击不生效
    if last_job_start_time.nil? || Time.now - last_job_start_time > 5
      JobBaselineRoleHistoryCompareJob.perform_later(quarter_id, compared_quarter_id)
      json_respond(success: true)
    else
      json_respond(success: false)
    end
  end

  def query_diff_status
    quarter_id          = params[:quarter_id]
    compared_quarter_id = params[:compared_quarter_id]
    status              = JobBaselineRoleHisComparedStatus.status(quarter_id, compared_quarter_id)

    if status
      json_respond status.reload.output
    else
      json_respond(
        quarter_id:          quarter_id,
        compared_quarter_id: compared_quarter_id,
        status:              'not_exist'
      )
    end
  end

  def query_diff_data
    search_params = JSON.parse(role_history_diff_params[:filter]).deep_symbolize_keys
    page = role_history_diff_params[:page]
    per_page = role_history_diff_params[:per_page]
    result = JobBaselineHistoryServices::DiffDataQuery.new(search_params, page, per_page).execute
    if result[:is_success]
      json_respond(success: true, data: result[:data])
    else
      json_respond(success: false, message: result[:message])
    end
  end

  def diff_detail
    history_diff = JobBaselineRoleHistoryDiff.find_by(id: params[:diff_id])
    json_respond(history_diff.output)
  end

  # 更新最新时间点岗位基线绑定角色历史
  def update_role_history
    newest_quarter = Quarter.last
    JobBaselineHistoryServices::GenerateHistory.new(newest_quarter).execute
    json_respond(success: true)
  end

  private

  def authenticate_policy!
    authorize JobBaselineRoleHistoryDiff
  end

  def role_history_diff_params
    params.permit(:page, :per_page, :filter)
  end
end
