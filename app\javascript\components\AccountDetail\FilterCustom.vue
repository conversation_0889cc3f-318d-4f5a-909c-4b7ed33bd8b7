<template>
  <div>
    <o32-temp-filter
      v-if="filterMethod === 'o32_temp'"
      :filterData="filterData"
      @change="afterFilter"
    />
  </div>
</template>

<script>
import O32TempFilter from './FilterCustoms/O32TempFilter.vue'
export default {
  components: {
    O32TempFilter
  },
  props: {
    filterData: {
      type: Array,
      required: true
    },
    tableSchema: {
      type: Array,
      required: true
    },
    filterMethod: {
      type: String,
      required: true
    }
  },
  methods: {
    afterFilter (payload) {
      this.$emit('change', payload)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
