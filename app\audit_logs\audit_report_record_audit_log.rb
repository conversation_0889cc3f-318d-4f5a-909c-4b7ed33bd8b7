# frozen_string_literal: true

# 业务系统维护的审计日志
class AuditReportRecordAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '审计报告导出'
    @operation_category = '数据导出'
    super
  end

  def create
    @operation = '创建审计报告导出记录'
    @comment   = output_record
    create_audit_log
  end

  def verify_anomalies
    @operation = '更新审计报告导出记录'
    @comment   = "「#{user.name}」核查并确认了「#{params.business_system.name}」的系统权限变更情况。"
    create_audit_log
  end

  def edit_detail
    @operation = '更新审计报告导出记录'
    @comment   = "「#{user.name}」更新了「#{params.business_system.name}」的异常情况为：#{detail_output}"
    create_audit_log
  end

  def destroy
    @operation = '删除审计报告导出记录'
    @comment   = "删除了#{Admin.find(params.admin_id).name}在#{params.start_at.strftime('%Y年%m月%d日')}创建的导出记录"
    create_audit_log
  end

  def create_report_success
    @operation    = '审计报告导出'
    @download_url = params[:download_url]
    @job_id       = params[:job_id]
    @comment      = "导出「#{report_output}」成功"
    create_audit_log
  end

  def create_report_faild
    @operation = '审计报告导出'
    @comment   = '审计报告导出失败'
    create_audit_log
  end

  private

  def output_record
    "开始日期：#{format_date(params.start_at)}, 结束日期：#{format_date(params.end_at)}, 系统：#{output_systems_name}, 告警分类：#{output_alerts_name}, 唯一标识：#{params.unique}"
  end

  def format_date(date)
    date.strftime('%Y年%m月%d日')
  end

  def output_systems_name
    params.details.map(&:business_system).pluck(:name).join(',')
  end

  def output_alerts_name
    GlobalAlertCategory.where(id: params.alert_ids).pluck(:name).join(',')
  end

  def report_output
    @record = params[:record]

    report_summarize
  end

  def detail_output
    @record = params.output

    detail_summarize
  end

  def detail_summarize
    output_by_detail.join('：')
  end

  def report_summarize
    "异常内容：#{output_by_record}"
  end

  def output_by_detail
    @record[:system_detail].map do |d|
      abnormal_output(d)
    end
  end

  def output_by_record
    @inserts = @record.details.map(&:output)

    output = ''
    @inserts.each do |insert|
      output += "「#{insert[:name]}」"
      insert[:system_detail].each do |d|
        output += "#{abnormal_output(d)}；"
      end
    end
    output
  end

  def abnormal_output(hash)
    hash[:status] ? "存在#{hash[:title]}，异常原因：#{hash[:content]}" : "不存在#{hash[:title]}"
  end
end
