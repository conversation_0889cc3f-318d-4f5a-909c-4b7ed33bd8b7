<template>
  <div class="container">
    <div class="step-container">
      <!-- eslint-disable vue/attribute-hyphenation -->
      <el-steps
        :active="active"
        finish-status="success"
      >
        <el-step
          title="新建导入数据"
          icon="el-icon-edit"
        />
        <el-step
          title="上传数据文件"
          icon="el-icon-upload"
        />
        <el-step
          title="完成导入"
          icon="el-icon-circle-check-outline"
        />
      </el-steps>
      <!-- eslint-enable vue/attribute-hyphenation -->
    </div>

    <div class="operation-container">
      <create-quarter
        v-if="active === 0"
        ref="createQuarter"
        @stepFinish="stepFinish"
        @create="newQuarter"
        @change="changeQuarter"
      />

      <upload-data
        v-if="active === 1"
        :quarter="quarter"
      />
      <div
        v-loading="loading"
        v-if="active === 2"
        class="operation-finish"
      >
        <p class="prompt">点击下方「完成」按钮开始进行数据导入。</p>
        <p class="prompt">数据导入需要一段时间执行，您可以在「{{this.$t('module_name.quarters')}}」功能中随时查看导入状态。</p>
        <p class="prompt">如果右侧显示错误通知，请返回上一步，修改并重新上传相应数据文件。</p>
      </div>
    </div>

    <div class="button-container">
      <el-button
        v-if="active === 0"
        @click="findOrCreateQuarter"
        :disabled="!$store.getters.hasPermission('admin_new_quarter.create')"
      >
        下一步
      </el-button>
      <el-button
        v-if="active === 1"
        @click="stepFinish"
      >
        下一步
      </el-button>
      <el-button
        v-if="active === 2"
        @click="returnUploadStep"
      >
        返回上一步
      </el-button>
      <el-button
        v-if="active === 2"
        @click="finish"
      >
        完成
      </el-button>
    </div>
  </div>
</template>

<script>
import CreateQuarter from './CreateQuarter'
import UploadData    from './UploadData'
import API           from '@/api'

export default {
  components: {
    CreateQuarter,
    UploadData
  },
  data () {
    return {
      active:                   0,
      quarter:                  { id: null },
      loading:                  false,
      check_success_system_ids: [],
      warnings:                 []
    }
  },
  methods:    {
    stepFinish () {
      this.active++
    },
    newQuarter (payload) {
      this.quarter = payload
    },
    changeQuarter (payload) {
      this.quarter.id = payload
    },
    findOrCreateQuarter () {
      this.$refs.createQuarter.findOrCreateQuarter()
    },
    returnUploadStep () {
      this.active--
    },
    finish () {
      this.loading = true
      API.quarters.checkUploadData(this.quarter.id)
        .then(response => {
          if (response.data.success) {
            this.loading                  = false
            this.warnings                 = response.data.warnings
            this.check_success_system_ids = response.data.check_success_system_ids

            const importParams = { quarter_id: this.quarter.id, system_ids: this.check_success_system_ids }
            return API.quarters.uploadData(importParams)
          } else {
            this.showCheckErrorsResult(response.data)
          }
        })
        .then(response => {
          if (response.data.success) {
            this.$message.success('数据导入进行中...')
            this.$router.push({ name: 'admin_quarters', params: { warnings: this.warnings } })
          } else {
            this.$message.error(response.data.error_message)
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    showCheckErrorsResult (checkResult) {
      for (const w of checkResult.warnings) {
        this.$notify.warning({ title: '警告', message: w, duration: 0 })
      }
      for (const e of checkResult.errors) {
        this.$notify.error({ title: '检查错误', message: e, duration: 0 })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/_variables";

.container {
  background-color: white;
  min-height:       600px;
  min-width:        1000px;
  padding:          3em;

  .step-container {
    @include vertical_center;
    height: 5em;
  }

  .operation-container {
    height: 450px;
  }
  .operation-finish {
    padding-top: 10em;

    .prompt {
      font-size:  1.2em;
      text-align: center;
    }
  }

  .el-steps {
    padding: 3em;
    width:   80%;
  }

  .button-container {
    width: 100%;
    @include vertical_center;
  }
}

</style>
