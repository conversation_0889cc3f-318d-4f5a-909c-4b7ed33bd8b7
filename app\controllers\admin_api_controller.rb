require 'chinese_pinyin'

class AdminApiController < ApplicationController
  before_action :authenticate_admin!
  before_action :should_login!, only: [:ghost_login]
  # before_action :authenticate_admin_api!, except: %i[my_function_codes my_admin_routers upload_system_data excel_list list_upload_data my_systems_status]
  before_action :check_upload_system_data, only: [:upload_system_data]
  skip_before_action :verify_authenticity_token, only: :upload_system_data

  before_action :authenticate_policy!, only: %i[my_systems_status upload_system_data check_upload_data department_create]

  def ghost_login
    if Setting.ghost && admin_signed_in? && pundit_user.super_admin?
      ghost_admin = Admin.find_by(id: params[:admin_id])

      json_custom_respond(400, success: false, error_message: 'not found admin') unless ghost_admin

      current_admin = ghost_admin
      admin_info    = current_admin.token_validation_response.merge(is_ghost: true)
      token         = current_admin.create_new_auth_token

      audit_log! current_admin, action: :ghost_login_success
      json_respond({ token: token, admin: admin_info })

    else
      audit_log! action: :ghost_login_failed
      json_custom_respond(403, success: false, error_message: 'not allow')
    end
  end

  # 前端获取权限
  def my_function_codes
    json_respond current_admin.module_function_codes
  end

  def my_admin_routers
    json_respond current_admin.admin_frontend_routers
  end

  def my_admin_privileges
    json_respond current_admin.admin_privileges
  end

  # 用于首页「我的系统」模块
  def my_systems_status
    quarter = Quarter.last
    return [] unless quarter

    systems = current_admin.business_systems_in_maintain

    json_respond(systems.map { |x| x.quarter_focus_info(quarter) })
  end

  def test
    render json: 'success'.to_json
  end

  def users_regenerate_export_files
    export_mode = Setting.users_manager&.[]('regenerate_export_files') || 'last'
    case export_mode
    when 'last'
      users_regenerate_the_quarter_export_files
    when 'all'
      users_regenerate_all_quarter_export_files
    else
      return json_custom_respond(400, success: false, error_message: "错误的导出配置项: #{export_mode}")
    end

    audit_log!

    json_respond(success: true, errors: [])
  end

  def user_upload
    # file store
    user_file            = UserUploader.new
    user_file.custom_dir = 'users/'
    begin
      user_file.store!(params[:file])

      audit_log! params[:file]
    rescue CarrierWave::IntegrityError => e
      audit_log! params[:file], action: :user_upload_failed

      json_custom_respond(415, success: false, error_message: e.message)
      return false
    end

    begin
      if user_file.file.exists?
        user_import = DataImport.find_class.new(user_file.path)
        json = user_import.check_file
        return json_custom_respond(415, success: false, error_message: json[:errors].join(', ')) unless json[:success]

        user_import.import

        audit_log! params[:file], action: :import_user_data

        AfterImport::UsersLogTask.new(Quarter.last.id).run_to_do if Quarter.last.present?
      end
    rescue DataImport::ValidateFailure => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }

      audit_log! params[:file], action: :import_user_data_failed

      json_custom_respond(500, success: false, error_message: e.message)
      return false
    rescue StandardError => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }

      audit_log! params[:file], action: :import_user_data_failed

      json_custom_respond(500, success: false, error_message: 'DataFile parse failed.')
      return false
    end

    json_respond(success: true, errors: [])
  end

  def fzfb_user_info_upload
    begin 
      service = AdminApiServices::FzfbUserImporterService.new(params[:file]).execute
      if service[:is_success] 
        render json: { success: true, message: '上传成功' }
      else
        render json: { success: false, message: service[:message] } 
      end
    rescue StandardError => e
      json_custom_respond(500, success: false, error_message: e.message)
      return false
    end
  end

  def fzfb_oa_contact_info_upload
    begin
      service = AdminApiServices::FzfbOaContactInfoUploadService.new(params[:file]).execute
      if service[:is_success]
        render json: { success: true, message: '上传成功' }
      else 
        render json: { success: false, message: service[:message] } 
      end
    rescue StandardError => e
      json_custom_respond(500, success: false, error_message: e.message)
      return false
    end
  end

  def list_upload_data
    quarter_id         = params[:quarter_id].to_i
    business_system_id = params[:business_system_id].to_i

    quarter         = Quarter.find_by(id: quarter_id)
    business_system = BusinessSystem.find(business_system_id)
    upload_dir = DataImport.import_path(quarter, business_system)
    files      = Dir.glob("#{upload_dir}/*.{xls,xlsx,txt,csv}")
                   .delete_if { |x| File.basename(x).match(/^~\$.*/) }
                   .map { |x| { name: File.basename(x) } }
    json_custom_respond(200, success: true, files: files)
  end

  def upload_system_data
    upload_business_system_data
  end

  def delete_uploaded_data
    quarter_id         = params[:quarter_id].to_i
    business_system_id = params[:business_system_id].to_i
    file_name          = params[:file].gsub(%r{(\s|/)}, '_')

    quarter         = Quarter.find_by(id: quarter_id)
    business_system = BusinessSystem.find(business_system_id)

    upload_dir = DataImport.import_path(quarter, business_system)

    FileUtils.rm_f "#{upload_dir}/#{file_name}"

    quarter_name = quarter.present? ? quarter.name : '无时间点'
    audit_log!({
                 'quarter_name'         => quarter_name,
                 'business_system_name' => business_system.name,
                 'file_name'            => file_name
               })

    json_custom_respond(200, success: true, error_message: nil)
  end

  def check_upload_data
    quarter_id               = params[:quarter_id].to_i
    quarter                  = Quarter.find(quarter_id)
    errors                   = []
    warnings                 = []
    check_success_system_ids = []
    BusinessSystem.inservice.upload_type.each do |the_system|
      the_class = if the_system.id.in? [5, 6, 7, 27, 28, 29]
                    'DataImport::ZhixiaoImport'
                  else
                    "DataImport::#{(the_system.prefix + '_import').camelize}"
                  end
      # 直销三系统，只检查 5
      next if the_system.id.in? [6, 7, 28, 29]

      import = Kernel.const_get(the_class).new(quarter)
      if import.data_files.empty?
        warnings << "#{the_system.name}: 未上传数据文件，跳过该系统数据导入。"
      else
        result = import.check
        if result[:success]
          check_success_system_ids << the_system.id
          warnings += result[:warnings]
        else
          errors   += result[:errors]
          warnings += result[:warnings]
        end
      end
    end
    if errors.size.zero?
      json_respond(success: true, check_success_system_ids: check_success_system_ids, errors: [], warnings: warnings)
    else
      json_respond(success: false, errors: errors, warnings: warnings)
    end
  end

  def import_upload_data
    quarter_id = params[:quarter_id].to_i
    system_ids = params[:system_ids].map(&:to_i)

    quarter = Quarter.find_by(id: quarter_id)

    system_ids.each do |the_system_id|
      the_system = BusinessSystem.find(the_system_id)
      DataImportJob.perform_later(quarter_id, the_system.id, system_ids)

      quarter_name = quarter.present? ? quarter.name : '无时间点'
      audit_log!({
                   'quarter_name'         => quarter_name,
                   'business_system_name' => the_system.name
                 })
    end

    json_custom_respond(200, success: true, error_message: nil)
  end

  def department_create
    name = params[:name]

    if Department.find_by_name(name)
      return_data = { success: false, errors: ['部门已存在'] }
      json_respond return_data
      return nil
    end

    department           = Department.new
    department.name      = name
    department.inservice = true

    if department.save

      audit_log! department

      return_data = { success: true, data: { id: department.id, name: department.name } }
      json_respond return_data
    else

      audit_log! department, action: :department_create_failed

      return_data = { success: false, error_message: 'Cannot create department.' }
      json_respond return_data
    end
  end

  def department_update
    name = params[:name]
    id   = params[:id].to_i
    begin
      department = Department.find(id)
      authorize department, policy_class: AdminApiPolicy
    rescue ActiveRecord::RecordNotFound => e
      json_custom_respond(404, success: false, error_message: 'Department not found.')
      return nil
    end
    if department.update(name: name)

      audit_log! department

      json_respond(success: true, error_message: nil)
    else

      audit_log! department, action: :department_update_failed

      json_respond(success: false, error_message: 'Update department failed.')
    end
  end

  def department_destroy
    begin
      department = Department.find(params[:id].to_i)
      authorize department, policy_class: AdminApiPolicy
    rescue ActiveRecord::RecordNotFound => e
      json_custom_respond(404, success: false, error_message: 'Department not found.')
      return nil
    end

    audit_log! department

    department.users.update_all(department_id: nil)
    department.destroy
    json_respond(success: true, error_message: nil)
  end

  private

  def authenticate_policy!
    authorize nil, policy_class: AdminApiPolicy
  end

  # 校验上传的excel名称以及excel里列名
  def check_upload_system_data
    @quarter         = Quarter.find_by(id: params[:quarter_id].to_i)
    @business_system = BusinessSystem.find(params[:business_system_id].to_i)
    file_name = params[:file].original_filename.split('.').first
    return unless Setting.create_system&.[]('enable') && Setting.upload_quarter_file&.[]('enable')

    system_scaffold = @business_system.system_scaffold
    return if system_scaffold.blank?

    system_ids = current_admin.business_systems_in_query.map(&:id)
    unless system_ids.include?(@business_system.id)
      message = "您没有权限上传系统【#{@business_system.name}】的EXCEL"
      audit_log!({ 'file' => params[:file], 'quarter_name' => @quarter&.name, 'business_system_name' => @business_system.name, 'message' => "上传文件失败：#{message}" })
      return json_custom_respond(415, success: false, error_message: message)
    end

    file_names = system_scaffold.import_file_names
    unless file_names.any? { |x| file_name =~ /^#{x}(.*)/ }
      message = "只支持上传【#{file_names.map { |name| name + '.xlsx' }.join('、')}】"
      audit_log!({ 'file' => params[:file], 'quarter_name' => @quarter&.name, 'business_system_name' => @business_system.name, 'message' => "上传文件失败：#{message}" })
      return json_custom_respond(415, success: false, error_message: message)
    end
    mapping_info = system_scaffold.all_mapping_infos.find { |json| json[:file_name] == file_name }
    return if mapping_info.nil?

    book = RubyXL::Parser.parse(params[:file])
    sheet = book.worksheets[0]
    if sheet[0].cells.compact.map(&:value).compact != mapping_info[:columns]
      message = "#{@business_system.name}错误：#{file_name}.xlsx 列结构与预期不符，预期为【#{mapping_info[:columns].join(',')}】"
      audit_log!({ 'file' => params[:file], 'quarter_name' => @quarter&.name, 'business_system_name' => @business_system.name, 'message' => "上传文件失败：#{message}" })
      return json_custom_respond(415, success: false, error_message: message)
    end
  end

  # 如果不是自定义系统，先保存，后校验，如果校验失败则删除文件
  # 先校验的话，文件名是存到/temp下的，不是原始文件名
  def check_file(files)
    system_scaffold = Setting.create_system&.[]('enable') ? @business_system.system_scaffold : nil
    return { success: true } unless system_scaffold.nil?

    importer_class = @business_system.import_class.new(nil)
    importer_class.set_files(files)
    importer_class.check
  end

  def upload_business_system_data
    # 如果没有时间点，就是直接上传excel到系统，并且校验
    upload_file = if @quarter
                    QuarterUploader.new
                  else
                    BusinessSystemUploader.new
                  end
    upload_file.custom_dir = DataImport.import_path(@quarter, @business_system)
    quarter_name = @quarter.present? ? @quarter.name : '无时间点'
    begin
      upload_file.store!(params[:file])
      if Setting.create_system&.[]('enable') && Setting.upload_quarter_file&.[]('enable') && @business_system.system_scaffold
        @business_system.system_scaffold.update(update_date: Date.today)
      end
      file_path = upload_file.file.file
      result = check_file([file_path])
      if result[:success]
        audit_log!({ 'file' => params[:file], 'quarter_name' => quarter_name, 'business_system_name' => @business_system.name })
      else
        FileUtils.rm_f(file_path) if File.exist? file_path
        msg = result[:errors].flatten.uniq.compact.join('，')
        audit_log!({ 'file' => params[:file], 'quarter_name' => @quarter&.name, 'business_system_name' => @business_system.name, 'message' => "上传文件失败：#{msg}" })
        return json_custom_respond(415, success: false, error_message: msg)
      end
    rescue CarrierWave::IntegrityError => e
      audit_log!({ 'file' => params[:file] }, action: :upload_business_system_data_failed)
      return json_custom_respond(415, success: false, error_message: e.message)
    end
    json_custom_respond(200, success: true, error_message: nil)
  end

  def users_regenerate_all_quarter_export_files
    Quarter.all.each do |quarter|
      users_regenerate_the_quarter_export_files(quarter)
    end
  end

  def users_regenerate_the_quarter_export_files(quarter = Quarter.last)
    FileUtils.rm_rf DataExport.word_export_base_path(quarter.id)
    FileUtils.rm_rf DataExport.excel_export_base_path(quarter.id)

    FileUtils.rm_rf ReportExport.export_base_path(quarter.id)

    BusinessSystem.inservice.each do |the_system|
      the_system.account_class.where(quarter_id: quarter.id).each do |account|
        account.export_word
        account.export_excel
      end
    end

    AnalyzeDepartmentSystemCountJob.perform_later(quarter.id)
    GlobalAnalysisReportJob.perform_later(quarter.id)
  end
end
