<template>
  <el-card :class="{'global-card': true, 'alert-group-card': true, 'global-card-large': biggerStyle }">
    <div slot="header" class="clearfix">
      <div class="place-box"></div>
      <div>告警任务处理进度</div>
    </div>

    <div class="legend-container">
      <div class="block">
        <div class="number no-processed">{{ noProcessedCount }}</div>
        <div class="label-line">
          <div class="box no-processed-box" />
          <div class="label">未处理告警</div>

        </div>
      </div>
      <div class="block">
        <div class="number processed">{{ processedCount }}</div>
        <div class="label-line">
          <div class="box processed-box" />
          <div class="label">已处理告警</div>
        </div>
      </div>
    </div>
    <div
      :id="id"
      v-loading="getLoading"
      :class="className"
      :style="{ height: height, width: width }"
    />
  </el-card>
</template>

<script>
import * as echarts from 'echarts'
import resize       from './mixins/resize'

export default {
  mixins: [resize],
  props:  {
    className:      {
      type:    String,
      default: 'chart'
    },
    id:             {
      type:    String,
      default: 'chart'
    },
    processedCount: {
      type:    Number,
      default: 0
    },
    allAlertsCount: {
      type:    Number,
      default: 0
    },
    biggerStyle: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      chart:      null,
      val:        '',
      timer:      null,
      getLoading: false,
      width: '100%',
      height: '270px'
    }
  },
  computed: {
    mainVal () {
      return (Math.ceil((this.processedCount / this.allAlertsCount) * 100) || this.defaultPercent(this.allAlertsCount)).toFixed(0)
    },
    noProcessedCount () {
      return this.allAlertsCount - this.processedCount
    }
  },
  watch:    {
    mainVal (newVal, oldVal) {
      this.resetChart(newVal)
    }
  },
  mounted () {
    this.resetChart(this.val)
  },
  beforeDestroy () {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    resetChart (val) {
      this.getLoading = true
      this.val        = val
      this.initChart()
      this.getLoading = false
    },
    defaultPercent (allAlertsCount) {
      let defaultPercent

      if (allAlertsCount > 0) {
        defaultPercent = 0
      } else {
        defaultPercent = 100
      }
      return defaultPercent
    },
    initChart () {
      this.chart && this.chart.dispose()
      const chartDom = document.getElementById(this.id)
      this.chart     = echarts.init(chartDom)

      // 占位使用, 不让两个扇形连接起来, 占总体约 1% 的宽度
      const PlaceHolderValue = this.allAlertsCount / 100
      const placeHolderItem = {
        name:      '',
        value:     PlaceHolderValue,
        itemStyle: {
          normal: {
            label:       {
              show: false
            },
            labelLine:   {
              show: false
            },
            color:       'rgba(0, 0, 0, 0)',
            borderColor: 'rgba(0, 0, 0, 0)',
            borderWidth: 0
          }
        }
      }
      const processedItem   = {
        name:      '已处理',
        value:     this.processedCount,
        itemStyle: {
          color: '#0E6EFF'
        }
      }
      const noProcessedItem = {
        name:      '未处理',
        value:     this.noProcessedCount,
        itemStyle: {
          color:         '#11D4FF',
          shadowColor:   'rgba(0,0,0,0.1)',
          shadowBlur:    5,
          shadowOffsetY: 5,
          shadowOffsetX: 5
        }
      }
      let data              = []

      if (this.processedCount === 0 && this.noProcessedCount === 0) {
        data.push(processedItem)
      } else {
        data.push(processedItem)
        data.push(placeHolderItem)
        data.push(noProcessedItem)
        data.push(placeHolderItem)
      }

      const option = {
        title:  [
          {
            text:      `${this.mainVal}%`,
            x:         '50%',
            y:         '38%',
            textAlign: 'center',
            textStyle: {
              fontSize:   '40',
              fontWeight: '800',
              color:      '#0E6EFF',
              textAlign:  'center'
            }
          },
          {
            text:      '处理进度',
            left:      '50%',
            top:       '55%',
            textAlign: 'center',
            textStyle: {
              fontSize:   '18',
              fontWeight: '400',
              textAlign:  'center'
            }
          }
        ],
        series: [
          {
            type:      'pie',
            radius:    [85, 110],
            center:    ['50%', '50%'],
            roundCap:  true,
            clockwise: false,
            labelLine: {
              normal: {
                show: true
              }
            },
            label:     {
              show: false,
            },
            data:      data
          }
        ]
      }

      this.chart.setOption(option, true)
    }
  }
}
</script>

<style lang="scss">
@import "~@/components/_variables";
.global-card{
  width:       380px;
  height:      400px;
}
.global-card-large{
    width:    calc((100vw - 100px) / 3) !important;
}

.alert-group-card {
  margin-top:  0px !important;

  .el-card__header {
    padding:    15px;
    text-align: left;

    .clearfix {
      @include vertical_center_left;
      font-family: "微软雅黑";

      .place-box {
        margin-right:  10px;
        width:         5px;
        height:        22px;
        line-height:   22px;
        border-radius: 5px;
        background:    linear-gradient(to bottom, #001FFF, #ffffff);
      }
    }
  }

  .el-card__body {
    padding: 10px 20px 20px 20px;
  }
}

.legend-container {
  @include vertical_center_between;
  height: 60px;

  .block {
    padding-left:  40px;
    padding-right: 40px;

    .number {
      font-weight: bolder;
      font-size:   30px;
    }

    .label-line {
      display:   block;
      @include vertical_center;
      font-size: 12px;

      .box {
        width:         10px;
        margin-right:  10px;
        height:        10px;
        line-height:   10px;
        border-radius: 0px;
      }

      .label {
        text-align: left;
      }
    }

    .no-processed {
      color: #11D4FF;
    }

    .no-processed-box {
      background-color: #11D4FF;
    }

    .processed {
      color: #0E6EFF;
    }

    .processed-box {
      background-color: #0E6EFF;
    }
  }
}
</style>
