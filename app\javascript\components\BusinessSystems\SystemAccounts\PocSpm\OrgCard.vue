<template>
  <el-card
    shadow="hover"
    class="org-card"
  >
    <div slot="header">
      <span>机构管理</span>
    </div>
    <div class="container">
      <el-tree
        v-loading="loading"
        :data="orgTree"
        :props="dataProps"
        :expand-on-click-node="false"
        :default-expanded-keys="['999999']"
        node-key="department_id"
        @node-click="handleNodeClick"
      />
    </div>
  </el-card>
</template>

<script>
import { catchError } from '@/utils/axios_utils'

export default {
  props:   {
    systemId: {
      type:     Number,
      required: true
    }
  },
  data () {
    return {
      loading:   false,
      orgTree:   [],
      dataProps: {
        children: 'children',
        label:    'org_name'
      }
    }
  },
  created () {
    this.getOrgs()
  },
  methods: {
    getOrgs () {
      if (this.systemId === 0) return

      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/organizations/tree`)
        .then(response => {
          this.loading = false
          this.orgTree = response.data
        })
        .catch(error => {
          this.loading = false
          catchError(error, '获取机构列表失败')
        })
    },
    handleNodeClick (data, node, vm) {
      this.$emit('change', data.org_id)
    }
  }
}
</script>

<style lang="scss" scoped>
  .org-card {
    width: auto;
    display:inline-block !important;
    display:inline;
    overflow-x: auto;
    min-height: calc(100vh - 450px);
  }
</style>