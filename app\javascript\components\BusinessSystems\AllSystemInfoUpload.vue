<template>
  <!-- eslint-disable vue/attribute-hyphenation -->
  <el-upload
    :action="uploadUrl"
    :headers="headers"
    :file-list="fileList"
    :show-file-list="false"
    :on-success="handleSuccess"
    :on-error="handleError"
    :before-upload="beforeFileUpload"
    :on-progress="handleProgress"
    :disabled="!$store.getters.hasPermission('all_system_info.system_info_import')"
  >
  <!-- 上传按钮需要设置el-upload的disabled，否则还会出现弹窗 -->
    <el-button
      v-loading.fullscreen.lock="loading"
      element-loading-text="正在导入系统数据，请稍后……"
      type="primary"
      size="small"
      :disabled="!$store.getters.hasPermission('all_system_info.system_info_import')"
    >
      导入
    </el-button>
  </el-upload>
  <!-- eslint-enable vue/attribute-hyphenation -->
</template>

<script>
import API from '@/api'
export default {
  data () {
    const headers = API.tool.getToken()
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    headers['X-CSRF-Token'] = csrfToken

    return {
      headers:  headers,
      loading: false,
      fileList: []
    }
  },
  computed: {
    uploadUrl () {
      return API.tool.absoluteUrlFor(`/api/all_system_info/upload`)
    }
  },
  methods: {
    handleSuccess (response, file, fileList) {
      this.loading = false
      if (response.fail_errors.length > 0) {
        this.OpenNotify(response)
      }
      this.$message.success('系统数据已成功更新')
      this.$emit('update')
    },
    handleError (err, file, fileList) {
      this.loading = false
      const errorMsg = JSON.parse(err.message)
      this.$message.error(errorMsg.error_message)
    },
    beforeFileUpload (file) {
      const matches = file.name.match(/.*\.xlsx/i)
      // 上一行匹配成功返回匹配字符串，失败返回 null
      // 而结果只会判断 true or false，因此要做一下转换
      const isxlsx = matches !== null

      if (!isxlsx) {
        this.$message.error('只能够上传 xlsx 格式文件')
      }
      return isxlsx
    },
    handleProgress () {
      this.loading = true
    },
    OpenNotify (errors) {
      this.$notify({
        title: '警告',
        type: 'warning',
        message: errors.message,
        duration: 0
      })
    }
  }
}
</script>
