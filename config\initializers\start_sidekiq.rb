# 在Tomcat环境下自动启动Sidekiq，但在warble打包时不执行

# 定义启动Sidekiq的模块，确保方法在整个文件中可用
module SidekiqStarter
  module_function
  
  # 检查是否有运行中的Sidekiq实例
  def check_running_sidekiq
    # 如果全局变量中已经有Sidekiq实例，则先关闭它
    if defined?($sidekiq_launcher) && $sidekiq_launcher
      log_dir = ENV['IMPORT_PATH'] || "/opt/aas-app"
      log_file = "#{log_dir}/logs/sidekiq.log"
      
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Detected running Sidekiq instance, shutting it down first..."
      end
      
      begin
        # 设置超时强制关闭
        shutdown_thread = Thread.new do
          # 先尝试正常关闭
          $sidekiq_launcher.stop
        end
        
        # 等待最多5秒
        shutdown_thread.join(5)
        
        # 如果超时，强制终止
        if shutdown_thread.alive?
          File.open(log_file, 'a') do |f|
            f.puts "[#{Time.now}] Previous Sidekiq shutdown timed out, forcing exit..."
          end
          
          # 强制终止线程
          shutdown_thread.kill
        else
          File.open(log_file, 'a') do |f|
            f.puts "[#{Time.now}] Previous Sidekiq shutdown completed successfully"
          end
        end
        
        # 清理资源
        $sidekiq_launcher = nil
        GC.start
        sleep(1) # 等待短暂时间确保资源完全释放
        
        return true # 返回成功关闭了之前的实例
      rescue => e
        File.open(log_file, 'a') do |f|
          f.puts "[#{Time.now}] Error shutting down previous Sidekiq: #{e.message}"
          f.puts e.backtrace.join("\n")
        end
      end
    end
    
    return false # 没有关闭之前的实例或关闭失败
  end
  
  # 健康检查方法
  def sidekiq_health_check
    return false unless defined?($sidekiq_launcher) && $sidekiq_launcher

    begin
      # 检查 launcher 是否还在运行
      return false unless $sidekiq_launcher.respond_to?(:stopping?) && !$sidekiq_launcher.stopping?

      # 检查 Redis 连接
      Sidekiq.redis { |conn| conn.ping }
      return true
    rescue => e
      log_file = File.join(Rails.root, 'log', 'sidekiq_initializer.log')
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Health check failed: #{e.message}"
      end
      return false
    end
  end

  # 启动健康检查监控
  def start_health_monitor
    return if defined?($sidekiq_health_monitor) && $sidekiq_health_monitor&.alive?

    log_file = File.join(Rails.root, 'log', 'sidekiq_initializer.log')

    $sidekiq_health_monitor = Thread.new do
      Thread.current.name = 'sidekiq-health-monitor'
      loop do
        sleep(30) # 每30秒检查一次

        unless sidekiq_health_check
          File.open(log_file, 'a') do |f|
            f.puts "[#{Time.now}] Sidekiq health check failed, attempting restart..."
          end

          # 尝试重启 Sidekiq
          begin
            check_running_sidekiq  # 关闭现有实例
            sleep(2)
            start_sidekiq_with_ruby  # 重新启动
          rescue => e
            File.open(log_file, 'a') do |f|
              f.puts "[#{Time.now}] Failed to restart Sidekiq: #{e.message}"
            end
          end
        end
      end
    rescue => e
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Health monitor error: #{e.message}"
      end
    end
  end

  # 使用Ruby代码启动Sidekiq
  def start_sidekiq_with_ruby
    # 使用日志文件而不是标准输出，避免流关闭问题
    log_file = File.join(Rails.root, 'log', 'sidekiq_initializer.log')
    FileUtils.mkdir_p(File.dirname(log_file)) unless File.directory?(File.dirname(log_file))
    
    # 检查并关闭已有的Sidekiq实例
    previous_instance_shutdown = check_running_sidekiq
    
    # 写入日志
    File.open(log_file, 'a') do |f|
      if previous_instance_shutdown
        f.puts "[#{Time.now}] Restarting Sidekiq after shutting down previous instance..."
      else
        f.puts "[#{Time.now}] Starting Sidekiq using Ruby code..."
      end
    end
    
    # 不使用线程，直接配置Sidekiq
    begin
      # 加载配置文件
      config_file = File.join(Rails.root, 'config', 'sidekiq.yml')
      config = YAML.load_file(config_file)
      
      # 记录日志
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Loaded Sidekiq configuration from #{config_file}"
      end
      
      # 配置Sidekiq客户端
      Sidekiq.configure_client do |sidekiq_config|
        redis_options = config[:redis] || {}
        redis_conn = { 
          url: ENV['REDIS_URL'] || 'redis://localhost:6379/0',
          size: (redis_options[:size] || 5).to_i,
          network_timeout: 5
        }
        sidekiq_config.redis = redis_conn
      end
      
      # 使用更稳定的方式启动Sidekiq，避免线程问题
      # 使用日志文件而不是标准输出
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Starting Sidekiq worker in production mode"
      end

      # 避免重复配置，使用现有的 Redis 配置
      # 不在这里重新配置 Sidekiq，使用 config/initializers/sidekiq.rb 中的配置

      # 使用require而不是-r参数
      require File.join(Rails.root, 'config', 'environment') unless defined?(Rails.application)

      # 设置环境变量来影响Sidekiq的行为
      ENV['TERM_CHILD'] = '1'  # 使用更安全的子进程终止方式
      ENV['NOKOGIRI_USE_SYSTEM_LIBRARIES'] = '1'  # 加速Nokogiri

      # 直接使用Sidekiq::Launcher而不是CLI
      require 'sidekiq/launcher'

      # 加载配置，优先使用环境特定配置
      env_config = config[ENV['RAILS_ENV'].to_sym] || {}
      base_config = config.reject { |k, v| k.is_a?(Symbol) && %w[production staging development].include?(k.to_s) }
      merged_config = base_config.merge(env_config)

      options = {}
      options[:queues] = merged_config[:queues] || config[:queues] || ['default']
      options[:concurrency] = (merged_config[:concurrency] || config[:concurrency] || 10).to_i
      options[:environment] = ENV['RAILS_ENV'] || 'development'
      options[:timeout] = (merged_config[:timeout] || config[:timeout] || 8).to_i
      options[:verbose] = merged_config[:verbose] || config[:verbose] || false

      # 记录配置信息
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Sidekiq configuration: #{options.inspect}"
      end

      # 在后台线程中启动，但使用更安全的方式
      sidekiq_thread = Thread.new do
        Thread.current.name = 'sidekiq-launcher'
        begin
          # 创建Launcher并运行
          launcher = Sidekiq::Launcher.new(options)

          # 存储launcher实例到实例变量而不是全局变量
          Thread.current[:sidekiq_launcher] = launcher
          $sidekiq_launcher = launcher  # 保持向后兼容

            File.open(log_file, 'a') do |f|
              f.puts "[#{Time.now}] Sidekiq launcher created, starting worker..."
            end

            launcher.run
          rescue => e
            File.open(log_file, 'a') do |f|
              f.puts "[#{Time.now}] Sidekiq launcher error: #{e.message}"
              f.puts e.backtrace.join("\n")
            end
            raise
          end
        end

        # 设置线程为守护线程，避免阻塞主进程
        sidekiq_thread.abort_on_exception = true

        # 存储线程引用以便后续管理
        $sidekiq_thread = sidekiq_thread

      rescue => e
        # 将错误写入日志文件而不是标准输出
        File.open(log_file, 'a') do |f|
          f.puts "[#{Time.now}] Error during Sidekiq initialization: #{e.message}"
          f.puts e.backtrace.join("\n")
        end
      end
      
      # 记录日志
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Sidekiq initialization complete"
      end
      
      # 启动健康监控（仅在生产环境）
      if ENV['RAILS_ENV'] == 'production'
        start_health_monitor
      end

      # 在控制台显示消息
      puts "Sidekiq started in background. Check log/sidekiq_initializer.log for details."
      
    rescue => e
      # 将错误写入日志文件
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Error during Sidekiq initialization: #{e.message}"
        f.puts e.backtrace.join("\n")
      end
      # 在控制台显示错误
      puts "Error starting Sidekiq: #{e.message}. Check log/sidekiq_initializer.log for details."
    end
  end

  # 安全关闭 Sidekiq 的方法
  def shutdown_sidekiq_safely
    log_file = File.join(Rails.root, 'log', 'sidekiq_initializer.log')

    # 记录关闭过程
    File.open(log_file, 'a') do |f|
      f.puts "[#{Time.now}] Shutting down Sidekiq safely..."
    end

    begin
      # 停止健康监控
      if defined?($sidekiq_health_monitor) && $sidekiq_health_monitor&.alive?
        $sidekiq_health_monitor.kill
        $sidekiq_health_monitor = nil
      end

      # 关闭 Sidekiq launcher
      if defined?($sidekiq_launcher) && $sidekiq_launcher
        # 设置超时强制关闭
        shutdown_thread = Thread.new do
          Thread.current.name = 'sidekiq-shutdown'
          # 先尝试正常关闭
          $sidekiq_launcher.stop
        end

        # 等待最多10秒（生产环境需要更长时间）
        shutdown_thread.join(10)

        # 如果超时，强制终止
        if shutdown_thread.alive?
          File.open(log_file, 'a') do |f|
            f.puts "[#{Time.now}] Sidekiq shutdown timed out, forcing exit..."
          end

          # 强制终止线程
          shutdown_thread.kill

          # 强制清理资源
          $sidekiq_launcher = nil
          GC.start
        else
          File.open(log_file, 'a') do |f|
            f.puts "[#{Time.now}] Sidekiq shutdown completed successfully"
          end
        end
      end

      # 清理线程引用
      if defined?($sidekiq_thread) && $sidekiq_thread&.alive?
        $sidekiq_thread.kill
        $sidekiq_thread = nil
      end

    rescue => e
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Error during Sidekiq shutdown: #{e.message}"
        f.puts e.backtrace.join("\n")
      end
    end
  end
end

# 判断当前是否在warble打包过程中
# 如果是warble命令，则不启动Sidekiq
def warble_command?
  # 检查是否在warble命令执行过程中
  caller.any? { |c| c =~ /warble/ }
end

# 判断当前是否在Tomcat环境中
def tomcat_environment?
  # 检查是否存在Tomcat相关的环境变量或路径
  ENV['TOMCAT_HOME'] || 
  ENV['CATALINA_HOME'] || 
  ENV['CATALINA_BASE'] || 
  File.exist?('/usr/local/tomcat') || 
  File.exist?('/opt/tomcat') ||
  # 如果是WAR包部署，通常会有这些特征
  defined?(JRuby) && 
  ENV['RAILS_ENV'] == 'production' && 
  !defined?(Rails::Console) # 不是控制台环境
end

# 在生产环境下自动启动Sidekiq，但在warble打包时不执行
if (tomcat_environment? || ENV['FORCE_SIDEKIQ_START']) && 
   !warble_command? && 
   !ENV['DISABLE_SIDEKIQ_AUTOSTART']
  require 'yaml'
  require 'sidekiq'
  require 'sidekiq/cli'
  
  # 检查是否已经有Sidekiq进程在运行
  sidekiq_pid_file = 'tmp/pids/sidekiq.pid'
  if File.exist?(sidekiq_pid_file)
    pid = File.read(sidekiq_pid_file).to_i
    begin
      Process.getpgid(pid)
      puts "Sidekiq is already running with PID: #{pid}"
      # 如果Sidekiq已经在运行，则不需要再启动
    rescue Errno::ESRCH
      # PID文件存在但进程不存在，可能是之前的Sidekiq没有正常退出
      puts "Removing stale Sidekiq PID file"
      File.delete(sidekiq_pid_file) if File.exist?(sidekiq_pid_file)
      
      # 使用Ruby代码启动Sidekiq
      SidekiqStarter.start_sidekiq_with_ruby
    end
  else
    # 使用Ruby代码启动Sidekiq
    SidekiqStarter.start_sidekiq_with_ruby
  end
  
  # 注册一个退出钩子，确保Rails控制台退出时Sidekiq也能正常退出
  at_exit do
    # 使用新的安全关闭方法
    SidekiqStarter.shutdown_sidekiq_safely

    # 同时处理 PID 文件（向后兼容）
    if File.exist?(sidekiq_pid_file)
      pid = File.read(sidekiq_pid_file).to_i
      begin
        puts "Shutting down Sidekiq (PID: #{pid})"
        Process.kill('TERM', pid)
      rescue Errno::ESRCH
        # 进程已经不存在
      end
    end
  end
end

