# 在Tomcat环境下自动启动Sidekiq，但在warble打包时不执行

# 定义启动Sidekiq的模块，确保方法在整个文件中可用
module SidekiqStarter
  module_function
  
  # 检查是否有运行中的Sidekiq实例
  def check_running_sidekiq
    # 如果全局变量中已经有Sidekiq实例，则先关闭它
    if defined?($sidekiq_launcher) && $sidekiq_launcher
      log_dir = ENV['IMPORT_PATH'] || "/opt/aas-app"
      log_file = "#{log_dir}/logs/sidekiq.log"
      
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Detected running Sidekiq instance, shutting it down first..."
      end
      
      begin
        # 设置超时强制关闭
        shutdown_thread = Thread.new do
          # 先尝试正常关闭
          $sidekiq_launcher.stop
        end
        
        # 等待最多5秒
        shutdown_thread.join(5)
        
        # 如果超时，强制终止
        if shutdown_thread.alive?
          File.open(log_file, 'a') do |f|
            f.puts "[#{Time.now}] Previous Sidekiq shutdown timed out, forcing exit..."
          end
          
          # 强制终止线程
          shutdown_thread.kill
        else
          File.open(log_file, 'a') do |f|
            f.puts "[#{Time.now}] Previous Sidekiq shutdown completed successfully"
          end
        end
        
        # 清理资源
        $sidekiq_launcher = nil
        GC.start
        sleep(1) # 等待短暂时间确保资源完全释放
        
        return true # 返回成功关闭了之前的实例
      rescue => e
        File.open(log_file, 'a') do |f|
          f.puts "[#{Time.now}] Error shutting down previous Sidekiq: #{e.message}"
          f.puts e.backtrace.join("\n")
        end
      end
    end
    
    return false # 没有关闭之前的实例或关闭失败
  end
  
  # 使用Ruby代码启动Sidekiq
  def start_sidekiq_with_ruby
    # 使用日志文件而不是标准输出，避免流关闭问题
    log_file = File.join(Rails.root, 'log', 'sidekiq_initializer.log')
    FileUtils.mkdir_p(File.dirname(log_file)) unless File.directory?(File.dirname(log_file))
    
    # 检查并关闭已有的Sidekiq实例
    previous_instance_shutdown = check_running_sidekiq
    
    # 写入日志
    File.open(log_file, 'a') do |f|
      if previous_instance_shutdown
        f.puts "[#{Time.now}] Restarting Sidekiq after shutting down previous instance..."
      else
        f.puts "[#{Time.now}] Starting Sidekiq using Ruby code..."
      end
    end
    
    # 不使用线程，直接配置Sidekiq
    begin
      # 加载配置文件
      config_file = File.join(Rails.root, 'config', 'sidekiq.yml')
      config = YAML.load_file(config_file)
      
      # 记录日志
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Loaded Sidekiq configuration from #{config_file}"
      end
      
      # 配置Sidekiq客户端
      Sidekiq.configure_client do |sidekiq_config|
        redis_options = config[:redis] || {}
        redis_conn = { 
          url: ENV['REDIS_URL'] || 'redis://localhost:6379/0',
          size: (redis_options[:size] || 5).to_i,
          network_timeout: 5
        }
        sidekiq_config.redis = redis_conn
      end
      
      # 在一个新线程中启动Sidekiq，但不使用puts
      Thread.new do
        begin
          # 使用日志文件而不是标准输出
          File.open(log_file, 'a') do |f|
            f.puts "[#{Time.now}] Starting Sidekiq worker thread"
          end
          
          # 配置Sidekiq服务器
          Sidekiq.configure_server do |sidekiq_config|
            redis_options = config[:redis] || {}
            redis_conn = { 
              url: ENV['REDIS_URL'] || 'redis://localhost:6379/0',
              size: (redis_options[:size] || 15).to_i,
              network_timeout: 5
            }
            sidekiq_config.redis = redis_conn
          end
          
          # 使用require而不是-r参数
          require File.join(Rails.root, 'config', 'environment')
          
          # 使用配置文件启动Sidekiq
          # 禁用需要终端的功能，避免closed stream错误
          # 设置环境变量来影响Sidekiq的行为
          ENV['TERM_CHILD'] = '1'  # 使用更安全的子进程终止方式
          ENV['NOKOGIRI_USE_SYSTEM_LIBRARIES'] = '1'  # 加速Nokogiri
          
          # 直接使用Sidekiq::Launcher而不是CLI
          # 这样可以避免一些需要终端的操作
          require 'sidekiq/launcher'
          
          # 加载配置
          options = {}
          options[:queues] = config[:queues] || ['default']
          options[:concurrency] = (config[:concurrency] || 10).to_i
          options[:environment] = ENV['RAILS_ENV'] || 'development'
          options[:timeout] = (config[:timeout] || 8).to_i
          options[:verbose] = config[:verbose] || false
          
          # 创建Launcher并运行
          launcher = Sidekiq::Launcher.new(options)
          launcher.run
          
          # 存储launcher实例到全局变量，以便在退出时访问
          $sidekiq_launcher = launcher
          
          # 注册信号处理程序，确保可以正常关闭
          at_exit do
            # 记录关闭过程
            File.open(log_file, 'a') do |f|
              f.puts "[#{Time.now}] Shutting down Sidekiq..."
            end
            
            begin
              # 设置超时强制关闭
              shutdown_thread = Thread.new do
                # 先尝试正常关闭
                $sidekiq_launcher.stop
              end
              
              # 等待最多5秒
              shutdown_thread.join(5)
              
              # 如果超时，强制终止
              if shutdown_thread.alive?
                File.open(log_file, 'a') do |f|
                  f.puts "[#{Time.now}] Sidekiq shutdown timed out, forcing exit..."
                end
                
                # 强制终止线程
                shutdown_thread.kill
                
                # 强制清理资源
                $sidekiq_launcher = nil
                GC.start
              else
                File.open(log_file, 'a') do |f|
                  f.puts "[#{Time.now}] Sidekiq shutdown completed successfully"
                end
              end
            rescue => e
              File.open(log_file, 'a') do |f|
                f.puts "[#{Time.now}] Error during Sidekiq shutdown: #{e.message}"
                f.puts e.backtrace.join("\n")
              end
            end
          end
        rescue => e
          # 将错误写入日志文件而不是标准输出
          File.open(log_file, 'a') do |f|
            f.puts "[#{Time.now}] Sidekiq thread error: #{e.message}"
            f.puts e.backtrace.join("\n")
          end
        end
      end
      
      # 记录日志
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Sidekiq initialization complete"
      end
      
      # 在控制台显示消息
      puts "Sidekiq started in background. Check log/sidekiq_initializer.log for details."
      
    rescue => e
      # 将错误写入日志文件
      File.open(log_file, 'a') do |f|
        f.puts "[#{Time.now}] Error during Sidekiq initialization: #{e.message}"
        f.puts e.backtrace.join("\n")
      end
      # 在控制台显示错误
      puts "Error starting Sidekiq: #{e.message}. Check log/sidekiq_initializer.log for details."
    end
  end
end

# 判断当前是否在warble打包过程中
# 如果是warble命令，则不启动Sidekiq
def warble_command?
  # 检查是否在warble命令执行过程中
  caller.any? { |c| c =~ /warble/ }
end

# 判断当前是否在Tomcat环境中
def tomcat_environment?
  # 检查是否存在Tomcat相关的环境变量或路径
  ENV['TOMCAT_HOME'] || 
  ENV['CATALINA_HOME'] || 
  ENV['CATALINA_BASE'] || 
  File.exist?('/usr/local/tomcat') || 
  File.exist?('/opt/tomcat') ||
  # 如果是WAR包部署，通常会有这些特征
  defined?(JRuby) && 
  ENV['RAILS_ENV'] == 'production' && 
  !defined?(Rails::Console) # 不是控制台环境
end

# 在生产环境下自动启动Sidekiq，但在warble打包时不执行
if (tomcat_environment? || ENV['FORCE_SIDEKIQ_START']) && 
   !warble_command? && 
   !ENV['DISABLE_SIDEKIQ_AUTOSTART']
  require 'yaml'
  require 'sidekiq'
  require 'sidekiq/cli'
  
  # 检查是否已经有Sidekiq进程在运行
  sidekiq_pid_file = 'tmp/pids/sidekiq.pid'
  if File.exist?(sidekiq_pid_file)
    pid = File.read(sidekiq_pid_file).to_i
    begin
      Process.getpgid(pid)
      puts "Sidekiq is already running with PID: #{pid}"
      # 如果Sidekiq已经在运行，则不需要再启动
    rescue Errno::ESRCH
      # PID文件存在但进程不存在，可能是之前的Sidekiq没有正常退出
      puts "Removing stale Sidekiq PID file"
      File.delete(sidekiq_pid_file) if File.exist?(sidekiq_pid_file)
      
      # 使用Ruby代码启动Sidekiq
      SidekiqStarter.start_sidekiq_with_ruby
    end
  else
    # 使用Ruby代码启动Sidekiq
    SidekiqStarter.start_sidekiq_with_ruby
  end
  
  # 注册一个退出钩子，确保Rails控制台退出时Sidekiq也能正常退出
  at_exit do
    if File.exist?(sidekiq_pid_file)
      pid = File.read(sidekiq_pid_file).to_i
      begin
        puts "Shutting down Sidekiq (PID: #{pid})"
        Process.kill('TERM', pid)
      rescue Errno::ESRCH
        # 进程已经不存在
      end
    end
  end
end

