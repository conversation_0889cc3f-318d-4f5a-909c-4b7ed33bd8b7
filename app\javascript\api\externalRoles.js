import axios                           from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const list = (systemId, params = {}) => {
  return axios.get(`/api/external/business_systems/${systemId}/roles`, { params: params })
}

export const detail = (systemId, id) => {
  return axios.get(`/api/external/business_systems/${systemId}/roles/${id}`)
}

export const update = (systemId, id, params = {}) => {
  return axios.put(`/api/external/business_systems/${systemId}/roles/${id}`, params)
}

export const create = (systemId, params = {}) => {
  return axios.post(`/api/external/business_systems/${systemId}/roles`, params)
}

export const destroy = (systemId, id) => {
  return axios.delete(`/api/external/business_systems/${systemId}/roles/${id}`)
}
