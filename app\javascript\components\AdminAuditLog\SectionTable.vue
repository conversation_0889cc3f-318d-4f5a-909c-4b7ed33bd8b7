<template>
  <el-table
    :data="tableData"
    border
    stripe
  >
    <el-table-column
      property="operation_time_p"
      label="操作时间"
      width="155"
    />
    <el-table-column
      property="generate_type"
      label="日志类型"
      width="80"
    />
    <el-table-column
      property="name"
      label="操作人"
      width="80"
    />
    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-table-column
      property="description"
      label="详细信息"
    >
      <template slot-scope="scope">
        <section-popover
          v-if="scope.row.multiple"
          :section="scope.row"
        />
        <div v-else> {{ scope.row.description }}</div>
      </template>
    </el-table-column>
    <!-- eslint-enable vue/attribute-hyphenation -->

    <el-table-column
      property="operation_module"
      label="功能模块"
      width="100"
    />
    <el-table-column
      property="operation_category"
      label="功能类别"
      width="100"
    />
    <el-table-column
      property="ip_address"
      label="IP 地址"
      width="100"
    />
    <el-table-column
      property="agent_p"
      label="浏览器"
      width="100"
    />
  </el-table>
</template>

<script>
import SectionPopover from './SectionPopover.vue'

export default {
  components: {
    SectionPopover
  },
  props:      {
    tableData: {
      type:     Array,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
