class LedgerDepartmentAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '台账部门管理'
    @operation_category = '台账部门'
    super
  end

  def create
    @operation = '创建台账部门'
    @comment = "创建了台账部门：ID：「#{params.id}」系统：「#{params.business_system.name}」账号部门：「#{params.account_department.name}」员工部门：「#{params.department.name}」"
    create_audit_log
  end

  def create_fail
    @operation = '创建台账部门'
    error_message = params.errors.full_messages.join('; ')
    @comment = '创建台账部门失败：'
    @comment += "系统：「#{params.business_system&.name}」" if params.business_system&.name.present?
    @comment += "账号部门：「#{params.account_department&.name}」" if params.account_department&.name.present?
    @comment += "员工部门：「#{params.department&.name}」" if params.department&.name.present?
    @comment += "错误信息：#{error_message}"
    create_audit_log
  end

  def update
    @operation = '更新台账部门'
    data = params[0]
    before_data = params[1]
    after_data = params[2]
    str = update_detail(before_data, after_data)
    @comment = "更新了台账部门：ID：「#{data.id}」"
    @comment += "详情：#{str}" if str.present?
    create_audit_log
  end

  def update_fail
    @operation = '更新台账部门'
    error_message = params.errors.full_messages.join('; ')
    @comment = "更新台账部门失败：ID：「#{params.id}」 错误信息：#{error_message}"
    create_audit_log
  end

  def destroy
    @operation = '删除台账部门'
    @comment   = "删除了台账部门：ID：「#{params.id}」 系统：「#{params.business_system.name}」账号部门：「#{params.account_department.name}」员工部门：「#{params.department.name}」"
    create_audit_log
  end

  protected

  def update_detail(before_data, after_data)
    str = ''
    str += "系统名称：「#{before_data[:business_system_name]}」改为「#{after_data[:business_system_name]}」" if after_data[:business_system_name] != before_data[:business_system_name]
    str += "账号部门：「#{before_data[:account_department_name]}」改为「#{after_data[:account_department_name]}」" if after_data[:account_department_name] != before_data[:account_department_name]
    str += "员工部门：「#{before_data[:department_name]}」改为「#{after_data[:department_name]}」" if after_data[:department_name] != before_data[:department_name]
    str
  end
end
