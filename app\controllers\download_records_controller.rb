class DownloadRecordsController < ApplicationController
  before_action :authenticate_admin!

  # 下载文件
  def download
    records = DownloadRecord.undownload.where(admin_id: current_admin.id)
    record = records.first
    return json_respond_no_content if record.nil?

    record.update(status: 'download')
    send_file_compatible_with_msie record.file.path
  end

  # 失败文件，如果存在返回true
  def download_failure
    records = DownloadRecord.failure_unnotice.where(admin_id: current_admin.id)
    record = records.first
    return json_respond(status: false) if record.nil?

    record.update_column(:status, 'failure')
    json_respond(status: true, error_message: record.error_message)
  end
end
