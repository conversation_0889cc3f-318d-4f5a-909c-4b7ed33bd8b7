<template>
  <div
    v-loading="loading"
    class="container"
  >
    <el-form
      ref="form"
      label-width="125px"
      :model="{rules: rules}"
    >
      <el-form-item
        label="添加异常确认规则"
        class="add-item"
      >
        <el-button
          size="small"
          @click="createNewRule"
        >
          点击添加
        </el-button>
      </el-form-item>
      <el-divider />
      <transition-group name="list-complete">
        <div
          v-for="(item, index) in rules"
          :key="index"
          class="list-complete-item form-item"
        >
          <div class="title">
            {{ `规则 ${index + 1}` }}
          </div>
          <el-form-item
            :prop="`rules.${index}.system_type`"
            label="系统名称"
            :rules="[
              { required: true, message: '请选择系统', trigger: 'change' },
            ]"
          >
            <el-select
              v-model="rules[index].system_type"
              filterable
              size="small"
              placeholder="请选择系统"
            >
              <el-option
                v-for="system_type in systemNamesArray"
                :key="system_type.value"
                :value="system_type.value"
                :label="system_type.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :prop="`rules.${index}.account_code`"
            label="系统账号"
            :rules="[
              { required: true, message: '请选择账号', trigger: 'change' },
            ]"
          >
            <el-select
              v-if="rules[index].system_type === null"
              v-model="rules[index].fund_code"
              disabled
              size="small"
            />
            <el-select
              v-if="rules[index].system_type === 'reference_system'"
              v-model="rules[index].account_code"
              filterable
              size="small"
              placeholder="请选择账号"
            >
              <el-option
                v-for="account in reference_accounts"
                :key="account.account_code"
                :value="account.account_code"
                :label="`${account.account_code} - ${account.account_name}`"
              />
            </el-select>
            <el-select
              v-if="rules[index].system_type === 'alignment_system'"
              v-model="rules[index].account_code"
              filterable
              size="small"
              placeholder="请选择账号"
            >
              <el-option
                v-for="account in alignment_accounts"
                :key="account.account_code"
                :value="account.account_code"
                :label="`${account.account_code} - ${account.account_name}`"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :prop="`rules.${index}.fund_code`"
            label="基金产品"
            :rules="[
              { required: true, message: '请选择基金', trigger: 'change' },
            ]"
          >
            <el-select
              v-if="rules[index].system_type === null"
              v-model="rules[index].fund_code"
              disabled
              size="small"
            />
            <el-select
              v-if="rules[index].system_type === 'reference_system'"
              v-model="rules[index].fund_code"
              filterable
              size="small"
              placeholder="请选择基金"
            >
              <el-option
                v-for="fund in reference_funds"
                :key="fund.fund_code"
                :value="fund.fund_code"
                :label="`${fund.fund_code} - ${fund.name}`"
              />
            </el-select>
            <el-select
              v-if="rules[index].system_type === 'alignment_system'"
              v-model="rules[index].fund_code"
              filterable
              size="small"
              placeholder="请选择基金"
            >
              <el-option
                v-for="fund in alignment_funds"
                :key="fund.fund_code"
                :value="fund.fund_code"
                :label="`${fund.fund_code} - ${fund.name}`"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :prop="`rules.${index}.remark`"
            label="备注"
          >
            <el-input
              v-model="rules[index].remark"
              :disabled="rules[index].system_type === null"
              size="small"
              placeholder=""
            />
          </el-form-item>
          <div class="button">
            <el-button
              size="small"
              type="text"
              class="delete-button"
              @click="handleDeleteRule(index)"
            >
              删除
            </el-button>
          </div>
        </div>
      </transition-group>
      <div class="submit-buttons">
        <el-button
          size="small"
          type="primary"
          @click="submitForm('form')"
        >
          更新设置
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="recheckComparison"
        >
          重新检查当前时间点数据
        </el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import RecheckButton from '@/components/AdminSettings/FdfgCompareGzyss45/RecheckButton'

export default {
  mixins: [RecheckButton],
  data () {
    return {
      loading:            false,
      rules:              [],
      system_names:       {},
      reference_accounts: [],
      alignment_accounts: [],
      reference_funds:    [],
      alignment_funds:    []

    }
  },
  computed: {
    systemNamesArray () {
      return [
        { value: 'reference_system', label: this.system_names.reference_system },
        { value: 'alignment_system', label: this.system_names.alignment_system }

      ]
    }
  },
  created () {
    this.getSettings()
  },
  methods: {
    getSettings () {
      this.loading = true
      this.$axios.get('/admin_api/settings/fdfg_gzyss45_comparison/funds_check')
        .then(response => {
          this.loading            = false
          this.rules              = response.data.exclude_rules
          this.system_names       = response.data.system_names
          this.reference_accounts = response.data.reference_accounts
          this.alignment_accounts = response.data.alignment_accounts
          this.reference_funds    = response.data.reference_funds
          this.alignment_funds    = response.data.alignment_funds
        })
        .catch(error => {
          this.loading = false

          switch (error.status) {
            case 403:
              break
            case 456:
              break

            default:
              console.error(error)
              this.$message.error('读取设置信息失败')
          }
        })
    },
    updateConfirm () {
      this.$confirm('此操作将更新系统设置，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.updateSettings()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    updateSettings () {
      this.$axios.post('/admin_api/settings/fdfg_gzyss45_comparison/funds_check', {
        exclude_rules: this.rules
      }).then(response => {
        if (response.data.have_duplicate_data) {
          this.$message.success('设置已更新，发现重复规则，已合并')
        } else {
          this.$message.success('设置已更新')
        }
        this.getSettings()
      })
        .catch(() => {})
    },

    createNewRule () {
      const itemData = {
        system_type:  null,
        account_code: null,
        fund_code:    null,
        remark:       ''
      }
      const length   = this.rules.length
      this.$set(this.rules, length, itemData)
    },
    handleDeleteRule (index) {
      this.$delete(this.rules, index)
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.updateConfirm()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.form-item {
  @include vertical_center_between;
  height: 63px;

}

.el-select {
  width: 160px;
}

.title {
  width:         60px;
  color:         $font-color;
  line-height:   40px;
  margin-bottom: 22px;
}

.delete-button {
  margin-left:   10px;
  line-height:   40px;
  margin-bottom: 22px;

}

.list-complete-item {
  transition: all 0.5s;
}

.error {
  color:       #E54D42;
  font-weight: bold;
}

.list-complete-enter, .list-complete-leave-to
  /* .list-complete-leave-active for below version 2.1.8 */
{
  opacity:   0;
  transform: translateY(-30px);
}

.submit-buttons {
  margin-top: 20px;
}

.add-item {
  margin-top: 10px;
}
</style>
