<template>
  <el-dialog
    :visible.sync="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    width="600px"
    appendToBody
  >
    <el-form
      ref="form"
      :model="dynamicValidateForm"
      label-width="80px"
    >

      <el-form-item
        prop="name"
        label="职务"
      >
        <el-select
          v-model="dynamicValidateForm.position"
          value-key="id"
          placeholder="选择职务"
          filterable
          class="form-item"
        >
          <el-option
            v-for="userPostion in userPostions"
            :key="userPostion"
            :label="`${userPostion}`"
            :value="userPostion"
            :disabled="validateSelectPosition(userPostion)"
          />
        </el-select>
      </el-form-item>
      <el-divider />
      <div v-for="(domain, index) in dynamicValidateForm.domains" class="div-margin">
        <el-form-item label="系统">
          <el-select
            v-model="domain.business_system_id" @change="changeSystem(domain.business_system_id)" placeholder="请选择系统"
            :class="(dynamicValidateForm.domains.length-1) !== 0 ? 'input-padding-right system': 'input-padding-right system-width' "
          >
            <el-option
              v-for="item in systems"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              :disabled="validateIncludeSystem(item.id)"
            />
          </el-select>
          <el-button
            @click.prevent="removeDomain(domain)"
            v-if="(dynamicValidateForm.domains.length-1) !== 0"
          >
            删除
          </el-button>
        </el-form-item>
        <el-form-item label="角色">
          <el-select placeholder="请选择角色" multiple filterable v-model="domain.role_code" class="form-item role">
            <el-option
              v-for="item in getRolesForSystem(domain.business_system_id)"
              :key="item.code"
              :value="`${[item.code, item.name]}`"
              :label="item.name"
            />
          </el-select>
          <el-button
            size="small"
            @click="addDomain"
            v-if="dynamicValidateForm.domains.length == index+1"
          >
            添加
          </el-button>
        </el-form-item>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleCommit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import * as Message from '@/utils/general_messages'

export default {
  props: {
    account: {
      type: Object,
      default: () => {}
    },
    systems: {
      type: Array,
      default: () => []
    },
    mode: {
      type: String,
      default: 'edit'
    }
  },
  data () {
    return {
      visible: false,
      AllSystemsRoles: [],
      formTitle: this.$t('position_role_check.create_role_rule'),
      dynamicValidateForm: {},
      userPostions: [],
      roles: [],
      editIds: [],
      selectSystemsId: [],
      selectedPositions: []
    }
  },
  computed: {
    dialogTitle () {
      if (this.mode === 'edit') return this.$t('position_role_check.edit_role_rule')
      if (this.mode === 'create') return this.$t('position_role_check.create_role_rule')
    }
  },
  watch: {
    visible () {
      if (this.visible) {
        this.getUsersPostitions()
        this.initialization()
        this.selectSystemsId = []
        if (this.mode === 'edit'){
          this.editData()
        }
      }
    }
  },
  created () {
    this.getAllSystemsRoles()
  },
  methods: {
    initialization () {
      this.dynamicValidateForm = {
            position: null,
            domains: [{ business_system_id: '', role_code: [] }]
          }
    },
    editData(){
      this.$axios.get(`api/position_role_checks/edit_data_for_position?position=${this.account.position}`)
        .then(response => {
          this.dynamicValidateForm.position = this.account.position
          var arr = new Array();
          var ids = new Array();
          response.data.forEach( (position_rule,i) => {
          ids.push(position_rule.id)
          arr.push({
            business_system_id: parseInt(position_rule.system_id),
            role_code: position_rule.role_code
          })

          });
          this.dynamicValidateForm.domains = arr
          this.editIds = ids
          this.selectSystemIds()
        })
        .catch(error => {
          if (error.status === 404) Message.notFoundQuarter()
        })
    },
    addDomain() {
      var last = this.dynamicValidateForm.domains.slice(-1)

      if (!last[0].business_system_id) return this.$message.error('请选择系统')
      if (last[0].role_code.length == 0) return this.$message.error('请选择角色')
      this.dynamicValidateForm.domains.push(
          { business_system_id: '', role_code: [] }
        );
    },
    removeDomain(item) {
      var index = this.dynamicValidateForm.domains.indexOf(item)
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1)
      }
    },
    validateIncludeSystem(system_id){
      return this.selectSystemsId.includes(system_id)
    },
    changeSystem (systemId) {
      this.roles = []
      var select_system = this.dynamicValidateForm.domains.find(system => system.business_system_id === systemId)
      if(select_system){
        select_system.role_code = []
      }
      this.selectSystemIds()
    },
    selectSystemIds() {
      this.selectSystemsId = []
      this.dynamicValidateForm.domains.forEach( (select_system,i) => {
        if(!this.selectSystemsId.includes(select_system.business_system_id)) this.selectSystemsId.push(select_system.business_system_id)
      })
    },
    handleCommit () {
      var last = this.dynamicValidateForm.domains.slice(-1)
      if (!this.dynamicValidateForm.position) return this.$message.error('请选择职务')
      if (!last[0].business_system_id) return this.$message.error('请选择系统')
      if (!this.validateRoles(this.dynamicValidateForm.domains)) return this.$message.error('请选择角色')
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.mode === 'edit'){
            this.$confirm(`确定更新?`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.updateAccountAuthority()
            })
          }

          if (this.mode === 'create') return this.createAccountAuthority()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    validateRoles (domains) {
      var allSelect = true
      domains.forEach( (domain,i) => {
        if(domain.role_code.length == 0) return allSelect = false
      })
      return allSelect
    },
    getUsersPostitions () {
      this.$axios.get(`api/position_role_checks/users_positions`)
        .then(response => {
          this.userPostions = response.data.positions
          this.selectedPositions = response.data.selected_positions
        })
        .catch(error => {
          if (error.status === 404) Message.notFoundQuarter()
        })
    },
    validateSelectPosition (position) {
      return this.selectedPositions.includes(position)
    },
    getAllSystemsRoles () {
      this.$axios.get(`api/position_role_checks/position_and_account_roles`)
        .then(response => {
          this.AllSystemsRoles = response.data
          this.system_ids = Array.from(this.AllSystemsRoles,({system_id})=>system_id)
        })
        .catch(error => {
          if (error.status === 404) Message.notFoundQuarter()
        })
    },
    getRolesForSystem (systemId) {
      if(systemId) return this.AllSystemsRoles.find(obj => obj.system_id === systemId).system_roles
    },
    createAccountAuthority () {
      this.$axios.post(`api/position_role_checks`, {
        position_role_checks: this.dynamicValidateForm
      })
        .then(response => {
          this.$emit('accountsRolesList')
          this.$message.success(this.$t('position_role_check.create_role_rule_success'))
          this.visible = false
        })
        .catch(() => {})
    },
    updateAccountAuthority () {
      this.$axios.put(`api/position_role_checks/update_data_for_position`, {
       position_role_checks: this.dynamicValidateForm,
       edit_ids: this.editIds
      })
        .then(response => {
          this.$message.success(this.$t('position_role_check.edit_role_rule_success'))
          this.visible = false
          this.$emit('update')
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-form-item {
    margin-bottom: 0px;
  }
  .form-item{
    width: 100%;
  }
  .padding-top {
    padding-top: 10px;
  }
  .system {
    width: 84%;
  }
  .system-width {
    width: 100%;
  }
  .role {
    padding-top: 10px;
  }
  .div-margin {
    margin-bottom: 40px;
  }
</style>

