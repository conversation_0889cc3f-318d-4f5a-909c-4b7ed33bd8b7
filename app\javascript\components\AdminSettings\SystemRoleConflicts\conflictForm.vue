<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :title="title"
    width="650px"
    append-to-body
    destroy-on-close
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="conflictForm"
      label-width="40px"
    >
      <el-form-item width="200px">
        <system-roles-select
          clearable
          systemRequired
          roleRequired
          size="small"
          @change="updateSystemRole"
        />
      </el-form-item>
      <el-form-item>
        <system-roles-select
          clearable
          multiple
          systemRequired
          roleRequired
          size="small"
          @change="updateConflictSystemRole"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          :disabled="!$store.getters.hasPermission('system_role_conflicts.conflicts_admin')"
          size="small"
          type="primary"
          @click="updateConflictRoles"
        >
          添 加
        </el-button>
        <el-button
          size="small"
          @click="handleClose"
        >
          关 闭
        </el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
import API               from '@/api'
import SystemRolesSelect from './systemRolesSelect'

export default {
  components: {
    SystemRolesSelect
  },
  data () {
    return {
      loading:             false,
      localSystem:         '',
      conflictSystem:      '',
      localRole:           '',
      conflictRoles:       [],
      systemRoles:         [],
      conflictSystemRoles: [],
      systemWithRoles:     [],
      conflicts:           [],
      currentPage:         1,
      pageSize:            15,
      dialogVisible:       false,
      title:               this.$t('system_role_conflicts.module_setting'),
      conflictForm:        {}
    }
  },
  computed: {
    conflictsByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.conflicts.slice(start, end)
    }
  },
  created () {
    this.gettingSystemRoles()
  },
  methods: {
    gettingSystemRoles () {
      this.loading = true
      API.systemRoleConflicts.systemRoles()
        .then(response => {
          this.loading             = false
          this.systemWithRoles     = response.data
          this.systemRoles         = response.data.find(x => x.id === this.localSystem).roles
          this.conflictSystemRoles = response.data.find(x => x.id === this.conflictSystem).roles
        })
        .catch(_ => {
          this.loading = false
        })
    },
    updateConflictRoles () {
      if (this.validateConfilct()) {
        const params = {
          bs_id:          this.localSystem,
          role_code:      this.localRole,
          conflict_bs_id: this.conflictSystem,
          conflict_roles: this.conflictRoles
        }
        API.systemRoleConflicts.create(params)
          .then(response => {
            this.loading = false
            this.handleClose()
            this.$message.success('成功创建冲突规则')
          })
          .catch(_ => {
            this.loading = false
          })
      } else {
        this.$message.warning("请检查必填项")
      }
    },
    validateConfilct () {
      if (!this.localSystem || !this.localRole || !this.conflictSystem || !this.conflictRoles) {
        return false
      } else {
        return true
      }
    },
    updateSystemRole (data) {
      this.localSystem = data.system_id
      this.localRole   = data.role_code
    },
    updateConflictSystemRole (data) {
      this.conflictSystem = data.system_id
      this.conflictRoles  = data.role_code

      if (this.conflictSystem === this.localSystem && this.conflictRoles.includes(this.localRole)) {
        this.$message.warning('不能选择相同系统的相同角色')
        this.conflictRoles.splice(this.conflictRoles.indexOf(this.localRole), 1)
      }
    },
    handleClose () {
      this.$emit('update')
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
  @import '~@/components/variables';
  .conflicts-pagination{
    margin-top: 20px;
  }
</style>
