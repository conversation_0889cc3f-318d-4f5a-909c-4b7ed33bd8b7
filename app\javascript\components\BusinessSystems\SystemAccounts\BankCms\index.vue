<template>
  <el-container>
    <el-aside width="220">
      <org-card
        :systemId="systemId"
        @change="handleChangeOrgId"
      />
    </el-aside>
    <el-main class="org-container">
      <div>
        <div class="tool-bar">
          <div class="left">
            <el-button
              :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
              @click="handleCreate"
            >
              新增用户
            </el-button>
            <accounts-import
              :systemId="systemId"
              style="margin-left: 10px;"
              @update="handleUpdateRoles"
            />
          </div>
          <div class="right">
            <el-autocomplete
              v-model="filter.id"
              :fetchSuggestions="roleIdSuggestions"
              placeholder="搜索用户编号"
              style="width: 140px"
            />
            <el-autocomplete
              v-model="filter.name"
              :fetchSuggestions="roleNameSuggestions"
              placeholder="搜索用户名称"
              style="width: 140px"
            />
            <el-button
              @click="handleRunFilter"
            >
              在全部机构中搜索
            </el-button>
            <el-button
              @click="handleResetFilter"
            >
              重置
            </el-button>
          </div>
        </div>
        <el-divider />
        <div class="table-container">
          <el-table
            v-loading="loading"
            :data="dataByPage"
            border
            stripe
            style="width: 100%"
          >
            <el-table-column
              prop="user_id"
              label="用户编号"
              minWidth="80"
              fixed
            />
            <el-table-column
              prop="login_id"
              label="登录账号"
              minWidth="80"
            />

            <el-table-column
              prop="user_name"
              label="用户姓名"
              minWidth="100"
            />

            <el-table-column
              prop="belong_org_id"
              label="所属机构"
              minWidth="100"
            >
              <template slot-scope="scope">
                {{ orgName(scope.row.belong_org_id) }}
              </template>
            </el-table-column>

            <el-table-column
              prop="status_string"
              label="状态"
              minWidth="80"
            />
            <el-table-column
              label="操作"
              fixed="right"
              width="245"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click="handleShow(scope.row)"
                >
                  详情
                </el-button>
                <el-button
                  size="mini"
                  :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
                  @click="handleResetPassword(scope.row.user_id)"
                >
                  重置密码
                </el-button>
                <el-button
                  type="danger"
                  size="mini"
                  :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
                  @click="handleDisableResource(scope.row)"
                >
                  停用
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :pageSize.sync="pageSize"
            :total="dataByFilter.length"
            :currentPage.sync="currentPage"
            :style="{ marginTop: '20px' }"
            :pageSizes="[10, 30, 50, 100]"
            background
            hideOnSinglePage
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
        <account-form
          ref="accountForm"
          :systemId="systemId"
          :account="currentAccount"
          :orgs="orgs"
          @change="handleUpdateRoles"
        />
      </div>
    </el-main>
  </el-container>
</template>

<script>
import AccountForm     from './form.vue'
import OrgCard         from './OrgCard.vue'
import CmsDisableResource from '@/components/BusinessSystems/mixins/CmsDisableResource'
import AccountsImport  from '@/components/BusinessSystems/SystemAccounts/BankCms/AccountsImport'
import { catchError }    from '@/utils/axios_utils'

export default {
  components: {
    AccountForm,
    AccountsImport,
    OrgCard
  },
  mixins: [CmsDisableResource],
  props:      {
    systemId:       {
      type:     Number,
      required: true
    },
    systemSettings: {
      type:     Object,
      required: true
    }
  },
  data () {
    return {
      resourceType:  'accounts',
      resourceMode:  'disable',
      loading:        false,
      orgId:          1,
      accounts:       [],
      orgs:           [],
      pageSize:       10,
      currentPage:    1,
      currentAccount: null,
      filter:         {
        id:   '',
        name: ''
      }
    }
  },
  computed:   {
    dataByPage () {
      let start = (this.currentPage - 1) * this.pageSize
      let end   = this.currentPage * this.pageSize
      return this.dataByFilter.slice(start, end)
    },
    dataByFilter () {
      let accounts = this.accounts

      if (this.filter.id) {
        accounts = accounts.filter(x => x.user_id.toString().match(new RegExp(this.filter.id)))
      }
      if (this.filter.name) {
        accounts = accounts.filter(x => x.user_name.match(new RegExp(this.filter.name)))
      }
      return accounts
    }
  },
  watch:      {
    orgId () {
      this.dataInitialize()
    }
  },

  methods: {
    dataInitialize () {
      this.getSystemAccounts()
      this.getAllOrgs()
    },
    handleChangeOrgId (payload) {
      this.orgId = payload
    },
    resourceRefresh () {
      this.getSystemAccounts()
    },
    getSystemAccounts () {
      if (this.systemId === 0) return

      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/accounts`, {
        params: {
          parent_id: this.orgId
        }
      })
        .then(response => {
          this.loading  = false
          this.accounts = response.data

          if (this.accounts.length === 0) this.$message.info('该机构下不存在用户')
        })
        .catch(error => {
          this.loading = false
          catchError(error, '获取用户列表失败')
        })
    },
    getAllOrgs () {
      if (this.systemId === 0) return

      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/organizations`, {
        params: {
          parent_id: 1
        }
      })
        .then(response => {
          this.loading = false
          this.orgs    = response.data
        })
        .catch(error => {
          this.loading = false
          catchError(error, '获取机构列表失败')
        })
    },
    handleCreate () {
      const form          = this.$refs.accountForm
      form.dialogFlag     = 'create'
      form.visible        = true
      this.currentAccount = null
    },
    handleShow (row) {
      const form          = this.$refs.accountForm
      form.dialogFlag     = 'show'
      form.visible        = true
      this.currentAccount = row
    },
    branchesIsDisable (row) {
      return Number(row.org_level) !== 6
    },
    handleOpenBranches (row) {
      const form          = this.$refs.branches
      form.visible        = true
      this.currentAccount = row
    },
    handleResetPassword (id) {
      this.$confirm('确认重置密码并解锁该用户吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.operationReviewResetPassword(id)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })

    },
    operationReviewResetPassword (id) {
      const guid = this.$guidGenerate()
      this.$operationReview(this.systemId, guid)
        .then(() => {
          this.resetPassword(id, guid)
        })
        .catch(() => {})
    },
    resetPassword (id, guid) {
      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts/${id}/reset_password?guid=${guid}`)
        .then(response => {
          this.$message.success('用户密码已重置')
          this.getSystemAccounts()
        })
        .catch(error => {
          catchError(error, '重置时出现问题')
        })
    },
    handleUpdateRoles () {
      this.getSystemAccounts()
    },
    roleIdSuggestions (string, callback) {
      const data      = this.accounts.map(x => { return { value: x.user_id.toString() } })
      let suggestions = string ? data.filter(x => x.value.match(new RegExp(string))) : data
      callback(suggestions)
    },
    roleNameSuggestions (string, callback) {
      const data      = this.accounts.map(x => { return { value: x.user_name.toString() } })
      let suggestions = string ? data.filter(x => x.value.match(new RegExp(string))) : data
      callback(suggestions)
    },
    handleResetFilter () {
      this.filter = {
        id:   '',
        name: ''
      }
      this.getSystemAccounts()
    },
    handleRunFilter () {
      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/accounts/search`, {
        params: {
          code: this.filter.id,
          name: this.filter.name
        }
      })
        .then(response => {
          this.loading  = false
          this.accounts = response.data

          if (this.accounts.length === 0) this.$message.info('未搜索到指定用户')
        })
        .catch(error => {
          this.loading = false
          catchError(error, '搜索用户信息失败')
        })
    },
    orgName (org_id) {
      const org = this.orgs.find(x => x.org_id === org_id)
      return org ? org.org_name : ''
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .org-container {
    padding: 0 0 0 20px;
  }

  .tool-bar {
    @include vertical_center_between;
    height: 50px;

    .left{
      @include vertical_center_left;
    }

    .right {
      margin-right: 10px;
    }
  }

  .el-table {
    width: 100%;
  }

  .el-divider--horizontal {
    margin: 12px 0;
  }
</style>
