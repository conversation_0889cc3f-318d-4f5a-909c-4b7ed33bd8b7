# frozen_string_literal: true

class FundAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '基金邮箱白名单管理'
    super
  end

  def update
    set_account_category
    @operation = '基金邮箱白名单'
    @comment   = "更新了基金邮箱白名单 ：「#{params[0]}」为「#{params[1]}」"
    create_audit_log
  end

  private

  def set_account_category
    @operation_category = '基金邮箱白名单管理'
  end
end
