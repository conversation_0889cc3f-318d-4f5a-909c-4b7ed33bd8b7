<template>
  <div v-loading="loading">
    <h3>授权超30天，白名单配置</h3>
    <el-form
      ref="form"
      label-width="0px"
      :inline="true"
    >
      <el-form-item
        label=""
      >
        <div class="form-item-container">
          <system-account-select
            v-model="selectAccounts"
            placeholder="被授权人"
            multiple
            clearable
            filterable
            :system-id="31"
          />
        </div>
      </el-form-item>
      <el-form-item
        label=""
      >
        <div class="form-item-container">
          <system-account-select
            v-model="selectAuthorizerAccounts"
            placeholder="授权人"
            multiple
            clearable
            filterable
            :system-id="31"
          />
        </div>
      </el-form-item>
      <el-form-item
        label=""
      >
        <div class="form-item-container">
          <el-select
            v-model="selectFundCodes"
            placeholder="产品"
            multiple
            clearable
            filterable
            :system-id="31"
          >
          <el-option
            v-for="fund in allFunds"
            :key="fund.code"
            :label="fund.fund_code+'-'+fund.name"
            :value="fund.code"
          />
        </el-select>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          size="small"
          type="primary"
          @click="addDateCheckAccount()"
        >
          添加
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="dateCheckAccounts"
      border
      style="width: 100%"
    >
      <el-table-column
        label="授权人"
      >
        <template slot-scope="scope">
          {{ scope.row.accounts.map((x) => { return x.code + '-' + x.name }).join(', ')}}
        </template>
      </el-table-column>
      <el-table-column
        label="被授权人"
      >
        <template slot-scope="scope">
          {{ scope.row.authorizer_accounts.map((x) => { return x.code + '-' + x.name }).join(', ')}}
        </template>
      </el-table-column>
      <el-table-column
        label="产品"
      >
        <template slot-scope="scope">
          {{ scope.row.funds.map((x) => { return x.fund_code + '-' + x.name }).join(', ')}}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            @click="deleteDateCheckAccount(scope.row.code)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import SystemAccountSelect from '@/components/common/SystemAccountSelect.vue'
export default {
  components: {
    SystemAccountSelect
  },
  data () {
    return {
      loading:          false,
      selectAccounts:    [],
      selectAuthorizerAccounts: [],
      selectFundCodes: [],
      allFunds: [],
      dateCheckAccounts: []
    }
  },
  created () {
    this.getDateCheckAccounts()
  },
  methods: {

    getDateCheckAccounts () {
      this.loading = true
      this.$axios.get('/api/jiaoyi_temporary_permission/date_check_accounts')
        .then(response => {
          this.dateCheckAccounts = response.data.settings
          this.allFunds = response.data.funds
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    addDateCheckAccount () {
      if (this.selectAccounts.length == 0 || this.selectAuthorizerAccounts.length == 0 || this.selectFundCodes.length == 0) return this.$message.error('请选择账号和产品')
      const theParams = {
        accounts: this.selectAccounts,
        authorizer_accounts: this.selectAuthorizerAccounts,
        fund_codes: this.selectFundCodes
      }
      this.$axios.post('/api/jiaoyi_temporary_permission/add_date_check_account', theParams)
        .then(response => {
          this.$message.success('添加成功')
          this.getDateCheckAccounts()
        })
        .catch(() => {
        })
    },
    deleteDateCheckAccount (code) {
      this.$axios.delete(`/api/jiaoyi_temporary_permission/delete_date_check_account/${code}`)
        .then(response => {
          this.$message.success('删除成功')
          this.getDateCheckAccounts()
        })
        .catch(() => {
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.form-item-container {
  @include vertical_center_left;
  height: 40px;
}
</style>
