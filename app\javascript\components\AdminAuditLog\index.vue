<template>
  <div
    v-loading="loading"
    class="container"
  >
    <h3>审计日志</h3>
    <hr>
    <filter-bar
      v-for="(x, index) in filters"
      :key="index"
      :filter="x"
      :filters="filters"
    />
    <el-row>
      <el-col :span="12">
        <div
          v-if="isFilterEnable"
          class="button-group"
        >
          <el-button
            icon="el-icon-plus"
            circle
            size="small"
            @click="addFilter"
          />
          <el-button
            type="primary"
            size="small"
            @click="getLogs"
          >
            搜索
          </el-button>
          <el-button
            size="small"
            @click="resetData()"
          >
            重置
          </el-button>
          <el-button
            :disabled="!saveSearchRecordPermission"
            type="text"
            size="small"
            @click="handleCreate"
          >
            保存此搜索条件
          </el-button>
        </div>
        <div
          v-else
          class="button-group-no-active"
        >
          <el-button
            size="small"
            :disabled="!searchPermission"
            @click="addFilter"
          >
            点击添加搜索条件
          </el-button>
          <el-dropdown
            trigger="click"
            placement="bottom"
          >
            <el-button
              size="small"
              :disabled="disabled || !searchPermission"
              @click="getSearchRecords"
            >
              选择已保存的搜索
            </el-button>
            <el-dropdown-menu
              slot="dropdown"
              class="dropdown-menu"
            >
              <el-dropdown-item
                v-for="record in searchRecords"
                :key="record.id"
                :value="record.id"
                class="dropdown-button"
              >
                <el-row class="dropdown-item">
                  <el-col :span="20">
                    <el-button
                      size="small"
                      type="text"
                      @click="setFilters(record.filters)"
                    >
                      <el-popover
                        placement="left-start"
                        width="200"
                        trigger="hover"
                        :content="record.content.join(&quot;,&quot;)"
                      >
                        <el-button
                          slot="reference"
                          size="small"
                          type="text"
                          style="text-align: left;"
                        >
                          {{ record.name }}
                        </el-button>
                      </el-popover>
                    </el-button>
                  </el-col>
                  <el-col :span="4">
                    <el-button
                      size="small"
                      :disabled="!deleteSearchRecordPermission"
                      type="danger"
                      @click="deleteSearchRecord(record.id)"
                    >
                      删除
                    </el-button>
                  </el-col>
                </el-row>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-col>
      <el-col :span="12">
        <el-button
          size="small"
          :disabled="!exportPermission"
          class="export-button"
          @click="exportAuditLogs"
        >
          导出
        </el-button>
      </el-col>
    </el-row>
    <log-table
      v-if="mode === 'log'"
      :table-data="dataByPage"
    />
    <section-table
      v-if="mode === 'section'"
      :table-data="dataByPage"
    />
    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-pagination
      :page-size="pageSize"
      :total="count"
      :current-page.sync="currentPage"
      background
      layout="total, prev, pager, next, jumper"
      @current-change="getData"
    />
    <el-dialog
      title="保存搜索条件"
      width="400px"
      label-width="100px"
      :visible.sync="dialogFormVisible"
    >
      <el-form
        ref="recordName"
        :model="recordForm"
        label-width="80px"
      >
        <el-form-item
          label="搜索名称"
          prop="searchName"
          :rules="rules"
        >
          <el-input
            v-model="recordForm.searchName"
            autocomplete="off"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
    <!-- eslint-enable vue/attribute-hyphenation -->
  </div>
</template>

<script>
import API          from '@/api'
import FilterBar    from './FilterBar.vue'
import SectionTable from '@/components/AdminAuditLog/SectionTable'
import LogTable     from '@/components/AdminAuditLog/LogTable'

export default {
  components: {
    FilterBar,
    SectionTable,
    LogTable
  },
  data () {
    return {
      mode:              'section',
      pageSize:          25,
      currentPage:       1,
      count:             0,
      loading:           false,
      filters:           [],
      dataByPage:        [],
      searchRecords:     [],
      dialogFormVisible: false,
      disabled:          false,
      recordForm:        {
        searchName: ''
      },
      rules:             [
        { required: true, message: '名称不能为空' },
        { max: 20, message: '长度在20个字以内', trigger: 'blur' }
      ]
    }
  },
  computed: {
    isFilterEnable () {
      return this.filters.length > 0
    },
    exportPermission () {
      return this.$store.getters.hasPermission('admin_audit_log.export')
    },
    saveSearchRecordPermission () {
      return this.$store.getters.hasPermission('admin_audit_log.save_search_record')
    },
    searchPermission () {
      return this.$store.getters.hasPermission('admin_audit_log.query')
    },
    deleteSearchRecordPermission () {
      return this.$store.getters.hasPermission('admin_audit_log.delete_search_record')
    }
  },
  created () {
    this.getSections()
    this.getSearchRecords()
  },
  methods: {
    addFilter () {
      const filterJson = {
        id:        this.filters.length,
        relation:  'and',
        type:      '',
        condition: '',
        query:     ''
      }
      if (this.filters.length === 0) {
        filterJson.relation = 'first'
      }
      this.filters.push(filterJson)
    },
    resetData () {
      this.filters     = []
      this.currentPage = 1
      this.getData()
    },
    getData () {
      if (this.mode === 'section') this.getSections()
      if (this.mode === 'log') this.getLogs()
    },
    getLogs () {
      if (this.checkFilters()) return

      this.loading = true
      const params = {
        page:    this.currentPage,
        filters: this.filters
      }

      API.auditLogs.getLogs(params)
        .then(response => {
          this.mode       = 'log'
          this.loading    = false
          this.dataByPage = response.data.logs
          this.count      = response.data.size
        })
        .catch(() => {
          this.loading = false
        })
    },
    checkFilters () {
      function isBlank (element, index, array) {
        return element.relation === '' ||
          element.type === '' ||
          element.condition === '' ||
          element.query === ''
      }

      const anyBlank = this.filters.some(isBlank)
      if (anyBlank) this.$message.error('筛选类型、条件、内容均不能为空')

      return anyBlank
    },
    getSections () {
      this.loading = true
      const params = {
        page: this.currentPage
      }
      API.auditLogs.getSections(params)
        .then(response => {
          this.mode       = 'section'
          this.loading    = false
          this.dataByPage = response.data.logs
          this.count      = response.data.size
        })
        .catch(() => {
          this.loading = false
        })
    },
    exportAuditLogs () {
      if (this.checkFilters()) return
      this.loading = true
      const params = {
        filters: this.filters
      }
      API.auditLogs.exportAuditLogs(params)
        .then(response => {
          this.$message.info(this.$t('download_record.success'))
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleCreate () {
      if (this.checkFilters()) return
      this.dialogFormVisible = true
    },
    handleSubmit () {
      this.$refs.recordName.validate((valid) => {
        if (valid) {
          this.dialogFormVisible = false
          this.createSearchRecord()
        } else {
          this.$message.error('请按要求填写')
          return false
        }
      })
    },
    createSearchRecord () {
      this.loading = true
      const params = {
        filters: this.filters,
        name:    this.recordForm.searchName
      }
      API.auditLogs.createSearchRecord(params)
        .then(response => {
          this.loading = false
          this.recordForm.searchName = ''
          this.$message.success('成功创建搜索条件')
          this.getSearchRecords()
        })
        .catch(() => {
          this.loading = false
        })
    },
    deleteSearchRecord (recordId) {
      const params = {
        id: recordId
      }
      API.auditLogs.deleteSearchRecord(params)
        .then(response => {
          this.$message.success('搜索记录已成功删除')
          this.getSearchRecords()
        })
        .catch(() => {})
    },
    afterCreateSearchRecords () {
      this.disabled = !this.searchRecords.length > 0
    },
    getSearchRecords () {
      this.loading = true
      API.auditLogs.getSearchRecords()
        .then(response => {
          this.loading       = false
          this.searchRecords = response.data.records
          this.afterCreateSearchRecords()
        })
        .catch(() => {
          this.loading = false
        })
    },
    setFilters (filters) {
      for (const filter of filters) {
        this.filters.push(filter)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding:          2em;
  background-color: white;
  min-width:        1000px;
}

.el-pagination {
  margin-top: 1em;
}

.dropdown-item {
  padding-right: 5px;
  padding-left: 5px;
}

.button-group {
  margin-bottom: 20px;
  margin-left:   10px;
  margin-top:    -8px;
}

.button-group-no-active {
  margin-bottom: 20px;
}

.export-button {
  margin-right: 10px;
  float:        right;
}

.dropdown-button .el-button--text {
  min-width: 250px;
  padding: 0 5px 0 0;
}

.dropdown-menu .el-dropdown-menu__item {
  padding: 0 10px;
}
</style>
