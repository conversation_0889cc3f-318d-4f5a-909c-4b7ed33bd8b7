class Zyfd::Accounts<PERSON><PERSON>roller < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy
  before_action :set_business_system_api, only: [:reset_password]

  def reset_password
    @api.reset_account_password(params[:account_id], current_admin, request)
    json_respond success: true
  rescue @api_return_error => e
    logger.error(e.message)
    logger.error(e.backtrace.join("\n"))
    json_custom_respond 456, success: false, error_message: e.message
  end

  private

  def set_business_system_api
    @business_system = BusinessSystem.find_by(id: params[:system_id])
    raise '系统不存在，请检测system_id' if @business_system.nil?

    @api = @business_system.api
    @api_return_error = @business_system.api_return_error.safe_constantize
  rescue StandardError => e
    logger.error(e.message)
    logger.error(e.backtrace.join("\n"))

    json_custom_respond(404, success: false, error_message: e.message)
  end

  def authenticate_policy
    authorize @business_system, policy_class: Zyfd::AccountPolicy
  end
end
