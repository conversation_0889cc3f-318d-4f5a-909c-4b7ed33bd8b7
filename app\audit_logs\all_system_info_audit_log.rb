# frozen_string_literal: true

# 业务系统维护的审计日志
class AllSystemInfoAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '系统管理'
    @operation_category = '业务系统信息维护'
    super
  end

  def create
    @operation = '创建系统'
    comments = create_data
    @comment = comments.join('，')
    create_audit_log
  end

  def import_success
    @operation = '系统数据导入'
    @comment   = '成功导入系统数据'
    create_audit_log
  end

  def import_failed
    @operation = '系统数据导入'
    @comment   = '导入系统数据失败'
    create_audit_log
  end

  def export_success
    @operation = '系统数据导出'
    @comment   = '成功导出系统数据'
    create_audit_log
  end

  def update
    @operation = '更新系统'
    comments = update_data
    @comment = comments.join('，')
    create_audit_log
  end

  def delete
    @operation = '删除系统'
    @comment   = "删除了系统「#{params.name}」"
    create_audit_log
  end

  private

  def set_system_data
    @system_hash = { name: '系统名称', company: '系统厂商', version: '系统版本', admin: '管理员姓名', email: '管理员邮箱', business_system_id: '关联业务系统' }
  end

  def update_data
    set_system_data
    comments = ["修改了系统 「#{params[:name]}」"]

    @system_hash.each do |key, value|
      next unless params.previous_changes[key].present?

      comment = "#{value}由「#{params.previous_changes[key][0]}」修改为「#{params.previous_changes[key][1]}」"
      if key == :business_system_id
        comment = "#{value}由「#{BusinessSystem.find_by(id: params.previous_changes[key][0])&.name}」修改为「#{BusinessSystem.find_by(id: params.previous_changes[key][1])&.name}」"
      end

      comments << comment
    end
    comments
  end

  def create_data
    set_system_data
    comments = ["全部系统管理：创建系统「#{params.name}」"]

    @system_hash.each do |key, value|
      comments << "#{value}：「#{params[key]}」" if params[key].present?
    end
    comments
  end
end
