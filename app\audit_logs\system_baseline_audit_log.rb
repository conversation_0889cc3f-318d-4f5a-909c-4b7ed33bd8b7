# frozen_string_literal: true

# 系统基线的审计日志
class SystemBaselineAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '系统基线管理'
    @operation_category = '系统基线管理'
    super
  end

  def export_baselines
    @operation = '系统基线导出'
    @comment   = "#{params.name}：导出系统基线表"
    create_audit_log
  end

  def export_select_baselines
    @operation = '系统基线导出'
    baseline_ids = params[1]
    return if baseline_ids.blank?

    baselines = SystemBaseline.where(id: baseline_ids)
    @comment  = "#{params[0].name}：导出指定系统基线表，#{baselines.pluck(:name).join('、')}"
    create_audit_log
  end

  def export_accounts
    @operation = '系统基线账号导出'
    @comment   = "#{params.name}：导出系统基线账号列表"
    create_audit_log
  end

  def export_select_accounts
    @operation = '系统基线账号导出'
    baseline_ids = params[1]
    return if baseline_ids.blank?

    baselines = SystemBaseline.where(id: baseline_ids)
    @comment  = "#{params[0].name}：导出系统基线账号列表，#{baselines.pluck(:name).join('、')}"
    create_audit_log
  end

  def import_success
    @operation = '系统基线导入'
    @comment   = "#{params.name}：成功导入系统基线表"
    create_audit_log
  end

  def import_failed
    @operation = '系统基线导入'
    @comment   = "#{params.name}：导入系统基线表失败"
    create_audit_log
  end

  def link
    @operation = '系统基线关联'
    baseline = params[:follow_job] ? "关联至岗位基线" : "关联至系统基线「#{params[:baseline]&.name}」"
    @comment = "#{params[:business_system].name}：账号 #{params[:accounts].join('、')} #{baseline}"
    create_audit_log
  end

  def revoke_bulk
    @operation = '批量取消基线关联'
    @comment   = "#{params[:business_system].name}：多个账号 #{params[:accounts].join('、')} 解除基线关联"
    create_audit_log
  end

  def create
    @operation = '创建系统基线'
    @comment   = "#{params.business_system.name}：创建系统基线「#{params.name}」"
    create_audit_log
  end

  def update
    @operation = '变更系统基线'
    comments   = ["#{params.business_system.name}：修改 ID 为 #{params.id} 的系统基线"]
    if params.previous_changes[:name].present?
      comments << "名称由「#{params.previous_changes[:name][0]}」修改为「#{params.previous_changes[:name][1]}」"
    end
    if params.previous_changes[:output_schema_data].present?
      comments << "告警维度由「#{get_output_schema_data(params.previous_changes[:output_schema_data][0])}」修改为「#{get_output_schema_data(params.previous_changes[:output_schema_data][1])}」"
    end
    if params.previous_changes[:department_id].present?
      comments << "部门由「#{get_department_name(params.previous_changes[:department_id][0])}」修改为「#{get_department_name(params.previous_changes[:department_id][1])}」"
    end
    if params.previous_changes[:compare_rule].present?
      comments << "告警条件由「#{get_compare_rule(params.previous_changes[:compare_rule][0])}」修改为「#{get_compare_rule(params.previous_changes[:compare_rule][1])}」"
    end
    if params.previous_changes[:comment].present?
      comments << "备注由「#{params.previous_changes[:comment][0]}」修改为「#{params.previous_changes[:comment][1]}」"
    end
    if params.previous_changes[:output_datas].present?
      # system_baseline_histories默认是倒序的, 最后的版本取first
      comments << "系统基线权限发生变化，历史版本#{params.system_baseline_histories.first&.created_at&.strftime('%Y-%m-%d %H:%M:%S')}"
    end
    @comment = comments.join('，')
    create_audit_log
  end

  def destroy
    @operation = '删除系统基线'
    @comment   = "#{params.business_system.name}：删除了系统基线「#{params.name}」"
    create_audit_log
  end

  def clone_from_baseline
    @operation = '系统基线复制'
    @comment   = "#{params[:baseline].business_system.name}：#{params[:mode_display]}系统基线「#{params[:baseline].name}」，权限来源为复制系统基线「#{params[:source].name}」"
    create_audit_log
  end

  def clone_from_account
    @operation = '系统基线复制'
    @comment   = "#{params[:baseline].business_system.name}：#{params[:mode_display]}系统基线「#{params[:baseline].name}」，权限来源为复制账号「#{params[:source].name}」"
    create_audit_log
  end

  def delete_permission
    @operation = '删除权限'
    schema = params[0].output_schema.find{|x| x[:data_key] == params[1][:data_key] }
    data_name = schema ? schema[:name] : ''
    permission = change_permission(schema, JSON.parse(params[1][:permission], symbolize_names: true))
    @comment   = "「#{params[0].business_system.name}」删除基线权限「#{params[0].name}」「#{data_name}」「#{permission}」"
    create_audit_log
  end

  def create_permission
    @operation = '新建权限'
    schema = params[0].output_schema.find{|x| x[:data_key] == params[1][:data_key] }
    data_name = schema ? schema[:name] : ''
    permission = change_permission(schema, params[1][:permission])
    @comment   = "「#{params[0].business_system.name}」新建基线权限「#{params[0].name}」「#{data_name}」「#{permission}」"
    create_audit_log
  end

  def update_permission
    @operation = '编辑权限'
    schema = params[0].output_schema.find{|x| x[:data_key] == params[1][:data_key] }
    data_name = schema ? schema[:name] : ''
    permission = change_permission(schema, params[1][:permission])
    @comment   = "「#{params[0].business_system.name}」编辑基线权限「#{params[0].name}」「#{data_name}」「#{permission}」"
    create_audit_log
  end

  def restore_from_history
    @operation = '从历史版本还原系统基线'
    @comment = "「#{params[0]}」 从历史版本还原系统基线 系统基线ID: 「#{params[1]}」, 系统基线历史版本ID: 「#{params[2]}」"
    create_audit_log
  end

  def merge_baselines
    @operation = '合并系统基线'
    merge_params = params[1]
    @comment = "合并系统基线 系统ID：「#{params[0]}」，待合并基线Ids: 「#{merge_params[:origin_baseline_ids].join('、')}」，"
    if merge_params[:merge_type].to_s == '1'
      department_name = Department.find_by(id: merge_params[:department_id])&.name
      compare_rule_desc = merge_params[:compare_rule].to_s == '0' ? '权限不一致' : '超出基线范围'
      @comment += "合并方式：「创建新基线」，基线名称：「#{merge_params[:name]}」，选择部门：「#{department_name}」，"\
                  "备注：「#{merge_params[:comment]}」，告警条件：「#{compare_rule_desc}」，"
    else
      @comment += "合并方式：「合并至指定基线」，指定基线ID：「#{merge_params[:baseline_id]}」，"
    end
    @comment += "是否移动原基线管理账号：「#{merge_params[:move_account_bind].to_s == 'true' ? '是' : '否'}」"
    create_audit_log
  end

  def remove_baseline
    @operation = '移除来源基线'
    @comment = "移除来源基线 合并基线ID：「#{params[0]}」，来源基线ID：「#{params[1]}」"
    create_audit_log
  end

  protected

  def change_permission(schema, permission)
    return '' unless schema
    output_str = ''
    schema[:schema].each do |x|
      output_str += "#{x[:label]}: #{permission[x[:property].to_sym]}#{x != schema[:schema].last ? ', ' : ''}"
    end
    output_str
  end

  def baseline_name(id)
    name = SystemBaseline.find_by(id: id)&.name
  end

  # 功能: 获取要通知的报警权限
  # 注意: 过滤is_notice为true
  def get_output_schema_data(data)
    data.select { |obj| obj[:is_notice] }.map { |obj| obj[:name] }.join('、')
  rescue StandardError
    ''
  end

  def get_department_name(department_id)
    Department.find_by(id: department_id)&.name || '无'
  end

  def get_compare_rule(compare_rule)
    return '' if compare_rule.blank?
    compare_rule == 'all_contrast' ? '权限不一致' : '超出基线范围'
  end
end
