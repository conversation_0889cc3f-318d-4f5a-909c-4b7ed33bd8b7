# frozen_string_literal: true

class AnalysisReportExportAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '数据功能'
    @operation_category = '数据导出'
    super
  end

  def global_base_report_success
    @operation = '权限审核总表导出'
    @comment   = "成功导出「#{quarter_name(params[:other_quarter_id])} - #{quarter_name(params[:quarter_id])}」的 「权限审核总表」"
    create_audit_log
  end

  def global_base_report_failed
    @operation = '权限审核总表导出'
    @comment   = "导出「#{quarter_name(params[:other_quarter_id])} - #{quarter_name(params[:quarter_id])}」的 「权限审核总表」失败"
    create_audit_log
  end

  def diff_global_base_report_success
    @operation = '权限变化总表导出'
    @comment   = "成功导出「#{quarter_name(params[:other_quarter_id])} - #{quarter_name(params[:quarter_id])}」的 「权限变化总表导出」"
    create_audit_log
  end

  def diff_global_base_report_failed
    @operation = '权限变化总表导出'
    @comment   = "导出「#{quarter_name(params[:other_quarter_id])} - #{quarter_name(params[:quarter_id])}」的 「权限变化总表导出」失败"
    create_audit_log
  end

  def global_inspect_report_success
    @operation = I18n.t('account_inspect_report.title')
    @comment   = "成功导出「#{quarter_name(params[:other_quarter_id])} - #{quarter_name(params[:quarter_id])}」的 「#{I18n.t('account_inspect_report.title')}」"
    create_audit_log
  end

  def global_inspect_report_failed
    @operation = I18n.t('account_inspect_report.title')
    @comment   = "导出「#{quarter_name(params[:other_quarter_id])} - #{quarter_name(params[:quarter_id])}」的 「#{I18n.t('account_inspect_report.title')}」失败"
    create_audit_log
  end

  private

  def quarter_name(quarter_id)
    Quarter.find_by(id: quarter_id)&.name
  end
end
