# frozen_string_literal: true

# 员工控制器
class UsersController < ApplicationController
  before_action :authenticate_admin!, except: %i[export]
  before_action :authenticate_policy!, except: %i[list info quarter_user_info export sort_data history my_messages search_users]

  before_action :set_user, only: %i[user_detail user_accounts info quarter_user_info history update accounts_export accounts_list_export account_list_differences job_baseline_user_compare]
  before_action :set_quarter, only: %i[search mine accounts_export accounts_list_export export_users job_baseline_user_compare]
  before_action :set_filter, only: %i[search export_users]
  before_action :set_batch_params, only: [:batch_update]
  before_action -> { check_dependent_task_status(:quarter_users_accounts_caches_task) }, only: %i[search accounts_export accounts_list_export]
  before_action :admin_query_bs_ids, only: %i[history account_list_differences job_baseline_user_compare user_accounts]

  # 用于全部账号或搜索页面
  def search
    json_respond User.all_users(@quarter, @page, @per_page, current_admin, @filter, @account_filter)
  end

  # 用于个人权限页面
  def mine
    quarter = Quarter.find(params[:quarter_id])
    binding_user = current_admin.user
    return json_custom_respond(404, error_message: '该用户未绑定员工信息', binding_user: false) unless binding_user

    json_respond current_admin.user.my_page_data(quarter)
  end

  def my_messages
    # messages = current_admin.global_alert_messages.includes(quarter_global_alert: :global_alert)
    # result = { all_count: messages.count, data: messages.page(page).per(per_page)[0..2].map(&:full_output) }
    # json_respond result

    messages = current_admin.global_alert_messages

    json_respond messages.map(&:full_output)
  end

  # 用于用户选择器 & 后台账号管理创建账号职位接口 & 员工信息管理批量编辑的审核人
  def list
    users =
      User
        .includes(%i[department manager])
        .inservice
        .to_a
        .sort_by { |x| Pinyin.t(x.name) }
        .map(&:information)

    json_respond users
  end

  # 最新用户信息
  def info
    json_respond @user.information
  end

  # 选择当前时间点用户信息（quarter_user）
  def quarter_user_info
    json_respond @user.quarter_user_information(params[:quarter_id])
  end

  # 导出方法，跳过了登录验证，所以也不判断权限
  def export
    send_file_compatible_with_msie DataExport::UserExcelExport.new.export
    audit_log! action: :export_success
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! action: :export_faild
    json_custom_respond(500, error_message: e.message)
  end

  # 用于一键导出员工权限表
  def accounts_export
    caches = AccountSummaryCache.where(quarter_id: @quarter.id, user_id: @user.id)
    accounts = if Setting.frontendSettings&.[]('disableUserAllAccounts')
                 bs_ids = current_admin.business_systems_in_query.map(&:id)
                 caches.select { |x| bs_ids.include?(x.bs_id) }.map { |x| x.bs&.account_class&.find(x.account_id) }
               else
                 caches.map { |x| x.bs&.account_class&.find(x.account_id) }
               end
    return json_custom_respond(404, error_message: '该用户未在系统中发现任何账号') if accounts.blank?

    files = accounts.map(&:export_excel)
    unique = "_#{current_admin.id}_#{Time.now.to_i}"
    rar_file = DataExport.batch_zip(files, unique)

    send_data(
      File.open(rar_file, 'rb').read,
      filename: URI.encode_www_form_component("#{@user.name}账号权限导出.rar".gsub(/\s+/, '')),
      type:     'application/octet-stream;charset=utf-8'
    )
    audit_log! ({ quarter_name: @quarter.name, account_names: accounts.map(&:name), user_name: @user.name }), action: :accounts_export_success
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! ({ quarter_name: @quarter.name, account_names: accounts.map(&:name), user_name: @user.name }), action: :accounts_export_faild
    json_custom_respond(500, error_message: e.message)
  end

  def accounts_list_export
    quarter_user = @user.quarter_user(@quarter.id)
    return json_custom_respond(404, error_message: "#{@quarter.name}未发现导入用户信息") if quarter_user.blank?
    caches = AccountSummaryCache.where(quarter_id: @quarter.id, user_id: quarter_user.user_id)
    # 如果开启disableUserAllAccounts禁用导出全部系统账号，使其只导出拥有系统权限的账号
    accounts = if Setting.frontendSettings&.[]('disableUserAllAccounts')
                 bs_ids = current_admin.business_systems_in_query.map(&:id)
                 caches.select { |x| bs_ids.include?(x.bs_id) }.map { |x| x.bs&.account_class&.find(x.account_id) }
               else
                 caches.map { |x| x.bs&.account_class&.find(x.account_id) }
               end
    return json_custom_respond(404, error_message: '该用户未在系统中发现任何账号') if accounts.blank?

    all_system_infos = Setting.frontendSettings&.[]('exportUserAccountAllSystemInfo') ? AllSystemInfo.all.map(&:output) : []

    exporter = DataExport::AccountsListExport.new(quarter_user.information, accounts, all_system_infos)
    send_data(
      exporter.export,
      filename: URI.encode_www_form_component("#{quarter_user.name}账号列表导出.xlsx".gsub(/\s+/, '')),
      type:     'application/octet-stream;charset=utf-8'
    )
    audit_log! ({ quarter_name: @quarter.name, department_name: quarter_user.department&.name, user_name: quarter_user.name }), action: :accounts_list_export_success
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! ({ quarter_name: @quarter.name, department_name: quarter_user.department&.name, user_name: quarter_user.name }), action: :accounts_list_export_faild
    json_custom_respond(500, error_message: e.message)
  end

  def history
    authorize params[:permission_type], policy_class: UserPolicy
    current_page = params[:page] || 1
    per_page = params[:per_page] || 15
    change_histories = UserAccountChangeHistory.where(user_id: @user.id).order(log_time: :desc, log_type: :desc)
    histories = change_histories.page(current_page).per(per_page)
    # 过滤无系统权限的数据
    history_list = histories.map do |history|
      output = history.output
      output[:systems] = output[:systems].select { |x| @bs_ids.include? x[:id] }
      output
    end
    # 上面过滤了系统，会出现账号变化，但是系统为空的情况，所以需要进一步过滤
    history_list = history_list.reject { |x| x[:type] == 'account_log' && x[:systems].blank? }
    json_respond(
      history:             history_list,
      history_task_status: QuarterTaskStatus.history_task_status,
      last_quarter:        Quarter.last.name,
      total_count:         histories.total_count
    )
  end

  # 用于员工信息管理页面
  def list_all
    # 有权限的部门
    # 因为有没部门的user，应该加在权限下
    admin_department_ids = current_admin.departments_in_query&.pluck(:id).uniq | [nil]

    params_page     = params[:page] || 1
    params_per_page = params[:all] == 'true' ? User.count : (params[:per_page] || 25)

    users =
      User.department(admin_department_ids)
        .query_name(params[:query])
        .department(params[:department_id])
        .status(params[:status])
        .order_by(params[:condition], params[:sort_type])

    json_respond({
                   users: users.page(params_page).per(params_per_page),
                   count: users.count
                 })
  end

  # 用于员工信息管理页面
  def create
    user = User.new(new_user)

    if user.save
      audit_log! user
      json_respond({ success: true, data: user.information })
    else
      json_respond({ success: false, message: user.errors.full_messages.join('; ') })
    end
  end

  # 用于员工信息管理页面
  def update
    audit_log! @user if @user.update_with_jobs(user_params)

    json_respond success: true
  end

  # 用于员工信息管理界面，批量更新用户信息
  def batch_update
    if @users.length == 0
      json_respond success: false, errors: ['不能批量修改离职员工信息']
    else
      result = []
      @users.each do |user|
        update_result = user.update_with_jobs(@update_columns)
        audit_log! user, action: :batch_update
        result <<
          if update_result
            { success: true, errors: [] }
          else
            { success: false, errors: user.errors }
          end
      end
      User.find_by(id: params[:manager_id])&.update(manager_id: @old_manager_id)

      if result.all? { |x| x[:success] == true }
        json_respond success: true, errors: []
      else
        json_respond success: false, errors: result.map { |x| x[:errors] }.flatten
      end
    end
  end

  # 用于员工信息管理页面排序
  def sort_data
    json_respond User.all.order(params[:condition].to_sym)
  end

  def account_list_differences
    contrast_quarter_id = params[:contrast_quarter_id].zero? ? params[:current_quarter_id] : params[:contrast_quarter_id]
    json_respond @user.account_list_differences(params[:current_quarter_id], contrast_quarter_id, @bs_ids)
  end

  def job_baseline_user_compare
    compare_data = @user.job_baseline_compare(@quarter.id, @bs_ids)
    json_respond compare_data
  rescue StandardError => e
    json_custom_respond(404, error_message: e.message)
  end

  def user_detail
    json_respond @user.output_detail(params[:quarter_id])
  end

  def user_accounts
    systems_accounts, no_account_systems = @user.get_systems_accounts(params[:quarter_id], @bs_ids)
    json_respond systems_accounts: systems_accounts, no_account_systems: no_account_systems
  end

  # 导出用户列表
  def export_users
    options = { filter: @filter, account_filter: @account_filter }
    DownloadExport::UserExport.new(@quarter, current_admin, options).async_export

    # audit_log! ({quarter_name: @quarter.name, user_size: data[:size]}), action: :export_users_success
    json_respond(status: true)
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    # audit_log! ({quarter_name: @quarter.name, user_size: data[:size]}), action: :export_users_faild
    json_respond(status: false, error_message: e.message)
  end

  # 1. 用于外部系统选择用户
  # 2. 在电子证书列表也请求一次，为了编辑时回显，不做分页
  def search_users
    users = User.inservice
    users = if params[:user_ids].present?
              users.where(id: params[:user_ids])
            else
              users
                .search(name_or_code_cont: params[:query])
                .result
                .limit(20)
            end
    users = users.sort_by { |x| Pinyin.t(x.name) }.map(&:output)
    json_respond users
  end

  private

  def authenticate_policy!
    authorize User
  end

  def set_user
    @user = User.find(params[:user_id])
  rescue StandardError => e
    json_custom_respond(404, error_message: e.message)
  end

  def set_filter
    @page           = params[:page] || 1
    @per_page       = params[:per_page] || 15
    @filter         = {
      job_id:             params[:job_id],
      name:               params[:u_name],
      department_id:      params[:department_id],
      status:             params[:status],
      position:           params[:position],
      join_date_range:    params[:join_date_range],
      disable_date_range: params[:disable_date_range],
      tag:                params[:tag]
    }
    @account_filter = { account_status: params[:account_status], system_id: params[:system_id] }
  end

  def set_batch_params
    @users          = User.inservice.where(id: params[:ids].map(&:to_i))
    @old_manager_id = User.find_by(id: params[:manager_id])&.manager_id
    @update_columns = {}

    %i[department_id manager_id position inservice].each do |key|
      @update_columns[key] = params[key] if params.key? key
    end
  end

  def new_user
    params.require(:user).permit(:name, :department_id, :manager_id, :position, :inservice, :code, :login_name)
  end

  def user_params
    params.require(:user).permit(:name, :code, :department_id, :manager_id, :position, :inservice, :login_name)
  end

  def admin_query_bs_ids
    @bs_ids = if Setting.frontendSettings&.[]('disableUserAllAccounts')
                current_admin.business_systems_in_query.map(&:id)
              else
                BusinessSystem.inservice.pluck(:id)
              end
  end
end
