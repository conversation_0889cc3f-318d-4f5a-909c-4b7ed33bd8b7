# frozen_string_literal: true

# 系统设置
class AdminSettingsController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!, except: %i[test_email test_sms system_general_settings]
  before_action :verify_update_general_settings, :user_defined_settings, only: %i[update_general_settings]
  before_action :format_send_reset_password, only: %i[update_send_reset_password]

  def update_license
    license = LicenseUploader.new
    license.store!(params[:file])
    license.update_file

    audit_log! action: :update_license_success
  rescue CarrierWave::IntegrityError => e
    audit_log! action: :update_license_failed

    json_custom_respond(500, error_message: e.message)
    false
  end

  # 返回general_settings,不校验权限
  def system_general_settings
    general_settings
  end

  def general_settings
    json_respond quarterMode:         UserDefinedSetting.quarterMode,
                 userInfoColumns:     UserDefinedSetting.user_info_columns,
                 accountDetail:       UserDefinedSetting.accountDetail,
                 appStatusAllowIp:    UserDefinedSetting.appStatusAllowIp,
                 homeSettings:        UserDefinedSetting.homeSettings,
                 globalAlertSettings: UserDefinedSetting.globalAlertSettings,
                 allUsers:            UserDefinedSetting.allUsers,
                 userDetail:          UserDefinedSetting.userDetail
  end

  def update_general_settings
    @settings.keys.each do |key|
      UserDefinedSetting[key] = @settings&.[](key)
    end

    audit_log! general_setting_to_audit_log, action: :update_general_settings_success
    json_respond success: true
  rescue StandardError => e
    audit_log! action: :update_general_settings_failed
    json_custom_respond(500, error_message: e.message)
  end

  def notification_settings
    app_roles = AppRole.all_roles
    app_roles.delete(:personal)
    json_respond notification:  setting_notification_after_fix,
                 templates:     Setting.email_templates,
                 sms_templates: Setting.sms_templates,
                 roles:         app_roles
  end

  def update_notification_settings
    notification                    = UserDefinedSetting.notification || {}
    UserDefinedSetting.notification = notification.merge(notification_params.to_h)

    audit_log! notification_to_audit_log

    json_respond success: true
  end

  # 离职未禁用通知
  def leave_user_serious_notification
    json_respond UserDefinedSetting.leave_user_serious_notification_info
  end

  # 更新离职未禁用通知
  def update_leave_user_serious_notification
    UserDefinedSetting.leave_user_serious_notification =
      UserDefinedSetting.leave_user_serious_notification_info.merge(leave_user_serious_notification_params.to_h)

    audit_log! leave_user_serious_notification_to_audit_log

    json_respond success: true
  end

  def test_email
    test_mail = TestMailer.test(params[:email])
    begin
      test_mail.deliver_now
      json_respond success: true, message: test_mail.message.to_s
    rescue StandardError => e
      json_respond success: false, message: test_mail.message.to_s, error: e.message
    end
  end

  # 短信测试
  def test_sms
    sms = QuarterNotify.find_sms_adapter.new(params[:mobile], params[:content])
    begin
      is_success = sms.send_sms!
      json_respond success: is_success, message: '发送成功'
    rescue StandardError => e
      json_respond success: false, message: '发送失败', error: e.message
    end
  end

  def update_quarter_auto_clear
    UserDefinedSetting.quarter_auto_clear = {
      clear_year:  params[:clear_year],
      clear_type:  params[:clear_type],
      select_days: params[:select_days]
    }

    clear_type_str = case params[:clear_type]
                     when 'all'
                       '全部清除'
                     when 'month'
                       "按月保留, 保留日期：#{params[:select_days]}"
                     when 'week'
                       "按周保留, 保留日期：#{change_week_select_days(params[:select_days])}"
                     end

    clear_year_str = case params[:clear_year].to_s
                     when '0'
                       '不清理数据'
                     else
                       "更改为清理#{params[:clear_year]}年前的数据"
                     end

    audit_log! [clear_year_str, clear_type_str]

    json_respond success: true
  end

  def get_update_quarter_auto_clear
    UserDefinedSetting.initialize_quarter_auto_clear unless UserDefinedSetting.quarter_auto_clear
    json_respond UserDefinedSetting.quarter_auto_clear
  end

  # 更新重置密码通知设置
  def update_send_reset_password
    UserDefinedSetting.send_reset_password = params[:send_reset_password].to_unsafe_h

    audit_log! UserDefinedSetting.send_reset_password

    json_respond success: true
  end

  def send_reset_password
    json_respond settings: UserDefinedSetting.dynamic_send_reset_password
  end

  private

  # 格式化 重置密码通知设置
  def format_send_reset_password
    notice_params = params['send_reset_password']&.[]('notice')
    return if notice_params.blank?

    notice_params.each do |x|
      x['frequency'] = x['frequency']&.to_i
    end
  end

  def user_defined_settings
    @settings =
      params.require('admin_setting').permit(
        :quarterMode,
        userInfoColumns:  %i[prop min_width label show_in_account_page show_in_user_page],
        accountDetail:    {},
        appStatusAllowIp: {},
        homeSettings:     {},
        globalAlertSettings: {},
        allUsers: {},
        userDetail: {}
      ).to_h
  end

  def change_week_select_days(days)
    week_json = %w[星期天 星期一 星期二 星期三 星期四 星期五 星期六]
    days.map { |x| week_json[x.to_i] }
  end

  def authenticate_policy!
    authorize nil, policy_class: AdminSettingPolicy
  end

  def notification_params
    params.require(:notification).permit(
      :enable,
      :sms_enable,
      roles:             {},
      send_setting:      {},
      oa_record_setting: {},
      emails:            [:email, templates: []],
      smses:             [:mobile, templates: []]
    )
  end

  def leave_user_serious_notification_params
    params.require(:leave_user_serious_notification).permit(emails: ['email', 'days', "copy_to": []])
  end

  # MARK: 防止在一些客户中，原 emails 的类型和新的有冲突，自动修复这个问题
  def setting_notification_after_fix
    notification           = UserDefinedSetting.notification
    notification['emails'] = [] if notification['emails'] == {}
    notification['smses']  = [] if notification['smses'] == {}
    notification['send_setting'] = { custom_time: false, send_time: nil } unless notification['send_setting']
    notification['oa_record_setting'] = { admin_ids: [], days: 3 } unless notification['oa_record_setting']
    notification
  end

  def general_setting_to_audit_log
    settings = params.require('admin_setting')

    quarter_modes = { select: '下拉菜单（名称）', select_time: '下拉菜单（导入时间）', datepicker: '日期选择' }
    search = settings[:globalAlertSettings]&.[](:search)
    change_account = settings[:homeSettings]&.[](:change_account)
    alarm_dimission_enable = change_account&.[](:alarm_dimission_enable)
    add_enable = change_account&.[](:add_enable)
    delete_enable = change_account&.[](:delete_enable)
    frozen_enable = change_account&.[](:frozen_enable)
    cancel_enable = change_account&.[](:cancel_enable)

    log_content = []
    log_content << "时间点样式：#{quarter_modes[settings[:quarterMode].to_sym]}"
    log_content << "账号详情：#{settings[:accountDetail]&.[]('show_diff_baseline_default') ? '对比基线' : '对比上个时间点'}"
    log_content << "员工详情页：#{settings[:userDetail]&.[]('disable_no_system_account') ? '不显示' : '显示'}「系统没有账号」"
    log_content << "安全设置：监控接口 IP 访问限制：#{settings[:appStatusAllowIp]&.[]('enable') ? '开启' : '关闭'}"
    log_content << "安全设置：监控接口 IP 访问列表：#{settings[:appStatusAllowIp]&.[]('allow_ip').join('，')}"
    log_content << "员工展示字段: 账号列表显示：#{settings[:userInfoColumns].select { |s| s[:show_in_account_page] }.map { |l| l[:label] }.join('，')}"
    log_content << "员工展示字段: 员工列表显示：#{settings[:userInfoColumns].select { |s| s[:show_in_user_page] }.map { |l| l[:label] }.join('，')}"
    log_content << "首页设置：账号状态变化「离职未禁用」天数为 #{alarm_dimission_enable ? change_account&.[](:alarm_dimission_day) : '全部'}"
    log_content << "首页设置：账号状态变化「新增账号」天数为 #{add_enable ? change_account&.[](:add_day) : '全部'}"
    log_content << "首页设置：账号状态变化「删除账号」天数为 #{delete_enable ? change_account&.[](:delete_day) : '全部'}"
    log_content << "首页设置：账号状态变化「冻结账号」天数为 #{frozen_enable ? change_account&.[](:frozen_day) : '全部'}"
    log_content << "首页设置：账号状态变化「注销账号」天数为 #{cancel_enable ? change_account&.[](:cancel_day) : '全部'}"
    log_content << "告警中心搜索默认设置：处理状态为 #{GlobalAlert.status_text(search&.[](:status))}"
    log_content << "告警中心搜索默认设置：恢复状态为 #{GlobalAlert.recover_status_text(search&.[](:recover_status))}"

    log_content.join('，')
  end

  def notification_to_audit_log
    data         = notification_params.to_h
    app_roles    = AppRole.all_roles
    descriptions = []

    descriptions << "「全局邮件通知：#{data[:enable] ? '启用' : '禁用'}」；"

    unless data['roles'].keys.blank?
      descriptions << '系统角色通知设置：'
      data['roles'].each do |role, templates|
        descriptions << app_roles[role.to_i].to_s + ':「'
        descriptions << templates.map do |template_key|
          template = Setting.email_templates.find { |x| x[:key] == template_key }
          template ? template[:name] : template_key
        end.join('、')
        descriptions << '」;'
      end
      descriptions << ''
    end

    unless data['emails'].blank?
      descriptions << '个人邮箱通知设置：'
      data['emails'].each do |personal|
        descriptions << "「#{personal['email']}」:「"
        descriptions << personal['templates'].map do |template_key|
          template = Setting.email_templates.find { |x| x[:key] == template_key }
          template ? template[:name] : template_key
        end.join('、')
        descriptions << '」;'
      end
    end

    unless data['smses'].blank?
      descriptions << '短信通知设置：'
      data['smses'].each do |personal|
        descriptions << "「#{personal['mobile']}」:「"
        descriptions << personal['templates'].map do |template_key|
          template = Setting.email_templates.find { |x| x[:key] == template_key }
          template ? template[:name] : template_key
        end.join('、')
        descriptions << '」;'
      end
    end

    if data['oa_record_setting'].present? && Setting.oa_record&.[]('enable')
      descriptions << 'OA流程临时授权通知设置：'
      admin_ids = data['oa_record_setting']&.[]('admin_ids')
      days = data['oa_record_setting']&.[]('days')
      admins = Admin.where(id: admin_ids)
      descriptions << "通知管理员「#{admins.pluck(:name).join('、')}」: " if admins.present?
      descriptions << "提前通知「#{days}」天" if days.present?
    end

    descriptions.join('')
  end

  def leave_user_serious_notification_to_audit_log
    data         = leave_user_serious_notification_params.to_h
    descriptions = []
    if data['emails'].blank?
      descriptions = ['无']
    else
      descriptions << ''
      data['emails'].each do |personal|
        descriptions << "#{personal['email']}: 离职#{personal['days']}日发送通知;"
      end
    end
    descriptions.join(' ')
  end

  # 校验更新设置的值
  # 首页账号状态变化统计天数设置为启用时，天数不能为空并且只能为数字
  def verify_update_general_settings
    change_account = user_defined_settings&.[]('homeSettings')&.[]('change_account')
    return unless change_account

    keys = change_account.keys.grep(/enable/)
    is_success = keys.all? do |key|
      day_key = key.sub('enable', 'day')
      !change_account[key] || change_account[key] && (change_account[day_key].present? && change_account[day_key].to_s.match(/^\d+$/).present?)
    end
    return json_custom_respond(400, error_message: '首页账号状态变化统计天数设置为启用时，指定天数不能为空并且只能是数字') unless is_success
  end
end
