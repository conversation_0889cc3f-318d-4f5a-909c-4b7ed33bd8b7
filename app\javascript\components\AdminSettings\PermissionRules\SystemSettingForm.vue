<template>
  <el-dialog
    :visible.sync="visible"
    title="告警系统设置"
    :close-on-click-modal="false"
    width="600px"
    appendToBody
  >
    <el-form
      ref="form"
      :model="dynamicValidateForm"
      label-width="80px"
    >
      <el-form-item label="选择系统">
        <system-select
          multiple
          style="width:400px;"
          :disabled="allSystem"
          v-model="systems"
        />
        <el-checkbox v-model="allSystem" style="margin-left: 5px;">所有系统</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="handleCommit"
        >
          保存
        </el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
  import API from '@/api'
  import SystemSelect from '@/components/common/SystemSelect.vue'
  export default {
  components: {
    SystemSelect
  },
  data () {
    return {
      visible: false,
      loading: false,
      systems: [],
      allSystem: true
    }
  },
  computed: {
  },
  created () {
    this.getSystemSetting()
  },
  methods: {
    getSystemSetting () {
      this.loading = true
      this.$axios.get(`api/position_role_checks/system_setting`)
        .then(response => {
          this.systems = response.data.systems
          this.allSystem = response.data.all_system
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleCommit () {
      this.loading = true
      let theParams = {
        systems: this.systems,
        all_system: this.allSystem
      }
      this.$axios.put(`api/position_role_checks/update_system_setting`, theParams)
        .then(response => {
          this.loading = false
          this.$message.success('提交成功')
          this.getSystemSetting()
          this.visible = false
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.form-item-container {
  @include vertical_center_left;
  height: 40px;
}
.float-right{
  float: right;
}
</style>