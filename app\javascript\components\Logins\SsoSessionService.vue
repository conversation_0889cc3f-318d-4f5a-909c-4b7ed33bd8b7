<template>
  <div>
    <el-container
      v-loading.fullscreen.lock="loginLoading"
      element-loading-text="拼命登录中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    />
    <el-dialog
      title="登录失败"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <span>{{ msg }}</span>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="login()"
        >
          重新登录
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import API from '@/api'
import LoginFunctions from '@/components/Logins/LoginFunctions'
import Token from '@/settings/token.js'

export default {
  mixins: [LoginFunctions],
  props: {
    message: {
      type:    String,
      default: ''
    }
  },
  data () {
    return {
      authMethod:   this.$settings.authMethod,
      loginLoading: false,
      dialogVisible: false,
      msg: ''
    }
  },
  computed: {
    customerId () {
      return this.$settings.customerId
    },
    ssoAdapter () {
      return this.$settings.sso.adapter
    },
    cookieKey () {
      return this.$settings.sso.cookie_key
    }
  },
  created () {
    if (this.message !== '') {
      this.$message.info(this.message)
    }
    this.onSubmit()
  },
  methods: {
    onSubmit () {
      // 在页面打开时，优先清除 token ，防止旧的过期 token 在 Header 中的其他接口返回 401 把界面重定向了。
      Token.delete()
      this.loginLoading = true
      let token = null
      if (this.ssoAdapter === 'zyfund') {
        token = Cookies.get(this.cookieKey)
      } else if (this.ssoAdapter === 'csfund' || this.ssoAdapter == 'yiban') {
        token = this.$route.query.token
      }
      API.auth.ssoLogin(token)
        .then(response => {
          this.loginLoading = false
          if (response.data.success) {
            // 直接使用 response.headers 会抛出 header 不安全的错误
            const headers     = {
              'access-token': response.headers['access-token'],
              'token-type':   response.headers['token-type'],
              expiry:         response.headers.expiry,
              uid:            response.headers.uid,
              client:         response.headers.client
            }
            Token.store(headers)
            this.$store.commit('adminSetter', response.data.data)
            this.getMyFunctions(headers, false)
            this.getAdminRouters(headers)
            this.getNewestQuarter(headers)
            this.getAdminPrivileges(headers)
            this.getUserTree(headers)
            this.getPositions(headers)
            this.noticeMessage(headers)
            if (this.$settings.o32Option.enable) {
              this.getNewestO32OptionQuarter(headers)
            }
          } else {
            this.dialogVisible = true
            this.msg = response.data.msg
          }
        })
        .catch(error => {
          this.loginLoading  = false
          console.error(error)
          const errorMessage = error.data.errors ? error.data.errors.join('\n') : error.data.error_message
          this.$message.error(errorMessage)
        })
    },
    login () {
      this.$router.push({ name: 'login' })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.login-container {
  width:            100%;
  height:           100%;
  background-color: #E1E1E1;
}

.form-container {
  width:         400px;
  height:        270px;
  border:        1px;
  border-radius: 5px;
  margin:        calc(100vh / 2 - 200px) auto 0;
  padding:       0 2em 2em 2em;

  .title {
    text-align: center;
  }

  .login-form {
    padding-right: 2em;
  }
}
</style>
