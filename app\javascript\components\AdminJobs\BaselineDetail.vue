<template>
  <el-dialog
    :visible.sync="visible"
    :title="`${modeTitle}详情`"
    width="90%"
    @open="handleOpen"
    @close="handleClose"
    append-to-body
  >
    <div class="dialog-container">
      <el-descriptions class="margin-bottom" direction="horizontal" title="" :column="2" border>
        <el-descriptions-item>
          <template slot="label">
            基线名称
          </template>
          {{ this.localBaseline.name }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            部门
          </template>
          {{ this.localBaseline.department_name }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            告警条件
          </template>
          {{ getCompareRule(this.localBaseline.compare_rule) }}
        </el-descriptions-item>
        <el-descriptions-item
          v-show="localBaseline.source_id"
          v-if="!isSingleSchemaData()"
        >
          <template slot="label">
            告警维度
          </template>
          {{ getOutputSchemaData(this.localBaseline.output_schema_data) }}
        </el-descriptions-item>
      </el-descriptions>

      <el-card v-show="localBaseline.source_id">
        <div
          slot="header"
          class="title-line"
        >
          <div class="title">权限内容</div>
        </div>
        <baseline-permissions-preview
          ref="preview"
          :systemId="systemId"
          :sourceType="localBaseline.source_type"
          :sourceId="localBaseline.source_id"
          :baseline="baseline"
          :outputSchemaData="localBaseline.output_schema_data"
          :disable-button="true"
          mode="edit"
        />
      </el-card>

      <comment-bar>
        注意: 如选择部门为非自己管理权限的部门，该基线将无法查看
      </comment-bar>
    </div>
  </el-dialog>
</template>

<script>
import BaselinePermissionsPreview from '@/components/AdminBaselines/BaselinePermissionsPreview.vue'
import CommentBar               from '@/components/common/CommentBar.vue'
export default {
  components: {
    BaselinePermissionsPreview,
    CommentBar
  },
  props:      {
    systemId: {
      type:     Number
    },
    baseline: {
      type:    Object,
      default: () => {}
    }
  },
  data () {
    return {
      visible:       false,
      loading:       false,
      accounts:      [],
      baselines:     [],
      localBaseline: {
        id:          null,
        name:        null,
        source_type: 'baseline',
        source_id:   null,
        linked:      false,
        output_schema_data: [],
        select_output_schema_data: [],
        compare_rule:'all_contrast',
        department_id: ''
      }
    }
  },
  computed:   {
    modeTitle () {
      return `${this.baseline.name}`
    }
  },
  watch:      {
    'localBaseline.source_type' () {
      this.localBaseline.source_id = null
      this.localBaseline.linked    = false
    }
  },
  methods:    {
    getCompareRule (compare_rule) {
      switch (compare_rule) {
        case 'all_contrast':
          return '权限不一致'
        case 'only_add':
          return '超出基线范围'
        default:
          return ''
      }
    },
    getOutputSchemaData (output_schema_data) {
      return output_schema_data.filter ( x => x.is_notice).map (x => x.name).join('、')
    },
    handleOpen () {
      this.localBaseline.id          = this.baseline.id
      this.localBaseline.name        = this.baseline.name
      this.localBaseline.compare_rule= this.baseline.compare_rule
      this.localBaseline.source_type = 'baseline'
      this.localBaseline.source_id   = this.baseline.id
      this.localBaseline.output_schema_data = this.baseline.output_schema_data
      this.localBaseline.department_id = this.baseline.department_id
      this.localBaseline.select_output_schema_data = this.filterOutputSchemaData(this.baseline.output_schema_data)
      this.$nextTick(() => {
        this.loadingRemoteData(true)
        this.$refs.preview.getPermissions()
      })
    },
    handleClose(){
      // 关闭系统基线编辑框的时候要重新设置source_type为baseline
      // 因为当source_type为account的时候，下次打开会导致获取权限时的api报错误(source_id为空)
      this.localBaseline.source_type = 'baseline'
    },
    loadingRemoteData (openOrClose) {
      if (openOrClose &&
        this.localBaseline.source_type === 'baseline' &&
        this.baselines.length === 0
      ) {
        // this.getBaselines()
      }
      if (openOrClose &&
        this.localBaseline.source_type === 'account' &&
        this.accounts.length === 0
      ) {
        // this.getAccounts()
      }
    },
    // getBaselines () {
    //   this.loading = true
    //   this.$axios.get(`/api/systems/${this.systemId}/baselines/all`)
    //     .then(response => {
    //       this.loading   = false
    //       this.baselines = response.data
    //     })
    //     .catch(() => {
    //       this.loading = false
    //     })
    // },
    // getAccounts () {
    //   this.loading = true
    //   this.$axios.get(`/api/systems/${this.systemId}/accounts_with_baseline/all`)
    //     .then(response => {
    //       this.loading  = false
    //       this.accounts = response.data.accounts
    //     })
    //     .catch(() => {
    //       this.loading = false
    //     })
    // },
    syncOutputSchemaData(){
      let data = this.localBaseline.output_schema_data
      let selectData = this.localBaseline.select_output_schema_data
      let newData = data.map(function(obj){
        obj.is_notice = selectData.indexOf(obj.name) > -1
        return obj
      })
      this.localBaseline.output_schema_data = newData
    },
    // el-checkbox-group 中标记哪个复选框处于已选状态
    filterOutputSchemaData(output_schema_data){
      return output_schema_data.filter(function(value, index, array){
        return value.is_notice;
      }).map(function(obj){
        return obj.name
      })
    },
    isSingleSchemaData(){
      return this.localBaseline.output_schema_data.length == 1
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";
</style>
