<template>
  <el-dialog
    :key="account.account_id"
    :title="title"
    :visible.sync="childVisible"
    :append-to-body="appendToBody"
    v-if="account.account_id && system.id"
    width="80%"
  >
    <div v-if="childVisible">
      <user-info
        :account="account"
        :account-info-schema="accountInfoSchema"
        :system-id="system.id"
        :system="system"
        :systemSettings="systemSettings"
        :other-quarter="otherQuarter"
        class="account-base"
      />
      <diff-datas-card
        :account-id="account.account_id"
        :system-id="system.id"
        :account="account"
        :other-quarter-id="otherQuarter.id"
        style="margin-top: 3em;"
        class="account-base"
        @otherQuarterChange="handleOtherQuarterChange"
      />
      <all-datas-card
        :account-id="account.account_id"
        :system-id="system.id"
        style="margin-top: 3em;"
        class="account-base"
      />
    </div>
  </el-dialog>
</template>

<script>
import UserInfo      from '@/components/AccountDetail/UserInfo.vue'
import AllDatasCard  from '@/components/AccountDetail/AllDatasCard.vue'
import DiffDatasCard from '@/components/AccountDetail/DiffDatasCard.vue'

export default {
  components: {
    UserInfo,
    AllDatasCard,
    DiffDatasCard
  },
  props:      {
    value:   {
      type:     Boolean,
      required: true
    },
    account: {
      type:     Object,
      required: true
    },
    system:  {
      type:     Object,
      required: true
    },
    appendToBody: {
      type:     Boolean,
      default:  false
    }
  },
  data () {
    return {
      accountInfoSchema:    [],
      childVisible:         false,
      permission_types:     1,
      otherQuarter:         {},
      systemSettings:       {}
    }
  },
  computed: {
    quarter () {
      return this.$store.state.current_quarter
    },
    title () {
      return `${this.system.name}账号权限详情`
    }
  },
  watch:    {
    childVisible (val) {
      this.$store.commit('dialogVisibleChange', val)
    },
    value (val) {
      this.childVisible = val
    },
    'system.id' () {
      this.getPermissionTypes()
    }
  },
  mounted(){
    this.$EventBus.$on('account_info:change', (response) => {
      // 变更系统基线
      if (this.account.system_baseline !== null) {
        this.account.system_baseline.id = response.baseline_id
        this.account.system_baseline.name = response.baseline_name
      } else {
        // 新增系统基线
        const system_baseline = {
          id: response.baseline_id,
          name: response.baseline_name
        }
        this.$set(this.account, "system_baseline", system_baseline)
      }

    })
  },
  created () {
    this.getPermissionTypes()
  },
  methods: {
    getPermissionTypes () {
      if (!this.system.id) {
        console.log(this.system)
        console.log('system id is undefined. skip load setting...')
        return
      }

      this.$axios.get(`/api/systems/${this.system.id}/settings`)
        .then(response => {
          this.permission_types  = response.data.permission_types
          this.accountInfoSchema = response.data.account_info_schema
          this.systemSettings    = response.data.settings
        })
        .catch(() => {})
    },
    handleOtherQuarterChange (otherQuarter) {
      this.otherQuarter = otherQuarter
    }
  },
  unmounted() {
    this.$EventBus.$off('account_info:change')
  }
}
</script>
<style scoped>
.account-base {
  margin-bottom: 3em;
}
</style>
