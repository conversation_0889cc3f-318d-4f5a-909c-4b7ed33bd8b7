<template>
  <div>
    <el-tabs v-model="activeTab">
      <el-tab-pane
        :disabled="!publicAccountPermission"
        label="功能账号管理"
        name="public_account"
      >
        <system-public-accounts
          v-if="activeTab === 'public_account'"
          :system-id="Number(systemId)"
          :systems="systems"
        />
      </el-tab-pane>
      <el-tab-pane
        :disabled="!publicAccountTypePermission"
        label="账号类型管理"
        name="public_account_type"
      >
        <public-account-type
          v-if="activeTab === 'public_account_type'"
          ref="publicAccountType"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import SystemPublicAccounts from './SystemPublicAccounts.vue'
import PublicAccountType from './PublicAccountType.vue'

export default {
  components: {
    SystemPublicAccounts,
    PublicAccountType
  },
  props: {
    systemId: {
      type: Number,
      required: true
    },
    systems: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      activeTab: this.getActiveName()
    }
  },
  computed: {
    publicAccountPermission () {
      return this.$store.getters.hasPermission('public_accounts.query')
    },
    publicAccountTypePermission () {
      return this.$store.getters.hasPermission('public_account_type_manager.query')
    }
  },
  methods: {
    getActiveName () {
      if (this.$store.getters.hasPermission('public_accounts.query')) { return 'public_account' }
      if (this.$store.getters.hasPermission('public_account_type_manager.query')) { return 'public_account_type' }
      return ''
    }
  }
}
</script>
<style lang="scss" scoped>
  @import "~@/components/variables";

  .tool-bar{
    @include vertical_center_between;
    height: 40px;

    .right{
      @include vertical_center_right;
      height: 40px;
    }

    .tool{
      margin-left: 10px;
    }
  }
</style>
