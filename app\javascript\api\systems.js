import axios from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const list = () => {
  return axios.get('/api/systems')
}

export const inserviceSystems = () => {
  return axios.get('/api/systems/list_with_inservice')
}

export const all = () => {
  return axios.get('/api/systems/all')
}

export const maintainList = () => {
  return axios.get('/admin_api/ledgers/systems')
}

export const uploadType = () => {
  return axios.get('/api/systems/upload_type')
}

export const excelList = () => {
  return axios.get('/api/systems/excel_list')
}

export const autoImportType = () => {
  return axios.get('/api/systems/auto_import_type')
}

export const roleOutputDatas = (params) => {
  return axios.get(`/api/systems/${params.system_id}/roles/${params.role_id}/output_datas`)
}

export const roleOutputDiffDatas = (params) => {
  return axios.get(`/api/systems/${params.system_id}/roles/${params.role_id}/output_diff_datas`, {
    params: { other_quarter_id: params.other_quarter_id, diff_mode: params.diff_mode, other_role_id: params.other_role_id }
  })
}

export const roleSchema = (params) => {
  return axios.get(`/api/systems/${params.system_id}/output_role_schema`)
}

export const accountSchema = (params) => {
  return axios.get(`/api/systems/${params.system_id}/account_schema`)
}

export const update = (systemId, params) => {
  return axios.put(`/api/systems/${systemId}`, params)
}

export const categories = () => {
  return axios.get('/api/systems/categories')
}

export const enable = (systemId) => {
  return axios.post(`/api/systems/${systemId}/enable`)
}

export const disable = (systemId) => {
  return axios.post(`/api/systems/${systemId}/disable`)
}

export const exportAllSystemInfo = (systemIds) => {
  return axios.get(`/api/all_system_info/export`, { responseType: 'blob', params: {system_ids: systemIds} })
    .then(response => {
      const fileName = parseFileName(response, 'all_system_info_export.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch(() => {})
}

// // 导出角色excel
// export const exportRoleBatch = (systemId, params) => {
//   return axios.get(`/api/systems/${systemId}/export_role_batch`, { params: params, responseType: 'blob' }
//   )
//     .then(response => {
//       const fileName = parseFileName(response, 'roles.xlsx')
//       downloadBlob(response.data, fileName)
//     })
//     .catch((error) => {
//       throw error
//     })
// }

export const baselines = (params) => {
  return axios.get(`/api/systems/${params.system_id}/baselines/all`)
}
