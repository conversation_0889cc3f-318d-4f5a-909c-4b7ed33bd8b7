<template>
  <div>
    <el-divider content-position="left"><span>界面样式</span></el-divider>
    <el-form
      label-width="210px"
    >
      <el-form-item
        label="时间点样式"
      >
        <el-radio-group
          v-model="localQuarterMode"
        >
          <el-radio
            label="select"
            class="radio-item"
          >
            下拉菜单（名称）
          </el-radio>
          <el-radio
            label="select_time"
            class="radio-item"
          >
            下拉菜单（导入时间）
          </el-radio>
          <el-radio
            label="datepicker"
            class="radio-item"
          >
            日期选择
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <el-form
      label-width="210px"
    >
      <el-form-item
        v-model="localAccountDetail"
        label="权限对比默认显示"
      >
        <el-radio-group
          v-model="localAccountDetail.show_diff_baseline_default"
        >
          <el-radio
            :label="false"
            class="radio-item"
          >
            对比上个时间点
          </el-radio>
          <el-radio
            :label="true"
            class="radio-item"
          >
            对比系统基线
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <el-form
      label-width="210px"
    >
      <el-form-item
        v-model="localAllUsers"
        label="员工列表页账号显示"
      >
        <el-radio-group
          v-model="localAllUsers.show_systems"
        >
          <el-radio
            :label="false"
            class="radio-item"
          >
            显示账号列表
          </el-radio>
          <el-radio
            :label="true"
            class="radio-item"
          >
            显示账号统计
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <el-form
      label-width="210px"
    >
      <el-form-item
        v-model="localUserDetail"
        label="员工详情页系统没有账号显示"
      >
        <el-radio-group
          v-model="localUserDetail.disable_no_system_account"
        >
          <el-radio
            :label="false"
            class="radio-item"
          >
            显示
          </el-radio>
          <el-radio
            :label="true"
            class="radio-item"
          >
            不显示
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <el-divider
      content-position="left"
      class="divider-margin"
    >
      员工展示字段
    </el-divider>
    <el-table
      :data="localUserInfoColumns"
      size="small"
      style="width: 100%;margin-left:30px"
    >
      <el-table-column
        prop="label"
        label="key"
        width="130"
      >
        <template slot-scope="scope">
          {{ scope.row.prop.replace(/info./g, '') }}
        </template>
      </el-table-column>
      <el-table-column
        prop="label"
        label="名称"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.label"
            :placeholder="scope.row.label"
            style="width: 180px"
            size="small"
            @blur="validateInput(scope.row.label)"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="min_width"
        label="字段宽度"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.min_width"
            style="width: 180px"
            size="small"
            oninput="value=value.replace(/[^\d]/g,'')"
          >
            <template slot="append">px</template>
          </el-input>
        </template>
      </el-table-column>
      <el-table-column
        prop="show_in_user_page"
        width="150"
        label="员工页显示"
      >
        <template slot-scope="scope">
          <el-checkbox
            v-model="scope.row.show_in_user_page"
          >
            员工页显示
          </el-checkbox>
        </template>
      </el-table-column>
      <el-table-column
        prop="show_in_account_page"
        label="账号列表页显示"
        width="150"
      >
        <template slot-scope="scope">
          <el-checkbox
            v-model="scope.row.show_in_account_page"
          >
            账号列表页显示
          </el-checkbox>
        </template>
      </el-table-column>
      <el-table-column
        label="展示顺序调整"
      >
        <template slot-scope="scope">
          <el-button
            icon="el-icon-arrow-up"
            size="small"
            @click="changeInfoColumnOrder(scope.$index, 'up')"
          >
          </el-button>
          <el-button
            icon="el-icon-arrow-down"
            size="small"
            @click="changeInfoColumnOrder(scope.$index, 'down')"
          >
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-divider
      content-position="left"
      class="divider-margin"
    >
      安全设置
    </el-divider>
    <el-form
      label-width="180px"
    >
      <el-form-item
        label="监控接口 IP 访问限制:"
      >
        <el-radio-group
          v-model="localAppStatusAllowIp.enable"
        >
          <el-radio :label="true">开</el-radio>
          <el-radio :label="false">关</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="监控接口 IP 访问列表:">
        <el-select
          v-model="localAppStatusAllowIp.allow_ip"
          multiple
          filterable
          allow-create
          clearable
          :disabled="!localAppStatusAllowIp.enable"
          style="width:50%"
          default-first-option
          placeholder="请输入监控接口 IP 访问列表"
        >
          <el-option
            v-for="item in localAppStatusAllowIp.allow_ip"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <el-divider
      v-if="isHomeSetting"
      content-position="left"
      class="divider-margin"
    >
      首页账号状态变化统计天数设置
    </el-divider>
    <el-form
      label-width="180px"
      v-if="isHomeChangeAccount"
    >
      <el-form-item
        label="离职未禁用"
        class="status-form-item"
      >
        <el-switch
          v-model="localHomeSettings.change_account.alarm_dimission_enable"
          active-text="指定天数"
          inactive-text="全部历史">
        </el-switch>
        <el-input
          v-if="displayAlarmDimissionDay"
          v-model="localHomeSettings.change_account.alarm_dimission_day"
          size="small"
          placeholder="请填写天数"
          auto-complete="off"
          style="width: 250px; margin-left: 10px;"
        />
      </el-form-item>
      <el-form-item
        label="新增账号"
        class="status-form-item"
      >
        <el-switch
          v-model="localHomeSettings.change_account.add_enable"
          active-text="指定天数"
          inactive-text="全部历史">
        </el-switch>
        <el-input
          v-if="displayAddDay"
          v-model="localHomeSettings.change_account.add_day"
          size="small"
          placeholder="请填写天数"
          auto-complete="off"
          style="width: 250px; margin-left: 10px;"
        />
      </el-form-item>
      <el-form-item
        label="删除账号"
        class="status-form-item"
      >
        <el-switch
          v-model="localHomeSettings.change_account.delete_enable"
          active-text="指定天数"
          inactive-text="全部历史">
        </el-switch>
        <el-input
          v-if="displayDeleteDay"
          v-model="localHomeSettings.change_account.delete_day"
          size="small"
          placeholder="请填写天数"
          auto-complete="off"
          style="width: 250px; margin-left: 10px;"
        />
      </el-form-item>
      <el-form-item
        label="冻结账号"
        class="status-form-item"
      >
        <el-switch
          v-model="localHomeSettings.change_account.frozen_enable"
          active-text="指定天数"
          inactive-text="全部历史">
        </el-switch>
        <el-input
          v-if="displayFrozenDay"
          v-model="localHomeSettings.change_account.frozen_day"
          size="small"
          placeholder="请填写天数"
          auto-complete="off"
          style="width: 250px; margin-left: 10px;"
        />
      </el-form-item>
      <el-form-item
        label="注销账号"
        class="status-form-item"
      >
        <el-switch
          v-model="localHomeSettings.change_account.cancel_enable"
          active-text="指定天数"
          inactive-text="全部历史">
        </el-switch>
        <el-input
          v-if="displayCancelDay"
          v-model="localHomeSettings.change_account.cancel_day"
          size="small"
          placeholder="请填写天数"
          auto-complete="off"
          style="width: 250px; margin-left: 10px;"
        />
      </el-form-item>
    </el-form>
    <el-divider
      v-if="isGlobalAlertSetting"
      content-position="left"
      class="divider-margin"
    >
      告警中心默认搜索设置
      <span class='red'>【重新登录生效】</span>
    </el-divider>
    <el-form
      label-width="180px"
      v-if="isGlobalAlertSetting"
      :inline="true"
    >
      <el-form-item
        label="处理状态"
      >
        <el-select
          v-model="localGlobalAlertSettings.search.status"
          clearable
          placeholder="请选择处理状态"
        >
          <el-option
            label="全部"
            value=""
          />
          <el-option
            label="已处理"
            value="1"
          />
          <el-option
            label="未处理"
            value="0"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        label="恢复状态"
      >
        <el-select
          v-model="localGlobalAlertSettings.search.recover_status"
          clearable
          placeholder="请选择恢复状态"
        >
          <el-option
            label="全部"
            value=""
          />
          <el-option
            label="已恢复"
            value="1"
          />
          <el-option
            label="未恢复"
            value="0"
          />
          <el-option
            label="已关闭"
            value="2"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <el-divider/>
    <el-form
      label-width="30px"
    >
      <el-form-item>
        <el-button
          type="primary"
          size="small"
          @click="validateSubmit"
        >
          更新设置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data () {
    return {
      localQuarterMode: 'select',
      localHomeSettings: {
        change_account: {
          // alarm_dimission_enable: false,
          // alarm_dimission_day: null
        },
      },
      localGlobalAlertSettings: {
        search: {}
      },
      localAccountDetail: {
        show_diff_baseline_default: false
      },
      localAppStatusAllowIp: {
        enable: false,
        allow_ip: []
      },
      localAllUsers: { 
        show_systems: false
      },
      localUserDetail: {
        disable_no_system_account: false
      },
      localUserInfoColumns: [{
        prop: 'info.name',
        min_width: '200',
        label: '员工姓名',
        show_in_account_page: true,
        show_in_user_page: true
      }, {
        prop: 'info.code',
        min_width: '200',
        label: '员工工号',
        show_in_account_page: true,
        show_in_user_page: true
      }, {
        prop: 'info.login_name',
        min_width: '200',
        label: '登录名',
        show_in_account_page: false,
        show_in_user_page: false
      }, {
        prop: 'info.department',
        min_width: '200',
        label: '部门',
        show_in_account_page: true,
        show_in_user_page: true
      }, {
        prop: 'info.inservice',
        min_width: '200',
        label: '员工状态',
        show_in_account_page: true,
        show_in_user_page: true
      }, {
        prop: 'info.manager',
        min_width: '200',
        label: '审核人',
        show_in_account_page: true,
        show_in_user_page: true
      }, {
        prop: 'info.position',
        min_width: '200',
        label: '职务',
        show_in_account_page: true,
        show_in_user_page: true
      }]
    }
  },
  created () {
    this.getCurrentSettings()
  },
  computed: {
    isJobBaselineEnable () {
      return this.$settings.jobBaseline.enable
    },
    isHomeChangeAccount () {
      return this.$store.getters.hasPermission('home.change_account')
    },
    isHomeSetting () {
      return this.isHomeChangeAccount
    },
    isGlobalAlertSetting () {
      return this.$settings.globalAlert.enable
    },
    displayAlarmDimissionDay () {
      return this.localHomeSettings.change_account.alarm_dimission_enable
    },
    displayAddDay () {
      return this.localHomeSettings.change_account.add_enable
    },
    displayDeleteDay () {
      return this.localHomeSettings.change_account.delete_enable
    },
    displayFrozenDay () {
      return this.localHomeSettings.change_account.frozen_enable
    },
    displayCancelDay () {
      return this.localHomeSettings.change_account.cancel_enable
    }
  },
  methods: {
    // 列表中是否有岗位基线
    hasJobBaseline () {
      const columns = this.localUserInfoColumns.filter(x => x.prop === 'info.job_baseline_name')
      return columns.length > 0
    },
    // 如果岗位基线启动了，但是列表中没有岗位基线，则添加
    addJobBaseline () {
      const json = {
        prop: 'info.job_baseline_name',
        min_width: '200',
        label: '岗位基线',
        show_in_account_page: true,
        show_in_user_page: true
      }
      if (this.isJobBaselineEnable && !this.hasJobBaseline()) {
        this.localUserInfoColumns.push(json)
      }else if(!this.isJobBaselineEnable) {
        this.localUserInfoColumns = this.localUserInfoColumns.filter(x => x.prop.indexOf('job_baseline_name') < 0)
      }
    },
    getCurrentSettings () {
      this.loading = true
      this.$axios.get('admin_api/settings/general_settings')
        .then(response => {
          this.loading = false
          if (response.data.userInfoColumns && response.data.userInfoColumns.length > 0) {
            this.localUserInfoColumns  = response.data.userInfoColumns
          }
          if (response.data.allUsers) { this.localAllUsers = response.data.allUsers }
          if (response.data.userDetail) { this.localUserDetail = response.data.userDetail }
          if (response.data.accountDetail) { this.localAccountDetail = response.data.accountDetail }
          if (response.data.quarterMode) { this.localQuarterMode = response.data.quarterMode }
          if (response.data.appStatusAllowIp) { this.localAppStatusAllowIp = response.data.appStatusAllowIp }
          if (response.data.homeSettings) { this.localHomeSettings = response.data.homeSettings }
          if (response.data.globalAlertSettings) { this.localGlobalAlertSettings = response.data.globalAlertSettings }
          this.addJobBaseline()
        })
        .catch(() => {
          this.loading = false
        })
    },
    validateInput (val) {
      if (!val) { this.$message.error('请输入名称') }
    },
    validateSubmit () {
      if (this.localUserInfoColumns.filter(x => !x.label).length > 0) {
        this.$message.error('请输入员工展示字段名称')
      } else {
        this.onSubmit()
      }
    },
    onSubmit () {
      this.loading = true
      const params = {
        quarterMode:         this.localQuarterMode,
        accountDetail:       this.localAccountDetail,
        appStatusAllowIp:    this.localAppStatusAllowIp,
        userInfoColumns:     this.localUserInfoColumns,
        homeSettings:        this.localHomeSettings,
        globalAlertSettings: this.localGlobalAlertSettings,
        allUsers:            this.localAllUsers,
        userDetail:          this.localUserDetail
      }
      this.$axios.put('admin_api/settings/general_settings', params)
        .then(response => {
          this.loading = false
          this.$message.success('设置已更新，重新刷新页面后生效')
          this.getCurrentSettings()
        })
        .catch(() => {
          this.loading = false
        })
    },
    changeInfoColumnOrder (index, orderType) {
      let localUserInfoColumns = [...this.localUserInfoColumns]
      if(orderType === 'up') {
        if(index > 0) {
          const element = localUserInfoColumns.splice(index, 1)[0]
          localUserInfoColumns.splice(index - 1, 0, element)
          this.localUserInfoColumns = localUserInfoColumns
        }
      } else {
        if (index < localUserInfoColumns.length - 1) {
          const element = localUserInfoColumns.splice(index, 1)[0]
          localUserInfoColumns.splice(index + 1, 0, element)
          this.localUserInfoColumns = localUserInfoColumns
        }
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.radio-item{
  margin-left: 20px;
  width: 120px;
}
.divider-margin{
  margin-top: 50px;
}
.red {
  color: #f56c6c;
  font-size: 12px;
}
// 修复点击后页面抖动问题
.status-form-item{
  height: 41px;
}
</style>
