# frozen_string_literal: true

# 系统后台账号（管理员）控制器
class AdminAccountsController < ApplicationController
  before_action :authenticate_admin!, except: [:forget_password]
  before_action :authenticate_policy!, except: %i[admin_systems_in_query selections forget_password]

  prepend_before_action :set_admin, except: %i[index create sync_from_domain selections forget_password admins_permission]
  before_action :verify_update_roles, only: :update_roles
  before_action :verify_update_all_admin_permissions, :verify_update_department_admin_permissions, :verify_update_system_admin_permissions, only: :update_admin_permissions
  before_action :verify_user_id, only: %i[create update]

  def selections
    json_respond Admin.enabled.all.map(&:output_simple)
  end

  # 只在后台管理用到
  def index
    json_respond Admin.includes(:user).all.map(&:output)
  end

  # 管理员的用户管理权限
  # 只展示部门权限的管理员和当前管理员（无限制权限除外）
  def admins_permission
    data = current_admin.admins_in_query.map(&:output)
    json_respond data
  end

  # 从 Windows 域中同步员工列表
  def sync_from_domain
    load "#{Rails.root}/lib/tasks/ldap_rakes.rb"
    LdapRakes.import
    json_respond(success: true)
  end

  # 管理员的角色
  def admin_roles
    json_respond @admin.roles.enabled
  end

  # 后台账号编辑业务系统绑定账号使用
  def admin_systems_in_query
    json_respond @admin.business_systems_in_query.map(&:output)
  end

  # 管理员的系统权限
  def admin_systems_permission
    data = @admin.systems_permissions_output
    add_admin_system_permission(data)
    json_respond data
  end

  # 管理员部门权限
  def admin_departments_permission
    data = @admin.departments_permission_output
    add_admin_department_permission(data)
    json_respond data
  end

  # 创建管理员
  def create
    @admin = Admin.new(admin_create_params)

    if @admin.save
      audit_log! @admin
      json_respond @admin.output
    else
      json_custom_respond(400, error_message: @admin.errors.full_messages.join('; '))
    end
  end

  # 更新管理员
  def update
    audit_params_struct    = Struct.new(:old_admin, :new_admin)
    audit_params           = audit_params_struct.new
    audit_params.old_admin = @admin.dup

    if @admin.update(admin_update_params)
      audit_params.new_admin = @admin
      audit_log! audit_params
      json_respond @admin.output
    else
      json_custom_respond(:unprocessable_entity, error_message: @admin.errors.full_messages.join('; '))
    end
  end

  # 禁用管理员
  def disable
    audit_log! @admin

    @admin.disable!
    json_respond(success: true)
  end

  # 启用管理员
  def enable
    audit_log! @admin

    @admin.enable!
    json_respond(success: true)
  end

  # def destroy
  #   @admin.destroy
  #   audit_log! @admin
  #   json_respond_no_content
  # end

  # 解锁管理员
  def unlock
    if @admin.unlock_access!
      audit_log! @admin
      json_respond(success: true)
    else
      json_custom_respond(456, error_message: 'unlock failed')
    end
  end

  # 重置管理员密码
  def change_password
    password              = params[:password]
    password_confirmation = params[:password_confirmation]

    return json_custom_respond(456, error_message: '两次输入的密码不同') if password != password_confirmation

    @admin.password              = password
    @admin.password_confirmation = password_confirmation
    @admin.need_change_password!
    if @admin.save
      audit_log! @admin
      json_respond(success: true)
    else
      json_custom_respond(456, error_message: @admin.errors.full_messages.join('; '))
    end
  end

  # 更新管理员角色
  def update_roles
    @admin.roles = AppRole.where(id: update_roles_params[:role_ids])
    audit_log! @admin
    json_respond(success: true)
    json_respond_no_content
  end

  # 更新管理员部门权限
  def update_admin_permissions
    @admin.update_admin_systems_permissions(systems_permissions_params)
    @admin.update_admin_departments_permissions(departments_permissions_params)
    audit_log! [@admin, departments_permissions_params[:all_departments_query]]
    json_respond_no_content
  end

  def set_operator_permission
    @admin.bind_operator_permission(operator_permission_params[:operator_options][:domains])
    audit_log! @admin
    json_respond_no_content
  rescue ActAsAdminPermission::NotFoundAccountCode => e
    json_custom_respond 456, error_message: "#{e.message}: #{I18n.t 'errors.admin_role.not_found_account_code'}"
  rescue ActAsAdminPermission::NotMatchAccountCode => e
    json_custom_respond 456, error_message: "#{e.message}: #{I18n.t 'errors.admin_role.not_match_account_code'}"
  end

  def get_operator_permission
    json_respond @admin.admin_special_permission&.output&.[](:options)
  end

  def check_operator
    @admin.check_operator_option(params[:operator_option])
    json_respond(success: true)
  rescue ActAsAdminPermission::NeedChooseASystem => e
    json_respond(success: false, message: I18n.t('errors.admin_role.need_choose_a_system'))
  rescue ActAsAdminPermission::NotFoundAccountCode => e
    json_respond(success: false, message: I18n.t('errors.admin_role.not_found_account_code'))
  rescue ActAsAdminPermission::NotMatchAccountCode => e
    json_respond(success: false, message: I18n.t('errors.admin_role.not_match_account_code'))
  end

  def forget_password
    auth_method = Setting.frontendSettings&.[]('authMethod') || 'local'
    if auth_method == 'local'
      admin = Admin.find_by(email: params[:login_code])
    else
      admin = Admin.find_by(code: params[:login_code])
    end
    if admin.present?
      SendResetPasswordInstructionsJob.perform_later(admin.id)
      json_respond(success: true, message: '邮件发送成功')
    else
      json_respond(success: false, message: I18n.t('devise_token_auth.passwords.user_not_found', email: params[:login_code]))
    end
  end

  private

  def authenticate_policy!
    authorize Admin
  end

  def operator_permission_params
    params.require(:admin_account).permit(:id, operator_options: {})
  end

  def set_admin
    @admin = Admin.find(params[:id])
  rescue StandardError => e
    json_custom_respond(404, error_message: e.message)
  end

  def update_roles_params
    params.permit(:id, role_ids: [])
  end

  def systems_permissions_params
    a_system_permission_params = [
      :id,
      permission: {}
    ]
    params[:system_data].permit(:id, :all_systems_query, :all_systems_maintain, systems_permissions: a_system_permission_params, systems_category_permissions: a_system_permission_params)
  end

  def departments_permissions_params
    params[:department_data].permit(:all_departments_query, :id, department_ids: [])
  end

  def admin_create_params
    params.require(:admin).permit(
      :name,
      :email,
      :code,
      :mobile,
      :user_id,
      :password,
      :password_confirmation,
      :allow_notification,
      :allow_sms_notification
    )
  end

  def admin_update_params
    params.require(:admin).permit(:name, :email, :code, :mobile, :allow_notification,
                                  :allow_sms_notification, :user_id, :default_home_page)
  end

  def role_params
    a_role_params = [
      :id,
      :name,
      :allow_all_systems,
      :allow_all_departments,
      :user_id,
      option:     {},
      system_ids: [],
      scope:      []
    ]
    params.permit(roles: a_role_params)
  end

  def is_all_permission?
    current_admin.permission? 'admin_account_manager.all_permission'
  end

  # 添加当前用户是否有系统查询和操作权限
  def add_admin_system_permission(data)
    if is_all_permission?
      data[:is_all_systems_query] = true
      data[:is_all_systems_maintain] = true
    else
      output = current_admin.systems_permissions_output
      data[:is_all_systems_query] = output[:all_systems_query]
      data[:is_all_systems_maintain] = output[:all_systems_maintain]
    end
  end

  # 添加当前用户是否有所有部门读取权限，用于用户设置部门权限
  def add_admin_department_permission(data)
    if is_all_permission?
      data[:is_all_departments_query] = true
    else
      output = current_admin.departments_permission_output
      data[:is_all_departments_query] = output[:all_departments_query]
    end
  end

  # 后台校验角色权限，避免用户更新不允许提交的角色
  def verify_update_roles
    return if is_all_permission?

    # 获取新增的角色
    new_role_ids = update_roles_params[:role_ids] - @admin.role_ids
    # 获取无权限的角色
    no_permission_role_ids = (new_role_ids - current_admin.role_ids)
    return if no_permission_role_ids.blank?

    roles = AppRole.where(id: no_permission_role_ids)
    json_custom_respond(456, error_message: "您无权限授权角色：#{roles.map(&:name).join('、')}")
  end

  # 后台校验系统、部门权限 所有权限
  def verify_update_all_admin_permissions
    return if is_all_permission?

    all_departments_query = current_admin.admin_special_permission&.all_departments_query
    all_systems_query = current_admin.admin_special_permission&.all_systems_query
    all_systems_maintain = current_admin.admin_special_permission&.all_systems_maintain
    param_all_departments_query = departments_permissions_params[:all_departments_query]
    param_all_systems_query = systems_permissions_params[:all_systems_query]
    param_all_systems_maintain = systems_permissions_params[:all_systems_maintain]
    # 如果设置启用所有部门权限 并且 当前管理员 没有该权限限时 报错
    is_no_department = param_all_departments_query && !@admin.admin_special_permission&.all_departments_query && !all_departments_query
    is_no_system_query = param_all_systems_query && !@admin.admin_special_permission&.all_systems_query && !all_systems_query
    is_no_system_maintain = param_all_systems_maintain && !@admin.admin_special_permission&.all_systems_maintain && !all_systems_maintain
    return json_custom_respond(456, error_message: '您无权限授启用「所有部门权限」') if is_no_department
    return json_custom_respond(456, error_message: '您无权限授启用「所有系统查询」') if is_no_system_query
    json_custom_respond(456, error_message: '您无权限授启用「所有系统操作」') if is_no_system_maintain
  end

  # 后台校验部门详细权限
  def verify_update_department_admin_permissions
    return if is_all_permission?
    return if current_admin.admin_special_permission&.all_departments_query

    # 获取新增的部门
    new_department_ids = departments_permissions_params[:department_ids] - @admin.department_ids
    # 获取无权限的部门
    no_permission_department_ids = (new_department_ids - current_admin.department_ids)
    return if no_permission_department_ids.blank?

    departments = Department.where(id: no_permission_department_ids)
    json_custom_respond(456, error_message: "您无权限授权部门：#{departments.map(&:name).join('、')}")
  end

  # 后台校验系统详细权限
  def verify_update_system_admin_permissions
    return if is_all_permission?

    all_systems_query = current_admin.admin_special_permission&.all_systems_query
    all_systems_maintain = current_admin.admin_special_permission&.all_systems_maintain
    data = []
    admin_system_permissions = @admin.admin_system_permissions
    current_admin_system_permissions = current_admin.admin_system_permissions
    new_system_permissions = systems_permissions_params[:systems_permissions].select { |x| x['permission']['query'] || x['permission']['maintain'] }
    new_system_permissions.each do |x|
      current_permission = current_admin_system_permissions.find { |n| n.business_system_id == x['id'] }
      admin_system_permission = admin_system_permissions.find { |n| n.business_system_id == x['id'] }
      new_query = x['permission']['query']
      new_maintain = x['permission']['maintain']
      if current_permission
        # 如果新增查询权限，但是当前管理员没有查询权限，并且没有全部查询权限，报错(注意是新增，之前如果就是已经有查询权限，跳过)
        option_data = []
        option_data << '查询' if new_query && !admin_system_permission&.query && !(current_permission.query || all_systems_query)
        option_data << '操作' if new_maintain && !admin_system_permission&.maintain && !(current_permission.maintain || all_systems_maintain)
        data << [x['id'], option_data] if option_data.present?
      else
        # 如果选中了查询并且查询是之前就有的权限，如果选中了操作并且操作是之前就有的权限，那么next
        next if admin_system_permission && (!new_query || admin_system_permission.query == new_query) && (!new_maintain || admin_system_permission.maintain == new_maintain)

        option_data = []
        # 如果是勾选了查询 并且 没有全部查询功能，报错
        option_data << '查询' if new_query && !all_systems_query
        option_data << '操作' if new_maintain && !all_systems_maintain
        data << [x['id'], option_data] if option_data.present?
      end
    end
    return if data.blank?

    msgs = data.map do |x|
      bs = BusinessSystem.find(x[0])
      "「#{bs.name} #{x[1].join('、')}」"
    end
    error_message = "您无权限授权：#{msgs.join('、')}"
    json_custom_respond(456, error_message: error_message)
  end

  # 如果开启了强制绑定员工，创建员工校验是否绑定员工
  # 拥有'无限制权限'可以不绑定
  def verify_user_id
    msg = '需要绑定员工'
    return unless Setting.admin_bind_user&.[]('enable')

    is_all_permission = current_admin.permission? 'admin_account_manager.all_permission'
    return if is_all_permission
    return json_custom_respond(400, error_message: msg) if params[:action] == 'create' && admin_create_params[:user_id].blank?
    return json_custom_respond(400, error_message: msg) if params[:action] == 'update' && admin_update_params[:user_id].blank?
  end
end
