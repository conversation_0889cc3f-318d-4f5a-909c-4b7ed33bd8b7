# frozen_string_literal: true

# 系统账号职务规则增删改查日志
class PositionRoleCheckAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = I18n.t('position_role_check.module_title')
    super
  end

  def create_role_rule
    role_rule('创建', params)
  end

  def create_fail
    set_account_category
    @operation = "创建规则"
    @comment   = "创建规则失败：「#{params}」"
    create_audit_log
  end

  def edit_role_rule
    role_rule('编辑', params)
  end

  def update_fail
    set_account_category
    @operation = "编辑规则"
    @comment   = "编辑规则失败：「#{params}」"
    create_audit_log
  end

  def delete_role_rule
    role_rule('删除', params)
  end

  def delete_fail
    set_account_category
    @operation = "删除规则"
    @comment   = "删除规则失败：「#{params}」"
    create_audit_log
  end

  private

  def set_account_category
    @operation_category = I18n.t('position_role_check.system_account_rule_manager')
  end

  def role_rule(action_name, params)
    position = params.is_a?(Array) ? params.first['position'] : params[:position]
    if params.is_a?(Array)
      domains = params
    else
      domains = params[:domains]
    end
    set_account_category
    @operation = "#{action_name}规则"
    @comment   = "#{action_name}了规则：「职务：#{position} #{bundle_comment(domains)}」"
    create_audit_log
  end

  def bundle_comment(params)
    text = ""

    systems = BusinessSystem.inservice
    params.each do |role_rule|
      the_roles = roles(role_rule[:role_code] || role_rule["role_code"])
      the_system = systems.find {|system| system.id == role_rule[:business_system_id] || role_rule["business_system_id"]}
      text += "；系统：#{the_system.name}, 角色：#{the_roles}"
    end

    text
  end

  def roles(role_codes)
    role_codes.map do |role_code|
      role_code.split(',').last
    end.join(', ')
  end
end
