class GlobalAlertMessageController < ApplicationController
  
  def update
    alert_message = GlobalAlertMessage.find params[:id]

    if alert_message.update(alert_message_update_params)
      json_respond alert_message.full_output
    else
      json_custom_respond(456, error_message: '更新失败')
    end
  end

  private

  def alert_message_update_params
    params.require(:global_alert_message).permit(:status, :context)
  end

end
