import axios from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const conflictUsers = (params) => {
  return axios.get('/api/system_role_conflicts/conflicts_users', { params: params })
}

export const createConflictUsers = (params) => {
  return axios.get('/api/system_role_conflicts/create_conflicts_users', { params: params })
}

export const conflicts = (params) => {
  return axios.get('/api/system_role_conflicts', { params: params })
}

export const systemRoles = () => {
  return axios.get('/api/systems/systems_with_roles')
}

export const systemAccounts = () => {
  return axios.get('/api/systems/systems_with_accounts')
}

export const create = (params) => {
  return axios.post('/api/system_role_conflicts', params)
}

export const destroy = (params) => {
  return axios.delete(`/api/system_role_conflicts/${params.conflict_id}`)
}

export const exportRules = () => {
  return axios.get(`/api/system_role_conflicts/export`,
    { responseType: 'blob' })
    .then(response => {
      const fileName = parseFileName(response, 'conflicts.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
