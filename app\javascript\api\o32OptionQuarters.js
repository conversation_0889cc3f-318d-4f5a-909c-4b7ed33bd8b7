import axios from '@/settings/axios'

// 不分页
export const list = (params = {}) => {
  return axios.get('/api/o32_option_quarters/list', { params: params })
}

// 分页
export const index = (params = {}) => {
  return axios.get('/api/o32_option_quarters', { params: params })
}

// 分页
export const newList = (headers = {}) => {
  return axios.get('/api/o32_option_quarters', { headers: headers, params: { filter: { status_eq: 1 } } })
}

export const destroy = (id) => {
  return axios.delete(`/api/o32_option_quarters/${id}`, {})
}

export const create = (params = {}) => {
  return axios.post('/api/o32_option_quarters', params)
}