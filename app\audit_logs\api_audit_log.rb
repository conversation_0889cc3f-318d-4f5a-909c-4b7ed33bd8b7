# frozen_string_literal: true

class ApiAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '数据功能'
    @operation_category = '数据导出'
    super
  end

  def generate_all_export_data_success
    @operation = '全部员工权限导出'
    @comment   = "成功导出「#{quarter_name(params[:quarter_id])} -- #{quarter_name(params[:other_quarter_id])}」的 「全部员工权限导出」"
    create_audit_log
  end

  def generate_all_export_data_faild
    @operation = '全部员工权限导出'
    @comment   = "导出「#{quarter_name(params[:quarter_id])} -- #{quarter_name(params[:other_quarter_id])}」的 「全部员工权限导出」失败"
    create_audit_log
  end

  def audit_report_success
    @operation    = '审计报告导出'
    @download_url = params[:download_url]
    @job_id       = params[:job_id]
    @comment = "成功导出「#{params[:system_names].join(',')}」「#{params[:date_range]&.first} -- #{params[:date_range]&.last}」的「审计报告」数据"
    create_audit_log
  end

  def audit_report_faild
    @operation = '审计报告导出'
    @comment   = "导出「#{params[:system_names].join(',')}」「#{params[:date_range]&.first} -- #{params[:date_range]&.last}」的「审计报告」失败"
    create_audit_log
  end

  private

  def quarter_name(quarter_id)
    Quarter.find_by(id: quarter_id)&.name
  end
end
