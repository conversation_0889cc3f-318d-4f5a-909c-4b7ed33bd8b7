class ReportAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '产品报表发送规则监测'
    super
  end

  def update
    set_account_category
    @operation = '报表设置'
    @comment   = "更新了报表 ：报表名称：#{params[0]}，是否敏感：#{params[1][:is_sensitive]}，基金产品：#{params[1][:funds]}，状态：#{params[1][:status]}，组合：#{params[1][:fund_code]}"
    create_audit_log
  end

  private

  def set_account_category
    @operation_category = '报表设置'
  end
end
