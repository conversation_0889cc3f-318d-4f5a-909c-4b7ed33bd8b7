import axios from '@/settings/axios'
import Token from '@/settings/token.js'

export const isBlank = (str) => {
  return str === undefined || str === null || JSON.stringify(str) === '{}' || (typeof(str) === 'string' && str.trim() === '') || (Array.isArray(str) && str.length === 0)
}

// 下载文件
export const download = (url, fileName, params = {}) => {
  return axios
    .get(url, { params: params, responseType: 'blob' })
    .then(response => {
      downloadBlob(response.data, fileName)
    })
    .catch(error => {
      console.error(error)
    })
}

export const downloadBlob = (blob, fileName) => {
  const url = window.URL.createObjectURL(new Blob([blob]))
  const link = document.createElement('a')
  link.href = url
  link.setAttribute('download', fileName)
  document.body.appendChild(link)
  link.click()
  link.remove()
}

export const parseFileName = (response, defaultFileName = '') => {
  const contentDisposition = response.headers['content-disposition']
  let fileName = defaultFileName

  if (contentDisposition) {
    const fileNameMatch = contentDisposition.match(/filename="(.+)"/)
    fileName = decodeURI(fileNameMatch[1])
  }
  return fileName
}

// 对上传、新窗口下载等
export const absoluteUrlFor = url => {
  let baseURL = axios.defaults.baseURL
  // 去掉最后一个 '/'
  const lastChar = baseURL.charAt(baseURL.length - 1)
  if (lastChar === '/') {
    baseURL = baseURL.substring(0, baseURL.length - 1)
  }
  return `${baseURL}${url}`
}

// 返回token相关，用于前端
export const getToken = () => {
  const token = Token.fetch()
  const headers = {}
  headers['access-token'] = token['access-token']
  headers['token-type'] = token['token-type']
  headers.client = token.client
  headers.expiry = token.expiry
  headers.uid = token.uid
  return headers
}

export const isLogin = () => {
  const token = getToken()
  return token['access-token'] !== ''
}
