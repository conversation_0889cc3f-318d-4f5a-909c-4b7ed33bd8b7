# frozen_string_literal: true

# 员工信息管理
class QuarterComparedAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '权限差异数据'
    super
  end

  def create
    operation_category
    @operation = '权限差异数据生成'
    @comment   = "创建了「#{params[0]}」「#{params[1]}」-「#{params[2]}」的差异数据"
    create_audit_log
  end

  def destroy
    operation_category
    @operation = '权限差异数据删除'
    @comment   = "删除了「#{params[0]}」「#{params[1]}」-「#{params[2]}」的差异数据"
    create_audit_log
  end

  def excel_difference_export_success
    operation_category
    @operation = '权限差异数据导出'
    @comment   = "导出了「#{params[:system_name]},#{params[:quarter_name]}」的「差异数据」"
    create_audit_log
  end

  def excel_difference_export_faild
    operation_category
    @operation = '权限差异数据导出'
    @comment   = "导出「#{params[:system_name]}, #{params[:quarter_name]}」的「差异数据」失败"
    create_audit_log
  end

  def history_excel_difference_export_success
    operation_category
    @operation = '权限历史差异数据导出'
    @comment   = "导出了「#{params[:system_name]}, #{params[:start_quarter_name]} - #{params[:end_quarter_name]}」的「历史差异数据」"
    create_audit_log
  end

  def history_excel_difference_export_faild
    operation_category
    @operation = '权限历史差异数据导出'
    @comment   = "导出「#{params[:system_name]},#{params[:start_quarter_name]} - #{params[:end_quarter_name]}」的「历史差异数据」失败"
    create_audit_log
  end

  private

  def operation_category
    @operation_category = '差异数据管理'
  end
end
