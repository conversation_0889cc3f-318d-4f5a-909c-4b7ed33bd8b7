# frozen_string_literal: true

# 系统账号相关 api 的控制器
class SystemAccountsController < ApplicationController
  before_action :authenticate_admin!, except: %i[word_export excel_export]
  before_action :authenticate_policy!,
                except: %i[output_account account_output_schema accounts detail search_accounts]
  before_action :set_business_system
  before_action :set_system_account,
                except: %i[account_output_schema accounts output_account search_accounts]

  def accounts
    quarter_id = params[:quarter_id] || @business_system.quarters_after_import.last.id
    json_respond @business_system.account_class.where(quarter_id: quarter_id).pluck(:code, :name).uniq
  end

  def diff_roles_with_other_quarter
    other_quarter_id = params[:other_quarter_id].to_i

    json_respond diff: @account.diff_roles_with_other_quarter(other_quarter_id), schema: @account.roles_schema
  end

  # 同一时间点，同一系统不同账号的角色差异数据
  def diff_roles_with_other_account
    other_account_code = params[:other_account_code].to_s.blank? ? @account.code : params[:other_account_code].to_s

    json_respond diff: @account.diff_roles_with_other_account(other_account_code), schema: @account.roles_schema
  end

  # 同一账号，同一系统不同时间点的差异数据
  def account_output_diff_datas
    other_quarter_id = params[:other_quarter_id].to_i

    schema = BusinessSystem.compat_with_old_schema(@business_system.account_class.output_schema)
    datas  = BusinessSystem.compat_with_old_diff_data(@account.diff_datas_between_two_quarters(other_quarter_id))

    json_respond diff_datas: datas, output_schema: schema
  end

  # 同一时间点，同一系统不同账号的权限差异数据
  def account_output_diff_datas_compare_with_other_account
    other_account_code = params[:other_account_code].to_s.blank? ? @account.code : params[:other_account_code].to_s

    schemas = BusinessSystem.compat_with_old_schema(@business_system.account_class.output_schema)
    datas = BusinessSystem.compat_with_old_diff_data(@account.compare_with_other_account(other_account_code, schemas))

    json_respond diff_datas: datas, output_schema: schemas
  end

  def account_output_all_datas
    all_datas = BusinessSystem.compat_with_old_data(@account.display_output_all_datas)
    all_datas[:role] = @account.output_role_data if params[:with_role].to_s == 'true' && @account.the_system.role_class
    json_respond all_datas
  end

  def account_output_schema
    schema = BusinessSystem.compat_with_old_schema(@business_system.account_class.output_schema)
    json_respond schema
  end

  def word_export
    send_file_compatible_with_msie(@account.export_word, not_found_file_i18n_key)
    audit_log! ({ account: @account, content: '审核表' }), action: :word_export_success
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! ({ account: @account, content: '审核表' }), action: :word_export_faild
    json_custom_respond(500, error_message: e.message)
  end

  def excel_export
    other_quarter_id = params[:otherQuarterId].to_i
    send_file_compatible_with_msie(@account.export_excel(other_quarter_id), not_found_file_i18n_key)
    audit_log! ({ account: @account, content: '权限表' }), action: :excel_export_success
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! ({ account: @account, content: '权限表' }), action: :excel_export_faild
    json_custom_respond(500, error_message: e.message)
  end

  def account_history
    current_page = params[:page] || 1
    per_page = params[:per_page] || 15
    account_history_list = AccountChangeHistory.where(account_code: @account.code, business_system_id: @business_system.id)
                             .order(quarter_id: :desc)
    history = account_history_list.page(current_page).per(per_page)
    json_respond name:                @account.name,
                 history:             history.map(&:output),
                 history_task_status: QuarterTaskStatus.history_task_status,
                 last_quarter:        Quarter.last.name,
                 total_count:         history.total_count
  end

  def output_account
    options = params[:account_id].present? ? { id: params[:account_id] } : { code: params[:other_account_code] }
    account = @business_system.account_class.find_by(options)
    json_respond account_info: account.system_summary_base_info
  end

  # 账户详情，用于 账号状态变化统计 dialog详情按钮
  def detail
    json_respond data: @account.system_summary_with_user_info
  end

  private

  def authenticate_policy!
    authorize nil, policy_class: SystemAccountPolicy
  end

  def not_found_file_i18n_key
    'errors.export.account_file_not_found'
  end
end
