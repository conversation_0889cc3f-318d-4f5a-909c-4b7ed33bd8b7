<template>
  <el-card
    :class="elCardClass"
  >
    <div
      @click="changeCategory"
      style='cursor: pointer;'
    >
      <alert-item :alert="alert" />
    </div>
  </el-card>
</template>

<script>
import AlertItem      from '@/components/AdminGlobalAlert/AlertItem.vue'
import AlertItemMixin from '@/components/AdminGlobalAlert/mixins/AlertItemMixin'
export default {
  components: {
    AlertItem
  },
  mixins: [AlertItemMixin],
  props: {
    alert: {
      type:     Object,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    },
    biggerStyle: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    changeCategory () {
      this.$emit('changeCategory', this.alert)
    }
  },
  computed: {
    isFinishStyle () {
      return this.alertPercent(this.alert) === 100
    },
    elCardClass () {
      const customerId = this.$settings.customerId
      let clazz = 'alert-card'
      if (this.isActive) {
        clazz += ' active'
      }
      if (this.biggerStyle) {
        clazz += ' alert-card-large'
      }
      if (this.isFinishStyle) {
        clazz += ' finish-alert-card'
      }
      if (customerId === 'sldfund') {
        clazz += ' sldfund-alert-card'
      }
      return clazz
    }
  }
}
</script>

<style lang="scss">
.alert-card-large{
  width: calc((100vw - 100px) / 3)  !important;
}
.el-card.alert-card{
  width: 340px;
  margin-top: 0;
  margin-bottom: 15px;
  .el-card__body{
    padding: 14px ;
  }
}
.alert-card.active, .alert-container.active {
  border: 1px solid #12D4FF;
}

.sldfund-alert-card.active {
  border: 1px solid #e13131 !important;
}

.finish-alert-card.active {
  border: 1px solid #0e6eff !important;
}

</style>
