<template>
  <el-dialog
    :visible.sync="visible"
    :title="`${modeTitle}岗位基线`"
    width="980px"
    @open="handleOpen"
  >
    <div class="dialog-container">
      <el-form
        ref="localForm"
        v-loading.fullscreen.lock="loading"
        element-loading-text="加载中"
        element-loading-spinner="el-icon-loading"
        :model="params"
        label-width="120px"
      >
        <el-form-item
          prop="name"
          label="岗位名称"
        >
          {{ job.name }}
        </el-form-item>

        <el-form-item
          v-for="(system, index) in systemBaselines"
          :key="index"
        >
          <el-select
            v-model="system.id"
            :loading="loadingSystems"
            filterable
            clearable
            placeholder="请选择系统"
            style="width: 238px"
            @change="getSystemBaselines(system.id)"
          >
            <el-option
              v-for="item in filterAllBusinessSystems"
              :key="item.id"
              :value="item.id"
              :label="item.name"
              :disabled="item.is_disable"
            />
          </el-select>

          <el-select
            v-model="system.system_baseline_id"
            :loading="loadingSystems"
            filterable
            clearable
            placeholder="请选择系统基线"
            style="width: 238px"
            :disabled="system.ban"
          >
            <el-option
              v-for="item in system.system_baselines"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            />
          </el-select>
          <el-button
            icon="el-icon-minus"
            circle
            size="small"
            style="margin-left: 15px;"
            @click="deleteSystemAndBaseline(index)"
          />
          <el-button
            v-if="displaySystemBaselines(system.id) && !system.ban"
            size="mini"
            style="margin-left: 10px;"
            :disabled="system.ban"
            @click="openSystemBaselines(system)"
          >
            系统基线管理
          </el-button>
          <el-checkbox
            v-if="displaySystemBaselines(system.id)"
            v-model="system.ban"
            style="margin-left: 10px;"
            @change="handleSystemBaseline(system)"
          >
            该岗位禁止存在账号
          </el-checkbox>
        </el-form-item>

        <el-form-item
          prop=""
          label=""
        >
          <el-button
            icon="el-icon-plus"
            circle
            size="small"
            @click="addSystemAndBaseline"
          />
        </el-form-item>
      </el-form>
    </div>

    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="visible = false">关 闭</el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
      >
        确 定
      </el-button>
    </span>
    <system-baselines-dialog
      ref="systemBaselines"
      :system="system"
      @updateSystemBaselines="updateSystemBaselines"
    />
  </el-dialog>
</template>

<script>
import API                   from '@/api'
import SystemBaselinesDialog from './SystemBaselinesDialog.vue'
export default {
  components: {
    SystemBaselinesDialog
  },
  props:      {
    job: {
      type:    Object,
      default: () => { return {} }
    },
    mode:     {
      type:    String,
      default: 'update'
    }
  },
  data () {
    return {
      visible:         false,
      loadingSystems:  false,
      loading:         true, // 默人加载中，需要确保所有系统选项加载完，才能展示
      systemBaselines: [],
      allBusinessSystems: [],
      baselineDisabled: false,
      params: {
        id:     null
      },
      system: {},
      cascaderProps: {
        emitPath: false
      }
    }
  },
  computed:   {
    modeTitle () {
      return '设置'
    },
    // 将已选择的系统置为disable,添加is_disable参数
    filterAllBusinessSystems () {
      var allBusinessSystems = this.$lodash.clone(this.allBusinessSystems)
      var selectedSystemIds  = this.selectedSystemIds()
      var systems = allBusinessSystems.map(x => {
        x.is_disable = selectedSystemIds.indexOf(x.id) >= 0
        return x
      })
      return systems
    }
  },
  watch:      {
    job () {
      this.initJobBaseline()
      this.getJobBaselineSystemBaselines()
    }
  },
  created () {
    this.initAllBuisineSystems()
  },
  methods:    {
    // 打开dialog时重新获取岗位基线数据
    handleOpen () {
      this.initJobBaseline()
      this.getJobBaselineSystemBaselines()
    },
    // 获取岗位基线
    initJobBaseline () {
      var id = this.job.id
      if (API.tool.isBlank(id)) {
        this.loading = false
        return
      }
      this.loadingSystems = true
      API.jobBaselines.detail(id)
        .then(response => {
          this.loading = false
          this.loadingSystems = false
          this.params = response.data
        })
        .catch(() => {
          this.loadingSystems = false
        })
    },
    updateForm () {
      var params = { job_baseline: this.params, system_baselines: this.systemBaselines }
      this.loading = true
      API.jobBaselines.update(this.params.id, params)
        .then(response => {
          this.loading   = false
          if (response.data.success) {
            this.$emit('change')
            this.visible = false
            this.$refs.localForm.resetFields()
            this.$message.success(`${this.modeTitle}岗位基线成功`)
          } else {
            this.$message.error(response.data.error_message)
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    updateConfirmForm () {
      this.$confirm('确认更新该基线吗？修改的基线会反馈到所有已关联的账号上', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.updateForm()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    createForm () {
      var params = { job_baseline: this.params, system_baselines: this.systemBaselines }
      this.loading = true
      API.jobBaselines.create(params)
        .then(response => {
          this.loading   = false
          if (response.data.success) {
            this.$emit('change')
            this.visible = false
            this.$refs.localForm.resetFields()
            this.$message.success(`${this.modeTitle}岗位基线成功`)
          } else {
            this.$message.error(response.data.error_message)
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    createConfirmForm () {
      this.$confirm('确认创建该基线吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.createForm()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    handleConfirm () {
      this.$refs.localForm.validate((valid) => {
        if (valid) {
          if (this.blankSystemBaselines().length > 0) {
            this.$message.error('系统的基线为必填项')
            return false
          }
          if (this.systemBaselines.length === 0) {
            this.$message.error('至少选择一个系统基线')
            return false
          }
          if (this.params.id !== null) {
            this.updateConfirmForm()
          } else {
            this.createConfirmForm()
          }
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    // 弹出系统基线管理dialog
    openSystemBaselines (system) {
      var businessSystem = this.allBusinessSystems.find(x => x.id === system.id)
      system.name = businessSystem.name
      this.system = system
      this.$refs.systemBaselines.visible = true
    },
    updateSystemBaselines (row) {
      this.loadingSystems = true
      var params = { system_id: row }
      API.systems.baselines(params)
        .then(response => {
          this.loadingSystems = false
          var businessSystems = this.$lodash.clone(this.systemBaselines)
          var businessSystem = businessSystems.find(x => x.id === row)
          businessSystem.system_baselines = response.data
          this.systemBaselines = businessSystems
        })
        .catch(() => {
          this.loadingSystems = false
        })
    },
    // 添加系统和基线
    addSystemAndBaseline () {
      var json =  {
        id:                 null,
        name:               null,
        system_baseline_id: null,
        system_baselines:   []
      }
      this.systemBaselines.push(json)
    },
    // 删除系统和基线
    deleteSystemAndBaseline (index) {
      if (this.systemBaselines.length === 1) {
        this.$message.error('请至少选择一个系统的基线')
        return
      }
      this.systemBaselines.splice(index, 1)
    },
    // 获取所有系统
    initAllBuisineSystems () {
      API.systems.inserviceSystems()
        .then(response => {
          this.allBusinessSystems = response.data
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    // 已选择的系统ID
    selectedSystemIds () {
      return this.systemBaselines.filter(x => !API.tool.isBlank(x.id)).map(x => x.id)
    },
    // 获取指定岗位基线的系统基线
    getJobBaselineSystemBaselines () {
      if (typeof(this.job.id) !== 'number') return

      API.jobBaselines.outputSystemBaselines(this.job.id)
        .then(response => {
          this.systemBaselines = response.data
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    // 获取指定系统的系统基线
    getSystemBaselines (systemId) {
      if (API.tool.isBlank(systemId)) return

      API.systems.baselines({ system_id: systemId })
        .then(response => {
          var businessSystems = this.$lodash.clone(this.systemBaselines)
          var businessSystem = businessSystems.find(x => x.id === systemId)
          businessSystem.system_baselines = response.data
          businessSystem.system_baseline_id = null
          this.systemBaselines = businessSystems
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    blankSystemBaselines () {
      return this.systemBaselines.filter(x => !x.ban && API.tool.isBlank(x.system_baseline_id))
    },
    // 是否展示系统基线管理
    displaySystemBaselines (systemId) {
      return !API.tool.isBlank(systemId)
    },
    handleSystemBaseline(val) {
      val.system_baseline_id = null
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.red {
  color: #f56c6c;
  margin-left: 15px;
}

</style>
