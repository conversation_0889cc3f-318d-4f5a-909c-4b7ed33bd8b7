<template>
  <div>
    <div class="toolbar">
      <div class="search-box">
        <el-input
          v-model="filter.query"
          placeholder="账号名称或编码"
          prefix-icon="el-icon-search"
          clearable
          class="search-input"
          size="small"
          @keyup.enter.native="runSearch"
        />

        <el-select
          v-model="filter.status"
          placeholder="账号状态"
          size="small"
          clearable
          width="120px"
        >
          <el-option
            key="true"
            :label="getAccountStatusString(true)"
            value="true"
          />
          <el-option
            key="false"
            :label="getAccountStatusString(false)"
            value="false"
          />
        </el-select>
      </div>
      <div style="margin-top:-13px;">
        <el-button
          type="primary"
          size="small"
          style="margin-left:3px"
          @click="runSearch"
        >
          搜索
        </el-button>

        <el-button
          size="small"
          style="margin-left:3px"
          @click="resetSearch"
        >
          重置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import API from '@/api'

export default {
  components: {
  },
  props:      {
  },
  data () {
    return {
      filter: {
        query:  '',
        status: '',
      }
    }
  },
  computed: {
  },
  watch:    {

  },
  created () {
  },
  methods: {
    runSearch () {
      this.$emit('update:page', 1)
      this.$emit('update:filter', this.filter)
      this.$emit('requestData')
    },
    resetSearch () {
      this.filter = {
        query:           '',
        status:          '',
        user_status:     '',
        departments:     []
      }
      this.runSearch()
    },

  }
}
</script>

<style lang="scss" scoped>
@import '~@/components/variables';

.el-pagination {
  margin-top: 1.5em;
}

.toolbar {
  @include vertical_center_between;
  margin-bottom: 0.5em;

  .search-box {
    @include vertical_center_between;
    margin-bottom: 1em;
    margin-left:   13px;

    .el-input {
      width:        117px;
      margin-right: 10px;
    }

    .el-select {
      width:        117px;
      margin-right: 10px;
    }

    .search-input {
      width: 155px;
    }
  }

  .export-batch-button {
    margin-left:  10px;
    margin-right: 10px;
  }

}

.summary-table {
  width:      100%;
  border-top: 1px solid #EBEEF5;
}

.advanced-search-select {
  width: 155px !important;
}
</style>
