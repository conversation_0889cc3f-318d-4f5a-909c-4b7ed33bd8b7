# frozen_string_literal: true

# 用户操作审计
class DepartmentAuditLog < ApplicationAuditLog

  def initialize(user, request, params)
    @operation_module = '员工信息管理'
    @operation_category = '部门管理'
    super
  end

  def create
    @operation = '创建部门'
    @comment   = "创建部门「#{params.name}」成功"
    create_audit_log
  end

  def create_failed
    @operation = '创建部门'
    @comment   = "创建部门「#{params.name}」失败"
    create_audit_log
  end

  def edit
    @operation = '更新部门'
    @comment   = "成功修改 ID 为「#{params.id}」的部门名称为「#{params.name}」"
    create_audit_log
  end

  def edit_failed
    @operation = '更新部门'
    @comment   = "修改 ID 为「#{params.id}」的部门名称失败"
    create_audit_log
  end

  def destroy
    @operation = '部门删除'
    @comment   = "成功删除部门「#{params.name}」"
    create_audit_log
  end
end