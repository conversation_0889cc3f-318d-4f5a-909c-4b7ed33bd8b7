<template>
  <div class="toolbar">
    <div class="search-box">
      <el-select
        v-if="local_relation !== 'first'"
        v-model="local_relation"
        size="small"
        placeholder="选择关系"
      >
        <el-option
          label="并且"
          value="and"
        />
        <el-option
          label="或者"
          value="or"
        />
      </el-select>
      <el-select
        v-model="local_type"
        size="small"
        placeholder="筛选类型"
        @change="selectChanged"
      >
        <el-option
          label="操作人"
          value="operator"
        />
        <el-option
          label="日志类型"
          value="generate_type"
        />
        <el-option
          label="操作时间"
          value="operation_time"
        />
        <el-option
          label="功能模块"
          value="operation_module"
        />
        <el-option
          label="功能类别"
          value="operation_category"
        />
        <el-option
          label="操作内容"
          value="operation"
        />
        <el-option
          label="详细信息"
          value="comment"
        />
        <el-option
          label="事件 ID"
          value="event_id"
        />
        <el-option
          label="IP地址"
          value="ip_address"
        />
        <el-option
          label="浏览器"
          value="agent"
        />
      </el-select>
      <el-select
        v-if="local_type !== 'operation_time' && local_type !== 'agent' && local_type !== 'event_id' && local_type !== 'generate_type'"
        v-model="local_condition"
        size="small"
        placeholder="选择条件"
      >
        <el-option
          label="包括"
          value="include"
        />
        <el-option
          label="不包括"
          value="exclude"
        />
        <el-option
          label="等于"
          value="equal"
        />
        <el-option
          label="不等于"
          value="neq"
        />
      </el-select>
      <el-select
        v-else-if="local_type === 'agent' || local_type === 'event_id'"
        v-model="local_condition"
        size="small"
        placeholder="选择条件"
      >
        <el-option
          label="包括"
          value="include"
        />
        <el-option
          label="不包括"
          value="exclude"
        />
      </el-select>
      <el-select
        v-else-if="local_type === 'generate_type'"
        v-model="local_condition"
        size="small"
        placeholder="选择条件"
      >
        <el-option
          label="等于"
          value="equal"
        />
      </el-select>
      <el-select
        v-else
        v-model="local_condition"

        size="small"
        placeholder="选择条件"
      >
        <el-option
          label="大于等于"
          value="greater"
        />
        <el-option
          label="小于等于"
          value="less"
        />
      </el-select>
      <el-input
        v-if="local_type !== 'operation_time' && local_type !== 'generate_type'"
        v-model="local_query"

        size="small"
        placeholder="筛选内容"
        prefix-icon="el-icon-search"
        clearable
        class="query-input"
      />
      <el-select
        v-else-if="local_type === 'generate_type'"
        v-model="local_query"

        size="small"
        clearable
        placeholder="选择日志类型"
      >
        <el-option
          label="用户操作"
          value="1"
        />
        <el-option
          label="系统任务"
          value="2"
        />
      </el-select>
      <el-date-picker
        v-else
        v-model="local_query"

        size="small"
        type="datetime"
        placeholder="选择日期时间"
      />
      <el-button
        size="small"

        icon="el-icon-minus"
        circle
        @click="deleteFilter"
      />
    </div>
  </div>
</template>

<script>

export default {
  props:   {
    filter:  {
      type:     Object,
      required: true
    },
    filters: {
      type:     Array,
      required: true
    }
  },
  data () {
    return {
      local_relation:  '',
      local_type:      '',
      local_condition: '',
      local_query:     ''
    }
  },
  watch:   {
    filter () {
      this.refresh_data()
    },
    local_relation () {
      this.filter.relation = this.local_relation
    },
    local_type () {
      this.filter.type = this.local_type
    },
    local_condition () {
      this.filter.condition = this.local_condition
    },
    local_query () {
      this.filter.query = this.local_query
    }
  },
  created () {
    this.refresh_data()
  },
  methods: {
    selectChanged () {
      this.filter.condition = ''
      this.filter.query     = ''
      this.local_condition  = this.filter.condition
      this.local_query      = this.filter.query
    },
    refresh_data () {
      this.local_type      = this.filter.type
      this.local_condition = this.filter.condition
      this.local_query     = this.filter.query
      this.local_relation  = this.filter.relation
    },
    deleteFilter () {
      this.filters.splice(this.filter.id, 1)
      for (let j = 0; j < this.filters.length; j++) {
        if (j === 0) {
          this.filters[j].relation = 'first'
        }
        this.filters[j].id = j
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .role-item {
    margin: 0.5em;
  }

  .el-pagination {
    margin-top: 1.5em;
  }

  .toolbar {
    @include vertical_center_between;
    margin-bottom: 0.5em;

    .search-box {
      @include vertical_center_between;
      margin-bottom: 1em;
      margin-left:   13px;

      .el-input {
        width:        132px;
        margin-right: 10px;
      }

      .el-select {
        width:        130px;
        margin-right: 10px;
      }

      .query-input {
        width: 200px;
      }

      .el-date-editor--datetime {
        width: 200px;
      }
    }

    .export-batch-button {
      margin-left:  10px;
      margin-right: 10px;
    }

    .position-input {
      margin-right: 10px;
      width:        120px;
    }
  }

  .summary-table {
    width:      100%;
    border-top: 1px solid #EBEEF5;
  }
</style>
