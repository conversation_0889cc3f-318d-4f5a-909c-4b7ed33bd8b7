<template>
  <div class="ignore-matcher">
    <el-switch
      v-model="ignoreMatcher"
      :width="37"
      @change="changeSwitch"
    />

    <el-dialog
      :visible.sync="localVisible"
      :close-on-click-modal="false"
      width="420px"
      top="8em"
      title="提示"
      append-to-body
    >
      <template>
        <div class="notice">
          此操作将{{ ignoreStatus }}「{{ ledger.account_name }}」账号智能匹配, 是否继续?
        </div>
      </template>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="localVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="editIgnoreMatcher"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api'

export default {
  props:      {
    ledger:   {
      type:     Object,
      required: true
    },
    systemId: {
      type:     Number,
      required: true
    }
  },
  data () {
    return {
      ignoreMatcher: false,
      ignoreStatus: '禁用',
      localVisible: false
    }
  },
  watch: {
    ledger () {
      this.initData()
    },
    localVisible () {
      if (!this.localVisible) {
        this.$emit('update')
      }
    }
  },
  created () {
    this.initData()
  },
  methods: {
    initData () {
      this.ignoreMatcher = this.ledger.ignore_matcher
      this.ignoreStatus = this.ignoreMatcher ? '启用' : '禁用'
    },
    changeSwitch () {
      this.localVisible = true
    },
    editIgnoreMatcher () {
      API.ledgers.editIgnoreMatcher({
        systemId:      this.systemId,
        accountCode:   this.ledger.account_code,
        ignoreMatcher: this.ignoreMatcher
      })
        .then(response => {
          if (response.data.account_code) { // 成功返回数据会带有account_code数据
            this.localVisible = false
            if (response.data.ignore_matcher) {
              this.$message.success('已禁用智能匹配')
            } else {
              this.$message.success('已开启智能匹配')
            }
          }
        })
        .catch(() => {
          this.$message.error('服务器内部错误')
        })
    }
  }
}
</script>

<style lang="scss">
.ignore-matcher .el-switch__core{
  height: 16px;
}

.ignore-matcher .el-switch__core::after{
  height: 12px;
  width: 12px;
}

.ignore-matcher .el-switch.is-checked .el-switch__core::after {
  left: 100%;
  margin-left: -14px;
}
</style>
