<template>
  <span>
    <el-button
      :disabled="disabled"
      @click="baselineDialogVisible = true"
    >
      关联系统基线
    </el-button>
    <el-button
      :disabled="disabled"
      @click="dialogVisible = true"
    >
      复制为系统基线
    </el-button>
    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      title="创建系统基线"
      width="600px"
      append-to-body
      @open="handleOpen"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="110px"
      >
        <el-form-item
          prop="name"
          label="系统基线名称"
        >
          <el-input
            v-model="form.name"
            size="small"
            placeholder="请设置系统基线名称"
            autofocus
            style="width: 350px;"
          />
        </el-form-item>

        <el-form-item
            label="部门"
        >
          <department-select
              v-model="form.department_id"
              :all-users="true"
              placeholder="选择部门"
              :checkStrictly="false"
              collapseTags
              size="small"
              selectStyle="width: 350px"
          />
        </el-form-item>
        <el-form-item
            label="备注"
        >
           <el-input
              type="textarea"
              v-model="form.comment"
              :autosize="{ minRows: 3, maxRows: 4 }"
              style="width: 350px"
           />
        </el-form-item>

        <el-form-item
          v-if="isFoundBaseline"
          label="模式"
        >
          <el-radio v-model="mode" label="create">复制为新系统基线</el-radio>
          <el-radio v-model="mode" label="update">更新现有系统基线</el-radio>
        </el-form-item>

        <el-form-item
          label="告警维度"
        >
          <span
            v-for="(data, index) in form.output_schema_data"
          >
            <el-checkbox
              v-model="form.output_schema_data[index].is_notice"
              :label="data.name"
              :key="data.data_key"
              style="margin-right: 20px;"
            >
              {{data.name}}
            </el-checkbox>
          </span>
        </el-form-item>

        <hr/>
        <el-form-item label="基线对比字段" />
        <span v-for="item in form.output_schema">
          <el-form-item :label="getItemLabel(item)">
            <el-checkbox
              v-for="schema in item.schema"
              v-model="schema.is_diff"
            >
              {{ schema.label }}
            </el-checkbox>
          </el-form-item>
        </span>
        <hr/>

        <el-form-item
          label="告警条件"
        >
          <el-radio-group
            v-model="form.compare_rule"
          >
            <el-radio label="all_contrast">权限不一致</el-radio>
            <el-radio label="only_add">超出基线范围</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="form.linked">将该账号与系统基线关联</el-checkbox>
          <el-checkbox v-model="form.follow_job" v-if="form.linked">跟随岗位自动变更基线</el-checkbox>
        </el-form-item>

      </el-form>
      <comment-bar>
        * 注意：
        1. 需要管理员拥有系统基线管理中创建系统基线和更新系统基线的权限才能执行此操作。<br/>
        2. 如选择部门为非自己管理权限的部门，该基线将无法查看。
      </comment-bar>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
    <!-- eslint-enable vue/attribute-hyphenation -->
    <baseline-select
      :visible.sync="baselineDialogVisible"
      :system-id="systemId"
      :accounts="[accountInfo]"
      @change="getAccount()"
    />

  </span>
</template>

<script>
import CommentBar from '@/components/common/CommentBar.vue'
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'
import BaselineSelect from '@/components/AdminBaselines/BaselineSelect.vue'
import API            from '@/api'

export default {
  components: {
    CommentBar,
    DepartmentSelect,
    BaselineSelect
  },
  props:      {
    accountId: {
      type:     Number,
      required: true
    },
    systemId:  {
      type:     Number,
      required: true
    }
  },
  data () {
    return {
      dialogVisible:   false,
      baselineDialogVisible: false,
      form:            {
        id:          null,
        name:        null,
        compare_rule:null,
        source_type: 'account',
        source_id:   this.accountId,
        output_schema: [],
        output_schema_data: [],
        department_id: '',
        comment: '',
        follow_job: true
      },
      rules:           {
        name: [{ required: true, message: '系统基线不能为空', trigger: 'blur' }]
      },
      isFoundBaseline: false,
      mode:            'create',
      accountInfo:    {
        accountCode: null,
        baselineId: null,
        follow_job: true,
      }
    }
  },
  computed: {
    disabled () {
      const editPermission = 'system_baseline.edit'
      const permissions = this.$store.state.myFunctions
      return !permissions.includes(editPermission)
    }
  },
  created () {
    this.getAccount()
  },
  methods:    {
    // syncOutputSchemaData(){
    //   let data = this.form.output_schema_data
    //   let selectData = this.form.select_output_schema_data
    //   let newData = data.map(function(obj){
    //     obj.is_notice = selectData.indexOf(obj.name) > -1
    //     return obj
    //   })
    //   this.form.output_schema_data = newData
    // },
    handleConfirm () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const data = this.form.output_schema_data.filter (x => x.is_notice)
          if (data.length === 0) {
            this.$message.error('请至少选择一个告警维度')
            return false
          }
          this.createOrUpdateBaseline()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })

    },
    createOrUpdateBaseline () {
      if (this.mode === 'create') return this.createBaseline()
      if (this.mode === 'update') return this.updateBaseline()
    },
    createBaseline () {
      this.$axios.post(`/api/systems/${this.systemId}/baselines`, {
        baseline: this.form
      })
        .then(response => {
          this.dialogVisible = false
          this.$message.success('系统基线已复制成功')
        })
        .catch(() => {})
    },
    updateBaseline () {
      this.$axios.put(`/api/systems/${this.systemId}/baselines/${this.form.id}`, {
        baseline: this.form
      })
        .then(response => {
          this.dialogVisible = false
          this.$message.success('系统基线已更新成功')
        })
        .catch(() => {})
    },
    handleOpen () {
      this.foundBaseline()
    },
    foundBaseline () {
      this.$axios.get(`/api/systems/${this.systemId}/accounts/${this.accountId}/baseline`)
        .then(response => {
          this.isFoundBaseline    = response.data.is_found
          this.form.id           = response.data.id
          this.form.name         = response.data.name
          this.form.output_schema = response.data.output_schema
          this.form.comment       = response.data.comment
          this.form.compare_rule  = response.data.compare_rule
          this.form.department_id = response.data.department_id
          this.form.output_schema_data = response.data.output_schema_data
          this.form.select_output_schema_data = this.filterOutputSchemaData(response.data.output_schema_data)
        })
        .catch(() => {})
    },
    // 获取账户信息
    getAccount () {
      const theParams = {
        account_id: this.accountId,
        system_id:  this.systemId
      }
      API.systemAccounts.systemAccount(theParams)
        .then(response => {
          const account_info = response.data.account_info
          this.accountInfo.account_code = account_info.account_code
          this.accountInfo.baseline_id  = account_info.system_baseline.id
          this.accountInfo.follow_job   = account_info.follow_job
        })
        .catch(() => {})
    },
    filterOutputSchemaData(output_schema_data){
      return output_schema_data.filter(function(value, index, array){
        return value.is_notice;
      }).map(function(obj){
        return obj.name
      })
    },
    // 对比字段label
    getItemLabel(item) {
      return `${item.name}`
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/components/variables';
</style>
