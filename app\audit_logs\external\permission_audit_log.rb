# frozen_string_literal: true

# 外部系统权限
class External::PermissionAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '外部系统权限管理'
    @operation_category = '外部系统权限管理'
    super
  end

  def import_success
    @operation = '导入外部系统权限'
    bs         = params[0]
    file       = params[1]
    @comment   = "外部系统「#{bs&.name}」导入权限「#{file.original_filename}」成功"
    create_audit_log
  end

  def import_failed
    @operation = '导入外部系统权限'
    bs         = params[0]
    file       = params[1]
    @comment   = "外部系统「#{bs&.name}」导入权限「#{file.original_filename}」失败"
    create_audit_log
  end

  def destroy
    @operation = '删除外部系统权限'
    @comment   = "外部系统「#{params.external_business_system&.name}」删除权限「#{params.name}」，ID：#{params.id}，CODE：#{params.code}"
    create_audit_log
  end
end
