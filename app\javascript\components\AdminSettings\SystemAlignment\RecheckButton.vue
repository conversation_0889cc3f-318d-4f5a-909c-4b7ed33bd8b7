<script>
export default {
  computed: {
    currentQuarter () {
      return this.$store.state.current_quarter
    }
  },
  methods: {
    recheckComparison () {
      this.$confirm('请确认在操作前已经先保存了设置，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.runRecheck()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    runRecheck () {
      this.$axios.post('/admin_api/settings/system_alignment/recheck', {
        quarter_id: this.currentQuarter.id
      })
        .then(() => {
          const name = this.$t('module_name.quarters')
          this.$message.success(`任务已执行，稍后请在${name}中查看任务执行结果`)
        })
        .catch(error => {
          switch (error.status) {
            case 456:
              this.$message.warning('发现任务正在执行中，请等待当前任务结束后再操作')
              break

            default:
              this.$message.error('执行失败')
          }
        })
    }
  }
}
</script>
