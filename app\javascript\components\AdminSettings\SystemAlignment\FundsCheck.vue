<template>
  <div
    v-loading="loading"
    class="container"
  >
    <el-form
      ref="form"
      labelWidth="125px"
      :model="{rules: rules}"
    >
      <el-form-item
        label="添加异常确认规则"
        class="add-item"
      >
        <el-button
          size="small"
          @click="handleEditRule(null)"
        >
          点击添加
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="recheckComparison"
        >
          重新检查当前时间点数据
        </el-button>
      </el-form-item>
    </el-form>
    <el-divider />
    <el-table
      v-loading="loading"
      size="small"
      :data="rules.slice((currentPage-1)*pageSize, currentPage*pageSize)"
      border
      style="width: 100%"
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        width="100"
      />
      <el-table-column
        fixed
        prop="system_name"
        label="系统"
      />
      <el-table-column
        fixed
        prop="account_name"
        label="账号"
      />
      <el-table-column
        fixed
        prop="fund_name"
        label="产品"
      />
      <el-table-column
        fixed
        prop="remark"
        label="备注"
      />
      <el-table-column
        fixed
        label="操作"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="small"
            type="text"
            @click="handleEditRule(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            size="small"
            type="text"
            @click="handleDeleteRule(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-divider />
    <el-pagination
      background
      @size-change="handleSizeChange"
      :page-sizes="[10, 50, 100, 500]"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      layout="sizes, prev, pager, next"
      :total="rules.length"
    />

    <el-dialog
      title="编辑规则"
      :visible.sync="editVisible"
      width="30%"
      v-loading="settingLoading"
    >
      <el-form ref="form" :model="rule" label-width="120px" v-loading="settingLoading">
        <el-form-item label="选择系统" required>
          <el-select
            v-model="rule.system_type"
            v-loading="settingLoading"
            filterable
            size="small"
            placeholder="请选择系统"
            
          >
            <el-option
              v-for="system_type in systemNamesArray"
              :key="system_type.value"
              :value="system_type.value"
              :label="system_type.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择账号" required>
          <el-select
            v-if="rule.system_type === null"
            v-model="rule.account_code"
            disabled
            size="small"
          />
          <el-select
            v-if="rule.system_type === 'reference_system'"
            v-model="rule.account_code"
            filterable
            size="small"
            placeholder="请选择账号"
          >
            <el-option
              v-for="account in reference_accounts"
              :key="account.account_code"
              :value="account.account_code"
              :label="`${account.account_code} - ${account.account_name}`"
            />
          </el-select>
          <el-select
            v-if="rule.system_type === 'alignment_system'"
            v-model="rule.account_code"
            filterable
            size="small"
            placeholder="请选择账号"
          >
            <el-option
              v-for="account in alignment_accounts"
              :key="account.account_code"
              :value="account.account_code"
              :label="`${account.account_code} - ${account.account_name}`"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择产品" required>
          <el-select
            v-if="rule.system_type === null"
            v-model="rule.fund_code"
            disabled
            size="small"
          />
          <el-select
            v-if="rule.system_type === 'reference_system'"
            v-model="rule.fund_code"
            filterable
            size="small"
            placeholder="请选择基金"
          >
            <el-option
              v-for="fund in reference_funds"
              :key="fund.fund_code"
              :value="fund.fund_code"
              :label="`${fund.fund_code} - ${fund.name}`"
            />
          </el-select>
          <el-select
            v-if="rule.system_type === 'alignment_system'"
            v-model="rule.fund_code"
            filterable
            size="small"
            placeholder="请选择基金"
          >
            <el-option
              v-for="fund in alignment_funds"
              :key="fund.fund_code"
              :value="fund.fund_code"
              :label="`${fund.fund_code} - ${fund.name}`"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="rule.remark"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="updateSettings" size="small">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import RecheckButton from '@/components/AdminSettings/SystemAlignment/RecheckButton'

export default {
  mixins: [RecheckButton],
  data () {
    return {
      loading:            false,
      settingLoading:     false,
      rules:              [],
      nowRules:           [],
      system_names:       {},
      reference_accounts: [],
      alignment_accounts: [],
      reference_funds:    [],
      alignment_funds:    [],
      currentPage:        1,
      pageSize:           10,
      editVisible:        false,
      rule:               {
        id: null,
        system_type: '',
        account_code: '',
        fund_code: "",
        remark: ""
      }
    }
  },
  computed: {
    systemNamesArray () {
      return [
        { value: 'reference_system', label: this.system_names.reference_system },
        { value: 'alignment_system', label: this.system_names.alignment_system }

      ]
    }
  },
  created () {
    this.getRules()
    this.getSettings()
  },
  watch: {
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val
    },
    getRules () {
      this.loading = true
      this.$axios.get('/admin_api/settings/system_alignment/funds_rules')
        .then(response => {
          this.loading            = false
          this.rules              = response.data
        })
        .catch(error => {
          this.loading = false
          this.$message.error('读取设置信息失败')
        })
    },
    getSettings () {
      this.settingLoading = true
      this.$axios.get('/admin_api/settings/system_alignment/funds_check')
        .then(response => {
          this.settingLoading     = false
          this.system_names       = response.data.system_names
          this.reference_accounts = response.data.reference_accounts
          this.alignment_accounts = response.data.alignment_accounts
          this.reference_funds    = response.data.reference_funds
          this.alignment_funds    = response.data.alignment_funds
        })
        .catch(error => {
          this.settingLoading = false
          this.$message.error('读取设置信息失败')
        })
    },

    handleEditRule (item) {
      if (item){
        this.rule = JSON.parse(JSON.stringify(item))
      }
      else {
        this.rule = {
          id: null,
          system_type: '',
          account_code: '',
          fund_code: "",
          remark: "",
          type: "FundsExcludeRule"
        }
      }
      this.editVisible = true
    },
    updateSettings () {
      this.settingLoading = true
      this.$axios.post('/admin_api/settings/system_alignment/funds_check', {
        rule: this.rule
      }).then(response => {
        this.$message.success('规则已更新')
        this.getRules()
        if (!this.rule.id){
          this.currentPage = parseInt((this.rules.length + 1) / this.pageSize)
          if ((this.rules.length + 1) % this.pageSize > 0){
            this.currentPage = this.currentPage + 1
          }
        }
        this.editVisible = false
        this.settingLoading = false
      })
      .catch(error => {
        this.$message.error('规则参数不能为空')
        this.settingLoading = false
      })
    },

    handleDeleteRule (id) {
      this.$confirm('此操作将删除此配置，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
      .then(() => {
        this.loading = true
        this.$axios.delete('/admin_api/settings/system_alignment/funds_check/'+id)
        .then(response => {
          this.loading = false
          this.$message.success('规则已删除')
          this.getRules()
        })
        .catch(error => {
          this.loading = false
          this.$message.error('规则删除失败')
        })
      })
      .catch(() => {
        this.$message.info('已取消操作')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.form-item {
  @include vertical_center_between;
  height: 63px;

}

.el-select {
  width: 160px;
}

.title {
  width:         60px;
  color:         $font-color;
  line-height:   40px;
  margin-bottom: 22px;
}

.delete-button {
  margin-left:   10px;
  line-height:   40px;
  margin-bottom: 22px;

}

.list-complete-item {
  transition: all 0.5s;
}

.error {
  color:       #E54D42;
  font-weight: bold;
}

.list-complete-enter, .list-complete-leave-to
  /* .list-complete-leave-active for below version 2.1.8 */
{
  opacity:   0;
  transform: translateY(-30px);
}

.submit-buttons {
  margin-top: 20px;
}

.add-item {
  margin-top: 10px;
}
</style>
