<template>
  <div class="container">
    <h2>业务分组管理</h2>
    <hr>
    <el-tabs
      v-model="tab"
      type="border-card"
      @tab-click="routerChange"
    >
      <el-tab-pane
        :disabled="!hasPermission('systems.query')"
        name="systems"
        label="业务系统管理"
      >
        <systems v-if="tab === 'systems'" />
      </el-tab-pane>
      <el-tab-pane
        :disabled="!hasPermission('systems.category_list_tree')"
        name="system_manage_categories"
        label="分组管理"
      >
        <categories v-if="tab === 'system_manage_categories'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="false"
        name="system_manage_features"
        label="模块管理"
      >
        <features v-if="tab === 'system_manage_features'" />
      </el-tab-pane>

      <el-tab-pane
        :disabled="!hasPermission('all_system_info.system_info_query')"
        name="system_manage_all_system"
        label="业务系统信息维护"
      >
        <all-system-info v-if="tab === 'system_manage_all_system'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Systems from './systems.vue'
import Categories from './categories.vue'
import Features from './features.vue'
import AllSystemInfo from './AllSystemInfo.vue'

export default {
  components: {
    Systems,
    Categories,
    Features,
    AllSystemInfo
  },
  data () {
    return {
      isPermissionEditable: this.$settings.permissionEditable.enable,
      tab: this.getRoute()
    }
  },
  watch: {
  },
  methods: {
    getRoute () {
      if (this.hasPermission('systems.query')) {
        return 'systems'
      }else if(this.hasPermission('systems.category_list_tree')){
        return 'system_manage_categories'
      }else if(this.hasPermission('all_system_info.system_info_query')){
        return 'system_manage_all_system'
      }
      this.$message.error('您没有权限操作此功能')
      return ''
    },
    hasPermission (code) {
      return this.$store.getters.hasPermission(code)
    },
    routerChange (tab) {
      this.$router.push({ name: tab.name })
    }
  }
}
</script>

<style lang="scss" scoped>
  .container {
    padding: 2em;
    background-color: white;
    min-width: 800px;
  }
</style>
