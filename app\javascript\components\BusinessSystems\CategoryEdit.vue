<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :title="title"
    width="500px"
    appendToBody
    @close="dialogVisible = false"
  >
    <el-form
      ref="categoryForm"
      :model="categoryForm"
      :rules="rules"
      labelWidth="120px"
      statusIcon
      class="new-form"
    >
      <el-form-item label="分组名称：" prop="name">
        <el-input v-model="categoryForm.name" />
      </el-form-item>
      <el-form-item label="上级分组名称：" prop="parent_id">
        <el-select
          v-model="categoryForm.parent_id"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            v-for="item in parentCategoryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选择系统：">
        <el-select
          v-model="categoryForm.business_system_ids"
          placeholder="请选择"
          multiple
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="system in systems"
            :key="system.id"
            :label="system.name"
            :value="system.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="排序编号：">
        <el-input-number
          :min=0
          :step=1
          :step-strictly="true"
          v-model="categoryForm.order_number"
        />
      </el-form-item>
    </el-form>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="primary"
        @click="handleCommit"
      >
        {{ createMode ? '创建' : '更新' }}
      </el-button>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    category:     {
      type:     Object,
      required: true
    },
    systems:      {
      type:     Array,
      required: true
    },
    createMode:   {
      type:    Boolean,
      default: false
    },
    categoryList: {
      type:     Array,
      required: true
    }
  },
  data () {
    return {
      dialogVisible: false,
      categoryForm:  {
        name:                '',
        business_system_ids: [],
        order_number: null,
        parent_id: null
      },
      rules:         {
        name:                [
          { required: true, message: '分组名称不能为空', trigger: 'blur' },
        ],
        business_system_ids: []
      }
    }
  },
  computed: {
    title () {
      if (this.createMode) return '创建业务系统分组'
      return '修改业务系统分组'
    },
    parentCategoryList() {
      if(this.category) {
        return this.categoryList.filter(x => x.id !== this.category.id && x.parent_id !== this.category.id)
      } else {
        return this.categoryList
      }
    }
  },
  watch:    {
    category () {
      this.systemInitialize()
    }
  },
  created () {
    this.systemInitialize()
  },
  methods: {
    systemInitialize () {
      if (this.createMode) {
        this.categoryForm.name                = ''
        this.categoryForm.business_system_ids = []
        this.categoryForm.order_number        = null
        this.categoryForm.parent_id           = null
      } else {
        this.categoryForm.name                = this.category.name
        this.categoryForm.business_system_ids = this.category.business_system_ids
        this.categoryForm.order_number        = this.category.order_number
        this.categoryForm.parent_id           = this.category.parent_id
      }
    },
    updateCategory () {
      const params = {
        name:                this.categoryForm.name,
        business_system_ids: this.categoryForm.business_system_ids,
        order_number:        this.categoryForm.order_number || null,
        parent_id:           this.categoryForm.parent_id
      }
      this.$axios.put(`/api/systems/categories/${this.category.id}`, { category: params })
        .then(response => {
          this.$emit('update')
          this.$message.success(`更新${this.categoryForm.name}成功`)
          this.dialogVisible = false
        })
        .catch(() => {})
    },
    createCategory () {
      if (!this.categoryForm.name) return this.$message.error('分组名称不能为空')
      if (this.validateGroupName(this.categoryForm.name)) return this.$message.error(`已经存在分组名称${this.categoryForm.name}`)
      const params = {
        name:                this.categoryForm.name,
        business_system_ids: this.categoryForm.business_system_ids,
        order_number:        this.order_number,
        parent_id:           this.categoryForm.parent_id
      }
      this.$axios.post(`/api/systems/categories`, { category: params })
        .then(response => {
          this.$emit('update')
          this.$message.success(`创建分组${this.categoryForm.name}成功`)
          this.dialogVisible = false
        })
        .catch(() => {})
    },
    handleCommit () {
      if (!this.categoryForm.name) return this.$message.error('分组名称不能为空')
      const repeatSystemNames = this.validateUpdateRepeatSystem(this.categoryForm.name, this.categoryForm.business_system_ids)
      if (repeatSystemNames.length > 0) {
        this.formValid(this.categoryForm.name, repeatSystemNames)
      } else {
        if (this.createMode) return this.createCategory()
        this.updateCategory()
      }
    },
    formValid (categoryName, repeatSystemNames) {
      this.$confirm(`${repeatSystemNames}目前已在其它分组，是否移动到「${categoryName}」分组?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      }).then(() => {
        if (this.createMode) return this.createCategory()
        this.updateCategory()
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    validateUpdateRepeatSystem (categoryName, systemIds) {
      const otherCategory    = this.categoryList.filter(category => category.name !== categoryName)
      const otherCategoryIds = this.otherCategorySystems(otherCategory)
      return this.selectSystems(systemIds.filter(v => otherCategoryIds.includes(v)))
    },
    otherCategorySystems (categoryList) {
      let arrIds = []
      categoryList.forEach(function (category) {
        arrIds = arrIds.concat(category.business_system_ids)
      })
      return arrIds
    },
    selectSystems (systemIds) {
      let systemArr = []
      this.systems.forEach(function (system) {
        if (systemIds.includes(system.id)) systemArr = systemArr.concat(system.name)
      })
      return systemArr
    },
    validateGroupName (categoryName) {
      let repeatGroupName = false
      this.categoryList.forEach(function (category) {
        if (category.name === categoryName) {
          repeatGroupName = true
          return repeatGroupName
        }
      })
      return repeatGroupName
    }
  }
}

</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.new-form {
  margin-right: 3em;
}

.new-input {
  width: 100%;
}
</style>
