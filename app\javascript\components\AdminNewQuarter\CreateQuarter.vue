<template>
  <div class="operation-create-quarter">
    <div class="radio-container">
      <el-radio
        v-model="quarterRadio"
        :label="1"
      >
        新建导入数据名称：
        <el-input
          v-model="newName"
          autofocus
          :disabled="!$store.getters.hasPermission('admin_new_quarter.create')"
        />
      </el-radio>
    </div>
    <div
      v-show="isHasQuarters"
      class="radio-container"
    >
      <el-radio
        v-model="quarterRadio"
        :label="2"
      >
        或选择已导入数据：
        <el-select
          v-model="quarter.id"
          placeholder=""
          class="quarter-select"
          @change="changeQuarter"
          :disabled="!$store.getters.hasPermission('admin_new_quarter.create')"
        >
          <el-option
            v-for="item in quarters"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-radio>
    </div>
  </div>
</template>

<script>
import API from '@/api'

export default {
  data () {
    return {
      quarterRadio: 1,
      quarter:      { id: null },
      quarters:     [],
      newName:     ''
    }
  },
  computed:   {
    isHasQuarters () {
      return this.quarters.length !== 0
    }
  },
  created () {
    this.getQuarters()
  },
  methods: {
    getQuarters () {
      API.quarters.list()
        .then(response => {
          this.quarters   = response.data
          this.quarter.id = this.quarters[0].id
          this.changeQuarter(this.quarter.id)
        })
        .catch(() => {})
    },
    createQuarter () {
      API.quarters.createExcel(this.newName)
        .then(response => {
          if (response.data.success) {
            this.quarter = response.data.data
            this.$emit('create', this.quarter)
            this.$emit('stepFinish')
            this.$store.commit('enableReloadQuarter')
          } else {
            this.$message.error(response.data.error_message)
          }
        })
        .catch(() => {})
    },
    changeQuarter (value) {
      this.$emit('change', value)
    },
    findOrCreateQuarter () {
      if (this.quarterRadio === 1) {
        this.createQuarter()
      } else {
        this.$emit('stepFinish')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/_variables";

.operation-create-quarter {
  height: 450px;

  .radio-container {
    @include vertical_center_left;
    margin-top:  8em;
    margin-left: calc(100% / 2 - 150px);
  }

  .quarter-select {
    margin-left: 1em;
  }

  .el-input {
    width: 220px;
  }
}
</style>
