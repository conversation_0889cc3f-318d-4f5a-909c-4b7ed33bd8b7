<template>
  <div v-loading="loading">
    <h3>同时包含公募与专户产品账号忽略列表设置</h3>
    <el-form
      ref="form"
      label-width="0px"
      :inline="true"
    >
      <el-form-item
        label=""
      >
        <div class="form-item-container">
          <system-account-select
            v-model="selectAccount"
            :clearable="true"
            :system-id="31"
          />
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          size="small"
          type="primary"
          @click="addOperatorAccount()"
        >
          添加
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="operatorAccounts"
      border
      style="width: 100%"
    >
      <el-table-column
        prop="code"
        label="账号编码"
      />
      <el-table-column
        prop="name"
        label="账号名称"
      />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            @click="deleteOperatorAccount(scope.row.code)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import SystemAccountSelect from '@/components/common/SystemAccountSelect.vue'

export default {
  components: {
    SystemAccountSelect
  },
  data () {
    return {
      loading:          false,
      selectAccount:    '',
      operatorAccounts: []
    }
  },
  created () {
    this.getOperatorAccounts()
  },
  methods: {
    getOperatorAccounts () {
      this.loading = true
      this.$axios.get('/api/jiaoyi_temporary_permission/fund_type_ignore_accounts')
        .then(response => {
          this.operatorAccounts = response.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    addOperatorAccount () {
      if (!this.selectAccount) return this.$message.error('请选择账号')
      if (this.operatorAccounts.map(x => x.code).includes(this.selectAccount)) return this.$message.info('该账号已存在')
      const theParams = {
        code: this.selectAccount
      }
      this.$axios.post('/api/jiaoyi_temporary_permission/add_fund_type_ignore_account', theParams)
        .then(response => {
          this.$message.success('添加成功')
          this.getOperatorAccounts()
        })
        .catch(() => {
        })
    },
    deleteOperatorAccount (code) {
      this.$axios.delete(`/api/jiaoyi_temporary_permission/del_fund_type_ignore_account/${code}`)
        .then(response => {
          this.$message.success('删除成功')
          this.getOperatorAccounts()
        })
        .catch(() => {
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.form-item-container {
  @include vertical_center_left;
  height: 40px;
}
</style>
