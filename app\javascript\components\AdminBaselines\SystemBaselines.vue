<template>
  <div>
    <div class="tool-bar">
      <div class="left">
        <baseline-search-tool-box
          v-if="displaySearch"
          @change="handleSearch"
        />
      </div>
      <div class="right">
        <el-dropdown>
          <el-button
            type="primary"
            size="small"
          >
            系统基线操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown" size="small">
            <el-dropdown-item
              :disabled="!$store.getters.hasPermission('system_baseline.edit')"
              @click.native="createBaseline"
            >
              创建系统基线
            </el-dropdown-item>
            <el-dropdown-item
              @click.native="mergeBaselines"
            >
              合并系统基线
            </el-dropdown-item>
            <baseline-upload
              :system-id="systemId"
              class="tool"
              @update="getBaselines"
            />
            <baseline-export
              :system-id="systemId"
              :filter="filter"
              :baseline-ids="origin_baseline_ids"
              class="tool"
            />
            <baseline-list-export
              :system-id="systemId"
              :filter="filter"
              :baseline-ids="origin_baseline_ids"
              class="tool"
            />
          </el-dropdown-menu>
        </el-dropdown>

<!--         <el-button
          type="primary"
          size="small"
          :disabled="!$store.getters.hasPermission('system_baseline.edit')"
          @click="createBaseline"
        >
          创建系统基线
        </el-button>

        <el-button
            type="primary"
            size="small"
            @click="mergeBaselines"
        >
          合并系统基线
        </el-button>

        <baseline-export
          :system-id="systemId"
          class="tool"
        />
        <baseline-list-export
          :system-id="systemId"
          class="tool"
        />

        <baseline-upload
          :system-id="systemId"
          class="tool"
          @update="getBaselines"
        />
 -->
      </div>
    </div>

    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-table
      v-loading="loading"
      :data="baselines"
      border
      class="baseline-table"
      row-key="id"
      ref="baselineTables"
      @select-all="selectAll"
      @select="handleSelectionChange"
      @sort-change="sortMethod"
    >
      <el-table-column
         :reserve-selection="true"
         type="selection"
         width="55">
      </el-table-column>
      <el-table-column
        prop="id"
        label="基线 ID"
        sortable="custom"
        width="100"
      />
      <el-table-column
        prop="name"
        sortable="custom"
        label="基线名称"
      />
      <el-table-column
        prop="department_full_name"
        label="部门名称"
      />
      <el-table-column
        prop="accounts_count"
        label="已关联账号数量"
        width="120"
      >
        <template slot-scope="scope">
          <template v-if="scope.row.accounts_count > 0">
            <el-link type="primary" @click="handleAccountList(scope.row)">{{scope.row.accounts_count}}</el-link>
          </template>
          <template v-else>
            {{scope.row.accounts_count}}
          </template>
        </template>
      </el-table-column>
      <el-table-column
          prop="is_merge"
          label="是否是合并基线"
          width="120"
      >
        <template slot-scope="scope">
          {{ scope.row.is_merge ? '是' : '' }}
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        header-align="center"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            :disabled="!$store.getters.hasPermission('system_baseline.edit')"
            @click="editBaseline(scope.row)"
          >
            编辑基线
          </el-button>
          <el-button
              size="mini"
              @click="showHistory(scope.row)"
          >
            差异对比
          </el-button>
          <el-button
            size="mini"
            type="danger"
            :disabled="!$store.getters.hasPermission('system_baseline.delete')"
            @click="deleteConfirm(scope.row)"
          >
            删除基线
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-size="pageSize"
      :total="count"
      :current-page.sync="page"
      background
      layout="total, prev, pager, next, jumper"
      class="pagination"
      @current-change="getBaselines"
    />
    <!-- eslint-enable vue/attribute-hyphenation -->
    <system-baseline-edit
      ref="baselineCreate"
      :system-id="systemId"
      :baseline="currentBaseline"
      mode="create"
      @change="getBaselines"
    />
    <system-baseline-edit
      ref="baselineEdit"
      :system-id="systemId"
      :baseline="currentBaseline"
      mode="update"
      @change="getBaselines"
    />
    <system-baseline-history
        ref="baselineHistory"
        :system-id="systemId"
        :baseline="currentBaseline"
        @closeDialog="handleCloseDialog"
    >
    </system-baseline-history>

    <account-list
      ref="accountList"
      :account-ids="currentAccountIds"
      :businessSystemId="currentBusinessSystemId"
    />
    <merge-system-baseline-form
        ref="mergeSystemBaselineForm"
        :origin-baseline-ids="origin_baseline_ids"
        :system-id="systemId"
        @change="getBaselines"
        @resetSelection="handleResetSelection"
    />
  </div>
</template>

<script>
import BaselineUpload from './BaselineUpload.vue'
import BaselineExport from './BaselineExport.vue'
import BaselineListExport from './BaselineListExport.vue'
import BaselineSearchToolBox from './BaselineSearchToolBox.vue'
import SystemBaselineEdit from './SystemBaselineEdit.vue'
import SystemBaselineHistory from "./SystemBaselineHistory.vue";
import AccountList from '@/components/AccountList';
import MergeSystemBaselineForm from "./MergeSystemBaselineForm";

export default {
  components: {
    BaselineSearchToolBox,
    BaselineUpload,
    BaselineExport,
    BaselineListExport,
    SystemBaselineEdit,
    SystemBaselineHistory,
    AccountList,
    MergeSystemBaselineForm
  },
  props: {
    systemId: {
      type: Number,
      required: true
    },
    displaySearch: {
      type: Boolean,
      default: () => true
    }
  },
  data () {
    return {
      loading: false,
      baselines: [],
      count: 0,
      page: 1,
      pageSize: 25,
      filter: {
        showBaselineLinks: null, // 只显示未关联账号的系统基线
        property: 'baseline_id',
        value: null
      },
      currentBaseline: {
        id:   null,
        name: null
      },
      currentAccountIds: [],
      currentBusinessSystemId: 0,
      origin_baseline_ids: []
    }
  },
  watch: {
    systemId() {
      this.getBaselines() // 岗位基线调用组件后，需要切换系统
      this.handleResetSelection()
    }
  },
  created () {
    this.getBaselines()
  },
  methods: {
    sortMethod (val) {
      const sortParam = {
        sort_name: val.prop,
        sort_type: val.order
      }
      this.getBaselines(sortParam)
    },
    handleAccountList(row) {
      this.currentAccountIds = row.accounts_ids
      this.currentBusinessSystemId = row.business_system_id
      this.$refs.accountList.visible = true
    },
    getBaselines (sortParam = {}) {
      this.loading = true
      let theParams = {
        page:     this.page,
        per_page: this.pageSize,
        filter:   this.filter
      }
      Object.assign(theParams, sortParam)
      this.$axios.get(`/api/systems/${this.systemId}/baselines`, { params: theParams })
        .then(response => {
          this.loading   = false
          this.baselines = response.data.data
          this.count     = response.data.count
        })
        .catch(() => {
          this.loading = false
          this.baselines = [{ name: 'Read data error' }]
        })
    },
    deleteConfirm (baseline) {
      let notice = '是否确认删除该系统基线?'
      if (baseline.accounts_count > 0) {
        notice = `该系统基线目前关联了${baseline.accounts_count}个账号，是否确认删除该系统基线?`
      }
      this.$confirm(notice, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteBaseline(baseline.id)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    deleteBaseline (id) {
      this.loading = true

      this.$axios.delete(`/api/systems/${this.systemId}/baselines/${id}`)
        .then(response => {
          this.loading = false
          this.getBaselines()
          this.$message.success('删除系统基线成功')
        })
        .catch(() => {
          this.loading = false
        })
    },
    editBaseline (row) {
      this.$refs.baselineEdit.visible = true
      this.currentBaseline = row
    },
    showHistory (row) {
      this.currentBaseline = row
      this.$refs.baselineHistory.visible = true
    },
    createBaseline () {
      this.$refs.baselineCreate.visible = true
    },
    handleSearch (payload) {
      this.filter = payload
      this.page = 1
      this.getBaselines()
    },
    handleCloseDialog() {
      this.getBaselines()
    },
    selectAll (selection) {
      this.origin_baseline_ids = selection.map(x => x.id)
    },
    handleSelectionChange(selections) {
      this.origin_baseline_ids = selections.map(x => x.id)
    },
    mergeBaselines () {
      if(this.origin_baseline_ids.length === 0) {
        this.$message.error('请勾选需要合并的基线')
        return
      }
      this.$refs.mergeSystemBaselineForm.visible = true
    },
    handleResetSelection () {
      this.$refs.baselineTables.clearSelection()
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .tool-bar{
    @include vertical_top_between;
    margin-top: 5px;
    margin-bottom: 20px;

    .right{
      @include vertical_center_right;
      margin-top: 4px;
    }

    .tool{
      margin-left: 10px;
    }
  }
  .baseline-table{
    margin-top: 20px;
    border-top: 1px solid #EBEEF5;
  }
  .pagination{
    margin-top: 20px;
  }
  .baseline-table-expand {
    line-height: 40px;
    font-weight: 800;

    .account-no-link{
      height: 60px;
      font-size: 14px;
      color: #a9a7b1;
      line-height: 60px;
      text-align: center;
    }
    .label{
      width: 90px;
    }
    .content{
      color: #99a9bf;
    }
  }

</style>
