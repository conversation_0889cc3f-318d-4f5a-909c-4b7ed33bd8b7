import axios from '@/settings/axios'
import { parseFileName, downloadBlob   } from './tool'

export const list = (params) => {
  return axios.get('/admin_api/dimission_account_files', { params: params })
}

export const create = (params) => {
  return axios.post('/admin_api/dimission_account_files', params)
}

export const destroy = (id) => {
  return axios.delete(`/admin_api/dimission_account_files/${id}`)
}

export const download = (id) => {
  return axios.get(`/admin_api/dimission_account_files/${id}/download`,{
    responseType: 'blob'
  })
    .then(response => {
      const fileName = parseFileName(response, 'dimission_accounts.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
export const downloadTemplate = () => {
  return axios.get(`/admin_api/dimission_account_files/download_template`,{
    responseType: 'blob'
  })
    .then(response => {
      const fileName = parseFileName(response, 'dimission_account_template.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}