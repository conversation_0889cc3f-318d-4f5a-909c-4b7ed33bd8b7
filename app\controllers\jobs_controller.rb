# frozen_string_literal: true

# 岗位
class JobsController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!, except: %i[list show]
  before_action :set_job, only: %i[show update destroy users]
  after_action :sync_update_user_position, only: %i[update]
  after_action :sync_destroy_user_position, only: %i[destroy]

  # 系统岗位管理，用于岗位表单下拉框
  # 默认是只获取部门权限下的岗位
  def index
    jobs = Job.job_list(current_admin, tree_mode?, without_permission?)
    json_respond data: jobs, count: jobs.count
  end

  def show
    json_respond @job.output_detail
  end

  def create
    job = Job.new(jobs_params)

    if job.save
      audit_log! job
      json_respond(success: true, data: job.output_detail)
    else
      audit_log! job, action: :create_fail
      json_respond(success: false, error_message: job.errors.full_messages.join('; '))
    end
  end

  def update
    before_job = @job.output_detail
    if @job.update(jobs_params)
      audit_log! [@job, before_job, @job.output_detail]
      json_respond(success: true, data: @job.output_detail)
    else
      audit_log! @job, action: :update_fail
      json_respond(success: false, error_message: @job.errors.full_messages.join('; '))
    end
  end

  def destroy
    @user_ids = @job.users.pluck(:id)
    @job.destroy
    audit_log! @job
    json_respond(success: true)
  end

  # 返回岗位基线列表
  def list
    filter = params[:filter] ? JSON.parse(params[:filter], symbolize_names: true) : {}
    per_page = params[:pageSize] || 25
    search_params = {}
    search_params[:department_id_eq] = filter[:department_id] if filter[:department_id].present?
    search_params[:department_id_in] = filter[:department_ids] if filter[:department_ids].present?
    search_params[:name_cont] = filter[:job_name] if filter[:job_name].present?
    # 这里需要判断系统基线是否存在，否则会出现系统基线为空，岗位基线存在，但是依然算作关联基线
    search_params[:job_baseline_id_null] = 1 if filter[:showJobBaselineLinks] == '0'
    search_params[:job_baseline_id_null] = 0 if filter[:showJobBaselineLinks] == '1'
    search_params[:inservice_true] = 1 if filter[:inservice].to_s == 'true'
    search_params[:inservice_true] = 0 if filter[:inservice].to_s == 'false'
    jobs = current_admin.all_departments_query? ? Job : Job.where(department_id: current_admin.departments_in_query.pluck(:id))
    jobs = jobs
             .includes(:department, job_baseline: :system_baselines)
             .ransack(search_params)
             .result
             .distinct
    children_job_ids = Job.where(ancestry_depth: 1, ancestry: jobs.ids.map(&:to_s)).ids
    jobs = Job.where(id: (jobs.ids + children_job_ids))
              .page(params[:page])
              .per(per_page)
              .order(ancestry: :asc, name: :asc)
    quarter = Quarter.find(params[:quarter_id])
    data = jobs.map do |x|
      filter = { job_id: x.id }
      account_filter = {}
      count = User::SearchQuarterUsers.new(quarter, 1, 0, current_admin, filter, account_filter)
                .send(:search_quarter_users)
                .count
      x.output_detail.merge(user_count: count, full_name: x.full_name)
    end
    json_respond data: data, count: jobs.total_count
  end

  # 返回岗位员工，用于岗位员工管理
  def users
    job_users = @job.users.includes(%i[department]).uniq
    users = User.includes(%i[department]).where.not(id: job_users.pluck(:id)).order_pinyin
    department_tree_ids = @job.department&.tree&.pluck(:id) || []
    json_respond job: @job.output.merge(department_tree_ids: department_tree_ids), users: users.map(&:output), job_users: job_users.map(&:output)
  end

  protected

  def set_job
    @job = Job.find(params[:id])
  end

  def jobs_params
    params.require(:job).permit(:code, :name, :inservice, :department_id, :parent_id)
  end

  def authenticate_policy!
    authorize nil, policy_class: JobPolicy
  end

  def without_permission?
    params[:without_permission].present? && params[:without_permission] == 'true'
  end

  def tree_mode?
    params[:mode] == 'tree'
  end

  # 同步更新员工职务
  def sync_update_user_position
    user_ids = @job.users.pluck(:id)
    return if user_ids.blank?

    UserPositionJob.perform_later(user_ids)
  end

  def sync_destroy_user_position
    return if @user_ids.blank?

    UserPositionJob.perform_later(@user_ids)
  end
end
