<template>
  <div>
    <div class="filter-bar">
      <filter-bar
        v-show="filterable"
        :filter-data="tableData"
        :table-schema="tableSchema"
        :filter-method="filterCustomMethod"
        @change="afterFilterCustom"
      />
    </div>
    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-table
      :data="dataByPage"
      :row-style="rowStyle"
      :border="border"
      :size="tableSize"
    >
      <el-table-column
        v-for="schema in tableSchema"
        :key="schema.property"
        :property="schema.property"
        :label="schema.label"
        :width="schema.label === '菜单代码' ? 150 : ''"
      >
        <template slot-scope="scope">
          <div v-if="schema.is_show_custom">
            <div v-if="schema.property === 'comment' && scope.row.comment">
              <div v-if="scope.row.comment.is_temp">
                <div style="font-weight: 800;">临时权限</div>
                <div>授权人：{{ scope.row.comment.authorizer }}</div>
                <div>开始日期：{{ scope.row.comment.start_date }}</div>
                <div>结束日期：{{ scope.row.comment.end_date }}</div>
              </div>
            </div>
            <div v-if="schema.property === 'temp_info' && scope.row.temp_info">
              <div v-if="scope.row.temp_info.is_temp">
                <div style="font-weight: 800;">临时权限</div>
                <div>授权人：{{ scope.row.temp_info.authorizer }}</div>
                <div>开始日期：{{ scope.row.temp_info.start_date }}</div>
                <div>结束日期：{{ scope.row.temp_info.end_date }}</div>
              </div>
            </div>
          </div>
          <div v-else>
            {{ scope.row[scope.column.property] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tableType === 'baseline' && isNowBaseline && !disableButton"
        label="操作"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="handleUpdateComfirm(scope.row, scope.$index)"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="mini"
            @click="handleDestroyComfirm(scope.row, scope.$index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :background="background"
      :small="small"
      :page-size="pageSize"
      :total="tableDataAfterFilter.length"
      :current-page.sync="currentPage"
      hide-on-single-page
      layout="prev, pager, next"
    />
    <!-- eslint-enable vue/attribute-hyphenation -->
  </div>
</template>

<script>
import FilterBar from './FilterBar.vue'
export default {
  components: {
    FilterBar
  },
  filters: {
    lf_to_br (value) {
      return value
    }
  },
  props: {
    tableType: {
      type: String,
      default: ''
    },
    tableData: {
      type: Array,
      default: () => []
    },
    isNowBaseline: {
      type: Boolean,
      default: false
    },
    dataKey: {
      type: String,
      default: ''
    },
    tableSchema: {
      type: Array,
      required: true
    },
    tableSize: {
      type: String,
      default: ''
    },
    filterable: {
      type: Boolean,
      default: false
    },
    filterCustomMethod: {
      validator: function (val) {
        return val === null || typeof val === 'string'
      },
      default: null
    },
    pageSize: {
      type: Number,
      default: 10
    },
    border: {
      type: Boolean,
      default: false
    },
    background: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    addedRow: {
      type: Boolean,
      default: false
    },
    reduceRow: {
      type: Boolean,
      default: false
    },
    disableButton: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      currentPage: 1,
      tableDataAfterFilter: []
    }
  },
  computed: {
    dataByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.tableDataAfterFilter.slice(start, end)
    },
    rowStyle () {
      if (this.addedRow) {
        return this.addedStyle
      }
      if (this.reduceRow) {
        return this.reduceStyle
      }
    }
  },
  methods: {
    addedStyle () {
      return { backgroundColor: '#F0F9EB' }
    },
    reduceStyle () {
      return { backgroundColor: '#FFEEEE' }
    },
    afterFilterCustom (payload) {
      this.tableDataAfterFilter = payload
    },
    handleUpdateComfirm (row, index) {
      this.$emit('showUpdateBaseline', row, this.dataKey, index)
    },
    handleDestroyComfirm (row, index) {
      this.$confirm('确认删除系统基线权限吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        this.destroyBaseline(row, index)
      })
      .catch(() => {
        this.$message.info('已取消操作')
      })
    },
    destroyBaseline (row, index) {
      const dataIndex = this.pageSize * (this.currentPage - 1) + index
      this.$emit('destroyBaseline', row, this.dataKey, dataIndex)
    }
  }
}
</script>

<style scoped>
  .el-pagination{
    margin-top: 1em;
  }
</style>
