<template>
  <div
    v-loading="loading"
    class="container"
  >
    <el-form
      ref="form"
      label-width="140px"
      :model="{emails: emailSettings}"
    >
      <el-form-item
        label="通知设置"
      >
        <el-button
          size="small"
          @click="createNewEmail"
        >
          添加
        </el-button>
      </el-form-item>
      <transition-group name="list-complete">
        <div
          v-for="(item, index) in emailSettings"
          :key="index"
          class="list-complete-item"
        >
          <el-form-item
            :prop="`emails.${index}.email`"
            :label="`邮箱 ${index + 1}`"
            :rules="rules.email"
            class="form-item-first-line"
          >
            <el-input
              v-model="emailSettings[index].email"
              size="small"
            />
            <el-button
              size="small"
              @click="handleDeleteEmail(index)"
            >
              删除
            </el-button>
          </el-form-item>
          <el-form-item
            :prop="`emails.${index}.copy_to`"
            label="抄送"
            class="form-item-first-line"
          >
            <el-select
              v-model="emailSettings[index].copy_to"
              multiple
              clearable
              filterable
              allow-create
              default-first-option
              size="small"
              style="width: 260px;"
            >
              <el-option
                v-for="admin in admins"
                :key="admin.id"
                :label="admin.email"
                :value="admin.email"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :prop="'emails.' + index + '.days'"
            :rules="rules.days"
            label="离职天数"
            class="form-item-second-line"
          >
            <el-input-number
              v-model="emailSettings[index].days"
              placeholder="请添加天数"
              size="small"
              :min="1"
              label="描述文字"
            />
          </el-form-item>
          <el-divider />
        </div>
      </transition-group>
      <el-form-item>
        <el-button
          type="primary"
          size="small"
          @click="submitForm()"
        >
          更新设置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>

export default {
  data () {
    return {
      loading: false,
      emailSettings: [],
      admins: [],
      rules: {
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email',  message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        copy_to: [],
        days: [
          { required: true, message: '请设置离职天数', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getNotificationSettings()
    this.gettingAdmins()
  },
  methods: {
    getNotificationSettings () {
      this.loading = true
      this.$axios.get('admin_api/settings/leave_user_serious_notification')
        .then(response => {
          this.loading       = false
          this.emailSettings = response.data.emails
          if (!this.emailSettings) this.emailSettings = []
        })
        .catch(() => {
          this.loading = false
        })
    },
    updateNotificationConfirm () {
      this.$confirm('此操作将更新离职员工账号关闭告警升级设置，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.updateNotificationSettings()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    updateNotificationSettings () {
      this.$axios.put('admin_api/settings/update_leave_user_serious_notification', {
        leave_user_serious_notification: {
          emails: this.emailSettings
        }
      })
        .then(() => {
          this.$message.success('更新通知设置完成')
          this.getNotificationSettings()
        })
        .catch(() => {
          this.$message.error('更新通知设置失败')
        })
    },
    gettingAdmins () {
      this.$axios.get('/admin_api/admin_accounts')
        .then(response => {
          this.admins = response.data
        })
        .catch(_ => {})
    },
    submitForm () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.updateNotificationConfirm()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    createNewEmail () {
      const itemData = { email: '', days: null }
      const length   = this.emailSettings.length
      this.$set(this.emailSettings, length, itemData)
    },
    handleDeleteEmail (index) {
      this.$delete(this.emailSettings, index)
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .form-item-first-line {
    // MARK: 为了把表单验证的字露出来
    margin-bottom: 14px;

    .el-input {
      width: 200px;
    }
  }

  .error {
    color: #E54D42;
    font-weight: bold;
  }
</style>
