import axios                           from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const list = (params = {}) => {
  return axios.get(`/api/external/ecerts`, { params: params })
}

export const externalSystemNames = (params = {}) => {
  return axios.get(`/api/external/ecerts/system_names`, { params: params })
}

export const getAccountSettings = () => {
  return axios.get(`/api/external/ecerts/account_settings`)
}

export const update = (id, params) => {
  return axios.put(`/api/external/ecerts/${id}`, { params: params })
}

export const destroy = (id) => {
  return axios.delete(`/api/external/ecerts/${id}`)
}

export const exportExcel = (params = {}) => {
  return axios.get(`/api/external/ecerts/export_excel`, { responseType: 'blob', params: params})
      .then(response => {
        const fileName = parseFileName(response, 'exportEcerts.xlsx')
        downloadBlob(response.data, fileName)
      })
      .catch(error => {
        throw error
      })
}
