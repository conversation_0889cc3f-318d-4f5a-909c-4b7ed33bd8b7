<template>
  <div class="container">
    <h2>系统用户管理</h2>
    <hr>
    <el-tabs
      v-model="activeName"
      type="border-card"
    >
      <el-tab-pane
        :disabled="!adminUsersPermission"
        label="用户管理"
        name="first"
      >
        <admins-manage v-if="activeName === 'first'" />
      </el-tab-pane>
      <el-tab-pane
        :disabled="!adminRolesPermission"
        label="角色管理"
        name="second"
      >
        <roles-manage v-if="activeName === 'second'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import AdminsManage from './AdminsManage'
import RolesManage from './RolesManage'

export default {
  components: {
    AdminsManage,
    RolesManage
  },
  data () {
    return {
      activeName: this.getActiveName()
    }
  },
  computed: {
    adminUsersPermission () {
      return this.$store.getters.hasPermission('admin_account_manager.query')
    },
    adminRolesPermission () {
      return this.$store.getters.hasPermission('admin_account_manager.roles_query')
    }
  },
  methods: {
    getActiveName () {
      if (this.$store.getters.hasPermission('admin_account_manager.query')) {
        return 'first'
      }
      if (
        this.$store.getters.hasPermission('admin_account_manager.roles_query')
      ) {
        return 'second'
      }
      this.$message.error('您没有权限操作此功能')
      return ''
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding:          2em;
  background-color: white;
  min-width:        800px;
}
</style>
