<template>
  <el-form inline>
    <el-form-item
      label="业务系统"
      :required="systemRequired"
    >
      <el-select
        v-model="localSystem"
        filterable
        :clearable="clearable"
        :size="size"
        placeholder="选择业务系统"
        style="width: 200px;"
      >
        <el-option
          v-for="system in systemWithRoles"
          :key="system.id"
          :value="system.id"
          :label="system.name"
        />
      </el-select>
    </el-form-item>
    <el-form-item
      v-if="showAccount"
      label="账号"
    >
      <el-select
        v-model="localAccount"
        filterable
        :clearable="clearable"
        :disabled="disabledAccountSelect"
        :placeholder="placeholderAccount"
        :size="size"
        style="width: 200px;"
      >
        <el-option
          v-for="account in systemAccounts"
          :key="account.code"
          :value="account.code"
          :label="account.name"
        />
      </el-select>
    </el-form-item>
    <el-form-item
      label="角色"
      :required="roleRequired"
    >
      <el-select
        v-model="localRole"
        filterable
        :clearable="clearable"
        :multiple="multiple"
        :disabled="disabledRoleSelect"
        :placeholder="placeholderRole"
        :size="size"
        style="width: 200px;"
      >
        <el-option
          v-for="role in systemRoles"
          :key="role.code"
          :value="role.code"
          :label="role.name"
        />
      </el-select>
    </el-form-item>
  </el-form>
</template>
<script>
import API               from '@/api'

export default {
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'normal'
    },
    clearable: {
      type: Boolean,
      deafult: false
    },
    showAccount: {
      type: Boolean,
      default: false
    },
    systemRequired: {
      type: Boolean,
      default: false
    },
    roleRequired: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      loading:             false,
      localSystem:         '',
      localRole:           [],
      localAccount:        '',
      systemRoles:         [],
      systemAccounts:      [],
      systemWithRoles:     [],
      systemWithAccounts:  [],
      placeholderRole:     '请先选择系统',
      placeholderAccount:  '请先选择系统',
      disabledRoleSelect:  true,
      disabledAccountSelect: true
    }
  },
  watch: {
    localSystem (val) {
      this.setRoleSelect(val)
      this.setAccountSelect(val)
      this.$emit('change', { system_id: val, role_code: this.localRole, account_code: this. localAccount})
    },
    localRole (val) {
      this.$emit('change', { system_id: this.localSystem, role_code: val, account_code: this. localAccount })
    },
    localAccount (val) {
      this.$emit('change', { system_id: this.localSystem, role_code: this.localRole, account_code: val })
    }
  },
  created () {
    this.gettingSystemRoles()
    if (this.showAccount) {
      this.gettingSystemAccounts()
    }
  },
  methods: {
    gettingSystemRoles () {
      this.loading = true
      API.systemRoleConflicts.systemRoles()
        .then(response => {
          this.loading = false
          this.systemWithRoles = response.data
          this.systemRoles = response.data.find(x => x.id === this.localSystem).roles
        })
        .catch(_ => {
          this.loading = false
        })
    },
    gettingSystemAccounts () {
      this.loading = true
      API.systemRoleConflicts.systemAccounts()
        .then(response => {
          this.loading = false
          this.systemWithAccounts = response.data
          this.systemAccounts = response.data.find(x => x.id === this.localSystem).accounts
        })
        .catch(_ => {
          this.loading = false
        })
    },
    setRoleSelect (val) {
      this.localRole = ''
      if (val) {
        this.placeholderRole = '选择角色'
        this.disabledRoleSelect = false
        this.systemRoles = this.systemWithRoles.find(x => x.id === val).roles
      } else {
        this.placeholderRole = '请先选择系统'
        this.disabledRoleSelect = true
        this.systemRoles = []
      }
    },
    setAccountSelect(val) {
      if (this.showAccount) {
        this.localAccount = ''
        if (val) {
          this.placeholderAccount = '选择账号'
          this.disabledAccountSelect = false
          this.systemAccounts = this.systemWithAccounts.find(x => x.id === this.localSystem).accounts
        } else {
          this.placeholderAccount = '请先选择系统'
          this.disabledAccountSelect = true
          this.systemAccounts = []
        }
      }
    }
  }
}
</script>
