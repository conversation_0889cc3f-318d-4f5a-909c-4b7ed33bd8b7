<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    title="修改用户信息"
    width="50%"
    append-to-body
    @close="dialogClose"
  >
    <el-form ref="edit_user" :model="accountForm" :rules="rules2" label-width="120px" status-icon class="new-form">
      <el-form-item label="姓名" prop="name">
        <div class="name-flex">
          <el-input v-model="accountForm.name" auto-complete="off" />
        </div>
      </el-form-item>
      <el-form-item prop="email" label="邮箱">
        <div class="name-flex">
          <el-input v-model="accountForm.email" auto-complete="off" />
        </div>
      </el-form-item>
      <el-form-item label="员工编号" prop="code">
        <div class="name-flex">
          <el-input v-model="accountForm.code" auto-complete="off" />
        </div>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <div class="name-flex">
          <el-input v-model="accountForm.mobile" auto-complete="off" />
        </div>
      </el-form-item>
      <el-form-item label="绑定员工" prop="user_id">
        <div class="name-flex">
          <el-cascader v-model="accountForm.user_id" style="width: 100%" :options="userTree" clearable filterable />
        </div>
      </el-form-item>
      <el-form-item label="设置默认首页" prop="default_home_page">
        <el-select
          v-model="accountForm.default_home_page"
          clearable
          filterable
          placeholder="请选择首页"
          class="name-flex"
        >
          <el-option
            v-for="item in admin_routes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <div class="name-flex">
          <div>
            <el-checkbox v-model="accountForm.allow_notification">接收邮件通知</el-checkbox>
            <el-checkbox v-if="isSmsNotificationEnable" v-model="accountForm.allow_sms_notification">
              接收短信通知
            </el-checkbox>
          </div>
          <el-button type="primary" style="margin-left:10px;" @click="handleUpdateName">
            保存用户信息
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <hr />
    <el-form
      ref="edit_user_password"
      :model="accountForm"
      :rules="rules2"
      label-width="120px"
      status-icon
      class="new-form"
    >
      <el-form-item prop="pass" label="重置密码">
        <el-input
          v-model="accountForm.pass"
          type="password"
          auto-complete="off"
          placeholder="设置新的密码"
          style="margin-bottom:1em;"
        />
      </el-form-item>
      <el-form-item prop="checkPass" label="确认密码">
        <div class="name-flex">
          <el-input v-model="accountForm.checkPass" type="password" auto-complete="off" placeholder="重新输入密码" />
        </div>
      </el-form-item>
      <div class="button-bar">
        <el-button
          v-show="showAccountBind"
          :disabled="!$store.getters.hasPermission('admin_account_manager.set_permissions')"
          @click="permissionOperatorSet()"
        >
          业务系统账号绑定
        </el-button>
        <el-button
          :disabled="user.status !== 'locked' || !$store.getters.hasPermission('admin_account_manager.admin')"
          type="primary"
          @click="unlockAccount"
        >
          解锁用户
        </el-button>
        <el-button
          type="primary"
          style="margin-left:10px;"
          :disabled="!$store.getters.hasPermission('admin_account_manager.password')"
          @click="handleUpdatePass"
        >
          重置密码
        </el-button>
      </div>
    </el-form>
    <permission-operator ref="permissionOperator" :user="currentAccount" />
  </el-dialog>
</template>

<script>
import API from '@/api'
import { validatePhone } from '@/utils/form_validates'
import permissionOperator from './permissionOperator.vue'
import UserSelect from '@/components/common/UserSelect.vue'
import RegexpTool from "../../utils/regexp_tool";

export default {
  components: {
    UserSelect,
    permissionOperator
  },
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  data() {
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.accountForm.pass) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    const rules = {
      name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      code: [{ required: true, message: '请输入员工编号', trigger: 'blur' }],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
      ],
      mobile: [{ validator: validatePhone, trigger: 'blur' }],
      pass: [{ validator: this.generateValidatePass(), trigger: 'blur' }],
      checkPass: [{ validator: validatePass2, trigger: 'blur' }]
    }
    return {
      bind_user_id: '',
      accountForm: {
        name: '',
        email: '',
        code: '',
        mobile: '',
        allow_notification: false,
        allow_sms_notification: false,
        pass: '',
        checkPass: '',
        user_id: '',
        default_home_page: ''
      },
      rules2: rules,
      dialogVisible: false,
      currentAccount: {},
      admin_routes: [],
      security_rule: {}
    }
  },
  computed: {
    isSmsNotificationEnable() {
      return this.$settings.showSmsNotification
    },
    showAccountBind() {
      return ['default', 'zyfd', 'zyfd_test'].includes(this.$settings.customerId)
    },
    userTree () {
      return this.$store.state.user_tree
    },
    // 如果开启强制绑定员工 并且 没有开启'无限制授权'
    isBindUser () {
      return this.$settings.adminBindUser.enable && !this.$store.getters.hasPermission('admin_account_manager.all_permission')
    }
  },
  watch: {
    user() {
      this.userInitialize()
    }
  },
  created() {
    this.userInitialize()
    this.initSecurityRule()
    this.initUserIdRule()
  },
  methods: {
    permissionOperatorSet() {
      this.currentAccount = this.user
      this.$refs.permissionOperator.dialogVisible = true
    },
    dialogClose() {
      this.dialogVisible = false
      this.clearInitPass()
    },
    clearInitPass() {
      this.accountForm.pass = ''
      this.accountForm.checkPass = ''
    },
    userInitialize() {
      this.accountForm.name = this.user.name
      this.accountForm.email = this.user.email
      this.accountForm.code = this.user.code
      this.accountForm.mobile = this.user.mobile
      this.accountForm.allow_notification = this.user.allow_notification
      this.accountForm.allow_sms_notification = this.user.allow_sms_notification
      this.accountForm.user_id = this.user.user_id
      this.accountForm.default_home_page = this.user.default_home_page
      this.admin_routes = this.user.admin_routes
    },
    handleUpdateName() {
      // 修改账号名称
      this.$refs.edit_user.validate(valid => {
        if (valid) {
          if (Array.isArray(this.accountForm.user_id)) {
            this.bind_user_id = this.accountForm.user_id.slice(-1).toString()
          } else {
            this.bind_user_id = this.accountForm.user_id
          }
          this.$axios
            .put('/admin_api/admin_accounts/' + this.user.id, {
              admin: {
                name: this.accountForm.name,
                email: this.accountForm.email,
                code: this.accountForm.code,
                allow_notification: this.accountForm.allow_notification,
                allow_sms_notification: this.accountForm.allow_sms_notification,
                mobile: this.accountForm.mobile,
                default_home_page: this.accountForm.default_home_page,
                user_id: this.bind_user_id
              }
            })
            .then(response => {
              if (response.status === 200) {
                this.$message.success('设置已修改')
                // this.accountForm.name = ''
                this.$emit('update')
              }
            })
            .catch(_ => {})
        }
      })
    },
    handleUpdatePass() {
      this.$refs.edit_user_password.validate(valid => {
        if (valid) {
          this.$axios
            .post('/admin_api/admin_accounts/' + this.user.id + '/change_password', {
              password: this.accountForm.pass,
              password_confirmation: this.accountForm.checkPass
            })
            .then(response => {
              if (response.status === 200) {
                // this.$refs.accountForm.resetFields()
                this.$message.success('修改密码成功')
              }
            })
            .catch(_ => {})
        }
      })
    },
    unlockAccount() {
      // 解锁账号
      this.$axios
        .post('/admin_api/admin_accounts/' + this.user.id + '/unlock')
        .then(response => {
          this.$emit('update')
          this.$message.success(`解锁${this.user.name}账号成功`)
        })
        .catch(_ => {})
    },
    initSecurityRule () {
      this.$axios.get(`/api/aas_security`)
        .then(res => {
          this.security_rule = res.data
          this.rules2.pass = [{ validator: this.generateValidatePass(), trigger: 'blur' }]
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 强制绑定员工
    initUserIdRule () {
      if (this.isBindUser) {
        this.rules2.user_id = [
          { required: true, message: '请绑定员工', trigger: 'change' }
        ]
      }
    },
    generateValidatePass () {
      const validatePassFunction = (rule, value, callback) => {
        if(!this.security_rule.password_complexity) return
        const password_min_length = this.security_rule.password_min_length
        const password_max_length = this.security_rule.password_max_length
        const upper = this.security_rule.password_complexity.upper
        const lower = this.security_rule.password_complexity.lower
        const digit = this.security_rule.password_complexity.digit
        const symbol = this.security_rule.password_complexity.symbol
        switch(true) {
          case value === '':
            callback(new Error('请输入密码'))
            break
          case (password_min_length && value.length < 8):
            callback(new Error(`密码长度最短为 ${password_min_length} 位`))
            break
          case (password_max_length && value.length > 128):
            callback(new Error(`密码长度最长为 ${password_max_length} 位`))
            break
          case (upper && !RegexpTool.matchUpper(value, upper)):
            callback(new Error(`必须包含至少${upper}个大写字母`))
            break
          case (lower && !RegexpTool.matchLower(value, lower)):
            callback(new Error(`必须包含至少${lower}个小写字母`))
            break
          case (digit && !RegexpTool.matchDigit(value, digit)):
            callback(new Error(`必须包含至少${digit}个数字`))
            break
          case (symbol && !RegexpTool.matchPunct(value, symbol)):
            callback(new Error(`必须包含至少${symbol}个特殊符号（!"#$%&\'()*+,-./:;<=>?@[\\]^_‘{|}~)`))
            break
          default:
            if (this.accountForm.checkPass !== '') {
              this.$refs.edit_user.validateField('checkPass')
            }
            callback()
            break
        }
      }
      return validatePassFunction
    }
  }
}
</script>

<style lang="scss">
.el-dialog__header {
  .el-dialog__headerbtn {
    .el-dialog__close {
      &:hover {
        animation: rotatefresh 1s;
      }
    }
  }
}

@keyframes rotatefresh {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
    transition: all 0.6s;
  }
}
</style>

<style lang="scss" scoped>
@import '~@/components/variables';

.new-form {
  margin-right: 3em;
}

.new-input {
  width: 100%;
}

.name-flex {
  @include vertical_center_between;
  height: 40px;
  width: 100%;
}

.button-bar {
  @include vertical_center_right;
  height: 40px;
  width: 100%;
}
</style>
