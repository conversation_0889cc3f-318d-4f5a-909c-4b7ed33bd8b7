#!/usr/bin/env ruby

puts "最终语法检查..."

begin
  # 读取文件内容
  content = File.read('config/initializers/start_sidekiq.rb')
  
  # 尝试编译
  RubyVM::InstructionSequence.compile(content)
  
  puts "✅ 语法检查通过！文件可以正常解析。"
  
  # 额外检查：统计关键字
  lines = content.split("\n")
  
  module_count = 0
  def_count = 0
  class_count = 0
  begin_count = 0
  if_count = 0
  unless_count = 0
  case_count = 0
  while_count = 0
  until_count = 0
  for_count = 0
  do_count = 0
  end_count = 0
  
  lines.each do |line|
    stripped = line.strip
    next if stripped.start_with?('#') || stripped.empty?
    
    module_count += 1 if stripped =~ /^module\b/
    def_count += 1 if stripped =~ /^def\b/
    class_count += 1 if stripped =~ /^class\b/
    begin_count += 1 if stripped =~ /^begin\b/
    if_count += 1 if stripped =~ /^if\b/
    unless_count += 1 if stripped =~ /^unless\b/
    case_count += 1 if stripped =~ /^case\b/
    while_count += 1 if stripped =~ /^while\b/
    until_count += 1 if stripped =~ /^until\b/
    for_count += 1 if stripped =~ /^for\b/
    do_count += 1 if stripped =~ /\bdo\s*(\|.*\|)?\s*$/
    end_count += 1 if stripped == 'end'
  end
  
  need_end = module_count + def_count + class_count + begin_count + if_count + unless_count + case_count + while_count + until_count + for_count + do_count
  
  puts "\n关键字统计:"
  puts "module: #{module_count}"
  puts "def: #{def_count}"
  puts "class: #{class_count}"
  puts "begin: #{begin_count}"
  puts "if: #{if_count}"
  puts "unless: #{unless_count}"
  puts "case: #{case_count}"
  puts "while: #{while_count}"
  puts "until: #{until_count}"
  puts "for: #{for_count}"
  puts "do: #{do_count}"
  puts "end: #{end_count}"
  puts "\n需要 end: #{need_end}"
  puts "实际 end: #{end_count}"
  
  if need_end == end_count
    puts "✅ 关键字匹配正确！"
  else
    puts "⚠️  关键字数量不匹配，但语法仍然正确（可能有其他结构）"
  end
  
rescue SyntaxError => e
  puts "❌ 语法错误："
  puts e.message
  
  # 尝试找到错误行
  if e.message =~ /\(eval\):(\d+)/
    error_line = $1.to_i
    puts "\n错误位置: 第 #{error_line} 行"
    
    lines = content.split("\n")
    start_line = [error_line - 3, 0].max
    end_line = [error_line + 2, lines.length - 1].min
    
    puts "\n错误附近的代码:"
    (start_line..end_line).each do |i|
      marker = (i + 1 == error_line) ? ">>> " : "    "
      puts "#{marker}#{i + 1}: #{lines[i]}"
    end
  end
  
rescue => e
  puts "❌ 其他错误："
  puts e.message
  puts e.backtrace.first(3)
end
