# frozen_string_literal: true

# 告警审计日志
class GlobalAlertAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '告警中心'
    super
  end

  def batch_deal
    set_operation_category
    @operation = '批量处理告警'
    @comment   = "批量处理告警ids：#{params[0]}, 修改内容：「#{params[1]}」"
    create_audit_log
  end

  private

  def set_operation_category
    @operation_category = '告警中心'
  end
end
