<template>
  <div class="container">
    <div class="tool-bar">
      <el-select
        v-model="accountSelect"
        filterable
        placeholder="请选择系统账号"
        style="width: 300px"
      >
        <el-option
          v-for="item in accounts"
          :key="item.account_code"
          :value="item.account_code"
          :label="item.account_name"
        />
      </el-select>
      <el-button
        @click="createNewAccount"
      >
        添加
      </el-button>

      <div class="accounts-container">
        <el-form
          ref="form"
          labelWidth="160px"
          label-position="left"
          :model="{settings: localSettings}"
        >

          <transition-group name="list-complete">
            <el-form-item
              v-for="(account, index) in localSettings"
              :key="account.code"
              :label="accountName(account.code)"
              :prop="`settings.${index}.permission_codes`"
              :rules="{ required: true, message: '权限不能为空', trigger: 'blur' }"
              class="list-complete-item"
            >
              <div class="form-item">
                <el-checkbox-group
                  v-model="account.permission_codes"
                  style="height: 40px;"
                  >
                  <el-checkbox label="1">查询</el-checkbox>
                  <el-checkbox label="2">操作</el-checkbox>
                  <el-checkbox label="3">复核</el-checkbox>
                  <el-checkbox label="4">审核</el-checkbox>
                </el-checkbox-group>
                <el-button
                  type="text"
                  style="margin-left: 50px; height: 40px;"
                  @click="handleRemoveAccount(account.code)"
                >
                  删除此账号
                </el-button>

              </div>
            </el-form-item>
          </transition-group>
        </el-form>

        <el-button
          type="primary"
          style="margin-top: 20px;"
          @click="submitForm('form')"
        >
          保存
        </el-button>
      </div>

    </div>

  </div>
</template>

<script>
export default {
  props: {
    index: {
      type: Number,
      required: true
    },
    accounts: {
      type: Array,
      default: () => []
    },
    settings: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      accountSelect: null,
      localSettings: this.settings
    }
  },
  watch: {
    settings () {
      this.localSettings = this.settings
    }
  },
  methods: {
    createNewAccount () {
      let data  = { code: this.accountSelect, permission_codes: [] }
      let exist = this.localSettings.find(x => x.code === this.accountSelect)

      if (exist) {
        this.$message.warning('该账号已添加到列表')
        return
      }

      this.localSettings.push(data)
    },
    accountName (code) {
      let account = this.accounts.find(x => x.account_code === code)
      if (!account) return '已禁用账号'

      return account.account_name
    },
    handleRemoveAccount (code) {
      let index = this.settings.findIndex(x => x.code === code)
      this.settings.splice(index, 1)
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('update', {
            index: this.index,
            settings: this.localSettings
          })
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";
  .container{
    padding: 20px;
  }
  .accounts-container{
    margin-top: 20px;
  }
  .form-item{
    @include vertical_center_left;
  }
</style>
