# frozen_string_literal: true

# 后台账号管理审计日志
class BusinessSystemPermissionAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '系统权限模块'
    @user = user
    @params = params
    super
  end

  def permission_abnormals_export
    @operation_category = "权限异常"
    @operation = '数据导出'
    @comment   = "#{@user&.name} 导出了「#{@params[0]&.name}-#{@params[1]&.name}」权限异常数据"
    create_audit_log
  end

  def set_reject
    @operation_category = "告警剔除"
    @operation = '设置剔除规则'
    @comment   = "#{@user&.name} 设置了「#{@params[0]&.name}」剔除规则 ：「#{@params[1].output}」"
    create_audit_log
  end

  def destroy_reject
    @operation_category = "告警剔除"
    @operation = '删除剔除规则'
    @comment   = "#{@user&.name} 删除「#{@params[0]&.name}」剔除规则 ：「#{@params[1].output}」"
    create_audit_log
  end

  def set_tags
    @operation_category = "权限标签"
    @operation = '设置标签'
    @comment   = "#{@user&.name} 设置「#{@params[0]&.name}}」 权限：「#{@params[1]}」 标签为： 「#{@params[2]}」 "
    create_audit_log
  end

  private

end
