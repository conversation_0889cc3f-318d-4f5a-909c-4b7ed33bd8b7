import axios                           from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const list = (systemId, params = {}) => {
  return axios.get(`/api/external/business_systems/${systemId}/accounts`, { params: params })
}

export const detail = (systemId, id) => {
  return axios.get(`/api/external/business_systems/${systemId}/accounts/${id}`)
}

export const update = (systemId, id, params = {}) => {
  return axios.put(`/api/external/business_systems/${systemId}/accounts/${id}`, params)
}

export const create = (systemId, params = {}) => {
  return axios.post(`/api/external/business_systems/${systemId}/accounts`, params)
}

export const destroy = (systemId, id) => {
  return axios.delete(`/api/external/business_systems/${systemId}/accounts/${id}`)
}

export const destroyBatch = (systemId, ids) => {
  let params = { ids: ids}
  return axios.delete(`/api/external/business_systems/${systemId}/accounts/destroy_batch`, { params: params })
}

export const updateRoles = (systemId, id,  params = {}) => {
  return axios.put(`/api/external/business_systems/${systemId}/accounts/${id}/update_roles`, params)
}

export const updatePermissions = (systemId, id,  params = {}) => {
  return axios.put(`/api/external/business_systems/${systemId}/accounts/${id}/update_permissions`, params)
}

export const exportExcel = (systemId, params = {}) => {
  return axios.get(`/api/external/business_systems/${systemId}/accounts/export`, { responseType: 'blob', params: params })
    .then(response => {
      const fileName = parseFileName(response, 'exportAccounts.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

export const exportAccountAndPermissions = (systemId, params = {}) => {
  return axios.get(`/api/external/business_systems/${systemId}/accounts/export_account_and_permissions`, { responseType: 'blob', params: params })
    .then(response => {
      const fileName = parseFileName(response, '账号授权权限表.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
