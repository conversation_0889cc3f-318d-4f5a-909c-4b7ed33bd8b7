class PublicAccountsController < ApplicationController
  before_action :authenticate_admin!
  before_action :set_business_system
  before_action :set_account, only: %i[update destroy]

  def index
    authorize PublicAccount.new(system_id: @business_system.id)
    json_respond PublicAccount.in_system(@business_system.id).order_pinyin.map(&:output)
  end

  def all_accounts
    authorize PublicAccount.new(system_id: @business_system.id)
    last_quarter = Quarter.last
    raise QuarterNotFound unless last_quarter

    public_accounts      = PublicAccount.in_system(@business_system.id).to_a
    account_unique_field = @business_system.account_unique_field
    all_accounts =
      @business_system
        .account_class
        .where(quarter_id: last_quarter.id)
        .map do |a|
        public_account = public_accounts.find { |x| x.send(account_unique_field) == a.send(account_unique_field) }
        is_public = public_account ? true : false

        {
          id:        a.id,
          code:      a.respond_to?(:code) ? a.code : nil,
          name:      a.name,
          is_public: is_public,
          status:    a.status,
          ledger_status: a.user_id.present?
        }
      end
    json_respond all_accounts
  rescue QuarterNotFound => _e
    json_custom_respond 404, {}
  end

  def create
    authorize PublicAccount.new(system_id: @business_system.id)
    result = PublicAccountServices::CreatePublicAccount.new(@business_system, public_account_params).execute
    if result[:is_success]
      PublicAccount.where(id: result[:success_account_ids]).each do |public_account|
        audit_log! public_account.to_audit_log
      end
      json_respond success: true
    else
      json_custom_respond(:unprocessable_entity, error_message: result[:message])
    end
  end

  def update
    authorize @account
    if @account.update(public_account_params.except(:user_id))
      @account.bind_user_by_ledger(public_account_params[:user_id])
      audit_log! @account.to_audit_log(public_account_params[:user_id])
      json_respond @account.output
    else
      json_custom_respond(:unprocessable_entity, error_message: @account.errors.full_messages.join('; '))
    end
  end

  def destroy
    authorize @account
    audit_log! @account.to_audit_log
    @account.destroy
    json_respond_no_content
  end

  private

  def public_account_params
    params.require(:public_account).permit(:system_id, :valid_date, :remark, :type_id, :user_id, used_user_ids: [], code: [], name: [])
  end

  def set_account
    @account = PublicAccount.find(params[:id])
  rescue StandardError => e
    json_custom_respond(404, error_message: e.message)
  end
end
