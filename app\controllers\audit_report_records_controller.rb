class AuditReportRecordsController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!, except: %i[list preview]
  before_action :audit_report_record, only: %i[destroy show create_report download]
  before_action :create_parameter, only: %i[create]

  def list
    records = AuditReportExportRecord.all.map(&:output)

    json_respond records
  end

  def create
    @record = AuditReportExportRecord.new(
      admin_id:  current_admin.id,
      unique:    @unique,
      start_at:  @start_at,
      end_at:    @end_at,
      alert_ids: @category_ids
    )

    if @record.save
      @record.create_details(@system_ids)
      audit_log! @record
      json_respond @record.output
    else
      json_respond(success: false, message: @record.errors.full_messages.join('; '))
    end
  end

  def edit_detail
    params = request.params&.[]('audit_report_record')

    detail = AuditReportSystemDetail.find(params&.[]('id'))
    detail.fill = true
    detail.context = { system_detail: params&.[]('system_detail') }
    detail.conform_remind1 = params&.[]('conform_remind1')
    detail.conform_remind2 = params&.[]('conform_remind2')
    detail.conform_remind3 = params&.[]('conform_remind3')

    if detail.save
      audit_log! detail, action: :verify_anomalies
      audit_log! detail
    else
      json_respond(success: false, message: detail.errors.full_messages.join('; '))
    end
  rescue StandardError => e
    json_respond(success: false, message: e.message)
  end

  def show
    maintain_system_ids = current_admin.business_systems_in_maintain.pluck(:id)
    json_respond @record.details.where(system_id: maintain_system_ids).map(&:output)
  end

  def destroy
    @record.destroy
    audit_log! @record
    json_respond_no_content
  end

  def create_report
    report_variable

    job = AuditExportJob.perform_later(@date_range, @system_ids, @category_ids, @department_ids, @unique)
    @job_id = job.provider_job_id

    audit_log! ({ date_range: @date_range, system_names: @systems.map(&:name), download_url: @download_url, job_id: @job_id, record: @record }), action: :create_report_success
    json_respond(success: true, url: @download_url, job_id: @job_id, message: I18n.t('audit_report.audit_job.success'))
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! ({ date_range: @date_range, system_names: @systems.map(&:name) }), action: :create_report_faild
    json_respond(success: false, error_message: e.message)
  end

  # 已经生成的，显示报告内容，返回显示模板内容
  def preview
    unique = params[:unique]
    file_path = File.join(DataExport.download_tmp_path, "_#{unique}", "#{I18n.t('audit_report.title')}.docx")

    return send_file_compatible_with_msie file_path if File.exist? file_path

    send_file_compatible_with_msie File.join(Setting.data_path&.[]('template'), "#{Setting.customer_id}_audit.docx")
  end

  def download
    if @record.details.map(&:output_simple).to_json == params.permit(insert_context_params)&.[]('insertContext').to_json
      audit_zip
    else
      json_custom_respond(404, error_message: '系统异常原因已修改，需要重新生成报告！')
    end
  end

  def download_audit_zip
    @unique = params[:unique]
    audit_zip
  end

  def audit_zip
    if File.exist? File.join(DataExport.download_tmp_path, "#{@unique}.rar")
      json_respond(success: true, download_url: "/api/download_batch_zip?unique=#{@unique}")
    else
      json_respond(success: false, error_message: '文件未创建，请先生成报告')
    end
  end

  private

  def authenticate_policy!
    authorize nil, policy_class: AuditReportRecordPolicy
  end

  def insert_context_params
    { insertContext: [
      :id,
      :record_id,
      :system_id,
      :name,
      :fill,
      { system_detail: %i[key title status content] }
    ] }
  end

  def audit_report_record
    @unique = params[:unique]
    @record = AuditReportExportRecord.find_by!(unique: @unique)
  rescue StandardError => e
    json_custom_respond(404, error_message: e.message)
  end

  def report_variable
    @system_ids     = @record.details.pluck(:system_id)
    @systems        = BusinessSystem.where(id: @system_ids)
    @category_ids   = @record.alert_ids
    @unique         = "#{@record.unique}"
    @download_url   = "/api/download_batch_zip?unique=#{@unique}"
    @date_range     = [@record.start_at.to_s, @record.end_at.to_s]
  end

  def create_parameter
    @unique         = "#{current_admin.id}_#{Time.now.to_i}"
    @system_ids     = params[:system_ids].empty? ? current_admin.business_systems_in_maintain.pluck(:id) : params[:system_ids]
    @category_ids   = params[:category_ids].empty? ? current_admin.alert_categories_in_maintain.pluck(:id) : params[:category_ids]
    @start_at       = params[:date_range].first
    @end_at         = params[:date_range].last
    @department_ids = params[:department_ids]
  end
end
