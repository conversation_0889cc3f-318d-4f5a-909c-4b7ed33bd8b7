<template>
  <span>
    <span v-show="status === 'not_found'">
      <i class="el-icon-warning" />
      未导入
    </span>
    <span v-show="status === 'pending'">
      <i class="el-icon-time" />
      准备导入
    </span>
    <span v-show="status === 'processing'">
      <i class="el-icon-loading" />
      正在导入
    </span>
    <span v-show="status === 'success'">
      <i class="el-icon-success" />
      导入成功
    </span>
    <span v-show="status === 'failed'">
      <i class="el-icon-error" />
      导入失败
    </span>
  </span>
</template>

<script>
export default {
  props: {
    status: {
      type:     String,
      required: false
    }
  }
}
</script>

<style scoped>
  @import './status.scss';
</style>
