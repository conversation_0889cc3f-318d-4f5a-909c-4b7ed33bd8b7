<template>
  <div
    v-loading="loading"
    class="container"
  >
    <div
      v-if="enableOperator"
    >
      <el-form
        ref="form"
        size="small"
        :inline="true"
      >
        <el-form-item>
          <el-select
            v-model="position"
            value-key="id"
            filterable
            placeholder="职务"
            class="form-item"
          >
            <el-option
              v-for="position in userPostions"
              :key="position"
              :label="position"
              :value="position"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="search()"
          >
            搜索
          </el-button>
          <el-button
            type="primary"
            :disabled="!$store.getters.hasPermission('position_roles_check.manage')"
            @click="handleCreate()"
          >
            添加
          </el-button>
          <el-button
            type="primary"
            :disabled="!$store.getters.hasPermission('position_roles_check.manage')"
            @click="handleSystemSetting()"
          >
            告警系统设置
          </el-button>
        </el-form-item>

        <el-form-item
          class="float-right"
        >
          <el-button
            :disabled="!$store.getters.hasPermission('position_roles_check.query')"
            @click="goRuleAlarm('position_roles_check')"
          >
            {{ routeButton }}
          </el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="permissionRules"
        border
        style="width: 100%"
      >
        <el-table-column
          fixed="left"
          prop="position"
          label="职务"
        />
        <el-table-column
          v-for="system in systems"
          :key="system['id']"
          :prop="String(system['id'])"
          :label="system['name']"
          min-width="150"
        />
        <el-table-column
          fixed="right"
          label="操作"
          width="150"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              :disabled="!$store.getters.hasPermission('position_roles_check.manage')"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>

            <el-button
              size="mini"
              type="danger"
              :disabled="!$store.getters.hasPermission('position_roles_check.manage')"
              @click="handleUnlinkComfirm(scope.row.position)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <system-setting-form
        ref="settingForm"
        :account="currentAccount"
        :systems="systems"
        :mode="formType"
        @update="accountsRolesList"
        @accountsRolesList="accountsRolesList"
      />
      <system-account-form
        ref="accountForm"
        :account="currentAccount"
        :systems="systems"
        :mode="formType"
        @update="accountsRolesList"
        @accountsRolesList="accountsRolesList"
      />
    </div>
  </div>
</template>
<script>
import API from '@/api'
import SystemAccountSelect from '@/components/common/SystemAccountSelect.vue'
import SystemAccountForm from '@/components/AdminSettings/PermissionRules/SystemAccountForm'
import SystemSettingForm from '@/components/AdminSettings/PermissionRules/SystemSettingForm'

export default {
  components: {
    SystemAccountSelect,
    SystemAccountForm,
    SystemSettingForm
  },
  data () {
    return {
      routeButton: this.$t('position_role_check.module_title'),
      userPostions: '',
      position: '',
      formType: 'create',
      selectRuls: '',
      loading: false,
      permissionRules: [],
      currentAccount: null,
      systems: []
    }
  },
  computed: {
    enableOperator () {
      return this.$settings.positionRoleCheck.enable
    }
  },
  created () {
    this.getSystemList()
  },
  methods: {
    goRuleAlarm (router_name) {
      this.$router.push({ name: router_name })
    },
    searchUserPositions(){
      this.$axios.get(`api/position_role_checks/users_positions`)
        .then(response => {
          this.userPostions = response.data.positions
        })
        .catch(error => {
          if (error.status === 404) Message.notFoundQuarter()
        })
    },
    getSystemList () {
      API.systems.inserviceSystems()
        .then(response => {
          this.systems = response.data
          this.accountsRolesList()
        })
    },
    search () {
      this.accountsRolesList(this.position)
    },
    accountsRolesList (position) {
      this.searchUserPositions()
      this.loading = true
      const theParams = {
        position: this.position
      }
      this.$axios.get('api/position_role_checks/accounts_roles_list', { params: theParams })
        .then(response => {
          this.permissionRules = response.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleCreate () {
      this.formType = 'create'
      this.currentAccount = null
      this.$refs.accountForm.visible = true
    },
    handleSystemSetting () {
      this.$refs.settingForm.visible = true
    },
    handleEdit (row) {
      this.formType = 'edit'
      this.currentAccount = row
      this.$refs.accountForm.visible = true
    },
    handleUnlinkComfirm (position) {
      this.$confirm(`将移除职务「${position}」在各系统中的角色, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deletePositionSystemRoles(position)
        })
    },
    deletePositionSystemRoles (position) {
      this.$axios.delete(`api/position_role_checks/delete_all_data_for_position?position=${position}`)
        .then(response => {
          this.$message.success('删除成功')
          this.accountsRolesList()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.form-item-container {
  @include vertical_center_left;
  height: 40px;
}
.float-right{
  float: right;
}
</style>
