<template>
  <el-dialog
    :visible.sync="localVisible"
    title="关联系统基线"
    width="30%"
    append-to-body
    v-loading="loading"
  >
    <el-form>
      <el-form-item
          label="选择系统基线"
          label-width="160"
      >
        <el-select
            v-model="baselineId"
            filterable
            size="small"
            placeholder="请选择"
            :disabled="followJob"
        >
          <el-option
              v-for="baseline in baselines"
              :key="baseline.id"
              :label="baseline.name"
              :value="baseline.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
          label="跟随岗位基线"
          label-width="160"
      >
        <el-radio-group v-model="followJob">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="localVisible = false">取 消</el-button>
      <el-button
        type="primary"
        @click="handleComfirm"
      >
        确 定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    systemId: {
      type: Number,
      required: true
    },
    visible: {
      type: Boolean,
      required: true
    },
    accounts: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      localVisible: false,
      accountCode: null,
      baselineId: null,
      baselines: [],
      followJob: true,
      loading: false
    }
  },
  watch: {
    visible () {
      if (this.visible) {
        this.localVisible = true
        this.setData()
        this.getBaselines()
      }
    },
    followJob () {
      if (this.followJob) {
        this.baselineId = null
      }
    },
    localVisible () {
      this.$emit('update:visible', this.localVisible)
    }
  },
  created () {
    this.getBaselines()
  },
  methods: {
    setData() {
      if (this.accounts.length >= 1) {
        this.accountCode = this.accounts[0].account_code
        this.baselineId  = this.accounts[0].baseline_id
        this.followJob   = this.accounts[0].follow_job
      }
      else {
        this.accountCode = null
        this.baselineId  = null
        this.followJob   = true
      }
    },
    getBaselines () {
      this.$axios.get(`/api/systems/${this.systemId}/baselines/all`)
        .then(response => {
          this.baselines = response.data
        })
        .catch(() => {})
    },
    handleComfirm () {
      this.updateLink()
    },
    updateLink () {
      this.loading = true
      //if (!this.baselineId) { return this.$message.warning('请选择系统基线') }
      this.$axios.put(`/api/systems/${this.systemId}/accounts_with_baseline/link`, {
        account_codes: this.accounts.map(item => item.account_code),
        baseline_id: this.baselineId,
        follow_job:  this.followJob
      })
        .then(response => {
          this.localVisible = false
          this.loading = false
          this.$message.success('关联系统基线成功')
          this.$emit('change')
          this.$EventBus.$emit('account_info:change', response.data);
        })
        .catch(() => {})
    }
  }
}
</script>
