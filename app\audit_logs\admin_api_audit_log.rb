# frozen_string_literal: true

# 员工信息管理
class AdminApiAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = 'Excel 数据导入'
    super
  end

  def ghost_login_success
    login_category
    @comment = "「#{user.name}」使用「模拟登录」功能成功登录「#{params.name}」的账号"
    create_audit_log
  end

  def ghost_login_failed
    login_category
    @comment = "「#{user.name}」使用「模拟登录」功能失败"
    create_audit_log
  end

  def users_regenerate_export_files
    regenerate_operation_category
    @operation = '员工数据文件生成'
    @comment   = '成功重新生成了所有员工的审核表、权限列表文件'
    create_audit_log
  end

  def user_upload
    upload_operation_category
    @operation = '文件上传'
    @comment   = "成功上传文件「#{params.original_filename}」"
    create_audit_log
  end

  def user_upload_failed
    upload_operation_category
    @operation = '文件上传'
    @comment   = "文件「#{params.original_filename}」上传失败"
    create_audit_log
  end

  def import_user_data
    import_operation_category
    @operation = '文件导入'
    @comment   = "成功导入文件「#{params.original_filename}」"
    create_audit_log
  end

  def import_user_data_failed
    import_operation_category
    @operation = '文件导入'
    @comment   = "导入文件「#{params.original_filename}」失败"
    create_audit_log
  end

  def delete_uploaded_data
    delete_file_category
    @operation = '文件删除'
    @comment   = "成功从 #{params['quarter_name']} => #{params['business_system_name']} 删除文件「#{params['file_name']}」"
    create_audit_log
  end

  def import_upload_data
    import_operation_category
    @operation = '系统数据导入'
    @comment   = "导入数据文件至 #{params['quarter_name']} => #{params['the_system_name']}"
    create_audit_log
  end

  def department_create
    department_category
    @operation = '创建部门'
    @comment   = "创建部门「#{params.name}」成功"
    create_audit_log
  end

  def department_create_failed
    department_category
    @operation = '创建部门'
    @comment   = "创建部门「#{params.name}」失败"
    create_audit_log
  end

  def department_update
    department_category
    @operation = '更新部门'
    @comment   = "成功修改 ID 为「#{params.id}」的部门名称为「#{params.name}」"
    create_audit_log
  end

  def department_update_failed
    department_category
    @operation = '更新部门'
    @comment   = "修改 ID 为「#{params.id}」的部门名称失败"
    create_audit_log
  end

  def department_destroy
    department_category
    @operation = '部门删除'
    @comment   = "成功删除部门「#{params.name}」"
    create_audit_log
  end

  def upload_system_data
    import_operation_category
    @operation = '文件导入'
    @comment   = params['message'] || "成功导入文件「#{params['file'].original_filename}」至 #{params['quarter_name']} => #{params['business_system_name']}"
    create_audit_log
  end

  def upload_business_system_data_failed
    import_operation_category
    @operation = '文件导入'
    @comment   = "导入文件「#{params['file'].original_filename}」失败"
    create_audit_log
  end

  private

  def upload_operation_category
    @operation_category = '上传数据管理'
  end

  def import_operation_category
    @operation_category = I18n.t('module_name.quarters')
  end

  def regenerate_operation_category
    @operation_category = '生成员工数据文件'
  end

  def delete_file_category
    @operation_category = '删除数据'
  end

  def department_category
    @operation_category = '部门管理'
  end

  def login_category
    @operation_category = '用户登录'
    @operation_module   = '模拟登录'
    @operation          = '模拟登录'
  end
end
