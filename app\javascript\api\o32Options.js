import axios from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const index = (params = {}) => {
  return axios.get('/api/o32_options', { params: params })
}

export const searchCategories = (params = {}) => {
  return axios.get('/api/o32_options/search_categories', { params: params })
}

export const exportData = (params = {}) => {
  return axios.get(`/api/o32_options/export`, { responseType: 'blob', params: params })
    .then(response => {
      const fileName = parseFileName(response, 'o32_option_list.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}