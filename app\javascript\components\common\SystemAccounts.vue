<template>
  <div>
    <el-row class="normal-system-account">
      <el-col
        :span="20"
        align="left"
      >
        <span>正常账号：</span>
      </el-col>
      <el-col
        :span="4"
        align="right"
      >
        {{ normalAccountCount }}
      </el-col>
    </el-row>
    <el-row class="disable-system-account">
      <el-col
        :span="20"
        align="left"
      >
        <span>禁用账号：</span>
      </el-col>
      <el-col
        :span="4"
        align="right"
      >
        {{ disableAccountCount }}
      </el-col>
    </el-row>
    <el-row class="data-overdue-account">
      <el-col
        :span="20"
        align="left"
      >
        <span>数据已过期：</span>
      </el-col>
      <el-col
        :span="4"
        align="right"
      >
        {{ overdueAccountCount }}
      </el-col>
    </el-row>
    <el-row class="all-system-account">
      <el-col
        :span="20"
        align="left"
      >
        <span>未对接系统：</span>
      </el-col>
      <el-col
        :span="4"
        align="right"
      >
        {{ allSystemInfoCount }}
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  props: {
    accounts: {
      type: Array,
      required: true
    },
    allSystemInfoCount: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {

    }
  },
  computed: {
    normalAccountCount () {
      return this.accounts ? this.accounts.filter(x => x.account.status === true && !x.account.overdue_status).map(x => x.system).length : 0
    },
    disableAccountCount () {
      return this.accounts ? this.accounts.filter(x => x.account.status === false && !x.account.overdue_status).map(x => x.system).length : 0
    },
    overdueAccountCount () {
      return this.accounts ? this.accounts.filter(x => x.account.overdue_status).map(x => x.system).length : 0
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/components/variables";

.data-overdue-account {
  color: #e49218;
  margin-top: 10px;
}

.all-system-account {
  color: #909399;
  margin-top: 10px;
}

.normal-system-account {
  color: #409EFF;
  margin-top: 10px;
}

.disable-system-account {
  color: #F56C6C;
  margin-top: 10px;
}
</style>
