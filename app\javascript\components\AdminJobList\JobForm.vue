<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
  >
    <!-- :loading='loading' -->
    <el-form
      v-loading='loading'
      ref="form"
      class="form"
      :rules="rules"
      :model="params"
      label-width="100px"
    >
      <el-form-item
        prop="department_id"
        label="部门"
        style="width: 96%;"
      >
        <el-cascader
          v-model="params.department_id"
          v-loading='loadingDepartments'
          element-loading-spinner="el-icon-loading"
          :options="treeDepartments"
          :props="cascaderDepartmentProps"
          filterable
          clearable
          style="width: 515px"
        >
        </el-cascader>
      </el-form-item>

      <el-form-item
        prop="parent_id"
        label="上级岗位"
        style="width: 96%;"
      >
        <el-cascader
          v-model="params.parent_id"
          v-loading='loadingJobs'
          element-loading-spinner="el-icon-loading"
          :options="treeJobs"
          :props="cascaderJobProps"
          filterable
          clearable
          style="width: 515px"
        >
        </el-cascader>
      </el-form-item>

      <el-form-item
        label="编码"
        prop="code"
        style="width: 96%;"
      >
        <el-input
          v-model="params.code"
          placeholder="岗位编码"
        />
      </el-form-item>

      <el-form-item
        label="名称"
        prop="name"
        style="width: 96%;"
      >
        <el-input
          v-model="params.name"
          placeholder="岗位名称"
        />
      </el-form-item>

      <el-form-item
        label="状态"
        prop="inservice"
      >
        <el-radio-group v-model="params.inservice">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          @click="handleSubmit()"
        >
          确 定
        </el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import API from '@/api'
export default {
  components: {
  },
  props: {
    mode: {
      type: String,
      default: 'create'
    },
    job: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      loading: false,
      loadingDepartments: false,
      loadingJobs: false,
      visible: false,
      params: {
        id: null,
        code: null,
        name: null,
        inservice: true,
        department_id: null,
        parent_id: null
      },
      rules: {
        code: [
          { required: true, message: '岗位编码不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '岗位名称不能为空', trigger: 'blur' }
        ]
      },
      treeDepartments: [],
      treeJobs: [],
      cascaderDepartmentProps: {
        emitPath: false,
        checkStrictly: true
      },
      cascaderJobProps: {
        checkStrictly: true,
        emitPath: false
      }
    }
  },
  computed: {
    title () {
      if (this.mode === 'create') return '创建岗位'
      if (this.mode === 'update') return '编辑岗位'
    }
  },
  watch: {
    visible () {
      if (this.visible) {
        this.resetForm()
        this.initForm()
      }
    },
    'params.department_id'(newVal, oldVal) {
      this.getTreeJobs()
    }
  },
  created () {
    this.getTreeDepartments()
  },
  methods: {
    initForm () {
      if (this.mode === 'update') {
        this.loading = true
        API.jobs.detail(this.job.id)
          .then(response => {
            this.loading = false
            this.params = response.data
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    handleSubmit () {
      this.$refs.form.validate((valid) => {
        if (!valid) return false
        if (this.mode === 'create') {
          this.createForm()
        } else {
          this.updateForm()
        }
      })
    },
    createForm () {
      const params = { job: this.params }
      API.jobs.create(params)
        .then(response => {
          if (response.data.success) {
            this.$message.success('创建成功')
            this.$emit('change')
            this.closeDialog()
          } else {
            this.$message.error(response.data.error_message)
          }
        })
        .catch(() => {})
    },
    updateForm () {
      const params = { job: this.params }
      API.jobs.update(this.job.id, params)
        .then(response => {
          if (response.data.success) {
            this.$message.success('更新成功')
            this.$emit('change')
            this.closeDialog()
          } else {
            this.$message.error(response.data.error_message)
          }
        })
        .catch(() => {})
    },
    closeDialog () {
      this.visible = false
    },
    resetForm () {
      // if (this.mode === 'create') {
        if (this.$refs.form) this.$refs.form.resetFields()
      // }
    },
    getTreeDepartments () {
      const params = { mode: 'tree' }
      this.loadingDepartments = true
      API.departments.index(params)
        .then(response => {
          this.loadingDepartments = false
          this.treeDepartments = response.data
        })
        .catch(() => {
          this.loadingDepartments = false
        })
    },
    getTreeJobs () {
      const params = {}
      if (this.params.department_id === null) return
      this.loadingJobs = true

      API.departments.jobs(this.params.department_id, params)
        .then(response => {
          this.loadingJobs = false
          this.treeJobs = response.data.data
          this.count = response.data.count
        })
        .catch(() => {
          this.loadingJobs = false
        })
    }

  }
}
</script>

<style lang="scss" scoped>
  .error{
    color: #E54D42;
    font-weight: bold;
    margin-bottom: 15px;
    padding-left: 50px;
  }
  .container {
    padding: 2em;
    background-color: white;
    min-width: 800px;
    .form {
      padding-right: 20px;
    }
  }
</style>
