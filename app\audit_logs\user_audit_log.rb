# frozen_string_literal: true

# 用户操作审计
class UserAuditLog < ApplicationAuditLog

  def initialize(user, request, params)
    @operation_module = '员工信息管理'
    @operation_category = '员工管理'
    super
  end

  def update
    @operation          = '修改员工'
    @comment            = "修改了员工「#{params.name}」的信息：#{params.to_audit_log}"
    create_audit_log
  end

  def create
    @operation          = '创建员工'
    @comment            = "创建了员工「#{params.name}」的信息：#{params.to_audit_log}"
    create_audit_log
  end

  def batch_update
    update
  end

  def export_success
    @operation          = '员工数据导出'
    @comment            = "员工数据导出成功"
    create_audit_log
  end

  def export_faild
    @operation          = '员工数据导出'
    @comment            = "员工数据导出失败"
    create_audit_log
  end

  def accounts_export_success
    @operation          = '员工权限导出'
    @comment            = "成功导出「#{params[:quarter_name]}, #{params[:user_name]}」的「#{params[:account_names].join(",")}」权限数据"
    create_audit_log
  end

  def accounts_export_faild
    @operation          = '员工权限导出'
    @comment            = "导出「#{params[:quarter_name]}, #{params[:user_name]}」的「#{params[:account_names].join(",")}」权限数据失败"
    create_audit_log
  end

  def accounts_list_export_success
    @operation          = '员工账号列表导出'
    @comment            = "成功导出#{params[:quarter_name]}, #{params[:department_name]}员工#{params[:user_name]}的业务系统账号列表数据"
    create_audit_log
  end

  def accounts_list_export_faild
    @operation          = '员工账号列表导出'
    @comment            = "#{params[:department_name]}员工#{params[:user_name]}的#{params[:quarter_name]}业务系统账号列表数据导出失败"
    create_audit_log
  end
end
