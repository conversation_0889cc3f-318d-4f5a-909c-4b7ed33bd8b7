class OaRecordAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = 'OA流程临时权限稽核管理'
    @operation_category = 'OA流程临时权限稽核管理'
    super
  end

  def update
    @operation = '编辑OA流程临时权限'
    before_status = params[0][:status_text]
    after_status = params[1][:status_text]
    before_remark = params[0][:remark]
    after_remark = params[1][:remark]
    title = params[1][:title]
    return if before_status == after_status && before_remark == after_remark

    str = "ID为「#{params[1][:id]}」的OA临时权限"
    str += "「#{title}」" if title.present?
    comments = [str]
    comments << "进行了#{after_status}操作" if before_status != after_status
    if before_remark != after_remark
      operation = before_remark.blank? ? '添加' : '更新'
      comments << "#{operation}了备注内容 #{after_remark}"
    end
    @comment = comments.join('，')
    create_audit_log
  end
end
