<template>
  <el-dialog
      :visible.sync="visible"
      title="合并系统基线"
      width="960px"
      append-to-body
      :close-on-click-modal="false"
      v-loading="loading"
  >
    <div class="dialog-container">
      <el-form
          ref="localForm"
          :model="localBaseline"
          label-width="100px"
      >
        <el-form-item
            label="合并类型"
        >
          <el-radio-group v-model="localBaseline.merge_type">
            <el-radio :label="1">创建新基线</el-radio>
            <el-radio :label="2">合并至指定基线</el-radio>
          </el-radio-group>
        </el-form-item>
        <template
            v-if="localBaseline.merge_type === 2"
        >
          <el-form-item
              v-if="localBaseline.merge_type === 2"
              label="合并至基线"
          >
            <el-select
                v-model="localBaseline.baseline_id"
                :loading="loading"
                filterable
                size="small"
                style="width: 476px"
                @visible-change="loadingRemoteData"
            >
              <el-option
                  v-for="item in mergeBaselines"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
              />
            </el-select>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item
              v-if="localBaseline.merge_type === 1"
              :rules="[{ required: true, message: '名称不能为空', trigger: 'blur' }]"
              prop="name"
              label="基线名称"
          >
            <el-input
                style="width: 660px"
                size="small"
                v-model="localBaseline.name" />
          </el-form-item>
          <el-form-item
              v-if="localBaseline.merge_type === 1"
              label="部门"
          >
            <department-select
                v-model="localBaseline.department_id"
                :all-users="true"
                placeholder="选择部门"
                :checkStrictly="false"
                selectStyle="width: 660px"
                size="small"
                collapseTags
            />
          </el-form-item>
          <el-form-item
              v-if="localBaseline.merge_type === 1"
              label="备注"
          >
            <el-input
                type="textarea"
                v-model="localBaseline.comment"
                :autosize="{ minRows: 4, maxRows: 6 }"
                style="width: 660px;"
            />
          </el-form-item>
          <el-form-item
              v-if="localBaseline.merge_type === 1"
              label="告警条件"
          >
            <el-radio-group
                v-model="localBaseline.compare_rule"
            >
              <el-radio label="all_contrast">权限不一致</el-radio>
              <el-radio label="only_add">超出基线范围</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
        <el-form-item
            label="是否移动原基线关联账号"
        >
          <el-radio-group
              v-model="localBaseline.move_account_bind"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <comment-bar v-if="localBaseline.merge_type === 1">
        注意: 如选择部门为非自己管理权限的部门，该基线将无法查看
      </comment-bar>
    </div>
    <span
        slot="footer"
        class="dialog-footer"
    >
      <el-button @click="visible = false">关 闭</el-button>
      <el-button
          type="primary"
          @click="handleConfirm"
      >
        确 定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'
import CommentBar       from '@/components/common/CommentBar.vue'
import API from '@/api'

export default {
  components: {
    DepartmentSelect,
    CommentBar
  },
  data() {
    return {
      visible: false,
      loading: false,
      mergeBaselines: [],
      localBaseline: {
        id:            null,
        name:          null,
        compare_rule: 'all_contrast',
        department_id: '',
        comment: '',
        origin_baseline_ids: [],
        merge_type: 1,
        baseline_id: null,
        move_account_bind: false
      }
    }
  },
  props: {
    originBaselineIds: {
      type: Array,
      default: function () {
        return []
      }
    },
    systemId: {
      type: Number,
      required: true
    }
  },
  methods: {
    handleConfirm() {
      this.loading = true
      this.localBaseline.origin_baseline_ids = this.originBaselineIds
      API.systemBaselines.mergeBaselines(this.systemId, this.localBaseline)
          .then(response => {
            this.loading = false
            if(response.data.success) {
              this.$emit('change')
              this.$emit('resetSelection')
              this.visible = false
              this.resetFields()
              this.$message.success("合并系统基线成功")
            } else {
              this.$message.error(response.data.message)
            }
          })
          .catch(error => {
            this.loading = false
            console.log(error)
          })
    },
    resetFields() {
      this.localBaseline = {
          id:            null,
          name:          null,
          compare_rule: 'all_contrast',
          department_id: '',
          comment: '',
          origin_baseline_ids: [],
          merge_type: 1,
          baseline_id: null
      }
      this.mergeBaselines = []
    },
    loadingRemoteData() {
      this.getMergeBaselines()
    },
    getMergeBaselines () {
      this.loading = true
      // 获取合并的系统基线
      this.$axios.get(`/api/systems/${this.systemId}/baselines/all?is_merge=true`)
          .then(response => {
            this.loading   = false
            this.mergeBaselines = response.data
          })
          .catch(() => {
            this.loading = false
          })
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

</style>
