import axios                           from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const list = (params = {}) => {
  return axios.get(`/api/external/business_systems`, { params: params })
}

export const create = (params = {}) => {
  return axios.post(`/api/external/business_systems`, params)
}

export const update = (id, params = {}) => {
  return axios.put(`/api/external/business_systems/${id}`, params)
}

export const updateSettings = (id, params = {}) => {
  return axios.put(`/api/external/business_systems/${id}/update_settings`, params)
}

export const destroy = (id) => {
  return axios.delete(`/api/external/business_systems/${id}`)
}

export const detail = (id) => {
  return axios.get(`/api/external/business_systems/${id}`)
}

export const getAccountSettings = (id) => {
  return axios.get(`/api/external/business_systems/${id}/account_settings`, {})
}

export const getRoleSettings = (id) => {
  return axios.get(`/api/external/business_systems/${id}/role_settings`, {})
}

export const updateTemplateSetting = (id, params = {}) => {
  return axios.put(`/api/external/business_systems/${id}/update_template_setting`, params)
}