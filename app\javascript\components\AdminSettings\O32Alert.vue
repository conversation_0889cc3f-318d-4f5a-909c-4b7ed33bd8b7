<template>
  <div class="container">
    <AlertCheck2 :alertSetting="alertSettings"/>
    <el-divider/>
    <AlertCheck3 :alertSetting="alertSettings"/>
    <el-divider/>
    <AlertCheck4 :alertSetting="alertSettings"/>
    <el-divider/>
    <AlertDepartment :alertSetting="alertSettings"/>
  </div>
</template>
<script>
import AlertCheck2 from '@/components/AdminSettings/O32Alert/AlertCheck2'
import AlertCheck3 from '@/components/AdminSettings/O32Alert/AlertCheck3'
import AlertCheck4 from '@/components/AdminSettings/O32Alert/AlertCheck4'
import AlertDepartment from '@/components/AdminSettings/O32Alert/AlertDepartment'
export default {
  components: {
    AlertCheck2,
    AlertCheck3,
    AlertCheck4,
    AlertDepartment
  },
  data () {
    return {
      loading:       false,
      alertSettings: { menus: [], roles: [], departments: [] , users: [], accounts: [] }
    }
  },
  created () {
    this.getAlertSettings()
  },
  computed: {
    isCheck2Enable() {
      return this.$settings.O32AlertConfig.check_2
    },
    isCheck3Enable() {
      return this.$settings.O32AlertConfig.check_3
    },
    isCheck4Enable() {
      return this.$settings.O32AlertConfig.check_4
    },
    isCheck5Enable() {
      return this.$settings.O32AlertConfig.department
    }
  },
  methods: {
    getAlertSettings () {
      this.loading = true
      this.$axios.get('/admin_api/global_alerts/o32_alert_settings')
        .then(response => {
          this.alertSettings = response.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>
