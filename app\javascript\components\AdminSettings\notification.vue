<template>
  <div
    v-loading="loading"
    class="container"
  >
    <el-form
      ref="form"
      label-width="140px"
      :model="{emails: emailSettings, smses: smsSettings, oa_record: oaRecordSettings}"
    >
      <el-form-item
        label="全局邮件通知"
      >
        <el-switch
          v-model="enable"
          active-text="开"
          inactive-text="关"
          style="margin-left: 70px"
          :disabled="!$store.getters.hasPermission('app_settings.notification')"
        />
      </el-form-item>
      <el-form-item
        label="发送时间设置"
      >
        <el-switch
          v-model="send_setting.custom_time"
          active-text="自选时间"
          inactive-text="跟随定时任务"
          :disabled="!$store.getters.hasPermission('app_settings.notification') || !enable"
        />

        <el-time-picker
          v-show="send_setting.custom_time"
          v-model="send_setting.send_time"
          value-format="HH:mm:ss"
          placeholder="选择时间"
          style="margin-left: 10px;"
          :disabled="!$store.getters.hasPermission('app_settings.notification') || !enable"
        >
        </el-time-picker>
      </el-form-item>
      <el-form-item>
        <test-mail />
      </el-form-item>

      <div v-if="isSmsNotificationEnable">
        <el-form-item
          label="短信通知"
        >
          <el-switch
            v-model="smsEnable"
            active-text="开"
            inactive-text="关"
            :disabled="!$store.getters.hasPermission('app_settings.notification')"
          />
        </el-form-item>
        <el-form-item>
          <test-sms />
        </el-form-item>
      </div>

      <el-divider />
      <el-form-item
        label="系统角色通知设置"
      >
        <el-dropdown @command="handleRoleChange">
          <el-button
            size="small"
            :disabled="!$store.getters.hasPermission('app_settings.notification')"
          >
            添加
            <i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(value, key) in roles"
              :key="key"
              :disabled="roleExist(key)"
              :command="key"
            >
              {{ value }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <transition-group name="list-complete">
        <el-form-item
          v-for="(value, role) in roleSettings"
          :key="role"
          :label="roleName(role)"
          class="list-complete-item"
        >
          <div class="form-item">
            <el-select
              v-model="roleSettings[role]"
              multiple
              size="small"
              :disabled="!$store.getters.hasPermission('app_settings.notification')"
              placeholder="请选择通知模板"
            >
              <el-option
                v-for="item in templates"
                :key="item.key"
                :value="item.key"
                :label="item.name"
              />
            </el-select>
            <el-button
              size="small"
              :disabled="!$store.getters.hasPermission('app_settings.notification')"
              @click="handleDeleteRole(role)"
            >
              删除
            </el-button>
          </div>
        </el-form-item>
      </transition-group>

      <el-divider />

      <el-tabs
        v-model="activeName"
        type="card"
        :disabled="!$store.getters.hasPermission('app_settings.notification')"
      >
        <el-tab-pane
          label="邮箱通知设置"
          name="mail"
        >
          <mail-notification
            :outer-email-settings="emailSettings"
            :outer-templates="templates"
          />
        </el-tab-pane>
        <el-tab-pane
          v-if="isSmsNotificationEnable"
          label="短信通知设置"
          name="sms"
        >
          <sms-notification
            :outer-sms-settings="smsSettings"
            :outer-templates="smsTemplates"
          />
        </el-tab-pane>
        <el-tab-pane
          v-if="isOaRecordNotificationEnable"
          label="OA流程临时授权通知设置"
          name="oa_record"
        >
          <oa-record-notification
            :outer-oa-record-settings="oaRecordSettings"
          />
        </el-tab-pane>
      </el-tabs>

      <el-form-item>
        <el-button
          type="primary"
          size="small"
          :disabled="!$store.getters.hasPermission('app_settings.notification')"
          @click="submitForm('form')"
        >
          更新设置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { validatePhone } from '@/utils/form_validates'
import TestMail from './TestMail.vue'
import TestSms from './TestSms.vue'
import MailNotification from './MailNotification.vue'
import SmsNotification from './SmsNotification.vue'
import OaRecordNotification from './OaRecordNotification.vue'

export default {
  components: {
    TestMail,
    TestSms,
    MailNotification,
    SmsNotification,
    OaRecordNotification
  },
  data () {
    return {
      activeName:       'mail',
      loading:          false,
      enable:           false,
      smsEnable:        false,
      send_setting:     {
        custom_time: false,
        send_time:   null,
      },
      roleSettings:     {},
      emailSettings:    [],
      smsSettings:      [],
      oaRecordSettings: {},
      templates:        [],
      smsTemplates:     [],
      roles:            [],
      rules: {
        email:  [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email',  message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        templates: [
          { required: true, message: '请选择通知模板', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    isSmsNotificationEnable () {
      return this.$settings.showSmsNotification
    },
    isOaRecordNotificationEnable () {
      return this.$settings.oaRecord.enable
    }
  },
  created () {
    this.getNotificationSettings()
  },
  methods: {
    getNotificationSettings () {
      this.loading = true
      this.$axios.get('admin_api/settings/notification')
        .then(response => {
          this.loading          = false
          this.enable           = response.data.notification.enable
          this.smsEnable        = response.data.notification.sms_enable
          this.send_setting     = response.data.notification.send_setting
          this.roleSettings     = response.data.notification.roles
          this.emailSettings    = response.data.notification.emails
          this.oaRecordSettings = response.data.notification.oa_record_setting
          this.smsSettings      = response.data.notification.smses
          this.templates        = response.data.templates
          this.smsTemplates     = response.data.sms_templates
          this.roles            = response.data.roles
          if (!this.emailSettings) this.emailSettings = []
          if (!this.oaRecordSettings) this.oaRecordSettings = {}
          if (!this.smsSettings) this.smsSettings = []
          if (!this.roleSettings) this.roleSettings = {}
        })
        .catch(() => {
          this.loading = false
        })
    },
    updateNotificationConfirm () {
      this.$refs.form.validate((valid) => {
        if(valid){
          this.$confirm('此操作将更新系统通知设置，是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText:  '取消',
            type:              'warning'
          })
            .then(() => {
              this.updateNotificationSettings()
            })
            .catch(() => {
              this.$message.info('已取消操作')
            })
        }
      })
    },
    updateNotificationSettings () {
      this.$axios.put('admin_api/settings/notification', {
        notification: {
          enable:            this.enable,
          sms_enable:        this.smsEnable,
          roles:             this.roleSettings,
          emails:            this.emailSettings,
          oa_record_setting: this.oaRecordSettings,
          smses:             this.smsSettings,
          send_setting:      this.send_setting
        }
      })
        .then(() => {
          this.$message.success('更新通知设置完成')
          this.getNotificationSettings()
        })
        .catch(() => {
          this.$message.error('更新通知设置失败')
        })
    },
    roleName (role) {
      return this.roles[role] || role
    },
    roleExist (role) {
      return !!this.roleSettings[role]
    },
    handleRoleChange (command) {
      this.$set(this.roleSettings, command, [])
    },
    handleDeleteRole (role) {
      this.$delete(this.roleSettings, role)
    },
    submitForm (formName) {
      const settingRoleArr = this.checkRoleSettings()
      if (this.enable && this.send_setting.custom_time && !this.send_setting.send_time) { return this.$message.error('请选择发送时间') }
      if (settingRoleArr.length > 0) return this.$message.error(`请填写角色：${settingRoleArr}的通知模版`)
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.checkRoleSettings()
          this.updateNotificationConfirm()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    checkRoleSettings () {
      const settingRoleArr = new Array();
      for (const key in this.roleSettings) {
        if(this.roleSettings[key].length < 1){
          settingRoleArr.push(this.roles[key])
        }
      }
      return settingRoleArr
    },
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .form-item {
    @include vertical_center_between;
    width: 100%;

    .el-select {
      width: calc(100% - 80px);
    }
  }

  .form-item-first-line {
    // MARK: 为了把表单验证的字露出来
    margin-bottom: 14px;

    .el-input {
      width: 200px;
    }
  }

  .form-item-second-line {
    margin-bottom: 25px;

    .el-select {
      width: 100%;
    }
  }

  .list-complete-item {
    transition: all 0.5s;
  }

  .error {
    color: #E54D42;
    font-weight: bold;
  }

  .list-complete-enter, .list-complete-leave-to
    /* .list-complete-leave-active for below version 2.1.8 */
  {
    opacity: 0;
    transform: translateY(-30px);
  }
</style>
