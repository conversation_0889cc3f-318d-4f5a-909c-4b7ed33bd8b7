<template>
  <el-popover
    placement="right"
    width="600"
    trigger="click"
    :offset="500"
    @show="handleShowPopover"
    @hide="handleHidePopover"
  >
    <div class="container">
      <el-tabs
        v-if="visible"
        v-model="activeTab"
      >
        <el-tab-pane
          label="权限差异"
          name="diff"
        >
          <div v-if="activeTab === 'diff'">
            <diff-datas-card
              :account-id="accountId"
              :account="account"
              :system-id="systemId"
              :other-quarter-id="otherQuarterId"
              :enable-other-quarter-id="true"
              :show-title="false"
              :default-diff-mode="diffMode"
              :default-data-key="defaultDataKey"
            />
          </div>
        </el-tab-pane>

        <el-tab-pane
          label="权限列表"
          name="all"
        >
          <div v-if="activeTab === 'all'">
            <all-datas-card
              :account-id="accountId"
              :system-id="systemId"
              :show-title="false"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-button
      slot="reference"
      :size="buttonSize"
      type="text"
      class="detail-button"
    >
      {{ buttonName }}
    </el-button>
  </el-popover>
</template>

<script>
import AllDatasCard  from '@/components/AccountDetail/AllDatasCard.vue'
import DiffDatasCard from '@/components/AccountDetail/DiffDatasCard.vue'

export default {
  components: {
    AllDatasCard,
    DiffDatasCard
  },
  props:      {
    systemId:       {
      type:     Number,
      required: true
    },
    accountId:      {
      type:     Number,
      required: true
    },
    otherQuarterId: {
      type:     Number,
      required: true
    },
    buttonName:     {
      type:    String,
      default: '权限详情'
    },
    buttonSize:     {
      type:    String,
      default: 'mini'
    },
    diffMode: {
      type:    String,
      default: 'compareHistory'
    },
    defaultDataKey: {
      type:    String,
      default: ''
    },
    account: {
      type:    Object,
      default: () => {
        return {
          account_code: ''
        }
      }
    }
  },
  data () {
    return {
      visible:          false,
      activeTab:        'diff',
      permission_types: 0
    }
  },
  watch:   {
    visible () {
      if (this.visible) this.getPermissionTypes()
    }
  },
  methods: {
    getPermissionTypes () {
      this.$axios.get(`/api/systems/${this.systemId}/settings`)
        .then(response => {
          this.permission_types = response.data.permission_types
        })
        .catch(() => {})
    },
    handleShowPopover () {
      this.visible = true
    },
    handleHidePopover () {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  max-height: calc(100vh - 60px);
  overflow-y: auto;
}

.detail-button {
  margin-left: 0px;
  margin-top:  -3px;
  height:      24px;
}
</style>
