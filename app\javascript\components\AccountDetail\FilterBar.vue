<template>
  <span class="filter-bar">
    <filter-custom
      v-if="filterMethod"
      :filter-data="filterData"
      :table-schema="tableSchema"
      :filter-method="filterMethod"
      class="custom-filter"
      @change="afterCustomFilter"
    />
    <div class="common-filter">
      <el-select
        v-model="filterProperty"
        placeholder="选择搜索字段"
        size="small"
      >
        <el-option
          v-for="item in filterProperties"
          :key="item.property"
          :label="item.label"
          :value="item.property"
        />
      </el-select>
      <el-input
        v-model="filterMatch"
        size="small"
        placeholder="请输入搜索内容"
        clearable
        class="common-input"
      />
    </div>
  </span>
</template>

<script>
import FilterCustom from './FilterCustom.vue'

export default {
  components: {
    FilterCustom
  },
  props: {
    filterData: {
      type: Array,
      required: true
    },
    tableSchema: {
      type: Array,
      required: true
    },
    filterMethod: {
      validator: function (val) {
        return val === null || typeof val === 'string'
      },
      required: true
    }
  },
  data () {
    return {
      filterProperty:        null,
      filterMatch:           null,
      afterCustomFilterData: []
    }
  },
  computed: {
    afterFilterData () {
      if (!this.filterMatch) return this.afterCustomFilterData
      if (!this.filterProperty) return this.afterCustomFilterData

      let escapeFilterMatch = this.$lodash.escapeRegExp(this.filterMatch)
      const query = new RegExp(escapeFilterMatch)
      return this.afterCustomFilterData.filter(x => {
        const prop = String(x[this.filterProperty] || '')
        return prop.match(query)
      })
    },
    filterProperties () {
      return this.tableSchema.filter(x => x.is_show_custom !== true)
    }
  },
  watch: {
    afterFilterData () {
      this.$emit('change', this.afterFilterData)
    },
    filterData () {
      this.getAfterCustomFilterData()
    }
  },
  created () {
    this.afterCustomFilterData = this.filterData
    // this.getAfterCustomFilterData()
  },
  methods: {
    afterCustomFilter (payload) {
      this.afterCustomFilterData = payload
    },
    getAfterCustomFilterData () {
      if (this.filterMethod === null) {
        this.afterCustomFilterData = this.filterData
      }
    },
    clearFilter () {
      this.filterMatch = null
      this.filterProperty = null
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .filter-bar{
    @include vertical_center_left;
    height: 50px;

    .custom-filter{
      margin-right: 20px;
    }
    .common-filter{
      @include vertical_center_left;
      height: 50px;
    }

    .common-input{
      margin-left: 10px;
    }
  }
</style>
