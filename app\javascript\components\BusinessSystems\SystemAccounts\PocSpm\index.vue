<template>
  <el-container>
    <el-aside width="320">
      <org-card
        :system-id="systemId"
        @change="handleChangeOrgId"
      />
    </el-aside>
    <el-main class="org-container">
      <div>
        <div class="tool-bar">
          <div class="left">
            <el-button
              @click="handleCreate"
              :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
            >
              新增用户
            </el-button>
            <!--
            <accounts-import
              :system-id="systemId"
              style="margin-left: 10px;"
              @update="handleUpdateRoles"
            /> -->
          </div>
          <div class="right">
            <el-autocomplete
              v-model="filter.name"
              :fetch-suggestions="roleNameSuggestions"
              placeholder="搜索登录名"
              style="width: 120px"
            />
            <el-button
              @click="handleRunFilter"
            >
              在全部机构中搜索
            </el-button>
            <el-button
              @click="handleResetFilter"
            >
              重置
            </el-button>
          </div>
        </div>
        <el-divider />
        <div class="table-container">
          <el-table
            v-loading="loading"
            :data="dataByPage"
            border
            stripe
            style="width: 100%"
          >

            <el-table-column
              prop="id"
              label="ID"
              min-width="60"
            />

            <el-table-column
              prop="login_name"
              label="登录名"
              min-width="60"
            />

            <el-table-column
              prop="account_name"
              label="用户名"
              min-width="60"
            />

            <el-table-column
              prop="account_email"
              label="电子邮箱"
              min-width="60"
            />
            <el-table-column
              prop="org_id"
              label="所属机构"
              min-width="100"
            >
              <template slot-scope="scope">
                {{ orgName(scope.row.org_id) }}
              </template>
            </el-table-column>

            <el-table-column
              prop="department_id"
              label="所属部门"
              min-width="100"
            >
              <template slot-scope="scope">
                {{ deptName(scope.row.department_id) }}
              </template>
            </el-table-column>

            <el-table-column
              prop="account_status"
              label="状态"
              min-width="30"
            >
            <template slot-scope="scope">
                {{ stateName(scope.row.account_status) }}
              </template>
            </el-table-column>>
            <el-table-column
              label="操作"
              fixed="right"
              width="245"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click="handleShow(scope.row)"
                  :disabled="!$store.getters.hasPermission('system_permission_manager.org_accounts_query')"
                >
                  详情
                </el-button>
                <el-button
                  size="mini"
                  @click="handleResetPassword(scope.row.login_name)"
                  :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
                >
                  重置密码
                </el-button>
                <el-button
                  type="danger"
                  size="mini"
                  @click="handleDisableResource(scope.row)"
                  :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
                  v-if="scope.row.account_status =='ENABLE'"
                >
                  停用
                </el-button>
                <el-button
                  type="primary"
                  size="mini"
                  @click="handleDisableResource(scope.row)"
                  :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
                  v-else
                >
                  启用
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :page-size.sync="pageSize"
            :total="dataByFilter.length"
            :current-page.sync="currentPage"
            :style="{ marginTop: '20px' }"
            :page-sizes="[10, 30, 50, 100]"
            background
            hide-on-single-page
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
        <account-form
          ref="accountForm"
          :system-id="systemId"
          :account="currentAccount"
          :orgs="orgs"
          :roles="roles"
          @change="handleUpdateRoles"
        />
      </div>
    </el-main>
  </el-container>
</template>

<script>
import AccountForm     from './form.vue'
import OrgCard         from './OrgCard.vue'
import PocSpmResource from '@/components/BusinessSystems/mixins/PocSpmResource'
import { catchError }    from '@/utils/axios_utils'

export default {
  components: {
    AccountForm,
    OrgCard
  },
  mixins: [PocSpmResource],
  props:      {
    systemId:       {
      type:     Number,
      required: true
    },
    systemSettings: {
      type:     Object,
      required: true
    }
  },
  data () {
    return {
      resourceType:  'accounts',
      resourceMode:  'both',
      loading:        false,
      orgId:          null,
      accounts:       [],
      orgs:           [],
      departments:    [],
      roles:          [],
      pageSize:       10,
      currentPage:    1,
      currentAccount: null,
      filter:         {
        name: ''
      }
    }
  },
  computed:   {
    dataByPage () {
      let start = (this.currentPage - 1) * this.pageSize
      let end   = this.currentPage * this.pageSize
      return this.dataByFilter.slice(start, end)
    },
    dataByFilter () {
      let accounts = this.accounts
      if (this.filter.name) {
        accounts = accounts.filter(x => x.login_name.match(new RegExp(this.filter.name)))
      }

      return accounts
    }
  },
  watch:      {
    orgId () {
      this.dataInitialize()
    }
  },
  created () {
    this.getAllOrgTree()
    this.getAllOrgs()
    this.getAllDepartments()
    this.getAllRoles()
  },

  methods: {
    dataInitialize () {
      this.getSystemAccounts()
    },
    handleChangeOrgId (payload) {
      this.orgId = payload
    },
    resourceRefresh () {
      this.getSystemAccounts()
    },
    getAllOrgTree (){
      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/organizations/tree`)
        .then(response => {
          this.loading = false
        })
        .catch(error => {
          this.loading = false
          catchError(error, '获取树状机构列表失败 ********')
        })
    },
    getSystemAccounts () {
      if (this.systemId === 0) return
      if (this.orgId === 0) return

      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/accounts`, {
        params: {
          parent_id: this.orgId
        }
      })
        .then(response => {
          this.loading  = false
          this.accounts = response.data

          if (this.accounts.length === 0) this.$message.info('该机构下不存在用户')
        })
        .catch(error => {
          this.loading = false
          catchError(error, '获取用户列表失败')
        })
    },
    getAllOrgs () {
      if (this.systemId === 0) return

      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/organizations`, {
        params: {
          parent_id: 1
        }
      })
        .then(response => {
          this.loading = false
          this.orgs    = response.data
        })
        .catch(error => {
          this.loading = false
          catchError(error, '获取机构列表失败222222')
        })
    },
    getAllDepartments (){
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/all_departments`, {})
      .then(response => {
        this.departments = response.data
      })
    },
    getAllRoles () {
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/roles`)
        .then(response => {
          this.roles = response.data
        })
        .catch(error => {
          catchError(error, '获取全部角色列表时出现问题')
        })
    },
    handleCreate () {
      const form          = this.$refs.accountForm
      form.dialogFlag     = 'create'
      form.visible        = true
      this.currentAccount = null
    },
    handleShow (row) {
      const form          = this.$refs.accountForm
      form.dialogFlag     = 'show'
      form.visible        = true
      this.currentAccount = row
    },

    handleUpdateRoles () {
      this.getSystemAccounts()
    },
    roleNameSuggestions (string, callback) {
      const data      = this.accounts.map(x => { return { value: x.login_name.toString() } })
      const suggestions = string ? data.filter(x => x.value.match(new RegExp(string))) : data
      callback(suggestions)
    },
    handleResetFilter () {
      this.filter = {
        id:   '',
        name: '',
        code:''
      }
      this.getSystemAccounts()
    },
    handleRunFilter () {
      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/accounts/search_by_code_name`, {
        params: {
          system_id: this.systemId,
          login_name: this.filter.name,
        }
      })
        .then(response => {
          this.loading  = false
          this.accounts = response.data

          if (this.accounts.length === 0) this.$message.info('未搜索到指定用户')
        })
        .catch(error => {
          this.loading = false
          catchError(error, '搜索用户信息失败')
        })
    },
    orgName (org_id) {
      const org = this.orgs.find(x => x.org_id === org_id)
      return org ? org.org_name : ''
    },
    deptName (department_id) {
      const department = this.departments.find (x => x.department_id === department_id)
      return department ? department.department_name : ''
    },
    stateName (account_status) {
      const state = account_status === 'DISABLE' ? '禁用' : '正常'
      return state
    },
    handleResetPassword (account_name) {
      this.$confirm('确认重置该用户密码吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.operationReviewResetPassword(account_name)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })

    },
    operationReviewResetPassword (account_name) {
      this.$operationReview(this.systemId)
        .then(() => {
          this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts/${account_name}/reset_password`)
            .then(response => {
                this.$message.success('用户密码已重置')
                this.getSystemAccounts()
            })
            .catch(error => {
                catchError(error, '重置时出现问题')
            })
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .org-container {
    padding: 0 0 0 20px;
  }

  .tool-bar {
    @include vertical_center_between;
    height: 50px;

    .left{
      @include vertical_center_left;
    }

    .right {
      margin-right: 10px;
    }
  }

  .el-table {
    width: 100%;
  }

  .el-divider--horizontal {
    margin: 12px 0;
  }
</style>
