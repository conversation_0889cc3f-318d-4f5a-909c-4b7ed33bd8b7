import axios from '@/settings/axios'

export const link       = (params) => {
  return axios.put(`/admin_api/ledgers/bs/${params.systemId}/accounts/${params.accountCode}`,
    { user_id: params.userId, ignore_matcher: params.ignoreMatcher }
  )
}
export const deleteLink = (params) => {
  return axios.delete(`/admin_api/ledgers/bs/${params.systemId}/accounts/${params.accountCode}`, {
  })
}

export const editIgnoreMatcher = (params) => {
  return axios.put(`/admin_api/ledgers/bs/${params.systemId}/accounts/${params.accountCode}/edit_ignore_matcher`, {
    ignore_matcher: params.ignoreMatcher
  })
}

