import axios from '@/settings/axios'

export const list = (headers = {}) => {
  return axios.get('/api/quarters', { headers: headers })
}

export const createExcel = (name) => {
  return axios.post('/api/quarters/create_excel', { name: name })
}

export const checkUploadData = (quarterId = null) => {
  return axios.get('/admin_api/check_upload_data', { params: { quarter_id: quarterId } })
}

export const uploadData = (data) => {
  return axios.post('/admin_api/import_upload_data', data)
}

export const importStatus = (quarterId, params) => {
  return axios.get(`/api/quarters/${quarterId}/import_status`, { params: params })
}

export const rakesRestart = (quarterId) => {
  return axios.post(`/api/quarters/${quarterId}/rakes_restart`)
}

export const rakeRestart = (params) => {
  return axios.post(`/api/quarters/${params.quarter_id}/rake_restart`, params)
}

// 运行系统依赖任务
export const rakeSystemRestart = (params) => {
  return axios.post(`/api/quarters/${params.quarter_id}/rake_system_restart`, params)
}

// 运行指定系统所有系统依赖任务
export const rakesSystemRestart = (params) => {
  return axios.post(`/api/quarters/${params.quarter_id}/rakes_system_restart`, params)
}

export const importsRestart = (quarterId) => {
  return axios.post(`/api/quarters/${quarterId}/imports_restart`)
}

export const importRestart = (params) => {
  return axios.post(`/api/quarters/${params.quarter_id}/import_restart`, params)
}

export const last = () => {
  return axios.get('/api/quarters/last', {})
}

export const info = (quarterId) => {
  return axios.get(`/api/quarters/${quarterId}`)
}
