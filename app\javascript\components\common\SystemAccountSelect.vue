<template>
  <span>
    <el-select
      v-model="localAccountCode"
      :class="className"
      :placeholder="placeholder"
      :filterable="filterable"
      :disabled="disabled"
      size="small"
      :multiple="multiple"
      :clearable="clearable"
    >
      <el-option
        v-for="item in accounts"
        :key="item[0]"
        :label="item[0]+'-'+item[1]"
        :value="item[0]"
      />
    </el-select>
  </span>
</template>

<script>
import API from '@/api'
export default {
  model: {
    prop: 'accountCode',
    event: 'change'
  },
  props: {
    systemId: {
      type: Number,
      required: true
    },
    placeholder: {
      type: String,
      default: '选择业务系统账号'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    accountCode: {
      type: [String, Number, Array],
      required: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'normal'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      localAccountCode: this.accountCode,
      accounts: [],
      className: 'el-select'
    }
  },
  watch: {
    accountCode () {
      this.localAccountCode = this.accountCode
    },
    localAccountCode () {
      this.$emit('change', this.localAccountCode)
    }
  },
  computed: {
    currentQuarter () {
      return this.$store.state.current_quarter
    },
  },
  created () {
    this.getAccounts()
  },
  methods: {
    getAccounts () {
      API.systemAccounts.accounts({ system_id: this.systemId, quarter_id: this.currentQuarter.id })
        .then(response => {
          this.accounts = response.data
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
