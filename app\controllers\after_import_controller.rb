class AfterImportController < ApplicationController
  def todo_non_system 
    quarter_id = params[:quarter_id]
    pp "---todo_non_system---- #{quarter_id}"
    Thread.new do 
      begin
        AfterImport::Rakes.to_do_non_system_tasks(quarter_id)
      rescue => e
        Rails.logger.error "Error in AfterImport::Rakes.to_do_non_system_tasks: #{e.message}"
      end
    end
    json_respond(success: true, message: 'After import Todo non systems tasks started')
  end

  def todo
    quarter_id = params[:quarter_id]
    pp "---todo---- #{quarter_id}"
    Thread.new do 
      begin
        AfterImport::Rakes.to_do(quarter_id)
      rescue => e
        Rails.logger.error "Error in AfterImport::Rakes.to_do: #{e.message}"
      end
    end
    json_respond(success: true, message: 'After import Todo tasks started')
  end

  def todo_system
    quarter_id = params[:quarter_id]
    bs_id = params[:bs_id]
    pp "---todo_system---- quarter_id: #{quarter_id} bs_id: #{bs_id}"
    Thread.new do 
      begin
        AfterImport::Rakes.to_do_system_tasks(quarter_id, bs_id)
      rescue => e
        Rails.logger.error "Error in AfterImport::Rakes.to_dosystems: #{e.message}"
      end
    end
    json_respond(success: true, message: 'After import TodoSystems tasks started')
  end
end