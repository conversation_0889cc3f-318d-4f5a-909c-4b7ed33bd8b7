<template>
  <div class="container">
    <h2>系统设置</h2>
    <hr />
    <el-tabs
      ref="tabs"
      v-model="tab"
      type="border-card"
      @tab-click="routerChange"
    >
      <el-tab-pane
        v-if="showFeature('app_settings.general_settings')"
        label="通用设置"
        name="general_settings"
      >
        <general-settings v-if="tab === 'general_settings'" />
      </el-tab-pane>
      <el-tab-pane
        v-if="showFeature('app_settings.notification')"
        name="notification"
        label="通知设置"
      >
        <notification v-if="tab === 'notification'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isLeaveUserAccountsSeriousNotificationEnable && showFeature('app_settings.leave_user_serious_notification')"
        name="level_user_account_notification_settings"
        label="离职员工账号关闭告警升级设置"
      >
        <leave-user-accounts-serious-notification
          v-if="tab === 'level_user_account_notification_settings'"
        />
      </el-tab-pane>

      <el-tab-pane
        v-if="showFeature('system_permission_manager.cms_keywords')"
        name="cms_keywords"
        label="CMS 机构关键字设置"
      >
        <cms-keywords v-if="tab === 'cms_keywords'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isFundPermissionAbnormalEnable && showFeature('fund_permission_abnormal.fund_permission_settings')"
        :label="`${$t('aas.fund_permission_abnormals.name')}设置`"
        name="fund_permission_settings"
      >
        <fund-permission-abnormals v-if="tab === 'fund_permission_settings'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isSystemAlignmentSettingEnable && showFeature('system_alignment.settings')"
        :label="`${$t('aas.system_alignments.name')}设置`"
        name="system_alignment_settings"
      >
        <system-alignments v-if="tab === 'system_alignment_settings'" />
      </el-tab-pane>

      <el-tab-pane

        v-if="isPcCompareGuzhiSettingEnable && showFeature('pc_compare_guzhi.settings')"
        :label="`${$t('aas.pc_guzhi_comparison.name')}设置`"
        name="pc_compare_guzhi_settings"
      >
        <pc-compare-guzhi v-if="tab === 'pc_compare_guzhi_settings'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isPcCompareO32SettingEnable && showFeature('pc_compare_o32.settings')"
        :label="`${$t('aas.pc_o32_comparison.name')}设置`"
        name="pc_compare_o32_settings"
      >
        <pc-compare-o32 v-if="tab === 'pc_compare_o32_settings'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isFdfgCompareGzyss45SettingEnable && showFeature('fdfg_compare_gzyss45.settings')"
        :label="`${$t('aas.fdfg_gzyss45_comparison.name')}设置`"
        name="fdfg_compare_gzyss45_settings"
      >
        <fdfg-compare-gzyss45 v-if="tab === 'fdfg_compare_gzyss45_settings'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isJiaoyiTemporaryPermissionEnable && isO32SettingEnable && showFeature('o32_temp.o32_temp_settings')"
        :label="`投资交易系统临时授权功能设置`"
        name="o32_temp_settings"
      >
        <o32-temp v-if="tab === 'o32_temp_settings'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isO32AlertConfigEnable && showFeature('o32_alerts.o32_alert_settings')"
        :label="`投资交易系统账号合规性监测`"
        name="o32_alert_settings"
      >
        <o32-alert v-if="tab === 'o32_alert_settings'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isShareDirectoryConfigEnable && showFeature('app_settings.share_directory_settings')"
        :label="`共享目录设置`"
        name="share_directory_settings"
      >
        <share-directory v-if="tab === 'share_directory_settings'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isQuarterAutoClearEnable && showFeature('app_settings.quarter_auto_clear')"
        :label="`数据定期清理`"
        name="quarter_auto_clear"
      >
        <quarter-auto-clear v-if="tab === 'quarter_auto_clear'" />
      </el-tab-pane>
      <el-tab-pane
        v-if="PermissionRulesEnable && PermissionRulesEnable"
        :disabled="!$store.getters.hasPermission('position_roles_check.permission_rule_query')"
        :label="permissionRulesSettingTitle"
        name="permission_rules"
      >
        <permission-rules v-if="tab === 'permission_rules'" />
      </el-tab-pane>
      <el-tab-pane
        v-if="isSystemRoleConflictsEnable && showFeature('system_role_conflicts.conflicts_query')"
        :label="systemRoleConflictsSettingTitle"
        name="system_role_conflict_settings"
      >
        <system-role-conflicts v-if="tab === 'system_role_conflict_settings'" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isSendResetPasswordEnable && showFeature('app_settings.send_reset_password')"
        :label="`重置密码通知设置`"
        name="send_reset_password"
      >
        <send-reset-password v-if="tab === 'send_reset_password'" />
      </el-tab-pane>

      <!-- 超级管理员权限 'admin_account_manager.set_permissions' -->
      <el-tab-pane
        v-if="showFeature('admin_account_manager.set_permissions')"
        label="系统信息"
        name="license_settings"
      >
        <license-settings v-if="tab === 'license_settings'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Notification                         from './notification.vue'
import LicenseSettings                      from './LicenseSettings.vue'
import GeneralSettings                      from './GeneralSettings.vue'
import LeaveUserAccountsSeriousNotification from './LeaveUserAccountsSeriousNotification.vue'
import FundPermissionAbnormals              from './FundPermissionAbnormals.vue'
import SystemAlignments                     from './SystemAlignment'
import PcCompareGuzhi                       from './PcCompareGuzhi'
import PcCompareO32                         from './PcCompareO32'
import FdfgCompareGzyss45                   from './FdfgCompareGzyss45'
import CmsKeywords                          from '@/components/AdminSettings/CmsKeywords'
import O32Temp                              from '@/components/AdminSettings/O32Temp'
import ShareDirectory                       from '@/components/AdminSettings/ShareDirectory'
import QuarterAutoClear                     from '@/components/AdminSettings/QuarterAutoClear'
import SendResetPassword                    from '@/components/AdminSettings/SendResetPassword'
import MyPermissions                        from '@/components/mixins/MyPermissions'
import PermissionRules                      from './PermissionRules.vue'
import O32Settings                          from '@/components/AdminSettings/O32Temp/O32Settings'
import SystemRoleConflicts                  from './SystemRoleConflicts/conflictRule.vue'
import O32Alert                             from '@/components/AdminSettings/O32Alert'

export default {
  components: {
    Notification,
    LeaveUserAccountsSeriousNotification,
    FundPermissionAbnormals,
    SystemAlignments,
    PcCompareGuzhi,
    PcCompareO32,
    FdfgCompareGzyss45,
    LicenseSettings,
    GeneralSettings,
    CmsKeywords,
    O32Temp,
    ShareDirectory,
    QuarterAutoClear,
    PermissionRules,
    SendResetPassword,
    O32Alert,
    SystemRoleConflicts
  },
  mixins:     [MyPermissions, O32Settings],
  data () {
    return {
      tab: "",
    }
  },
  computed: {
    permissionRulesSettingTitle() {
      return this.$t("position_role_check.system_account_rule_manager")
    },
    systemRoleConflictsSettingTitle() {
      return this.$t('system_role_conflicts.module_setting')
    },
    isPcCompareGuzhiSettingEnable () {
      return this.$settings.pcCompareGuzhi.enable
    },
    isPcCompareO32SettingEnable () {
      return this.$settings.pcCompareO32.enable
    },
    isFdfgCompareGzyss45SettingEnable () {
      return this.$settings.fdfgCompareGzyss45.enable
    },
    isFundPermissionAbnormalEnable () {
      return this.$settings.fundPermissionAbnormals.show
    },
    isSystemAlignmentSettingEnable() {
      return this.$settings.systemAlignment.enable
    },
    isJiaoyiTemporaryPermissionEnable() {
      return this.$settings.jiaoyiTemporary.enable
    },
    isO32AlertConfigEnable() {
      return this.$settings.O32AlertConfig && this.$settings.O32AlertConfig.enable
    },
    isShareDirectoryConfigEnable() {
      return this.$settings.showShareDirectoryConfig
    },
    isLeaveUserAccountsSeriousNotificationEnable() {
      return this.$settings.showLeaveUserAccountsSeriousNotification
    },
    PermissionRulesEnable () {
      return this.$settings.positionRoleCheck.enable
    },
    isQuarterAutoClearEnable() {
      return this.$settings.showQuarterAutoClear
    },
    isOperatorCheckEnable() {
      const operatorCheck = this.$settings.jiaoyiTemporary.operator_check
      return operatorCheck ? operatorCheck.enable : false;
    },
    isSystemRoleConflictsEnable() {
      return this.$settings.systemRoleConflicts.enable
    },
    isSendResetPasswordEnable() {
      return this.$settings.sendResetPassword.enable
    },
    customerId() {
      return this.$settings.customerId;
    },
    myFeatures () {
      const features = [
        'admin_account_manager.set_permissions',
        'app_settings.license_settings',
        'app_settings.general_settings',
        'app_settings.notification',
        'system_permission_manager.cms_keywords',
        'fund_permission_abnormal.fund_permission_settings',
        'system_alignment.settings',
        'pc_compare_guzhi.settings',
        'pc_compare_o32.settings',
        'fdfg_compare_gzyss45.settings',
        'o32_temp.o32_temp_settings',
        'share_directory_settings',
        'app_settings.quarter_auto_clear',
        'app_settings.share_directory_settings',
        'app_settings.leave_user_serious_notification',
        'app_settings.send_reset_password',
        'position_roles_check.permission_rule_query',
        'o32_alerts.o32_alert_settings',
        'position_roles_check.permission_rule_query',
        'quarter_auto_clear',
        'level_user_account_notification_settings',
        'permission_rules',
        'system_role_conflicts.conflicts_query'
      ]
      return features.filter(x => this.$store.getters.hasPermission(x))
    }
  },
  watch: {
    "$route.name"() {
      this.tab = this.getRoute()
    }
  },
  mounted() {
    this.tab = this.getRoute()
  },
  methods: {
    getRoute() {
      if (this.$route.name === "settings") return this.defaultTab()
      return this.$route.name
    },
    routerChange(tab) {
      this.$router.push({ name: tab.name })
    },
    showFeature(featureName) {
      return this.myFeatures.includes(featureName)
    },
    defaultTab() {
      return this.$refs.tabs.panes[0].name
    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 2em;
  background-color: white;
  min-width: 800px;
}
</style>
