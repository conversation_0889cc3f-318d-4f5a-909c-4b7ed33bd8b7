<template>
  <div class="container">
    <div class="tool-bar">
      <el-button
        size="small"
        @click="handleCreate"
        :disabled="!$store.getters.hasPermission('systems.categories_update')"
      >
        创建分组
      </el-button>
    </div>
    <el-table
      :data="categoriesByPage"
      border
    >
      <el-table-column
        prop="order_number"
        label="排序编号"
        width="100"
      />

      <el-table-column
        prop="name"
        label="分组名称"
        sortable
      />
      <el-table-column
        prop="parent_name"
        label="上级分组名称"
        sortable
      />
      <el-table-column
        prop="business_system_ids"
        label="业务系统列表"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.business_system_ids.length > 0">
            <ul>
              <li
                v-for="system_id in scope.row.business_system_ids"
                :key="system_id"
              >
                {{ systemName(system_id) }}
              </li>
            </ul>
          </div>
          <div
            v-else
            style="padding-left: 25px"
          >
            -
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="查询权限"
        sortable
      >
        <template slot-scope="scope">
          {{ scope.row.query_admins_with_ignore.map( x => x.name).join(" / ") }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作权限"
        sortable
      >
        <template slot-scope="scope">
          {{ scope.row.admins_with_ignore.map( x => x.name).join(" / ") }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="160"
      >
        <template slot-scope="scope">
          <el-button
            size="small"
            @click="handleEdit(scope.row)"
            :disabled="!$store.getters.hasPermission('systems.categories_update')"
          >
            编辑
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDestroyComfirm(scope.row.id)"
            :disabled="!$store.getters.hasPermission('systems.categories_update')"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page.sync="currentPage"
      :page-size="pageSize"
      :total="categories.length"
      background
      layout="total, prev, pager, next, jumper"
      class="categories-pagination"
    />

    <category-edit
      ref="categoryCreate"
      :category="{}"
      :categoryList="categories"
      :systems="systems"
      create-mode
      @update="getCategories"
    />
    <category-edit
      ref="categoryEdit"
      :category="currentCategory"
      :categoryList="categories"
      :systems="systems"
      @update="getCategories"
    />
  </div>
</template>

<script>
import CategoryEdit from './CategoryEdit.vue'
export default {
  components: {
    CategoryEdit
  },
  data () {
    return {
      systems: [],
      categories: [],
      currentCategory: {},
      currentPage: 1,
      pageSize: 15
    }
  },
  computed: {
    categoriesByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.categories.slice(start, end)
    }
  },
  created () {
    this.getSystems()
    this.getCategories()
  },
  methods: {
    getSystems () {
      this.$axios.get('/api/systems/all')
        .then(response => {
          this.systems = response.data
        })
        .catch(() => {})
    },
    getCategories () {
      this.$axios.get('/api/systems/category_list_tree')
        .then(response => {
          this.categories = response.data
        })
        .catch(() => {})
    },
    systemName (id) {
      let system = this.systems.find(x => x.id === id)
      if (system) return system.name
      return '-'
    },
    handleDestroyComfirm (id) {
      this.$confirm('删除分类无法撤销, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.destroyCategory(id)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    handleEdit (category) {
      this.currentCategory = category
      this.$refs.categoryEdit.dialogVisible = true
    },
    handleCreate () {
      this.$refs.categoryCreate.dialogVisible = true
    },

    destroyCategory (id) {
      this.$axios.delete(`/api/systems/categories/${id}`)
        .then(response => {
          this.$message.success('已成功删除分类')
          this.getCategories()
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  .container{
    padding: 20px;
  }
  .tool-bar{
    height: 50px;
    margin-bottom: 10px;
  }
  .el-table{
    width: 100%;
  }
  .categories-pagination{
    margin-top: 20px;
  }
</style>
