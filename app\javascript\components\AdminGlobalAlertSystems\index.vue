<template>
  <div class="container">
    <h2>告警分组管理</h2>
    <hr>
    <el-tabs
        v-model="tab"
        type="border-card"
        @tab-click="routerChange"
    >
      <el-tab-pane
          :disabled="!hasPermission('admin_global_alert_managers.query')"
          name="global_alert_managers"
          label="告警管理"
      >
        <global-alert-categories v-if="tab === 'global_alert_managers'"/>
      </el-tab-pane>
      <el-tab-pane
          :disabled="!hasPermission('admin_global_alert_managers.group_list_tree')"
          name="global_alert_category_groups"
          label="告警分组管理"
      >
        <global-alert-category-groups v-if="tab === 'global_alert_category_groups'"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import GlobalAlertCategories from './GlobalAlertCategories.vue'
import GlobalAlertCategoryGroups from './GlobalAlertCategoryGroups.vue'

export default {
  components: {
    GlobalAlertCategories,
    GlobalAlertCategoryGroups
  },
  data () {
    return {
      tab: this.getRoute()
    }
  },
  watch: {
  },
  methods: {
    getRoute () {
      if (this.hasPermission('admin_global_alert_managers.query')) {
        return 'global_alert_managers'
      }else if(this.hasPermission('admin_global_alert_managers.group_list_tree')){
        return 'global_alert_category_groups'
      }
      this.$message.error('您没有权限操作此功能')
      return ''
    },
    hasPermission (code) {
      return this.$store.getters.hasPermission(code)
    },
    routerChange (tab) {
      this.$router.push({ name: tab.name })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 2em;
  background-color: white;
  min-width: 800px;
}
</style>
