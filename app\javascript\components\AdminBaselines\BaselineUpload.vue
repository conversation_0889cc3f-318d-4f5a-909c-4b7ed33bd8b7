<template>
  <!-- eslint-disable vue/attribute-hyphenation -->
  <div>
    <el-upload
      :action="uploadUrl"
      :headers="headers"
      :file-list="fileList"
      :show-file-list="false"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeFileUpload"
      :on-progress="handleProgress"
    >
      <el-dropdown-item
        v-loading.fullscreen.lock="loading"
        element-loading-text="正在导入系统基线文件，请稍后……"
        type="primary"
        size="small"
        :disabled="!$store.getters.hasPermission('system_baseline.import')"
      >
        导入系统基线
      </el-dropdown-item>
    </el-upload>
    <!-- eslint-enable vue/attribute-hyphenation -->
    <dialog-confirm
      ref="dialogConfirm"
      :content="confirmContent"
      :title="confirmTitle"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script>
import API from '@/api'
import DialogConfirm from "../common/DialogConfirm";

export default {
  components: {
    DialogConfirm
  },
  props: {
    systemId: {
      type: Number,
      required: true
    }
  },
  data () {
    const headers = API.tool.getToken()
    return {
      loading: false,
      fileList: [],
      headers: headers,
      confirmContent: "",
      confirmTitle: "提示"
    }
  },
  computed: {
    uploadUrl () {
      return API.tool.absoluteUrlFor(`/api/systems/${this.systemId}/baselines/upload_validation`)
    }
  },
  methods: {
    handleSuccess (response, file, fileList) {
      this.loading = false
      this.$message.success('系统基线已成功更新')
      this.$emit('update')
    },
    handleError (err, file, fileList) {
      this.loading = false
      if (err.status === 456) {
        this.deleteConfirm(JSON.parse(err.message))
      } else {
        const errorMsg = JSON.parse(err.message)
        this.errorNotifies(errorMsg.error_message)
      }
    },
    errorNotifies (errorMsg) {
      this.$notify.error({
        title: '错误',
        message: errorMsg,
        duration: 0
      })
    },
    beforeFileUpload (file) {
      const matches = file.name.match(/.*\.xlsx/i)
      // 上一行匹配成功返回匹配字符串，失败返回 null
      // 而结果只会判断 true or false，因此要做一下转换
      const isxlsx = matches !== null

      if (!isxlsx) {
        this.$message.error('只能够上传 xlsx 格式文件')
      }
      return isxlsx
    },
    handleProgress () {
      this.loading = true
    },
    deleteConfirm (message) {
      this.$refs.dialogConfirm.dialogVisible = true
      this.confirmContent = message.error_message
    },
    handleConfirm() {
      this.importBaseline()
    },
    importBaseline () {
      this.loading = true

      API.systemBaselines.importBaseline(this.systemId)
        .then(response => {
          this.loading = false
          this.$message.success('系统基线已成功更新')
          this.$emit('update')
        }).catch(() => {
          this.loading = false
        })
    }
  }
}
</script>
