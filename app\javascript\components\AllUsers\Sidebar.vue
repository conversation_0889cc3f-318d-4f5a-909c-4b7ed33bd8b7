<template>
  <el-aside width="220px">
    <el-scrollbar style="height: 100%">
      <el-collapse-transition>
        <span>
          <el-menu
            v-loading="loading"
            :defaultActive="defaultActiveIndex"
            @select="selectItem"
          >
            <div
              v-for="(system_or_category, index) in showTree"
              :key="index"
            >
              <el-menu-item
                :index="`system-${system_or_category.id}`"
              >
                {{ system_or_category.name }}
              </el-menu-item>
            </div>
          </el-menu>
        </span>
      </el-collapse-transition>
    </el-scrollbar>
  </el-aside>
</template>

<script >
import SystemSelect from '@/components/SidebarSystems.vue'

export default {
  components: {
    SystemSelect
  },
  data () {
    return {
      localSidebarMode: 'system'
    }
  },
  computed: {
    sidebarMode () {
      return this.$store.state.sidebarMode
    },
    isShowSidebarSwitch () {
      return this.$settings.showSidebarSwitch
    }
  },
  watch: {
    sidebarMode () {
      this.localSidebarMode = this.sidebarMode
    }
  },
  created () {
    this.localSidebarMode = this.sidebarMode
  },
  methods: {
    sidebarSwitchClick (val) {
      this.$store.commit('sidebarModeSwitch', val)
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .el-aside{
    border-right: 1px solid #EBEEF5;
    height: calc(100vh -#{$header-height});

    .switch-placeholder{
      width: 100%;
      height: 40px;
    }
    .switch-container{
      position: fixed;
      // width - 1,把父的 border-right 露出来
      width: 219px;
      height: 30px;
      bottom: 0;
      padding-top: 10px;
      border-top: 1px solid #EBEEF5;
      background-color: #fff;

      .el-switch{
        padding-left: 20px;
      }
    }
  }

</style>
