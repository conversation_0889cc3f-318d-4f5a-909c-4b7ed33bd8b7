<template>
  <div>
    <div class="toolbar">
      <div class="left">
        <el-form
          :inline="true"
          class="search-box"
        >
          <el-form-item>
            <el-input
              v-model="query.filterText"
              placeholder="搜索用户名称或邮箱"
              size="small"
              prefix-icon="el-icon-search"
              clearable
              class="search-input"
              @clear="clearSearchName"
            />
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="query.code"
              placeholder="搜索员工编号"
              size="small"
              clearable
              class="search-input"
              @clear="clearSearchCode"
            />
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="query.roleIds"
              size="small"
              multiple
              collapse-tags
              placeholder="搜索角色"
            >
              <el-option
                v-for="role in roles"
                :key="role.id"
                :label="role.name"
                :value="role.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="query.status"
              size="small"
              clearable
              placeholder="搜索状态"
            >
              <el-option
                v-for="status in statuses"
                :key="status.id"
                :label="status.name"
                :value="status.key">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="right">
        <user-create @update="gettingUsers" />
        <el-button
          v-if="isShowWindowsDomainImport"
          size="small"
          :disabled="!$store.getters.hasPermission('admin_account_manager.sync_user_form_domain')"
          @click="syncUsersFromDomain"
        >
          从 Windows 域中同步员工列表
        </el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="dataByPage"
      border
    >
      <el-table-column
        property="email"
        label="邮箱"
        width="180"
        sortable
      >
        <template slot-scope="{row}">
          {{ row.email || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        property="name"
        label="姓名"
        width="150"
      >
        <template slot-scope="{row}">
          {{ row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        property="code"
        label="员工编号"
        width="100"
      >
        <template slot-scope="{row}">
          {{ row.code || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        property="status"
        label="状态"
        width="80"
      >
        <span
          slot-scope="{row}"
          :class="statusClass(row.status)"
        >
          {{ statusString(row.status) }}
        </span>
      </el-table-column>
      <el-table-column
        property="last_sign_in_at"
        label="上次登录时间"
        width="140"
      >
        <template slot-scope="{row}">
          {{ row.last_sign_in_at || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        property="roles"
        label="角色"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.roles.length > 0"
          >
            <el-tag
              v-for="item in scope.row.roles"
              slot="reference"
              class="role-tag"
            >
              {{ item.name }}
            </el-tag>
            </role-popover>
          </span>
          <span v-else>
            -
          </span>
        </template>
      </el-table-column>
      <el-table-column
        property="allow_notification"
        label="接收邮件"
        width="80"
      >
        <template slot-scope="scope">
          {{ scope.row.allow_notification ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column v-if="isSmsNotificationEnable"
        property="allow_sms_notification"
        label="接收短信"
        width="80"
      >
        <template slot-scope="scope">
          {{ scope.row.allow_sms_notification ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="240"
      >
        <template slot-scope="scope">
          <div>
            <el-button
              size="small"
              :disabled="!$store.getters.hasPermission('admin_account_manager.admin')"
              @click="accountEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              :disabled="!$store.getters.hasPermission('admin_account_manager.set_permissions')"
              size="small"
              @click="permissionSet(scope.row)"
            >
              权限设置
            </el-button>
            <el-button
              v-if="scope.row.status !== 'disabled'"
              size="small"
              type="danger"
              :disabled="!$store.getters.hasPermission('admin_account_manager.admin')"
              @click="userDisableConfirm(scope.row)"
            >
              禁用
            </el-button>
            <el-button
              v-if="scope.row.status === 'disabled'"
              size="small"
              :disabled="!$store.getters.hasPermission('admin_account_manager.admin')"
              @click="userEnableConfirm(scope.row)"
            >
              启用
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      :page-size.sync="pageSize"
      :total="dataBySearch.length"
      :current-page.sync="currentPage"
      :style="{ marginTop: '20px' }"
      :page-sizes="[15, 30, 50, 100]"
      background
      layout="total, sizes, prev, pager, next, jumper"
    />

    <account-edit
      ref="accountEdit"
      :user="currentAccount"
      @update="gettingUsers"
    />
    <permission-set
      ref="permissionSet"
      :user="currentAccount"
      @update="gettingUsers"
    />
  </div>
</template>

<script>
import UserCreate       from './AdminAccountCreate.vue'
import UsersCreateBatch from './UsersCreateBatch.vue'
import PermissionSet       from './PermissionSet.vue'
import AccountEdit      from './AdminAccountEdit.vue'
import API from '@/api'

export default {
  components: {
    UserCreate,
    AccountEdit,
    PermissionSet,
    UsersCreateBatch
  },
  data () {
    return {
      users:          [],
      roles:          [],
      loading:        false,
      currentAccount: {},
      query:          {
        filterText: null,
        code: null,
        roleIds: null,
        status: 'enabled'
      },
      pageSize:       15,
      currentPage:    1,
      dialogVisible: false,
      statuses:       [
        {
          id: 1,
          name: '正常',
          key: 'enabled'
        },{
          id: 2,
          name: '禁用',
          key: 'disabled'
        },{
          id: 3,
          name: '锁定',
          key: 'locked'
        }
      ]
    }
  },
  computed:   {
    dataBySearch () {
      let searchData = this.users

      if (this.query.filterText) {
        let filterText = this.$lodash.escapeRegExp(this.query.filterText)
        searchData = searchData.filter(x => x.name.match(new RegExp(filterText)) || x.email.match(new RegExp(filterText)))
      }

      if (this.query.code) {
        let queryCode = this.$lodash.escapeRegExp(this.query.code)
        searchData = searchData.filter(x => x.code.match(new RegExp(queryCode)))
      }

      if (this.query.roleIds && this.query.roleIds.length > 0) {
        searchData = searchData.filter(x => this.$lodash.intersection(x.roles.map(y => y.id), this.query.roleIds.map(x => x)).sort().toString() === this.query.roleIds.map(x => x).sort().toString())
      }

      if (this.query.status) {
        let queryStatus = this.$lodash.escapeRegExp(this.query.status)
        searchData = searchData.filter(x => x.status.match(new RegExp(queryStatus)))
      }

      return searchData
    },
    dataByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.dataBySearch.slice(start, end)
    },
    isShowWindowsDomainImport () {
      return this.$settings.showWindowsDomainImport
    },
    isSmsNotificationEnable () {
      return this.$settings.showSmsNotification
    }
  },
  created () {
    this.$emit('changePane', 'users')
    this.gettingUsers()
    this.gettingRoles()
  },
  methods:    {
    gettingUsers () {
      this.loading = true
      this.$axios.get('/admin_api/admin_accounts/admins_permission')
        .then(response => {
          this.users   = this.convertAdmins(response.data)
          this.loading = false
        })
        .catch(() => {
          this.loading = false
          this.users = [{ name: 'Read data error' }]
        })
    },
    gettingRoles () {
      this.loading = true
      this.$axios.get('/api/app_roles/list')
        .then(response => {
          this.roles   = response.data
        })
        .catch(() => {
        })
    },
    accountEdit (account) {
      this.currentAccount                  = account
      this.$refs.accountEdit.dialogVisible = true
    },
    permissionSet (account) {
      this.currentAccount               = account
      this.$refs.permissionSet.dialogVisible = true
    },
    userDisableConfirm (account) {
      this.$confirm('已禁用的用户将无法登陆系统, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.userDisable(account)
        })
        .catch(() => {
          this.$message.info('已取消禁用')
        })
    },
    userEnableConfirm (account) {
      this.$confirm('是否重新启用该用户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.userEnable(account)
        })
        .catch(() => {
          this.$message.info('已取消重新启用')
        })
    },
    userDisable (account) {
      API.adminAccounts.disable(account.id)
        .then(() => {
          this.$message.success('用户已禁用')
          this.gettingUsers()
        })
        .catch(_ => {})
    },
    userEnable (account) {
      API.adminAccounts.enable(account.id)
        .then(() => {
          this.$message.success('用户已重新启用')
          this.gettingUsers()
        })
        .catch(_ => {})
    },
    syncUsersFromDomain () {
      this.loading = true
      this.$axios.post('/admin_api/admin_accounts/sync_from_domain')
        .then(response => {
          if (response.data.success) {
            this.gettingUsers()
            this.loading = false
            this.$message.success('已完成同步')
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    clearSearchName () {
      this.query.filterText = null
    },
    clearSearchCode () {
      this.query.code = null
    },
    convertAdmins (admins) {
      const reverseAdmin = admins.reverse()
      return reverseAdmin.map(admin => {
        // TODO: 需要修改
        const index = admin.roles.findIndex(r => r.name == 'personal')

        if (index > -1 && admin.user.name) {
          admin.roles[index].display_name = `个人用户：${admin.user.name}`
        }
        return admin
      })
    },
    statusClass (status) {
      return status === 'enabled' ?  '' : 'statusClass'
    },
    statusString (string) {
      return this.$t('enumerize.admin.status')[string]
    }
  }
}

</script>

<style>
.statusClass {
  color: #f56c6c;
  font-weight: 600;
}
</style>

<style lang="scss" scoped>
@import '~@/components/variables';

.button-bar {
  @include vertical_center_between;

  .search-input {
    width: 200px;
  }
}

.toolbar{
  @include vertical_top_between;
  margin-bottom: 0px;

  .left {
    margin-top: 20px;
  }
}

.el-button + .el-button {
  margin-left: 5px;
}

.el-tag {
  margin-left: 3px;
}

.container {
  padding:          2em;
  background-color: white;
  min-width:        800px;
}
</style>
