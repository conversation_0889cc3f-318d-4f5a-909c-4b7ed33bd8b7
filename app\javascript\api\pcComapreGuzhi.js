import axios from "@/settings/axios";
import { parseFileName, downloadBlob } from "./tool";

export const index = (data) => {
  return axios.get("/api/pc_guzhi", { params: data });
};
export const exportData = (data) => {
  return axios
    .get(`/api/pc_guzhi/export`, {
      responseType: "blob",
      params: data,
    })
    .then((response) => {
      const fileName = parseFileName(response, "pc-guzhi-export.xlsx");
      downloadBlob(response.data, fileName);
    })
    .catch(() => {});
};
