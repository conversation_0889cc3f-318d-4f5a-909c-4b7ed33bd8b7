<template>
  <el-form
    :inline="true"
    class="search-box"
  >
  <!--
    <el-form-item
    >
      <el-cascader
        v-model="search.department_id"
        v-loading='loadingDepartments'
        element-loading-spinner="el-icon-loading"
        :options="treeDepartments"
        :props="cascaderDepartmentProps"
        filterable
        clearable
        @keyup.enter.native="handleChange"
        placeholder="搜索部门"
        style="width:250px;"
      >
      </el-cascader>
    </el-form-item>
 -->
    <el-form-item>
      <department-select
        v-model="search.department_ids"
        placeholder="搜索部门"
        :department-id="[]"
        size="small"
        :checkStrictly="true"
        multiple
        collapseTags
        filterable
        clearable
        selectStyle="width:250px;margin-right:0px;"
        @keyup.enter.native="handleChange"
      />
    </el-form-item>

    <el-form-item>
      <el-select
        v-model="search.inservice"
        placeholder="搜索状态"
        size="small"
        style="width:150px;"
        @keyup.enter.native="handleChange"
        clearable
        filterable
      >
        <el-option
          key="true"
          label="启用"
          value="true"
        />
        <el-option
          key="false"
          label="禁用"
          value="false"
        />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-input
        v-model="search.job_name"
        size="small"
        placeholder="搜索岗位"
        clearable
        class="common-input"
      />
    </el-form-item>

    <el-form-item>
      <el-button
        type="primary"
        size="small"
        @click="handleChange"
      >
        检索
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import API from '@/api'
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'

export default {
  components: {
    DepartmentSelect
  },
  data () {
    return {
      search: {
        // department_id:  null,
        department_ids: [],
        job_name:       null
      },
      departments: [],
      jobs: [],
      // treeDepartments: [],
      cascaderDepartmentProps: {
        emitPath: false
      }
    }
  },
  created () {
    // this.getDepartments()
    // this.getTreeDepartments()
  },
  methods: {
    handleChange () {
      this.$emit('change', this.$lodash.clone(this.search))
    }
    // getTreeDepartments () {
    //   const params = { mode: 'tree' }
    //   this.loadingDepartments = true
    //   API.departments.index(params)
    //     .then(response => {
    //       this.loadingDepartments = false
    //       this.treeDepartments = response.data
    //     })
    //     .catch(() => {
    //       this.loadingDepartments = false
    //     })
    // }

  }
}
</script>

<style lang="scss" scoped>
.el-form-item{
  margin-bottom: 0;
}
</style>
