<template>
  <div
    v-loading="loading"
    class="container"
  >
    <el-form
      ref="form"
      label-width="140px"
      :model="{rules: rules}"
    >
      <el-form-item
        label="开启角色匹配"
      >
        <el-switch
          v-model="enable"
          active-text="开"
          inactive-text="关"
        />
      </el-form-item>

      <el-form-item
        label="添加规则"
      >
        <el-button
          size="small"
          @click="createNewRule"
        >
          点击添加
        </el-button>
      </el-form-item>
      <el-divider />
      <transition-group name="list-complete">
        <div
          v-for="(item, index) in rules"
          :key="index"
          class="list-complete-item form-item"
        >
          <div class="title">
            {{ `规则 ${index + 1}` }}
          </div>
          <el-form-item
            :prop="`rules.${index}.reference_system`"
            :label="`${system_names.reference_system}角色`"
            :rules="[
              { required: true, message: '请选择角色', trigger: 'change' },
            ]"
          >
            <el-select
              v-model="rules[index].reference_system"
              filterable
              size="small"
              placeholder="请选择角色"
            >
              <el-option
                v-for="role in reference_roles"
                :key="role"
                :value="role"
                :label="role"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :prop="`rules.${index}.alignment_system`"
            :label="`${system_names.alignment_system}角色`"
            :rules="[
              { required: true, message: '请选择角色', trigger: 'change' },
            ]"
          >
            <el-select
              v-model="rules[index].alignment_system"
              filterable
              size="small"
              placeholder="请选择角色"
            >
              <el-option
                v-for="role in alignment_roles"
                :key="role"
                :value="role"
                :label="role"
              />
            </el-select>
          </el-form-item>
          <div class="button">
            <el-button
              size="small"
              type="text"
              class="delete-button"
              @click="handleDeleteRule(index)"
            >
              删除
            </el-button>
          </div>
        </div>
      </transition-group>
      <div class="submit-buttons">
        <el-button
          size="small"
          type="primary"
          @click="submitForm('form')"
        >
          更新设置
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="recheckComparison"
        >
          重新检查当前时间点数据
        </el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import RecheckButton from '@/components/AdminSettings/FdfgCompareGzyss45/RecheckButton'
export default {
  mixins: [RecheckButton],
  data () {
    return {
      loading:         false,
      enable:          false,
      rules:           [],
      system_names:    {},
      reference_roles: [],
      alignment_roles: []

    }
  },
  created () {
    this.getSettings()
  },
  methods: {
    getSettings () {
      this.loading = true
      this.$axios.get('/admin_api/settings/fdfg_gzyss45_comparison/roles_check')
        .then(response => {
          this.loading         = false
          this.enable          = response.data.sameable.enable
          this.rules           = response.data.sameable.rules
          this.system_names    = response.data.system_names
          this.reference_roles = response.data.reference_roles
          this.alignment_roles = response.data.alignment_roles
        })
        .catch(() => {
          this.loading = false
        })
    },
    updateConfirm () {
      this.$confirm('此操作将更新系统设置，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.updateSettings()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    updateSettings () {
      this.$axios.post('/admin_api/settings/fdfg_gzyss45_comparison/roles_check', {
        sameable: {
          enable: this.enable,
          rules:  this.rules
        }
      })
        .then(() => {
          this.$message.success('设置已更新')
          this.getSettings()
        })
        .catch(() => {})
    },
    createNewRule () {
      const itemData = { reference_system: null, alignment_system: null }
      const length   = this.rules.length
      this.$set(this.rules, length, itemData)
    },
    handleDeleteRule (index) {
      this.$delete(this.rules, index)
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.updateConfirm()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .form-item {
    @include vertical_center_between;
    height: 63px;

  }
  .el-select{
    width: 160px;
  }
  .title{
    width: 60px;
    color: $font-color;
    line-height: 40px;
    margin-bottom: 22px;
  }
  .delete-button{
    margin-left: 10px;
    line-height: 40px;
    margin-bottom: 22px;

  }
  .list-complete-item {
    transition: all 0.5s;
  }

  .error {
    color: #E54D42;
    font-weight: bold;
  }

  .list-complete-enter, .list-complete-leave-to
    /* .list-complete-leave-active for below version 2.1.8 */
  {
    opacity: 0;
    transform: translateY(-30px);
  }
  .submit-buttons{
    margin-top: 20px;
  }
</style>
