# frozen_string_literal: true

class ApiController < ApplicationController
  # TODO: 应当限制登录人才能下载
  before_action :authenticate_admin!, except: %i[download_all_export_data generate_all_export_data 
                                                 download_batch_zip jsc_base_info jsc_job_info jsc_department_info]
  before_action :authenticate_policy!, only: %i[audit_report]
  before_action :define_variable, only: %i[audit_report]
  before_action :set_filter, only: %i[change_account_list]
  prepend_before_action :set_start_at, only: %i[change_account_list change_account_info]

  # 部门接口，根据参数without_permission 判断是否返回的数据和权限有关
  def departments
    departments = Department.department_list(current_admin, tree_mode?, without_permission?)
    add_admin_permission(departments) if params[:is_admin_permission] == 'true'
    json_respond departments
  end

  # 台账用的用户树（此方法不加权限）
  def users_in_departments
    json_respond({ tree_user: Department.tree_with_user, inservice_user_count: User.where(inservice: true).count })
  end

  # 获取职务
  def positions
    positions = User.pluck(:position).compact.uniq
    positions.delete('')
    json_respond positions
  end

  def download_batch_zip
    file_path = File.join(DataExport.download_tmp_path, "#{params[:unique]}.rar")
    send_file_compatible_with_msie file_path
  end

  def audit_export_status
    job_status = Sidekiq::Status.status(params[:jid])
    case job_status
    when :complete
      json_respond(success: true, data: { download_url: params[:url] })
    when :queued, :working
      json_respond(success: false, message: I18n.t('audit_report.errors.response_202'))
    else
      json_custom_respond(500, message: I18n.t('audit_report.errors.response_404'))
    end
  end

  # 没有做登录验证
  def download_all_export_data
    options = params.to_unsafe_h.symbolize_keys
    quarter_id       = params[:quarter_id].to_i
    other_quarter_id = params[:other_quarter_id].to_i
    by_department    = params[:by_department]
    options.merge!({
      by_department:    by_department,
      quarter_id:       quarter_id,
      other_quarter_id: other_quarter_id
    })
    DownloadExport::AccountPermissionExport.new(current_admin, options).async_export

    json_respond(success: true)
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    json_custom_respond(500, success: false, error_message: e.message)
  end

  # 没有做登录验证
  def generate_all_export_data
    quarter_id       = params[:quarter_id].to_i
    other_quarter_id = params[:other_quarter_id].to_i

    FileUtils.rm_rf DataExport.excel_export_base_path(quarter_id, other_quarter_id)
    FileUtils.rm_rf DataExport.word_export_base_path(quarter_id)

    quarter = Quarter.find(quarter_id)

    BusinessSystem.inservice.each do |the_system|
      # 泰达的信披系统不导出
      next if Setting.customer_id == 'mfcteda' && the_system.id == 32

      # TODO: 没有做登录验证,后期改造增加
      # next unless current_admin.query_system?(the_system.id)
      # 泰达宏利导出全部员工权限需要跳过信披系统
      next if the_system.id == 32 && Setting.customer_id == 'mfcteda'

      quarter.send(the_system.account_method).all.each do |account|
        account.export_word
        account.export_excel(other_quarter_id)
      end
    end

    audit_log! ({ quarter_id: quarter_id, other_quarter_id: other_quarter_id }),
               action: :generate_all_export_data_success
    json_respond(success: true, errors: [])
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! ({ quarter_id: quarter_id, other_quarter_id: other_quarter_id }), action: :generate_all_export_data_faild
    json_custom_respond(500, error_message: e.message)
  end

  # 生成审计报告,输出离职未禁用、未关联台账
  def audit_report
    # job = AuditExportJob.perform_later(@date_range, @system_ids, @category_ids, @department_ids, @unique)
    # @job_id = job.provider_job_id
    options = params.to_unsafe_h.symbolize_keys
    options.merge!({
      date_range:     @date_range,
      system_ids:     @system_ids,
      category_ids:   @category_ids,
      department_ids: @department_ids,
      unique:         @unique
    })
    DownloadExport::AuditExport.new(current_admin, options).async_export

    audit_log! ({ date_range: @date_range, system_names: @systems.map(&:name), download_url: @download_url, job_id: @job_id }), action: :audit_report_success
    # json_respond(success: true, url: @download_url, job_id: @job_id, message: I18n.t('audit_report.audit_job.success'))
    json_respond(success: true, message: I18n.t('audit_report.audit_job.success'))
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! ({ date_range: @date_range, system_names: @systems.map(&:name) }), action: :audit_report_faild
    json_respond(success: false, error_message: e.message)
  end

  def change_account_info
    total_normal_count = BusinessSystem.inservice.map { |bs| bs.account_class.where(status: true, quarter_id: Quarter.last&.id).count }.sum
    total_normal_user_count = BusinessSystem.inservice.map { |bs| bs.account_class.where(status: true, quarter_id: Quarter.last&.id).pluck(:user_id) }.flatten.uniq.compact.count
    alarm_dimission_record = DimissionAccountsNotCloseAlert.send_message.ransack(created_at_gteq: @alarm_dimission_start_at).result
    add_record = ChangeAccountAlert.add_account.ransack(created_at_gteq: @add_start_at).result
    delete_record = ChangeAccountAlert.delete_account.ransack(created_at_gteq: @delete_start_at).result
    frozen_record = ChangeAccountAlert.change_account.ransack(created_at_gteq: @frozen_start_at, remark_cont: '冻结').result
    cancel_record = ChangeAccountAlert.change_account.ransack(created_at_gteq: @cancel_start_at, remark_cont_any: %w[注销 销户]).result
    alarm_dimission_count = alarm_dimission_record.count
    add_count = add_record.count
    delete_count = delete_record.count
    frozen_count = frozen_record.count
    cancel_count = cancel_record.count
    alarm_dimission_user_count = alarm_dimission_record.pluck(:user_id).uniq.compact.count
    add_user_count = add_record.pluck(:user_id).uniq.compact.count
    delete_user_count = delete_record.pluck(:user_id).uniq.compact.count
    frozen_user_count = frozen_record.pluck(:user_id).uniq.compact.count
    cancel_user_count = cancel_record.pluck(:user_id).uniq.compact.count
    data = {
      alarm_dimission_count:      alarm_dimission_count,
      add_count:                  add_count,
      delete_count:               delete_count,
      frozen_count:               frozen_count,
      cancel_count:               cancel_count,
      total_normal_count:         total_normal_count,
      alarm_dimission_user_count: alarm_dimission_user_count,
      add_user_count:             add_user_count,
      delete_user_count:          delete_user_count,
      frozen_user_count:          frozen_user_count,
      cancel_user_count:          cancel_user_count,
      total_normal_user_count:    total_normal_user_count,
      alarm_dimission_day:        @alarm_dimission_day,
      add_day:                    @add_day,
      delete_day:                 @delete_day,
      frozen_day:                 @frozen_day,
      cancel_day:                 @cancel_day
    }
    json_respond data
  end

  def change_account_list
    sort_name = params['sort_name']
    sort_type = params['sort_type'] == 'descending' ? 'desc' : 'asc'
    all_result = case params[:category]
                 when 'alarm_dimission'
                   DimissionAccountsNotCloseAlert
                     .includes(:business_system, user: %i[job_baselines department])
                     .send_message
                     .ransack(@search_params.merge(created_at_gteq: @alarm_dimission_start_at))
                     .result
                 when 'add'
                   ChangeAccountAlert
                     .includes(:business_system, user: %i[job_baselines department])
                     .add_account
                     .ransack(@search_params.merge(created_at_gteq: @add_start_at))
                     .result
                 when 'delete'
                   ChangeAccountAlert
                     .includes(:business_system, user: %i[job_baselines department])
                     .delete_account
                     .ransack(@search_params.merge(created_at_gteq: @delete_start_at))
                     .result
                 when 'frozen'
                   ChangeAccountAlert
                     .includes(:business_system, user: %i[job_baselines department])
                     .change_account
                     .ransack(@search_params.merge(remark_cont: '冻结', created_at_gteq: @frozen_start_at))
                     .result
                 when 'cancel'
                   ChangeAccountAlert
                     .includes(:business_system, user: %i[job_baselines department])
                     .change_account
                     .ransack(@search_params.merge(remark_cont_any: %w[注销 销户], created_at_gteq: @cancel_start_at))
                     .result
                 end
    result = all_result.order("#{sort_name} #{sort_type}") if sort_name.present?
    if params['download'] == 'excel'
      file_name = get_file_name(params[:category])
      exporter = DataExport::ChangeAccountExport.new(result, file_name)
      send_data(
        exporter.export,
        filename: URI.encode_www_form_component("#{file_name}.xlsx"),
        type:     'application/octet-stream;charset=utf-8'
      )
    else
      result = result.page(params[:page] || 1).per(per_page)
      json_respond(data: result.map(&:simple_output2), total_count: result.total_count)
    end
  end

  # 获取系统的所有岗位下的系统基线（目前主要供鹏华基金使用）
  def system_job_baselines
    authorize nil, policy_class: SystemBaselinePolicy

    inservice_bs = BusinessSystem.inservice
    system_baseline_datas = SystemBaseline.where(business_system_id: inservice_bs.ids)
                                          .map { |item| { id: item.id, name: item.name }}
    job_datas = Job.joins(:job_baseline)
                   .includes(:department, :job_baseline)
                   .map { |item| { id: item.id, name: "#{item.department&.name}-#{item.name}", job_baseline_id: item.job_baseline&.id }}
    systems = inservice_bs.map do |business_system|
      job_baseline_hash = Hash.new { |hash, key| hash[key] = [] }
      business_system.system_baselines.includes(:job_baselines).each do |baseline|
        if baseline.job_baselines.blank?
          job_baseline_hash[-1] << baseline.id
        else
          baseline.job_baselines.each do |job_baseline|
            job_baseline_hash[job_baseline.id] << baseline.id
          end
        end
      end
      jobs = job_baseline_hash.map do |job_baseline_id, baseline_ids|
        if job_baseline_id == -1
          job_id = -1
          job_name = '非标准岗位基线'
        else
          job_data = job_datas.find { |item| item[:job_baseline_id] == job_baseline_id }
          job_id = job_data&.[](:id)
          job_name = job_data&.[](:name)
        end
        baselines = system_baseline_datas.select { |item| baseline_ids.include?(item[:id]) }
        {
          job_id:    job_id,
          job_name:  job_name,
          baselines: baselines
        }
      end
      {
        system_id:   business_system.id,
        system_name: business_system.name,
        jobs:        jobs
      }
    end
    json_respond(systems)
  end

  def jsc_base_info
    data = JscDataServices::AllDataService.new.execute
    json_respond(data)
  end

  def jsc_job_info
    job_info = JscDataServices::AllDataService.new.job_info
    json_respond({job_info: job_info})
  end

  def jsc_department_info
    department_info = JscDataServices::AllDataService.new.all_department_infos
    json_respond({department_info: department_info})
  end

  private

  # 文件名称
  def get_file_name(category)
    case category
    when 'alarm_dimission' then '离职未禁用'
    when 'add' then '新增账号'
    when 'delete' then '删除账号'
    when 'frozen' then '冻结账号'
    when 'cancel' then '注销账号'
    else
      '账号状态变化统计'
    end
  end

  def define_variable
    @system_ids     = params[:system_ids].blank? ? current_admin.business_systems_in_maintain.pluck(:id) : params[:system_ids]
    @systems        = BusinessSystem.where(id: @system_ids)
    @category_ids   = params[:category_ids].blank? ? current_admin.alert_categories_in_maintain.pluck(:id) : params[:category_ids]
    @unique         = "#{current_admin.id}_#{Time.now.to_i}"
    @download_url   = "/api/download_batch_zip?unique=#{@unique}"
    @date_range     = params[:date_range]
    @department_ids = params[:department_ids]
  end

  def tree_mode?
    params[:mode] == 'tree'
  end

  # 想要默认是false, 默认需要权限
  def without_permission?
    params[:without_permission].present? && params[:without_permission] == 'true'
  end

  def filter_params
    JSON.parse params[:account_filter], symbolize_names: true
  end

  def authenticate_policy!
    authorize nil, policy_class: ApiPolicy
  end

  def set_start_at
    @add_day = UserDefinedSetting.add_day
    @delete_day = UserDefinedSetting.delete_day
    @frozen_day = UserDefinedSetting.frozen_day
    @cancel_day = UserDefinedSetting.cancel_day
    @alarm_dimission_day = UserDefinedSetting.alarm_dimission_day
    @add_start_at = @add_day.present? ? @add_day.days.ago : nil
    @delete_start_at = @delete_day.present? ? @delete_day.days.ago : nil
    @frozen_start_at = @frozen_day.present? ? @frozen_day.days.ago : nil
    @cancel_start_at = @cancel_day.present? ? @cancel_day.days.ago : nil
    @alarm_dimission_start_at = @alarm_dimission_day.present? ? @alarm_dimission_day.days.ago : nil
  end

  def set_filter
    @filter = if params['filter'].present?
                JSON.parse params['filter']
              else
                {}
              end
    @search_params = {
      account_code_or_account_name_cont: @filter['query'],
      user_department_id_in:             @filter['departments']
    }
    @search_params[:account_status_eq] = true if @filter['status'] == 'true'
    @search_params[:account_status_eq] = false if @filter['status'] == 'false'
    @search_params[:alert_user_inservice_eq] = true if @filter['user_status'] == 'true'
    @search_params[:alert_user_inservice_eq] = false if @filter['user_status'] == 'false'
    @search_params[:user_id_blank] = true if @filter['user_status'] == 'noLedger'
  end

  def add_admin_permission(data)
    is_all_permission = current_admin.permission? 'admin_account_manager.all_permission'
    if is_all_permission
      data.each { |x| x['is_query'] = true }
    else
      department_ids = current_admin.departments_in_query.pluck(:id)
      data.each { |x| x['is_query'] = department_ids.include?(x['id']) }
    end
  end
end
