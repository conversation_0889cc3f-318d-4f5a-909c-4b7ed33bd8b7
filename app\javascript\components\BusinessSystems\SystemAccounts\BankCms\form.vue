<template>
  <el-drawer
    :title="dialogTitle"
    :visible.sync="visible"
    :before-close="handleClose"
    size="800px"
    direction="rtl"
    destroy-on-close
    class="edit-permission-api-drawer"
  >
    <div class="container">

      <div class="button-bar">
        <el-button
          v-if="dialogFlag === 'show'"
          :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
          @click="dialogFlag = 'edit'"
        >
          修改用户
        </el-button>
        <el-button
          v-if="dialogFlag === 'show'"
          @click="handleLinkRolesReadonly"
        >
          角色详情
        </el-button>
        <el-button
          v-if="dialogFlag === 'show'"
          :disabled="localAccount.status !== '1' && !$store.getters.hasPermission('system_permission_manager.update_account')"
          @click="handleLinkRoles"
        >
          角色授权
        </el-button>
        <el-button
          v-if="dialogFlag === 'show' && deleteData"
          type="danger"
          @click="handleDestroyAccount"
          :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
        >
          删除用户
        </el-button>
      </div>

      <el-form
        ref="form"
        :model="localAccount"
        :rules="rules"
        label-width="140px"
      >
        <el-form-item
          prop="user_id"
          label="用户编号"
        >
          <el-input
            v-model="localAccount.user_id"
            :disabled="dialogFlag !== 'create'"
            class="form-item"
          />
        </el-form-item>
        <el-form-item
          prop="login_id"
          label="登录账号"
        >
          <el-input
            v-model="localAccount.login_id"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>
        <el-form-item
          prop="user_name"
          label="用户姓名"
        >
          <el-input
            v-model="localAccount.user_name"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="belong_org_id"
          label="所属机构"
        >
          <el-select
            v-model="localAccount.belong_org_id"
            :disabled="dialogFlag === 'show'"
            filterable
            class="form-item"
          >
            <el-option
              v-for="item in orgs"
              :key="item.org_id"
              :value="item.org_id"
              :label="item.org_name"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          prop="cb_difference"
          label="是否有催收权限"
        >
          <el-select
            v-model="localAccount.cb_difference"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          >
            <el-option
              value="1"
              label="是"
            />
            <el-option
              value="2"
              label="否"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          prop="status"
          label="状态"
        >
          <el-select
            v-model="localAccount.status"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          >
            <el-option
              value="0"
              label="锁定"
            />
            <el-option
              value="1"
              label="正常"
            />
            <el-option
              value="2"
              label="停用"
              disabled
            />
          </el-select>
        </el-form-item>

        <el-form-item
          prop="cert_type"
          label="证件类型"
        >
          <el-select
            v-model="localAccount.cert_type"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          >
            <el-option
              value="Ind11"
              label="其他个人证件"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          prop="cert_id"
          label="证件编号"
        >
          <el-input
            v-model="localAccount.cert_id"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>


        <el-form-item
          prop="company_tel"
          label="单位电话"
        >
          <el-input
            v-model="localAccount.company_tel"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="mobile_tel"
          label="手机号码"
        >
          <el-input
            v-model="localAccount.mobile_tel"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="email"
          label="电子邮件"
        >
          <el-input
            v-model="localAccount.email"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="online_percent"
          label="在岗时间比率"
        >
          <el-input
            v-model="localAccount.online_percent"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="birthday"
          label="出生日期"
        >
          <el-date-picker
            v-model="localAccount.birthday"
            :disabled="dialogFlag === 'show'"
            type="date"
            value-format="yyyy/MM/dd"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="gender"
          label="性别"
        >
          <el-select
            v-model="localAccount.gender"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          >
            <el-option
              value="1"
              label="男性"
            />
            <el-option
              value="2"
              label="女性"
            />
            <el-option
              value="0"
              label="未知的性别"
            />
            <el-option
              value="9"
              label="未说明性别"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          prop="family_add"
          label="家庭住址"
        >
          <el-input
            v-model="localAccount.family_add"
            :disabled="dialogFlag === 'show'"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 10}"
            maxlength="125"
            show-word-limit
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="educational_bg"
          label="学历"
        >
          <el-select
            v-model="localAccount.educational_bg"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          >
            <el-option
              value="1"
              label="本科或以上"
            />
            <el-option
              value="Graduate"
              label="研究生"
            />
            <el-option
              value="2"
              label="专科"
            />
            <el-option
              value="Undergraduate"
              label="大学本科"
            />
            <el-option
              value="3"
              label="其他"
            />
            <el-option
              value="College degree or"
              label="大学专科或专科学校"
            />
            <el-option
              value="Secondary & technical schools"
              label="中等专业学校或中等技术学校"
            />
            <el-option
              value="Technical schools"
              label="技术学校"
            />
            <el-option
              value="High School"
              label="高中"
            />
            <el-option
              value="Junior high school"
              label="初中"
            />
            <el-option
              value="Primary School"
              label="小学"
            />
            <el-option
              value="Illiterate or semi-literate"
              label="文盲或半文盲"
            />
            <el-option
              value="Unknown"
              label="未知"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          prop="am_level"
          label="客户经理级别"
        >
          <el-input
            v-model="localAccount.am_level"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="title"
          label="行内职务"
        >
          <el-input
            v-model="localAccount.title"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="education_exp"
          label="教育经历"
        >
          <el-input
            v-model="localAccount.education_exp"
            :disabled="dialogFlag === 'show'"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 10}"
            maxlength="250"
            show-word-limit
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="vocation_exp"
          label="工作经历"
        >
          <el-input
            v-model="localAccount.vocation_exp"
            :disabled="dialogFlag === 'show'"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 10}"
            maxlength="250"
            show-word-limit
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="position"
          label="职称"
        >
          <el-input
            v-model="localAccount.position"
            :disabled="dialogFlag === 'show'"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 10}"
            maxlength="125"
            show-word-limit
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="qualification"
          label="任职资格"
        >
          <el-input
            v-model="localAccount.qualification"
            :disabled="dialogFlag === 'show'"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 10}"
            maxlength="125"
            show-word-limit
            class="form-item"
          />
        </el-form-item>
        <el-form-item
          prop="remark"
          label="备注"
        >
          <el-input
            v-model="localAccount.remark"
            :disabled="dialogFlag === 'show'"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 10}"
            maxlength="100"
            show-word-limit
            class="form-item"
          />
        </el-form-item>
        <el-divider />

        <el-form-item
          prop="input_org"
          label="登记单位"
        >
          <el-input
            v-model="localAccount.input_org"
            disabled
            class="form-item"
          />
        </el-form-item>
        <el-form-item
          prop="input_user"
          label="登记人"
        >
          <el-input
            v-model="localAccount.input_user"
            disabled
            class="form-item"
          />
        </el-form-item>
        <el-form-item
          prop="input_date"
          label="登记日期"
        >
          <el-input
            v-model="localAccount.input_date"
            disabled
            class="form-item"
          />
        </el-form-item>
        <el-form-item
          prop="input_date"
          label="登记时间"
        >
          <el-input
            v-model="localAccount.input_date"
            disabled
            class="form-item"
          />
        </el-form-item>
        <el-form-item
          prop="update_user"
          label="更新人"
        >
          <el-input
            v-model="localAccount.update_user"
            disabled
            class="form-item"
          />
        </el-form-item>
        <el-form-item
          prop="update_date"
          label="更新日期"
        >
          <el-input
            v-model="localAccount.update_date"
            disabled
            class="form-item"
          />
        </el-form-item>
        <el-form-item
          prop="update_time"
          label="更新时间"
        >
          <el-input
            v-model="localAccount.update_time"
            disabled
            class="form-item"
          />
        </el-form-item>
      </el-form>
      <div class="button-bar">
        <el-button
          v-if="dialogFlag !== 'show'"
          @click="visible = false"
        >
          取 消
        </el-button>
        <el-button
          v-if="dialogFlag !== 'show'"
          type="primary"
          @click="handleCommit"
        >
          确 定
        </el-button>
      </div>
      <roles
        v-if="dialogFlag !== 'create'"
        ref="roles"
        :systemId="systemId"
        :accountId="localAccount && localAccount.user_id"
      />
      <roles-table
        v-if="dialogFlag !== 'create'"
        ref="rolesTable"
        :systemId="systemId"
        :accountId="localAccount && localAccount.user_id"
      />
    </div>
  </el-drawer>
</template>

<script>
import Roles         from './roles.vue'
import RolesTable    from '@/components/BusinessSystems/SystemAccounts/BankCms/rolesTable'
import MyPermissions from '@/components/mixins/MyPermissions'
import { catchError }          from '@/utils/axios_utils'
import { validateSpecialChars } from '@/utils/form_validates'

export default {
  components: {
    Roles,
    RolesTable
  },
  mixins: [MyPermissions],
  props:      {
    systemId: {
      type:     Number,
      required: true
    },
    account:  {
      validator: function (val) {
        return val === null || typeof val === 'object'
      },
      required:  true
    },
    orgs:     {
      type:     Array,
      required: true
    }
  },
  data () {
    return {
      visible:        false,
      dialogFlag:     'create',
      localAccount:   this.account,
      rules:          {
        user_id:       [
          { required: true, message: '用户编号不能为空', trigger: 'blur' },
          { validator: validateSpecialChars, trigger: 'blur' }
        ],
        login_id:      [
          { required: true, message: '登录账号不能为空', trigger: 'blur' },
          { validator: validateSpecialChars, trigger: 'blur' }
        ],
        user_name:     [
          { required: true, message: '用户姓名不能为空', trigger: 'blur' },
          { validator: validateSpecialChars, trigger: 'blur' }
        ],
        belong_org_id: [{ required: true, message: '所属机构不能为空', trigger: 'blur' }],
        status:        [{ required: true, message: '状态不能为空', trigger: 'blur' }]

      },
      AccountDefault: {
        user_id:        null,
        login_id:       null,
        user_name:      null,
        belong_org_id:  null,
        cb_difference:  null,
        status:         '1',
        cert_type:      null,
        cert_id:        null,
        company_tel:    null,
        mobile_tel:     null,
        email:          null,
        online_percent: null,
        birthday:       null,
        gender:         null,
        family_add:     null,
        educational_bg: null,
        am_level:       null,
        title:          null,
        education_exp:  null,
        vocation_exp:   null,
        position:       null,
        qualification:  null,
        remark:         null,
        input_org:      null,
        input_user:     null,
        input_date:     null,
        input_time:     null,
        update_user:    null,
        update_date:    null,
        update_time:    null
      },

    }
  },
  computed:   {
    dialogTitle () {
      if (this.dialogFlag === 'create') return '创建用户'
      if (this.dialogFlag === 'edit') return '修改用户'
      if (this.dialogFlag === 'show') return '用户详情'
    },
  },
  watch:      {
    systemId () {
      this.dialogFlag = 'create'
      this.initializeLocalAccount()
    },
    account () {
      this.initializeLocalAccount()
    }
  },
  created () {
    this.initializeLocalAccount()
  },
  methods:    {
    initializeLocalAccount () {
      if (this.account) {
        this.localAccount = this.$lodash.clone(this.account)
      } else {
        this.localAccount = this.$lodash.clone(this.AccountDefault)
      }
    },

    handleClose (done) {
      if (this.dialogFlag === 'show') return done()

      this.$confirm('确认直接关闭吗？未保存的数据将会丢失')
        .then(_ => {
          done()
        })
        .catch(_ => {})
    },
    handleCommit () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.dialogFlag === 'create') return this.operationReviewCreate()
          if (this.dialogFlag === 'edit') return this.operationReviewUpdate()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    resetForm () {
      this.$refs['form'].resetFields()
    },
    operationReviewCreate () {
      const guid = this.$guidGenerate()
      this.$operationReview(this.systemId, guid)
        .then(() => {
          this.createRole(guid)
        })
        .catch(() => {})
    },
    operationReviewUpdate () {
      const guid = this.$guidGenerate()
      this.$operationReview(this.systemId, guid)
        .then(() => {
          this.updateRole(guid)
        })
        .catch(() => {})
    },
    operationReviewDestroy () {
      const guid = this.$guidGenerate()
      this.$operationReview(this.systemId, guid)
        .then(() => {
          this.destroyAccount(guid)
        })
        .catch(() => {})
    },
    createRole (guid) {
      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts`, {
        guid:    guid,
        account: this.localAccount
      })
        .then(response => {
          this.$message.success('用户已创建')
          this.$emit('change')
          this.visible = false
        })
        .catch(() => {})

    },
    updateRole (guid) {
      this.$axios.put(`/admin_api/edit_api/${this.systemId}/accounts/${this.localAccount.org_id}`, {
        guid:    guid,
        account: this.localAccount
      })
        .then(response => {
          this.$message.success('用户已修改')
          this.$emit('change')
          this.visible = false
        })
        .catch(() => {})
    },
    handleLinkRoles () {
      const form   = this.$refs.roles
      form.visible = true
    },
    handleLinkRolesReadonly () {
      const form   = this.$refs.rolesTable
      form.visible = true
    },
    handleDestroyAccount () {
      this.$confirm('确认删除该用户吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.operationReviewDestroy()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    destroyAccount (guid) {
      this.$axios.delete(`/admin_api/edit_api/${this.systemId}/accounts/${this.localAccount.user_id}?guid=${guid}`)
        .then(response => {
          this.$message.success('用户已删除')
          this.$emit('change')
          this.visible = false
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .container {
    padding: 20px;
    height: calc(100vh - 80px);
    overflow-y: auto;
  }

  .button-bar {
    @include vertical_center_right;
    margin-bottom: 20px;
  }

  .form-item {
    width: 100%;
  }
</style>
