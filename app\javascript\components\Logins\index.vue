<template>
  <el-container>
    <the-sso-login
      v-if="isSsoLoginEnable"
      :message="message"
    />
    <the-user-login
      v-else
      :message="message"
    />
  </el-container>
</template>

<script>
import TheUserLogin from './TheUserLogin.vue'
import TheSsoLogin from './TheSsoLogin.vue'

export default {
  components: {
    TheUserLogin,
    TheSsoLogin
  },
  props: {
    message: {
      type:    String,
      default: ''
    }
  },
  data () {
    return {
    }
  },
  computed: {
    isSsoLoginEnable () {
      return this.$settings.authMethod === 'sso' && this.ssoStatus
    },
    ssoSetting() {
      return this.$settings.sso
    },
    ssoStatus () {
      // 易办跳转登录初次登录需要带上参数
      console.log(this.ssoSetting.adapter)
      if (this.ssoSetting.adapter == 'yiban') {
        return this.$store.state.loginSt == 'yiban' ? true : false
      }
      else {
        return true
      }
    }
  },
  created () {
    if (this.$route.query.login_st) {
      this.$store.commit('loginStChange', this.$route.query.login_st)
    }
  },
  methods: {
  }
}
</script>

