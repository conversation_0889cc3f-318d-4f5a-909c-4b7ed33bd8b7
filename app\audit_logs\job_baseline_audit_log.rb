class JobBaselineAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '岗位基线管理'
    @operation_category = '岗位基线'
    super
  end

  def create
    @operation = '创建岗位基线'
    @comment = "创建了岗位基线：「#{params.name}」 ID：「#{params.id}」，详情：#{job_baseline_detail(params)}"
    create_audit_log
  end

  def create_fail
    @operation = '创建岗位基线'
    @comment   = "创建岗位基线失败：「#{params.name}」"
    create_audit_log
  end

  def update
    @operation = '更新岗位基线'
    before_job_baselines = params[1]
    after_job_baselines = params[2]

    str = update_job_baseline_detail(before_job_baselines, after_job_baselines)
    @comment = "更新了岗位基线： ID：「#{params[0].id}」 名称：「#{params[0].name}」，详情：#{str}"
    create_audit_log
  end

  def update_fail
    @operation = '更新岗位基线'
    @comment   = "更新岗位基线失败： ID：「#{params.id}」 名称：「#{params.name}」"
    create_audit_log
  end

  def destroy
    @operation = '删除岗位基线'
    @comment   = "删除了岗位基线： ID：「#{params.id}」 名称：「#{params.name}」，详情：#{job_baseline_detail(params)}"
    create_audit_log
  end

  def export
    @operation = '导出岗位基线'
    @comment   = '导出了全部岗位基线'
    create_audit_log
  end

  def bind_roles_delete
    @operation = '删除岗位基线'
    @comment   = "删除了岗位对应的系统基线： 岗位名称：「#{params[0]}」 系统名称：「#{params[1]}」 系统基线ID：「#{params[2]}」"
    create_audit_log
  end

  def bind_roles_update
    @operation = '更新岗位基线'
    @comment = "更新了岗位对应的系统基线： 岗位名称：「#{params[0]}」 系统名称: 「#{params[1]}」，"\
               "系统基线ID：「#{params[2]}」 绑定角色：#{params[3]}"
    create_audit_log
  end

  def bind_roles_create
    @operation = '新建岗位基线'
    @comment = "新建了岗位对应的系统基线：岗位名称：「#{params[0]}」， 系统名称：「#{params[1]}」， 绑定角色：#{params[2]}"
    create_audit_log
  end

  protected

  # 岗位系统基线详情
  def job_baseline_detail(job_baseline)
    baselines = job_baseline.system_baselines.includes(:business_system)
    ban_systems = job_baseline.ban_systems

    "岗位基线：#{system_baseline_detail(baselines)}，禁止系统存在账号：#{ban_system_detail(ban_systems)}"
  end

  def system_baseline_detail(system_baselines)
    system_baselines.map { |x| "#{x.business_system.name}「#{x.name}」" }.join('、')
  end

  def ban_system_detail(ban_systems)
    return '无' unless ban_systems.present?

    ban_systems.pluck(:name).join('、')
  end

  # 岗位系统基线详情变化
  def update_job_baseline_detail(before_job_baselines, after_job_baselines)
    del_system_baselines = before_job_baselines[:system_baselines] - after_job_baselines[:system_baselines]
    add_system_baselines = after_job_baselines[:system_baselines] - before_job_baselines[:system_baselines]
    del_ban_system = before_job_baselines[:ban_systems] - after_job_baselines[:ban_systems]
    add_ban_system = after_job_baselines[:ban_systems] - before_job_baselines[:ban_systems]

    str = ''
    str += "删除系统基线：#{system_baseline_detail(del_system_baselines)}" if del_system_baselines.present?
    str += "新增系统基线：#{system_baseline_detail(add_system_baselines)}" if add_system_baselines.present?
    str += "删除禁止存在账号系统：#{ban_system_detail(del_ban_system)}" if del_ban_system.present?
    str += "新增禁止存在账号系统：#{ban_system_detail(add_ban_system)}" if add_ban_system.present?
    str
  end

  # 将无效的参数过滤掉，避免记录到日志里
  def filter_params(params)
    json = params.to_unsafe_h.clone
    bs_json = json['business_systems']
    bs_json&.select! { |o| o['system_baseline_id'].present? }
    json
  end
end
