import axios from '@/settings/axios'

export const login = (data) => {
  return axios.post('/api/auth/sign_in', { email: data.email, password: data.password })
}
export const ssoLogin = (token) => {
  return axios.post('/sso_sessions/sso_service', { token: token })
}
export const externalLogin = (data) => {
  return axios.post('/api/external_sign_in', { email: data.email, password: data.password })
}
export const myFunctions = (headers = {}) => {
  return axios.get('/admin_api/my_function_codes', { headers: headers })
}
export const myAdminRouters = (headers = {}) => {
  return axios.get('/admin_api/my_admin_routers', { headers: headers })
}

export const myAdminPrivileges = (headers = {}) => {
  return axios.get('/admin_api/my_admin_privileges', { headers: headers })
}

export const forgetPassword = (data) => {
  return axios.post('/admin_api/admin_accounts/forget_password', { login_code: data.login_code })
}
