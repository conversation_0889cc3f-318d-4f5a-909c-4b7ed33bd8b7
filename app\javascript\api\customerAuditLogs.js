import axios                           from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

// 搜索条件比较多，所以用post
export const list = (params) => {
  return axios.post('/api/customer_audit_logs/search', params)
}

// 业务系统列表，取自app.yml的customer_audit_log -> business_systems
export const systems = () => {
  return axios.get('/api/customer_audit_logs/systems')
}

// 获取所有操作类型
export const operationCategories = () => {
  return axios.get('/api/customer_audit_logs/operation_categories')
}