<template>
  <div class="container">
    <div class="select-data">
      <quarter-select
        v-model="quarterId"
        default-select="second"
        @quarterChange="handleQuarterChange"
      />
    </div>
    <div
      v-loading="loading"
      class="differences"
    >
      <diff-accounts-table
        :other-quarter-name="quarterName"
        :data-schema="dataSchema"
        :diff-datas="differences"
      />
    </div>
  </div>
</template>
<script>
import API               from '@/api'
import QuarterSelect     from '@/components/common/QuarterSelect.vue'
import DiffAccountsTable from '@/components/AllUsers/DiffAccountsTable'

export default {
  components: {
    QuarterSelect,
    DiffAccountsTable
  },
  props:      {
    userId: {
      type:     String,
      required: true
    }
  },
  data () {
    return {
      loading:     false,
      quarterId:   0,
      quarterName: '',
      differences: { increase: [], reduce: [] },
      dataSchema:  [
        { label: '系统名称', property: 'system_name' },
        { label: '账号编码', property: 'account_code' },
        { label: '账号名称', property: 'account_name' },
        { label: '账号状态', property: 'account_status' }
      ]
    }
  },
  computed: {
    currentQuarterId () {
      return this.$store.state.current_quarter.id
    }
  },
  watch: {
    userId () {
      this.getAccountListDifferences()
    },
    currentQuarterId () {
      this.getAccountListDifferences()
    }
  },
  created () {
  },
  methods: {
    handleQuarterChange (payload) {
      this.quarterName = payload.name
      this.getAccountListDifferences()
    },
    getAccountListDifferences () {
      if (this.quarterId === 0) return
      if (!this.userId) return

      this.reset()
      this.loading = true
      const params = {
        user_id:             this.userId,
        contrast_quarter_id: this.quarterId,
        current_quarter_id:  this.currentQuarterId
      }
      API.users.accountListDifferences(params)
        .then(response => {
          if (response.data.increase.length === 0 && response.data.reduce.length === 0) {
            // 啥也不干
          } else {
            this.differences = response.data
          }
          this.loading = false
        })
        .catch(error => {
          this.loading = false
        })
    },
    reset () {
      this.differences = { increase: [], reduce: [] }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.container {
  margin: auto;

  .differences{
    margin-top: 20px;
  }

}
</style>
