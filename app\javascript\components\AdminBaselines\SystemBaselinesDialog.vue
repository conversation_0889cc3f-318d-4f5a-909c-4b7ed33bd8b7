<template>
  <el-dialog
    :visible.sync="visible"
    :title="modeTitle"
    width="960px"
    @open="handleOpen"
    @close="handleClose"
    append-to-body
  >
    <div class="dialog-container">
      <system-baselines
        :system-id="systemId"
        :display-search="false"
      />
    </div>

    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="visible = false">关 闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import API             from '@/api'
import SystemBaselines from './SystemBaselines.vue'

export default {
  components: {
    SystemBaselines
  },
  props:      {
    system: {
      type:     Object,
      default: () => {},
      required: true
    }
  },
  data () {
    return {
      visible: false,
      loading: true,
      systemId: null
    }
  },
  computed:   {
    modeTitle () {
      return `${this.system.name} 系统基线管理`
    }
  },
  watch:      {
    system: {
      handler (_newV, _oldV){
        this.initParams()
      },
      deep: true
    }
  },
  created ()  {
  },
  methods:    {
    initParams () {
      this.systemId = this.system.id
    },
    handleOpen () {
    },
    handleClose () {
      this.$emit('updateSystemBaselines', this.systemId)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";
</style>
