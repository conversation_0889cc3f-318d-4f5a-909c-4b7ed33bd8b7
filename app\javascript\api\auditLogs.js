import axios                           from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'
import API                             from '@/api'

export const getSections = (params) => {
  return axios.get('/admin_api/audit_logs/sections', { params: params })
}

export const getLogs = (params) => {
  return axios.post('/admin_api/audit_logs', params)
}

export const createSearchRecord = (params) => {
  return axios.post('/admin_api/audit_logs/create_search_record', params)
}

export const getSearchRecords = () => {
  return axios.get('admin_api/audit_logs/audit_log_search_records')
}

export const deleteSearchRecord = (params) => {
  return axios.delete('admin_api/audit_logs/delete_search_record', { params: params })
}

export const exportAuditLogs = (params) => {
  const url = '/admin_api/audit_logs/export_audit_logs'
  return API.downloadRecords.downloadAsync(url, params, 'post')
}
