# frozen_string_literal: true

module ActAsSsoApis
  extend ActiveSupport::Concern

  private

  # Rails 本身会对 cookie 做 unescape 转义，但客户系统 cookie 不是我们系统生成的
  # 不符合 RFC 规范，我们需要获取到 cookie 原始数据
  def original_cookies
    cookies_hash           = {}
    original_cookie_string = request.get_header('HTTP_COOKIE')

    original_cookie_string.split(/[;,] */n).each do |p|
      next if p.empty?

      k, v = p.split('='.freeze, 2)
      cookies_hash[k] = v
    end
    cookies_hash
  end

  def get_resource_attribute
    case Setting.sso['adapter']
    when 'mszq'
      mszq_get_login_name
    when 'zyfund'
      zyfund_get_login_name
    when 'csfund'
      csfund_get_login_name
    when 'yiban'
      yiban_get_login_name
    else
      raise SsoConnector::AdapterNotFound, "not found adapter #{Setting.sso['adapter']}"
    end
  end

  def mszq_get_login_name
    token      = original_cookies['LRToken']
    login_name = SsoConnector::MszqSso.get_login_name(token)
    [:code, login_name]
  rescue SsoConnector::LoginNameParseFailed => e
    login_logger.tagged('sso', 'msza_sso') do
      login_logger.debug { "SSO LRToken is: #{token}" }
      login_logger.error { "parse sso get_login_name api return #{e.message}" }
    end
    raise SsoConnector::LoginNameParseFailed
  end

  def yiban_get_login_name
    token      = params['token']
    login_name = SsoConnector::YibanSso.get_login_name(token)
    [:code, login_name]
  rescue SsoConnector::LoginNameParseFailed => e
    login_logger.tagged('sso', 'yiban_sso') do
      login_logger.debug { "SSO PcToken is: #{token}" }
      login_logger.error { "parse sso get_login_name api return #{e.message}" }
    end
    raise SsoConnector::LoginNameParseFailed
  end

  def zyfund_get_login_name
    token      = params['token']
    login_name = SsoConnector::ZyfundSso.get_login_name(token)
    [:code, login_name]
  rescue SsoConnector::LoginNameParseFailed => e
    login_logger.tagged('sso', 'zyfund_sso') do
      login_logger.debug { "SSO PcToken is: #{token}" }
      login_logger.error { "parse sso get_login_name api return #{e.message}" }
    end
    raise SsoConnector::LoginNameParseFailed
  end

  def csfund_get_login_name
    token      = params['token']
    login_name = SsoConnector::CsfundSso.get_login_name(token)
    [:code, login_name]
  rescue SsoConnector::LoginNameParseFailed => e
    login_logger.tagged('sso', 'csfund_sso') do
      login_logger.debug { "SSO token is: #{token}" }
      login_logger.error { "parse sso get_login_name api return #{e.message}" }
    end
    raise SsoConnector::LoginNameParseFailed
  end

  def mszq_logout
    logout_url = Setting.sso&.[]('logout_url')
    Http.post_form(logout_url) if logout_url.present?
  end

end
