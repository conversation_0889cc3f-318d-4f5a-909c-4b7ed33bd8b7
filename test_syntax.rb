#!/usr/bin/env ruby

# 测试语法的简单脚本
puts "测试 start_sidekiq.rb 的语法..."

begin
  # 直接 require 文件来测试
  require_relative 'config/initializers/start_sidekiq'
  puts "✅ 文件加载成功！语法正确。"
rescue SyntaxError => e
  puts "❌ 语法错误："
  puts e.message
rescue LoadError => e
  puts "⚠️  加载错误（可能是依赖问题，但语法正确）："
  puts e.message
rescue => e
  puts "⚠️  运行时错误（语法正确，但执行时出错）："
  puts e.message
  puts "这通常意味着语法是正确的，只是运行时环境问题。"
end
