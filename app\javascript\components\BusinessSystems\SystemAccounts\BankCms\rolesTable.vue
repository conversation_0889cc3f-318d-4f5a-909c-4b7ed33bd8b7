<template>
  <el-drawer
    title="用户角色详情"
    :visible.sync="visible"
    size="600px"
    direction="rtl"
    append-to-body
    destroy-on-close
  >
    <div class="container">
      <el-table
        :data="roles"
        stripe
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="角色编号"
        />
        <el-table-column
          prop="name"
          label="角色名称"
        />
      </el-table>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="visible = false">关 闭</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { catchError } from '@/utils/axios_utils'

export default {
  props:    {
    systemId:  {
      type:     Number,
      required: true
    },
    accountId: {
      validator: function (val) {
        return val === null || typeof val === 'number' || typeof val === 'string'
      },
      required:  true
    }
  },
  data () {
    return {
      visible: false,
      roles:   []
    }
  },
  computed: {
    accountRoles () {
      this.allRoles.filter(x => true)
    }
  },
  watch:    {
    systemId () {
      this.initializeLocalRole()
    },
    accountId () {
      this.initializeLocalRole()
    }
  },
  created () {
    this.initializeLocalRole()
  },
  methods:  {
    initializeLocalRole () {
      this.getAccountRoles()
    },
    getAccountRoles () {
      if (!this.accountId) return

      this.$axios.get(`/admin_api/edit_api/${this.systemId}/accounts/${this.accountId}/roles`)
        .then(response => {
          this.roles = response.data
        })
        .catch(error => {
          catchError(error, '获取用户角色列表时出现问题')
        })

    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/components/variables';

.container {
  padding:    20px;
  height:     calc(100vh - 80px);
  overflow-y: auto;
}

.dialog-footer {
  margin-top: 20px;
}
</style>