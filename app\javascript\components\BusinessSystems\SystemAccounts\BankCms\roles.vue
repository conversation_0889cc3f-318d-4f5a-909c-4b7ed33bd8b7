<template>
  <el-drawer
    title="用户角色授权"
    :visible.sync="visible"
    :before-close="handleClose"
    size="600px"
    direction="rtl"
    append-to-body
    destroy-on-close
  >
    <div class="container">
      <el-form
        ref="form"
        :model="{ roles: accountRoleIds }"
        :rules="rules"
        label-width="20px"
      >
        <el-form-item
          prop="roles"
          label=""
        >
          <el-checkbox-group v-model="accountRoleIds">
            <div
              v-for="role in allRoles"
              :key="role.id"
            >
              <el-checkbox
                :label="role.id"
              >
                {{ role.id }} - {{ role.name }}
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="roleIdsReset">恢 复</el-button>
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="handleCommit">确 定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { catchError } from '@/utils/axios_utils'

export default {
  props:   {
    systemId:  {
      type:     Number,
      required: true
    },
    accountId: {
      validator: function (val) {
        return val === null || typeof val === 'number' || typeof val === 'string'
      },
      required:  true
    }
  },
  data () {
    return {
      visible:        false,
      roleMenus:      [],
      accountRoleIds: [],
      allRoles:       [],
      rules:          {}
    }
  },
  watch:   {
    systemId () {
      this.initializeLocalRole()
    },
    accountId () {
      this.initializeLocalRole()
    }
  },
  created () {
    this.initializeLocalRole()
    this.getAllRoles()
  },
  methods: {
    initializeLocalRole () {
      this.getAccountRoles()
    },
    handleClose (done) {
      this.$confirm('确认直接关闭吗？未保存的数据将会丢失')
        .then(_ => {
          done()
        })
        .catch(_ => {})
    },
    resetForm () {
      this.$refs['form'].resetFields()
    },
    roleIdsReset () {
      this.accountRoleIds = this.roleMenus.map(x => x.id)
    },
    getAccountRoles () {
      if (!this.accountId) return

      this.$axios.get(`/admin_api/edit_api/${this.systemId}/accounts/${this.accountId}/roles`)
        .then(response => {
          this.roleMenus      = response.data
          this.accountRoleIds = this.roleMenus.map(x => x.id)
        })
        .catch(error => {
          catchError(error, '获取用户角色列表时出现问题')
        })

    },
    getAllRoles () {
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/roles`)
        .then(response => {
          this.allRoles = response.data
        })
        .catch(error => {
          catchError(error, '获取全部角色列表时出现问题')
        })

    },
    handleCommit () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.operationReviewUpdate()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    operationReviewUpdate () {
      const guid = this.$guidGenerate()
      this.$operationReview(this.systemId, guid)
        .then(() => {
          this.updateAccountRoles(guid)
        })
        .catch(() => {})
    },
    updateAccountRoles (guid) {
      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts/${this.accountId}/roles`, {
        guid:     guid,
        role_ids: this.accountRoleIds
      })
        .then(response => {
          this.$message.success('用户角色授权已修改')
          this.visible = false
        })
        .catch(error => {
          catchError(error, '角色授权时出现问题')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .container {
    padding: 20px;
    height: calc(100vh - 80px);
    overflow-y: auto;
  }

  .form-item {
    width: 100%;
  }
</style>
