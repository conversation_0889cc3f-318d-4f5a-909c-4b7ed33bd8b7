<template>
  <div>
    <el-card
      v-loading="loading"
      style="margin: 0px 20px 20px 20px"
    >
      <el-tabs
        v-model="activeName"
        @tab-click="handleClick"
      >
        <el-tab-pane
          label="告警列表"
          name="first"
        >
          <div class="fromSty">
            <el-form
              label-width="80px"
              inline
              size="small"
            >
              <span v-for="schema in searchSchema">
                <el-form-item
                  :label="schema.label"
                  class="filter-form-item"
                >
                  <el-input
                    v-if="schema.type === 'input'"
                    v-model="filter[schema.property]"
                    :placeholder="schema.label"
                    clearable
                    @change="search()"
                  />
                  <el-date-picker
                    v-if="schema.type === 'date'"
                    v-model="filter[schema.property]"
                    :placeholder="schema.label"
                    type="date"
                    clearable
                    value-format="yyyy-MM-dd"
                    @change="search()"
                  />
                  <el-select
                    v-if="schema.type === 'select'"
                    v-model="filter[schema.property]"
                    :placeholder="schema.label"
                    clearable
                    @change="search()"
                  >
                    <el-option
                      v-for="item in schema.selct_schema"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <el-select
                    v-if="schema.type === 'select_system'"
                    v-model="filter[schema.property]"
                    multiple
                    filterable
                    clearable
                    default-first-option
                    placeholder="请选择系统名称"
                  >
                    <el-option
                      v-for="item in systemOptions"
                      :key="item.bs_id"
                      :label="item.sub_bs_name"
                      :value="item.bs_id"
                    />
                  </el-select>
                  <el-select
                    v-if="schema.type === 'select_fund_diff_status'"
                    v-model="filter[schema.property]"
                    multiple
                    filterable
                    clearable
                    default-first-option
                    placeholder="请选择差异状态"
                  >
                    <el-option
                      v-for="item in diffStatusOptions"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>

                  <department-select
                    v-if="schema.type === 'select_department'"
                    v-model="filter[schema.property]"
                    placeholder="选择部门"
                    :department-id="[]"
                    size="small"
                    :checkStrictly="true"
                    multiple
                    collapseTags
                    filterable
                    select-style="width: 220px; margin-right: 0px;"
                  />
                </el-form-item>

              </span>
              <span v-if="searchSchema.length == 0">
                <el-form-item
                  v-show="filters.includes('accountName')"
                  label="账号名称"
                  class="filter-form-item"
                >
                  <el-input
                    v-model="filter.account_name"
                    placeholder="请输入账号名称"
                    clearable
                  />
                </el-form-item>
                <el-form-item
                  v-show="filters.includes('accountCode')"
                  label="账号编码"
                  class="filter-form-item"
                >
                  <el-input
                    v-model="filter.account_code"
                    placeholder="请输入账号编码"
                    clearable
                  />
                </el-form-item>
                <el-form-item
                  v-show="filters.includes('fund_email')"
                  label="基金产品"
                  class="filter-form-item"
                >
                  <el-input
                    v-model="filter.fund_name"
                    placeholder="请输入基金名称"
                    clearable
                  />
                </el-form-item>
                <el-form-item
                  v-show="filters.includes('fund_email')"
                  label="报表名称"
                  class="filter-form-item"
                >
                  <el-input
                    v-model="filter.report_name"
                    placeholder="请输入报表名称"
                    clearable
                  />
                </el-form-item>
                <el-form-item
                  v-show="filters.includes('systemName')"
                  label="系统名称"
                  class="filter-form-item"
                >
                  <el-select
                    v-model="filter.systemName"
                    multiple
                    filterable
                    clearable
                    default-first-option
                    placeholder="请选择系统名称"
                  >
                    <el-option
                      v-for="item in systemOptions"
                      :key="item.bs_id"
                      :label="item.sub_bs_name"
                      :value="item.bs_id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  v-show="filters.includes('diffStatus')"
                  label="差异状态"
                  class="filter-form-item"
                >
                  <el-select
                    v-model="filter.diffStatus"
                    multiple
                    filterable
                    clearable
                    default-first-option
                    placeholder="请选择差异状态"
                  >
                    <el-option
                      v-for="item in diffStatusOptions"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </span>
              <el-form-item
                v-show="filters.includes('status')"
                label="处理状态"
                class="filter-form-item"
              >
                <el-select
                  v-model="filter.statusFilter"
                  clearable
                  placeholder="请选择处理状态"
                >
                  <el-option
                    label="已处理"
                    value="1"
                  />
                  <el-option
                    label="未处理"
                    value="0"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-show="filters.includes('recoverStatus')"
                label="恢复状态"
                class="filter-form-item"
              >
                <el-select
                  v-model="filter.recoverStatusFilter"
                  clearable
                  placeholder="请选择恢复状态"
                >
                  <el-option
                    label="已恢复"
                    value="1"
                  />
                  <el-option
                    label="未恢复"
                    value="0"
                  />
                  <el-option
                    label="已关闭"
                    value="2"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                label="告警日期"
                class="filter-form-item"
              >
                <el-date-picker
                  v-model="filter.systemTime"
                  type="daterange"
                  unlink-panels
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  :clearable="false"
                  :picker-options="pickerOptions"
                  class="filter-form-item-datepicker"
                />
              </el-form-item>
              <el-form-item
                label=" "
                label-width="10px"
                class="filter-form-item"
              >
                <el-button
                  type="primary"
                  size="small"
                  @click="filterChange"
                >
                  查 询
                </el-button>
                <el-button
                  type="primary"
                  :disabled="tableData.length <= 0 || !$store.getters.hasPermission('global_alerts.export')"
                  size="small"
                  @click="exportBtn"
                >
                  导 出
                </el-button>
                <el-button
                  type="primary"
                  :disabled="disableProcess.processed_count==disableProcess.count || activeIds.length<=0 || !maintain"
                  size="small"
                  @click="processBtn"
                >
                  批量处理
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <process
            :dialog-visible="dialogVisible"
            :active-ids="activeIds"
            :maintain="maintain"
            @dialogAcive="dialogAcive"
            @statusIdVal="statusIdVal($event)"
          />
          <div v-loading="tableLoading">
            <CustomAlert
              :table-data="tableData"
              :maintain="maintain"
              :from-title="fromTitle"
              :loading="loading"
              :table-schema="tableSchema"
              @detailId="detailId($event)"
              @activeId="activeId"
              @handleFilterItems="changeFilterItems"
              @sort="handleSort"
            />
            <el-pagination
              style="margin-top:20px"
              :current-page.sync="currentPage"
              :page-size.sync="pageSize"
              :page-sizes="[15, 20, 30, 50, 100, 200, 500, 1000]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane
          :disabled="!globalAlertManagerPermission"
          label="分类统计"
          name="second"
        >
          <ClassStatictical
            v-if="activeName=='second'"
            :total="total"
            :group-by-bs="groupByBs"
            :from-title="fromTitle"
            :table-title="tableTitlaActive"
            :date-val="dateVal"
          />
        </el-tab-pane>
      </el-tabs>
      <el-button
        size="small"
        class="float-full-screen-button"
        @click="alertsShowFullScreen = !alertsShowFullScreen"
      >
        <i
          v-show="!alertsShowFullScreen"
          class="el-icon-rank"
        />
        <i
          v-show="alertsShowFullScreen"
          class="el-icon-s-grid"
        />
      </el-button>
    </el-card>
  </div>
</template>

<script>
import API                      from '@/api'
import process                  from './process.vue'
import AlertListMixin           from '@/components/AdminGlobalAlert/mixins/AlertListMixin'
import ClassStatictical         from './components/ClassStatictical.vue'
import CustomAlert              from './components/CustomAlert.vue'
import DepartmentSelect         from '@/components/common/DepartmentSelect.vue'

export default {
  components: {
    process,
    ClassStatictical,
    CustomAlert,
    DepartmentSelect
  },
  mixins: [AlertListMixin],
  props:      {
    tabList:        {
      type: Array,
      //   required: true
      default: () => {}
    },
    tableTitle:     {
      type:     String,
      required: true
    },
    fromTitle:      {
      type:     String,
      required: true
    },
    systemSelectList: {
      type:    Array,
      default: () => {}
    },
    diffStatusList: {
      type:    Array,
      default: () => {}
    },
    newTotal:          {
      type:     Number,
      required: true
    },
    dateVal:        {
      type:    Array,
      default: () => {}
    },
    disableProcess: {
      type: Object
    },
    errorMessage:   {
      type: String
      // default: () => {}
    },
    groupByBs:      {
      type:     Array,
      required: true
    },
    maintain:       {
      type:     Boolean,
      default:  false
    },
    loading:       {
      type:     Boolean,
      default:  false
    },
    searchSchema:  {
      type:     Array,
      default: () => {}
    },
    tableSchema:  {
      type:     Array,
      default: () => {}
    }
  },
  computed: {
    globalAlertManagerPermission () {
      return this.$store.getters.hasPermission('admin_global_alert_managers.query') || this.$store.getters.hasPermission('global_alerts.query')
    }
  },
  created() {
    this.filter.systemTime = this.dateVal
  },
  data () {
    return {
      sortProps:            {},
      alertsShowFullScreen: false,
      total:                0,
      tableData:            [],
      newtableData:         [],
      gridData:             [],
      messageType:          [],
      activeIds:            [],
      BaselineMismatch:     '',
      activeName:           'first',
      filter: {
        account_name:         '',
        account_code:         '',
        fund_name:            '',
        report_name:          '',
        statusFilter:         this.$store.state.global_alert_search_status,
        recoverStatusFilter:  this.$store.state.global_alert_search_recover_status,
        systemName:           [],
        diffStatus:           [],
        systemTime:           '',
        user_code:            '',
        user_name:            '',
        column_1:             '',
        column_2:             '',
        column_3:             '',
        column_4:             '',
        column_5:             '',
        column_6:             '',
        column_7:             '',
        column_8:             '',
        column_9:             '',
        column_10:            '',
        departmentIds:        []
      },
      dialogVisible:        false,
      enable:               '',
      systemOptions:        [],
      diffStatusOptions:    [],
      tableTitlaActive:     '',
      rules:                {
        enable_at: [
          { type: 'date', required: true, message: '请选择日期', trigger: 'change' }
        ]
      },
      pickerOptions:        {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end   = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end   = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end   = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },

      filterItems: [
        'accountName',   // 账号名称
        'accountCode',   // 账号编码
        'systemName',    // 系统名称
        'status',        // 处理状态
        'recoverStatus'  // 恢复状态
        // 'diffStatus'  // 差异状态  目前除了 信披-投资交易系统权限差异 都不用，所以先注释
      ],
      filters: [],
    }
  },
  watch: {
    newTotal(newVal, old) {
      this.total = newVal
    },
    tableTitle (newVal, oldVal) {
      this.tableTitlaActive = newVal
      this.filters = [
        'status',        //处理状态
        'recoverStatus', //恢复状态
        'systemName',    //系统名称
      ]

      if (this.tableTitlaActive === 'fund_email') {
        this.filters.push('fund_email')
      }
      this.filterChange()
    },
    tabList (newVal, oldVal) {
      this.tableData    = newVal
      this.newtableData = newVal
      this.filter.systemTime   = this.dateVal
      this.filter.account_name = ''
      this.filter.account_code = ''
      this.filter.systemName   = []
      this.filter.diffStatus   = []
      this.filter  = {
        account_name:         '',
        account_code:         '',
        fund_name:            '',
        report_name:          '',
        statusFilter:         this.filter.statusFilter,
        recoverStatusFilter:  this.filter.recoverStatusFilter,
        systemName:           [],
        diffStatus:           [],
        systemTime:           '',
        user_code:            '',
        user_name:            '',
        column_1: '',
        column_2: '',
        column_3: '',
        column_4: '',
        column_5: '',
        column_6: '',
        column_7: '',
        column_8: '',
        column_9: '',
        column_10: '',
        departmentIds: []
      }
      // this.search()
    },
    systemSelectList (newVal, oldVal) {
      this.systemOptions = newVal
    },
    diffStatusList (newVal, oldVal) {
      this.diffStatusOptions = newVal
    },
    dateVal (newVal, oldVal) {
      this.filter.systemTime = newVal
      this.handleQueryChange()
    },
    detailId () {},
    statusIdVal (e) { },
    dialogAcive (e) {
      this.dialogVisible = false
    },
    alertsShowFullScreen () {
      this.$emit('fullScreen', this.alertsShowFullScreen)
    }
  },
  methods: {
    resetFilter() {
      this.filter  = {
        account_name:         '',
        account_code:         '',
        fund_name:            '',
        report_name:          '',
        statusFilter:         this.filter.statusFilter,
        recoverStatusFilter:  this.filter.recoverStatusFilter,
        systemName:           [],
        diffStatus:           [],
        systemTime:           '',
        user_code:            '',
        user_name:            '',
        column_1: '',
        column_2: '',
        column_3: '',
        column_4: '',
        column_5: '',
        column_6: '',
        column_7: '',
        column_8: '',
        column_9: '',
        column_10: '',
        departmentIds: []
      }
    },
    handleClick (e) {
      this.activeName = e.name
    },
    filterChange () {
      this.search()
    },
    activeId (e) {
      this.activeIds = e
    },
    detailId (e) {
      this.$emit('detailId', e)
    },
    statusIdVal (e) {
      this.$emit('statusIdVal', e)
    },
    dialogAcive (e) {
      this.dialogVisible = false
    },
    search () {
      this.currentPage = 1
      this.handleQueryChange()
    },
    resetForm (formName) {
      this.$axios.get('admin_api/global_alerts').then((res) => {
        this.tableData = res.data.filter(i => i.type === this.messageType)
      }).catch((err) => {
        console.log(err)
      })
    },
    exportBtn (e) {
      //this.getLoading = true
      API.users.messageAlert({
        alert_category: this.tableTitle,
        begin_time:     this.filter.systemTime[0],
        account_code:   this.filter.account_code,
        account_name:   this.filter.account_name,
        end_time:       this.filter.systemTime[1],
        bs_ids:         this.filter.systemName.join(', '),
        deal_status:    this.filter.statusFilter,
        recover_status: this.filter.recoverStatusFilter
      }).then((res) => {
        //this.getLoading = false
      }).catch((err) => {
        //this.getLoading = false
        console.log(err)
      })
    },
    processBtn () {
      this.dialogVisible = true
    },
    changeFilterItems (val) {
      this.filters = val
    },
    handleSort(sortProps) {
      this.sortProps = { prop: sortProps.prop, order: sortProps.order }
      this.search()
    }
  }
}

</script>
<style lang="scss" scoped>

.el-card {
  position: relative;
}

.float-full-screen-button {
  position:  absolute;
  top:       20px;
  right:     20px;
  //padding: 9px 15px;
  padding:   5px 10px;
  font-size: 16px;
  color:     #0077E7;
}
.filter-form-item{
  width: 300px;
}

.filter-form-item-datepicker{
  width: 220px !important;
}
.el-input{
  width: 220px;
}
.el-select{
  width: 220px;
}

.fromSty {
  margin-top: 10px;
}

</style>
