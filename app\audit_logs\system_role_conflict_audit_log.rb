# frozen_string_literal: true

# 用户操作审计
class SystemRoleConflictAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = I18n.t('system_role_conflicts.module_name')
    @operation_category = '角色冲突'
    super
  end

  def create_conflicts_users
    @operation          = '创建冲突数据'
    @comment            = '重新创建了冲突数据'
    create_audit_log
  end

  def create
    @operation          = '创建冲突规则'
    @comment            = "创建了冲突规则：#{conflict_info}"
    create_audit_log
  end

  def destroy
    @operation          = '删除冲突规则'
    @comment            = "删除了冲突规则：#{conflict_info}"
    create_audit_log
  end

  def export_success
    @operation          = '导出冲突规则'
    @comment            = '导出冲突规则成功'
    create_audit_log
  end

  def upload_success
    @operation          = '导入冲突规则'
    @comment            = '导入冲突规则成功'
    create_audit_log
  end

  def upload_failed
    @operation          = '导入冲突规则'
    @comment            = '导入冲突规则失败'
    create_audit_log
  end

  private

  def conflict_info
    "「#{params.output[:bs_name]}」的「#{params.output[:role_name]}」与「#{params.output[:conflict_bs_name]}」的「#{params.output[:conflict_role_name]}」相互冲突"
  end
end
