# frozen_string_literal: true

# 用户操作审计
class LedgerAuditLog < ApplicationAuditLog

  def initialize(user, request, params)
    @operation_module = '台账数据管理'
    super
  end

  def ledger_update
    bs, ledger, user    = params
    @operation_category = bs.name
    @operation          = '账号关联'
    @comment            = "#{bs.name}: 账号「#{ledger.account_code_value}」关联至员工 #{user.name}（#{user.code}）"
    create_audit_log
  end

  def ledger_delete
    bs, ledger, user    = params
    @operation_category = bs.name
    @operation          = '删除账号关联'
    @comment            = "#{bs.name}: 账号「#{ledger.account_code_value}」已删除关联至员工 #{user&.name}（#{user&.code}）"
    create_audit_log
  end

  def ignore_matcher_change
    bs, ledger, on_or_off = params
    @operation_category   = bs.name
    @operation            = '账号设置'
    @comment              = "#{bs.name}: 账号「#{ledger.account_code_value}」禁用智能匹配选项设置为：#{on_or_off ? '是' : '否'}"
    create_audit_log
  end

  def ledger_update_bulk
    bs, result          = params
    @operation_category = bs.name
    @operation          = '账号批量关联'

    @comment = result.map do |record|
      "#{bs.name}: 账号「#{record[:ledger].account_code_value}」关联至员工 #{record[:user].name}（#{record[:user].code}）"
    end.join('; ')
    create_audit_log
  end

end
