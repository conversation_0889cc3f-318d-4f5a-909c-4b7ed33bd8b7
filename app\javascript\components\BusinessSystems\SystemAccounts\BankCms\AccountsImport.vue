<template>
  <span>
    <el-upload
      :action="uploadUrl"
      :headers="headers"
      :fileList="fileList"
      :onSuccess="handleSuccess"
      :onError="handleError"
      :before-upload="handleStart"
      :show-file-list="false"
      :disabled="!$store.getters.hasPermission('system_permission_manager.user_excel_upload')"
    >
      <el-button
        v-loading="loading"
        :disabled="!$store.getters.hasPermission('system_permission_manager.user_excel_upload')"
      >
        Excel 导入
      </el-button>
    </el-upload>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="Excel 导入数据检查"
      width="950px"
    >
      <div v-loading="loadingUpload">
        <el-divider />
        <div
          v-for="(account, index) in accounts"
          :key="index"
          class="form-line"
        >
          <el-link
            icon="el-icon-close"
            @click="handleDeleteAccount(index)"
          />
          <el-form
            :ref="`form${index}`"
            :inline="true"
            :model="account"
            :rules="rulesFor(index, account.business_system_id)"
            label-width="100px"
            size="small"
          >
            <div class="tag-line">
              <el-form-item
                label=""
                prop=""
              >
                <el-tag>
                  {{ systemName(account.business_system_id) }}
                </el-tag>
                <el-tag
                  v-show="account.method === 'create'"
                  type="success"
                >
                  创建
                </el-tag>
                <el-tag
                  v-show="account.method === 'update'"
                  type="warning"
                >
                  更新
                </el-tag>

              </el-form-item>
            </div>
            <div class="line1">

              <el-form-item
                label="所属机构"
                prop="org_id"
              >
                <el-select
                  v-if="account.business_system_id === 301"
                  v-model="account.org_id"
                  filterable
                  placeholder="请选择机构"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'org_id')"
                >
                  <el-option
                    v-for="org in all_orgs[301]"
                    :key="org.org_id"
                    :label="`${org.main_frame_org_id} - ${org.org_name}`"
                    :value="org.org_id"
                  />
                </el-select>
                <el-select
                  v-if="account.business_system_id === 303"
                  v-model="account.org_id"
                  filterable
                  placeholder="请选择机构"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'org_id')"
                >
                  <el-option
                    v-for="org in all_orgs[303]"
                    :key="org.dept_id"
                    :label="`${org.dept_id} - ${org.dept_name}`"
                    :value="org.dept_id"
                  />
                </el-select>
                <!-- 资金系统导入B表是机构编号，提交的是机构ID -->
                <el-select
                  v-if="account.business_system_id === 304"
                  v-model="account.org_id"
                  filterable
                  placeholder="请选择机构"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'org_id')"
                >
                  <el-option
                    v-for="org in all_orgs[304]"
                    :key="org.org_id"
                    :label="org.org_name"
                    :value="org.i_id"
                  />
                </el-select>
                <el-select
                  v-if="account.business_system_id === 305"
                  v-model="account.org_id"
                  filterable
                  placeholder="请选择机构"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'org_id')"
                >
                  <el-option
                    v-for="org in all_orgs[305]"
                    :key="org.org_id"
                    :label="`${org.org_id} - ${org.org_name}`"
                    :value="org.org_id"
                  />
                </el-select>
                <el-select
                  v-if="account.business_system_id === 306"
                  v-model="account.org_id"
                  filterable
                  placeholder="请选择机构"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'org_id')"
                >
                  <el-option
                    v-for="org in all_orgs[306]"
                    :key="org.org_id"
                    :label="`${org.org_id} - ${org.org_name}`"
                    :value="org.org_id"
                  />
                </el-select>

              </el-form-item>

              <el-form-item
                label="用户编号"
                prop="user_id"
              >
                <el-input
                  v-model="account.user_id"
                  placeholder="用户编号"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'user_id')"
                />
              </el-form-item>

              <el-form-item
                label="用户名称"
                prop="user_name"
              >
                <el-input
                  v-model="account.user_name"
                  placeholder="用户名称"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'user_name')"
                />
              </el-form-item>
            </div>

            <div class="line1">
              <el-form-item
                label="手机号"
                prop="account_phone"
              >
                <el-input
                  v-model="account.account_phone"
                  placeholder="手机号"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'account_phone')"
                />
              </el-form-item>

              <el-form-item
                label="岗位基线"
                prop="baseline_id"
              >
                <el-select
                  v-model="account.baseline_id"
                  filterable
                  placeholder="请选择岗位基线"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'baseline_id')"
                >
                  <el-option
                    v-for="baseline in all_baselines[account.business_system_id]"
                    :key="baseline.id"
                    :label="baseline.name"
                    :value="baseline.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="所选角色"
                prop="role_ids"
              >
                <el-select
                  v-model="account.role_ids"
                  filterable
                  :multiple="isMultipleRole(account)"
                  placeholder="请选择额外角色"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'role_ids')"
                >
                  <el-option
                    v-for="role in all_roles[account.business_system_id]"
                    :key="role.id"
                    :label="`${role.id} - ${role.name}`"
                    :value="role.id"
                  />
                </el-select>
              </el-form-item>
            </div>

            <div class="line1">
              <el-form-item
                v-if="displayPhone(account.business_system_id)"
                label="联系电话"
                prop="phone"
              >
                <el-input
                  v-model="account.phone"
                  placeholder="联系电话"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'phone')"
                />
              </el-form-item>

              <el-form-item
                v-if="displayIdNumber(account.business_system_id)"
                label="身份证号"
                prop="id_number"
              >
                <el-input
                  v-model="account.id_number"
                  placeholder="身份证号"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'id_number')"
                />
              </el-form-item>

              <el-form-item
                v-if="displayEmail(account.business_system_id)"
                label="电子邮箱"
                prop="email"
              >
                <el-input
                  v-model="account.email"
                  placeholder="电子邮箱"
                  class="form-item"
                  @change="handleChangeOrg($event, index, 'email')"
                />
              </el-form-item>
            </div>
          </el-form>
          <el-divider />
        </div>
      </div>

      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="handleCancel">取 消</el-button>
        <el-button
          type="primary"
          @click="submitConfirm"
        >
          确认导入
        </el-button>
      </span>
    </el-dialog>
  </span>
</template>

<script>
import { catchError } from '@/utils/axios_utils'
import { validatePhone } from '@/utils/form_validates'
import API from '@/api'
export default {
  name:    'AccountsImportVue',
  props:   {
    systemId: {
      type:     Number,
      required: true
    }
  },
  data () {
    const headers = API.tool.getToken()

    return {
      uploadUrl:     `/admin_api/edit_api/${this.systemId}/accounts/upload`,
      fileList:      [],
      headers:       headers,
      loading:       false,
      loadingUpload: false,
      visible:       false,
      accounts:      [],
      all_orgs:      { 301: [], 303: [], 304: [], 305: [], 306: [] },
      all_roles:     { 301: [], 303: [], 304: [], 305: [], 306: [] },
      all_baselines: { 301: [], 303: [], 304: [], 305: [], 306: [] },
      formStatus:    [],
      requestId:     null,
      systems:       []
    }
  },
  created () {
    this.getSystems()
  },
  methods: {
    getSystems () {
      this.loading = true
      this.$axios.get('/admin_api/ledgers/systems')
        .then(response => {
          this.loading = false
          this.systems = response.data
        })
        .catch(() => {
          this.loading = false
        })
    },
    systemName (bsId) {
      const system = this.systems.find(x => x.id === bsId)
      return system.name
    },
    check () {
      this.accounts.forEach((account, index) => {
        const formName = `form${index}`
        this.$refs[formName][0].validate(valid => {
          this.formStatus[index] = !!valid
        })
      })
    },
    submitConfirm () {
      this.check()
      if (this.formStatus.every(x => x)) {
        this.operationReview()
      } else {
        this.$message.error('请检查必填项')
        return false
      }
    },
    operationReview () {
      const guid = this.requestId
      // 二次复核
      this.$operationReview(this.systemId, guid)
        .then(() => {
          this.submit(guid)
        })
        .catch(() => {})
    },
    submit (guid) {
      this.loadingUpload = true
      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts/upload/submit`, {
        guid:     guid,
        accounts: this.accounts
      })
        .then(() => {
          this.visible = false
          this.loadingUpload = false
          this.$message.success('数据导入成功')
          this.$emit('update')
        })
        .catch(error => {
          this.loadingUpload = false
          switch (error.status) {
            case 456:
              break
            case 457:
              this.accounts = error.data.data
              break
            default:
              break
          }
        })
    },
    handleSuccess (response, _file, _fileList) {
      this.loading   = false
      this.visible   = true
      this.accounts  = response.data
      this.requestId = response.request_id
      this.fileName  = response.file_name
      const bsIds = this.$lodash.uniq(this.accounts.map(x => x.business_system_id))
      this.getOptionData(bsIds)
    },
    handleExceed (files, fileList) {
      console.log(files, fileList)
    },
    handleError (err, _file, _fileList) {
      this.loading = false
      const errorMsg = JSON.parse(err.message)
      this.$message.error('文件上传失败，服务器返回：' + errorMsg.error_message)
    },
    handleClose (done) {
      this.$confirm('确认直接关闭吗？未上传的数据将会丢失')
        .then(_ => {
          this.cancelAndLog(done)
        })
        .catch(() => {})
    },
    handleStart () {
      this.loading = true
    },
    cancelAndLog (done) {
      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts/upload/cancel`, {
        request_id: this.requestId,
        file_name:  this.fileName
      })
        .then(() => {
          done()
        })
        .catch(() => {})
    },
    handleCancel () {
      const done = () => {
        this.visible = false
      }
      this.handleClose(done)
    },
    getOptionData (bsIds) {
      this.getAllOrgs(bsIds)
      this.getAllRoles(bsIds)
      this.getBaselines(bsIds)
    },
    getAllOrgs (bsIds) {
      if (this.systemId === 0) return

      this.loading = true
      if (bsIds.indexOf(301) >= 0) {
        this.$axios.get(`/admin_api/edit_api/301/organizations`, {
          params: {
            parent_id: '1'
          }
        })
          .then(response => {
            this.loading  = false
            this.all_orgs[301] = response.data
          })
          .catch(error => {
            this.loading = false
            catchError(error, '获取机构列表数据失败')
          })
      }

      if (bsIds.indexOf(303) >= 0) {
        this.$axios.get(`/admin_api/edit_api/303/organizations`, {
          params: {
            parent_id: '1'
          }
        })
          .then(response => {
            this.loading  = false
            this.all_orgs[303] = response.data
          })
          .catch(error => {
            this.loading = false
            catchError(error, '获取机构列表数据失败')
          })
      }
      if (bsIds.indexOf(304) >= 0) {
        this.$axios.get(`/admin_api/edit_api/304/organizations`, {
          params: {
            parent_id: '1'
          }
        })
          .then(response => {
            this.loading  = false
            this.all_orgs[304] = response.data
          })
          .catch(error => {
            this.loading = false
            catchError(error, '获取机构列表数据失败')
          })
      }
      if (bsIds.indexOf(305) >= 0) {
        this.$axios.get(`/admin_api/edit_api/305/organizations`, {
          params: {
            parent_id: '1'
          }
        })
          .then(response => {
            this.loading  = false
            this.all_orgs[305] = response.data
          })
          .catch(error => {
            this.loading = false
            catchError(error, '获取反洗钱机构列表数据失败')
          })
      }
      if (bsIds.indexOf(306) >= 0) {
        this.$axios.get(`/admin_api/edit_api/306/organizations`, {
          params: {
            parent_id: '1'
          }
        })
          .then(response => {
            this.loading  = false
            this.all_orgs[306] = response.data
          })
          .catch(error => {
            this.loading = false
            catchError(error, '获取统一门户机构列表数据失败')
          })
      }
    },
    getAllRoles (bsIds) {
      if (this.systemId === 0) return

      this.loading = true
      if (bsIds.indexOf(301) >= 0) {
        this.$axios.get('/admin_api/edit_api/301/roles')
          .then(response => {
            this.loading   = false
            this.all_roles[301] = response.data
          })
          .catch(error => {
            this.loading = false
            catchError(error, '获取CMS角色列表失败')
          })
      }
      if (bsIds.indexOf(303) >= 0) {
        this.$axios.get('/admin_api/edit_api/303/roles')
          .then(response => {
            this.loading   = false
            this.all_roles[303] = response.data
          })
          .catch(error => {
            this.loading = false
            catchError(error, '获取网银角色列表失败')
          })
      }
      if (bsIds.indexOf(304) >= 0) {
        this.$axios.get('/admin_api/edit_api/304/roles')
          .then(response => {
            this.loading   = false
            this.all_roles[304] = response.data
          })
          .catch(error => {
            this.loading = false
            catchError(error, '获取资金角色列表失败')
          })
      }
      if (bsIds.indexOf(305) >= 0) {
        this.$axios.get('/admin_api/edit_api/305/roles')
          .then(response => {
            this.loading   = false
            this.all_roles[305] = response.data
          })
          .catch(error => {
            this.loading = false
            catchError(error, '获取反洗钱角色列表失败')
          })
      }
      if (bsIds.indexOf(306) >= 0) {
        this.$axios.get('/admin_api/edit_api/306/roles')
          .then(response => {
            this.loading   = false
            this.all_roles[306] = response.data
          })
          .catch(error => {
            this.loading = false
            catchError(error, '获取统一门户角色列表失败')
          })
      }
    },
    getBaselines (bsIds) {
      if (bsIds.indexOf(301) >= 0) {
        this.$axios.get('/api/systems/301/baselines/all')
          .then(response => {
            this.all_baselines[301] = response.data
          })
          .catch(error => {
            catchError(error, '获取CMS基线列表失败')
          })
      }
      if (bsIds.indexOf(303) >= 0) {
        this.$axios.get('/api/systems/303/baselines/all')
          .then(response => {
            this.all_baselines[303] = response.data
          })
          .catch(error => {
            catchError(error, '获取网银基线列表失败')
          })
      }
      if (bsIds.indexOf(304) >= 0) {
        this.$axios.get('/api/systems/304/baselines/all')
          .then(response => {
            this.all_baselines[304] = response.data
          })
          .catch(error => {
            catchError(error, '获取资金基线列表失败')
          })
      }
      if (bsIds.indexOf(305) >= 0) {
        this.$axios.get('/api/systems/305/baselines/all')
          .then(response => {
            this.all_baselines[305] = response.data
          })
          .catch(error => {
            catchError(error, '获取反洗钱基线列表失败')
          })
      }
      if (bsIds.indexOf(306) >= 0) {
        this.$axios.get('/api/systems/306/baselines/all')
          .then(response => {
            this.all_baselines[306] = response.data
          })
          .catch(error => {
            catchError(error, '获取统一门户基线列表失败')
          })
      }
    },
    handleChangeOrg (value, index, prop) {
      this.accounts[index].status[prop] = true
    },

    rulesFor (index, bsId) {
      const account        = this.accounts[index]
      const validateStatus = (rule, value, callback) => {
        const field = rule.field
        if (account.status[field] === false) {
          callback(new Error(account.messages[field]))
        } else {
          callback()
        }
      }
      const userIdRule = [
        { required: true, message: '用户编号不能为空', trigger: 'blur' },
        { validator: validateStatus, trigger: 'blur' }
      ]
      const userNameRule = [
        { required: true, message: '用户姓名不能为空', trigger: 'blur' },
        { validator: validateStatus, trigger: 'blur' }
      ]
      const orgRule = [
        { required: true, message: '机构不能为空', trigger: 'blur' },
        { validator: validateStatus, trigger: 'blur' }
      ]
      const mobileRule = [
        { required: true, message: '手机号不能为空', trigger: 'blur' },
        { validator: validateStatus, trigger: 'blur' }
      ]

      const rule = {
        baseline_id: [
          { validator: validateStatus, trigger: 'blur' }
        ],
        role_ids:    [
          { validator: validateStatus, trigger: 'blur' }
        ]
      }

      if ([301, 303, 304, 305, 306].indexOf(bsId) >= 0) {
        rule.user_id = userIdRule
        rule.user_name = userNameRule
      }
      if ([301, 303, 304, 305, 306].indexOf(bsId) >= 0) {
        rule.org_id = orgRule
      }
      if (this.displayPhone(bsId)) {
        rule.account_phone = mobileRule
      }

      return rule
      // return {
      //   org_id:      [
      //     { validator: validateStatus, trigger: 'blur' }
      //   ],
      //   user_id:     [
      //     { required: true, message: '用户编号不能为空', trigger: 'blur' },
      //     { validator: validateStatus, trigger: 'blur' }
      //   ],
      //   user_name:   [
      //     { required: true, message: '用户姓名不能为空', trigger: 'blur' },
      //     { validator: validateStatus, trigger: 'blur' }
      //   ],
      //   baseline_id: [
      //     { validator: validateStatus, trigger: 'blur' }
      //   ],
      //   role_ids:    [
      //     { validator: validateStatus, trigger: 'blur' }
      //   ]
      // }
    },

    // 角色是否多选
    isMultipleRole (account) {
      // return account.business_system_id !== 305
      return true
    },
    displayIdNumber (bsId) {
      return [301, 303].indexOf(bsId) >= 0
    },
    displayEmail (bsId) {
      return [301, 303, 304, 306].indexOf(bsId) >= 0
    },
    displayPhone (bsId) {
      return [301, 303, 304].indexOf(bsId) >= 0
    },
    handleDeleteAccount (index) {
      this.$confirm('确定删除吗？')
        .then(_ => {
          this.deleteAccount(index)
        })
        .catch(() => {})
    },
    deleteAccount (index) {
      this.accounts.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
  .form-item {
    width: 170px;
  }

  .tag-line{
    margin-left: 100px;
  }
  .line1 {
    padding-bottom: 30px;
  }

  .line2 {
  }
</style>
