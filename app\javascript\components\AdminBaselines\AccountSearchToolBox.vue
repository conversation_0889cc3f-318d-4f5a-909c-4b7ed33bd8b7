<template>
  <el-form
    :inline="true"
    class="search-box"
  >
    
    <el-form-item>
      <el-select
        v-model="search.property"
        placeholder="检索条件"
        size="small"
        style="width:120px;"
      >
        <el-option
          label="账号编码"
          value="account_code"
        />
        <el-option
          label="账号名称"
          value="account_name"
        />
        <el-option
          label="基线 ID"
          value="baseline_id"
        />
        <el-option
          label="基线名称"
          value="baseline_name"
        />
      </el-select>
    </el-form-item>

    <el-form-item>
      <el-input
        v-model="search.value"
        placeholder="输入搜索内容"
        size="small"
        style="width:150px;"
        @keyup.enter.native="handleChange"
        clearable
      />
    </el-form-item>
    <el-form-item>
      <department-select
        v-model="search.departments"
        selectStyle="width:250px;"
        placeholder="请选择部门"
        :department-id="[]"
        size="small"
        :checkStrictly="true"
        multiple
        collapseTags
        filterable
        :accountBaseline="true"
      />
    </el-form-item>
    <el-form-item>
      <el-select
        v-model="search.roles"
        placeholder="请选择角色"
        style="width:140px;"
        size="small"
        clearable
        filterable
        multiple
        collapseTags
      >
        <el-option
          v-for="role in systemRoles"
          :key="role.code"
          :label="role.name"
          :value="role.code"
        />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-select
        v-model="search.account_status"
        placeholder="账号状态"
        size='small'
        style="width:120px;"
        clearable
      >
        <el-option
          :label="getAccountStatusString(true)"
          value="true"
        />
        <el-option
          :label="getAccountStatusString(false)"
          value="false"
        />
        <el-option
          label="已删除"
          value="delete"
        />
      </el-select>
    </el-form-item>

    <el-form-item>
      <el-select
        v-model="search.showBaselineLinks"
        placeholder="是否关联基线"
        size='small'
        style="width:130px;"
        clearable
      >
        <el-option
          label="已关联"
          value="true"
        />
        <el-option
          label="未关联"
          value="false"
        />
      </el-select>
    </el-form-item>

    <el-form-item>
      <el-button
        type="primary"
        size="small"
        @click="handleChange"
      >
        检索
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'

export default {
  components: {
    DepartmentSelect
  },
  props: {
    systemRoles: {
      type: Array,
      default: []
    }
  },
  data () {
    return {
      search: {
        showBaselineLinks: null,
        property: 'account_code',
        value: null,
        account_status: null,
        departments: [],
        roles: []
      }
    }
  },
  methods: {
    handleChange () {
      this.$emit('change', this.$lodash.clone(this.search))
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form-item{
  margin-bottom: 0;
}
</style>
