<template>
  <div v-loading="getLoading">
    <el-table
      :data="tableDatas.slice((currentPage-1)*pageSize,currentPage*pageSize)"
      style="width: 100%"
    >
      <el-table-column
        fixed
        prop="type"
        label="告警类型"
      />
      <el-table-column
        prop="bs_name"
        label="系统名称"
      >
        <template slot-scope="scope">
          {{ scope.row.bs_name == null ? '-' : scope.row.bs_name }}
        </template>
      </el-table-column>
      <el-table-column
        prop="admins"
        label="负责人"
      >
        <template slot-scope="scope">
          {{ showPeople(scope.row.admins) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="all_count"
        label="处理进度"
      >
        <template slot-scope="scope">
          <div class="progessFlex">
            <div class="progressSty">
              <el-progress
                type="line"
                :stroke-width="10"
                :percentage="Math.ceil((scope.row.processed_count/scope.row.all_count)*100)"
              />
            </div>
            <div class="textSty">{{ `(${scope.row.processed_count}/${scope.row.all_count})` }}</div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="margin-top:20px"
      :current-page="currentPage"
      :page-sizes="[15, 20, 30, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="tableDatas.length"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    fromTitle: {
      type: String,
      required: true
    },
    tableTitle: {
      type: String,
      require: true
    },
    dateVal: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      currentPage: 1, // 当前页码
      //   total: '', // 总条数
      pageSize: 15, // 每页的数据条数
      getLoading: false,
      groupByBs: []
    }
  },
  computed: {
    tableDatas() {
      return this.groupByBs.map(item => {
        return Object.assign(item, { type: this.fromTitle })
      })
    }
  },
  watch: {
    tableTitle (newVal, oldVal) {
      if(newVal) {
        this.getGroupByBs()
      }
    },
    dateVal (newVal, oldVal) {
      if(newVal) {
        this.getGroupByBs()
      }
    }
  },
  created () {
    this.getGroupByBs()
  },
  methods: {
    format (percentage) {
      return percentage === 100 ? '满' : `${percentage}%`
    },
    handleSizeChange (val) {
      this.currentPage = 1
      this.pageSize = val
    },
    handleCurrentChange (val) {
      this.currentPage = val
    },
    showPeople(people_names) {
      if (people_names === null || people_names.length === 0) return '-'
      return people_names.join(' / ')
    },
    getGroupByBs() {
      this.getLoading = true
      let params = { begin_time: this.dateVal[0], end_time: this.dateVal[1] }
      this.$axios.get(`/admin_api/global_alerts/category_statistic/${this.tableTitle}`, { params: params })
          .then(response =>{
            this.getLoading = false
            this.groupByBs = response.data
          })
          .catch(err => {
            this.getLoading = false
            console.log(err)
          })
    }
  }
}
</script>

<style scoped lang="scss" >
.progessFlex{
display:flex;
// justify-content:space-between

    .progressSty {
  width: 80%;
}
.textSty{
    font-size: 16px;
    position: relative;
    left: 15px;
    top: -4px;
}
}

</style>
