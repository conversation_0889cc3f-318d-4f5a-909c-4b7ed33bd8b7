class DataCenterGroupAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '自定义业务系统管理'
    super
  end

  def create
    data_center_group_category
    @operation = '创建数据库'
    @comment   = "创建了数据库：「数据库名称：#{params.name}，英文缩写：#{params.prefix}」"
    create_audit_log
  end

  def create_fail
    data_center_group_category
    @operation = '创建数据库'
    @comment   = "创建数据库失败：「#{params}」"
    create_audit_log
  end

  def update
    data_center_group_category
    @operation = '更新数据库'
    @comment   = "更新了数据库 ：「数据库名称：#{params[0].name}，英文缩写：#{params[0].prefix}」更新为 「数据库名称：#{params[1].name}，英文缩写：#{params[1].prefix}」"
    create_audit_log
  end

  def update_fail
    data_center_group_category
    @operation = '更新数据库'
    @comment   = "更新数据库失败 ：「#{params}」"
    create_audit_log
  end

  def destroy
    data_center_group_category
    @operation = '删除数据库'
    @comment   = "删除了数据库 ：「数据库名称：#{params.name}，英文缩写：#{params.prefix}」"
    create_audit_log
  end

  private

  def data_center_group_category
    @operation_category = '数据中心管理'
  end
end
