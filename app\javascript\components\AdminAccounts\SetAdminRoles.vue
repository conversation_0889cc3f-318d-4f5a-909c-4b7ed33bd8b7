<template>
  <div>
    <el-form inline @submit.native.prevent>
      <el-form-item>
        <el-input
          v-model="filterText"
          placeholder="输入角色名称进行过滤"
          clearable
          size="small"
          prefix-icon="el-icon-search"
          style="width: 300px;"
        />
      </el-form-item>
    </el-form>
    <el-table
      ref="roleTable"
      :data="rolesByPage"
      tooltip-effect="dark"
      style="width: 100%"
      row-key="id"
      border
      @select-all="selectAll"
      @select="handleSelectionChange"
    >
      <el-table-column
        :reserve-selection="true"
        type="selection"
        width="45"
        :selectable="isRowSelectable"
      />
      <el-table-column
        prop="id"
        label="角色ID"
        align="center"
        width="100"
      />
      <el-table-column
        prop="name"
        label="名称"
        width="150"
      />
      <el-table-column
        prop="description"
        label="描述"
      />
    </el-table>
    <el-pagination
      :current-page.sync="currentPage"
      :page-size="pageSize"
      :total="roles.length"
      background
      layout="total, prev, pager, next, jumper"
      class="roles-pagination"
    />
  </div>
</template>
<script>
import API from '@/api'

export default {
  props: {
    admin: {
      type: Object,
      required: true
    },
    flag: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      roles: [],
      allRoles: [],
      adminRoleIds: [],
      filterText: '',
      currentPage: 1,
      pageSize: 15
    }
  },
  computed: {
    rolesByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end = this.currentPage * this.pageSize
      return this.roles.slice(start, end)
    }
  },
  watch: {
    admin: {
      handler (admin) {
        this.adminRoleIds = []
        this.gettingAdminRoles()
      }
    },
    filterText: {
      handler (filter) {
        this.filterRoles(filter)
      }
    },
    flag: {
      deep: true,
      immediate: true,
      handler (newVal) {
        this.$emit('roles', this.admin.id, this.adminRoleIds)
      }
    }
  },
  created () {
    this.gettingRoles()
  },
  methods: {
    // 获取全部角色
    gettingRoles () {
      this.loading = true
      API.roles.roles({ filter: this.filterText, is_admin_permission: true })
        .then(response => {
          this.loading = false
          this.allRoles = response.data
          this.roles = response.data
          this.gettingAdminRoles()
        })
        .catch(() => {
          this.loading = false
        })
    },
    filterRoles (filter) {
      this.roles = filter ? this.allRoles.filter(x => x.name.match(filter)) : this.allRoles
    },
    // 获取当前admin的角色
    gettingAdminRoles () {
      this.loading = true
      API.adminPermissions.getAdminRoles(this.admin.id)
        .then(response => {
          this.toggleSelection(response.data)
          this.adminRoleIds = response.data.map(e => e.id)
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    toggleSelection (roles) {
      this.$refs.roleTable.clearSelection()
      if (roles.length > 0) {
        roles.forEach(role => {
          const selectRole = this.allRoles.find(x => x.id === role.id)
          this.$refs.roleTable.toggleRowSelection(selectRole)
        })
      }
    },
    handleSelectionChange (selections, row) {
      this.adminRoleIds = selections.map(x => x.id)
    },
    selectAll (selection) {
      this.adminRoleIds = selection.map(x => x.id)
    },
    isRowSelectable(row, index) {
      return row.is_query;
    }
  }
}
</script>
<style lang="scss" scoped>
.roles-pagination {
  margin-top: 20px;
}
</style>
