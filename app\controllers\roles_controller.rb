# frozen_string_literal: true

class RolesController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!, only: %i[output_diff_datas]
  before_action :set_business_system, only: %i[output_datas output_diff_datas system_baselines]

  # 用于角色视角展示
  def index
    bs = BusinessSystem.find_by(id: params[:system_id])

    quarter_id = params[:quarter_id] || bs.quarters_after_import.last&.id || Quarter.last.id
    roles = bs.role_class.where(quarter_id: quarter_id)
    json_respond roles: roles.map(&:output), count: roles.count
  end

  # 用于角色列表
  def list
    json = if params['filter'].present?
             JSON.parse params['filter']
           else
             {}
           end
    bs   = BusinessSystem.find_by(id: params[:system_id])
    sort_name = json['sort_name']
    sort_type = json['sort_type'] == 'descending' ? 'desc' : 'asc'
    role_class = bs.role_class
    result = if role_class.column_names.include?('code')
               role_class.ransack(quarter_id_eq: params[:quarter_id], name_or_code_cont: json['query']).result
             else
               role_class.ransack(quarter_id_eq: params[:quarter_id], name_cont: json['query']).result
             end
    roles = result.page(json['page']).per(json['per_page'])
    roles = roles.order("#{sort_name} #{sort_type}") if sort_name.present?
    data = roles.map do |role|
      output = role.output
      user_count = role.accounts.where(status: true).count
      output[:id] = role.id
      output[:user_count] = user_count
      output
    end
    json_respond roles: data, count: roles.total_count
  end

  def detail
    bs = BusinessSystem.find_by(id: params[:system_id])
    role = bs.role_class.find_by(id: params[:id])
    json_respond role.output
  end

  def output_datas
    role_id = params[:role_id].to_i
    role_class = @business_system.role_class
    schema = role_class.respond_to?(:output_schema) ? role_class.output_role_schema : []
    begin
      role = role_class.find(role_id)
    rescue ActiveRecord::RecordNotFound => e
      json_custom_respond(500, error_message: e.message)
      return
    end
    json_respond data: role.output_role_datas, output_schema: schema
  end

  def output_diff_datas
    role_id = params[:role_id].to_i
    role_class = @business_system.role_class
    schema = role_class.respond_to?(:output_schema) ? role_class.output_role_schema : []
    begin
      role = role_class.find(role_id)
      # 根据diff_mode判断对比是基于quarter还是role, 默认按compare_quarter
      datas = if params[:diff_mode].to_s == 'compare_role'
                role.diff_datas_with_other_role(params[:other_role_id])
              else
                role.diff_datas_between_two_quarters(params[:other_quarter_id])
              end
    rescue ActiveRecord::RecordNotFound => e
      json_custom_respond(500, error_message: e.message)
      return
    end
    json_respond diff_datas: datas, output_schema: schema
  end

  def system_baselines
    role_id = params[:role_id].to_i
    role_class = @business_system.role_class
    data = []
    begin
      role = role_class.find(role_id)
      data = role.system_baseline_data
    rescue ActiveRecord::RecordNotFound => e
      json_custom_respond(500, error_message: e.message)
      return
    end
    json_respond data: data, count: data.count
  end

  protected

  def authenticate_policy!
    authorize nil, policy_class: RolePolicy
  end
end
