import axios from '@/settings/axios'
import { absoluteUrlFor, download } from './tool'

export const list = (params = {}) => {
  return axios.get(`/api/job_baselines`, { params: params })
}

export const create = (params = {}) => {
  return axios.post(`/api/job_baselines`, params)
}

export const update = (id, params = {}) => {
  return axios.put(`/api/job_baselines/${id}`, params)
}

export const destroy = (id) => {
  return axios.delete(`/api/job_baselines/${id}`)
}

// export const detail = (id) => {
//   var params = { id: id }
//   return axios.get(`/api/job_baselines/detail`, { params: params })
// }

export const exportFile = () => {
  return axios.get(`/api/job_baselines/export`, { responseType: 'blob' })
    .then(response => {
      const fileName = parseFileName(response, 'job-baseline-export.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

// export const systemBaselines = (id) => {
//   return axios.get(`/api/job_baselines/${id}/system_baselines`, {})
// }

export const systemBaselines = (id) => {
  return axios.get(`/api/jobs/${id}/job_baselines/system_baselines`, { })
}

export const outputSystemBaselines = (id) => {
  return axios.get(`/api/jobs/${id}/job_baselines/output_system_baselines`, { })
}

export const detail = (id) => {
  return axios.get(`/api/jobs/${id}/job_baselines/detail`, {})
}


export const bindRolesCreate = (params = {}) => {
  return axios.post(`/api/job_baselines/bind_roles_create`, { job_baseline: params })
}

export const bindRolesUpdate = (params = {}) => {
  return axios.put(`/api/job_baselines/bind_roles_update`, { job_baseline: params })
}

export const bindRolesDelete = (params = {}) => {
  return axios.put(`/api/job_baselines/bind_roles_delete`, { job_baseline: params })
}
