<template>
  <div>
    <div class="tool-bar">
      <div class="left">
        <account-search-tool-box
          @change="handleSearch"
          :systemRoles="systemRoles"
        />
      </div>
      <div class="right">
        <el-dropdown size="small">
          <el-button type="primary" size="small">
            批量处理<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="batchLink()">批量关联基线</el-dropdown-item>
            <el-dropdown-item @click.native="revokeBulk()">批量取消关联</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-table
      v-loading="loading"
      :data="accounts"
      border
      class="baseline-table"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
      <el-table-column
        prop="account_code"
        label="账号编码"
      />
      <el-table-column
        prop="account_name"
        label="账号名称"
      />
      <el-table-column
        property="account_status"
        label="账号状态"
        width="70"
      >
        <template slot-scope="{row}">
          {{ row.account_status || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in userInfoColumns"
        :key="index"
        v-bind="item"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.user_info">
            {{ scope.row.user_info[item.prop.replace(/info./i,"")] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="baseline_id"
        label="基线 ID"
      />
      <el-table-column
        prop="baseline_name"
        label="基线名称"
      />
      <el-table-column
        v-if="canAutoMatch"
        prop="ignore_match"
        label="忽略自动关联"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.ignore_match"
            @change="handleIgnoreChange(scope.row)"
           >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
          prop="follow_job"
          label="跟随岗位基线"
      >
        <template slot-scope="scope">
          {{ scope.row.follow_job ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        header-align="center"
        width="220"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            :disabled="!$store.getters.hasPermission('system_baseline.link')"
            @click="linkSelect(scope.row)"
          >
            {{ scope.row.baseline_id | linkButtonName }}
          </el-button>
          <el-button
            :disabled="!scope.row.baseline_id || !$store.getters.hasPermission('system_baseline.link')"
            size="mini"
            type="danger"
            @click="unlinkConfirm(scope.row.account_code)"
          >
            取消关联
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-size="pageSize"
      :page-sizes="[25, 50, 100, 200]"
      :total="count"
      :current-page.sync="page"
      background
      layout="total, prev, pager, next, jumper, sizes"
      class="pagination"
      @current-change="getAccounts"
      @size-change="handleSizeChange"
    />
    <!-- eslint-enable vue/attribute-hyphenation -->
    <baseline-select
      :visible.sync="dialogVisible"
      :system-id="systemId"
      :accounts="selectRow"
      @change="getAccounts"
    />
  </div>
</template>

<script>
import AccountSearchToolBox from './AccountSearchToolBox.vue'
import BaselineSelect from './BaselineSelect.vue'

export default {
  components: {
    AccountSearchToolBox,
    BaselineSelect
  },
  filters: {
    linkButtonName (id) {
      if (id) return '变更系统基线'
      return '关联系统基线'
    }
  },
  props: {
    systemId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      accounts: [],
      count: 0,
      page: 1,
      pageSize: 25,
      dialogVisible: false,
      selectRow: [],
      canAutoMatch: false,
      multipleSelection: [],
      systemRoles: [],
      filter: {
        showBaselineLinks: null, // 只显示未关联账号
        property: 'account_code',
        value: null
      }
    }
  },
  computed: {
    userInfoColumns () {
      const infoColumns = JSON.parse(JSON.stringify(this.$settings.userInfoColumns))
      return infoColumns.filter((item) => item.show_in_account_page)
    }
  },
  watch: {
    systemId() {
      this.getAccounts()
    }
  },
  created () {
    this.getAccounts()
  },
  methods: {
    batchLink() {
      if (this.multipleSelection.length == 0) {
        this.$message.error("请选择账号")
      }
      else {
        this.dialogVisible = true
        this.selectRow = this.multipleSelection
      }
    },
    revokeBulk () {
      if (this.multipleSelection.length == 0) {
        this.$message.error("请选择账号")
      }
      else{
        this.loading = true
        this.$axios.post(`/api/systems/${this.systemId}/accounts_with_baseline/revoke_bulk`, {
          account_codes: this.multipleSelection.map(e => e.account_code)
        })
          .then(response => {
            if(response.data.success) {
              this.$message.success('已解除基线绑定')
              this.getAccounts()
              this.loading = false
            } else {
              this.$message.error(response.data.message)
              this.loading = false
            }
          })
          .catch(() => {})
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.page = 1
      this.getAccounts()
    },
    getAccounts () {
      this.loading = true
      const theParams = {
        page:     this.page,
        per_page: this.pageSize,
        filter:   this.filter
      }
      this.$axios.get(`/api/systems/${this.systemId}/accounts_with_baseline`, { params: theParams })
        .then(response => {
          this.loading      = false
          this.accounts     = response.data.data
          this.count        = response.data.count
          this.canAutoMatch = response.data.can_auto_match
          this.systemRoles  = response.data.system_roles
        })
        .catch(() => {
          this.loading = false
          this.accounts = [{ name: 'Read data error' }]
        })
    },
    unlinkConfirm (accountCode) {
      this.$confirm('是否确认取消关联?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.unlinkBaseline(accountCode)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    unlinkBaseline (accountCode) {
      this.loading = true
      this.$axios.post(`/api/systems/${this.systemId}/accounts_with_baseline/revoke_bulk`, {
        account_codes: [accountCode]
      })
        .then(response => {
          if(response.data.success) {
            this.$message.success('取消关联成功')
            this.getAccounts()
            this.loading = false
          } else {
            this.$message.error(response.data.message)
            this.loading = false
          }
        })
        .catch(() => {})
    },
    linkSelect (row) {
      this.dialogVisible = true
      this.selectRow = [row]
    },
    handleSearch (payload) {
      this.filter = payload
      this.page = 1
      this.getAccounts()
    },
    handleIgnoreChange (row) {
      let theParams = {
        account_code: row.account_code,
        ignore_match: row.ignore_match
      }
      this.$axios.put(`/api/systems/${this.systemId}/accounts_with_baseline/ignore_match`, theParams)
          .then(response => {
            if(response.data.success) {
              this.$message.success("更新忽略自动关联成功")
            } else {
              this.$message.error("更新忽略自动关联失败")
            }
            this.getAccounts()
          })
          .catch(error => {
            console.log(error)
          })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .tool-bar{
    @include vertical_top_between;
    margin-top: 5px;
    margin-bottom: 20px;

    .right{
      @include vertical_center_right;
      margin-top: 4px;
    }

    .tool{
      margin-left: 10px;
    }
  }
  .baseline-table{
    margin-top: 20px;
    border-top: 1px solid #EBEEF5;
  }
  .pagination{
    margin-top: 20px;
  }
  .baseline-table-expand {
    line-height: 40px;
    font-weight: 800;

    .account-no-link{
      height: 60px;
      font-size: 14px;
      color: #a9a7b1;
      line-height: 60px;
      text-align: center;
    }
    .label{
      width: 90px;
    }
    .content{
      color: #99a9bf;
    }
    .expand {
      padding: 10px 20px 10px 20px;
    }
  }

</style>
