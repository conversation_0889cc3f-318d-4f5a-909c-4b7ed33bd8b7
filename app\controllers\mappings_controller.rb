# frozen_string_literal: true

# 表映射
class MappingsController < ApplicationController
  before_action :authenticate_admin!

  def index
    json_respond Mapping.common_table_data
  end

  def detail
    item = Schema.new(data_json: params[:schema_data_json].to_unsafe_h)
    data_json = { 'display_fields' => params[:display_fields].map { |str| { 'property' => str, 'is_show' => true } } } if params[:display_fields].present?
    mapping = Mapping.new(t_name: params[:t_name], data_json: data_json || item.data_json, item: item)
    json_respond mapping.output
  end
end
