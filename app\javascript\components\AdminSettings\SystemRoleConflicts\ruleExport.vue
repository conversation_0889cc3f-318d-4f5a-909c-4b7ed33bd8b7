<template>
  <el-button
    v-loading="loading"
    :disabled="!$store.getters.hasPermission('system_role_conflicts.conflicts_admin')"
    @click="exportRules"
  >
    导 出
  </el-button>
</template>
<script>
import API from '@/api'

export default {
  data () {
    return {
      loading: false
    }
  },
  methods: {
    exportRules () {
      this.loading = true
      API.systemRoleConflicts.exportRules()
        .then(response => {
          this.loading = false
        })
        .catch(_ => {
          this.loading = false
        })
    },
  }
}
</script>
