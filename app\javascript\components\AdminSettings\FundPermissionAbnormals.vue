<template>
  <div
    v-loading="loading"
    class="container"
  >
    <div class="tool-bar">
      <div class="left">
        <el-form
          ref="form"
          label-width="160px"
          :model="settings"
        >
          <el-form-item
            label="监测功能开启"
          >
            <el-switch
              v-model="settings.enable"
              active-text="开"
              inactive-text="关"
              @change="updateSettings"
            />
          </el-form-item>
        </el-form>
      </div>
      <!-- <div class="right">
        <el-button
          type="primary"
          @click="submitForm('form')"
        >
          更新设置
        </el-button>
      </div> -->
    </div>
    <div>
      <el-form
        v-show="isShowFund"
        ref="form2"
        label-width="160px"
        :model="settings"
      >
        <el-form-item
          label="监测条件"
        >
          <el-checkbox v-model="settings.only_inservice_fund">
            {{ $t('aas.fund_permission_abnormals.conditions.only_inservice_fund') }}
          </el-checkbox>
          <el-checkbox v-model="settings.only_inservice_account">
            {{ $t('aas.fund_permission_abnormals.conditions.only_inservice_account') }}
          </el-checkbox>
        </el-form-item>
        <el-form-item
          label="告警规则"
        >
          <el-checkbox v-model="settings.permissions_not_match">
            {{ $t('aas.fund_permission_abnormals.abnormals.permissions_not_match') }}
          </el-checkbox>
          <el-checkbox v-model="settings.not_exist_in_rule">
            {{ $t('aas.fund_permission_abnormals.abnormals.not_exist_in_rule') }}
          </el-checkbox>
          <el-checkbox v-model="settings.not_exist_in_fund">
            {{ $t('aas.fund_permission_abnormals.abnormals.not_exist_in_fund') }}
          </el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="small"
            @click="submitForm('form')"
          >
            更新设置
          </el-button>
        </el-form-item>
        <el-divider />
        <el-form-item
          v-show="isShowFund"
          prop="fundSelect"
          label="添加待监测的基金产品"
        >
          <el-select
            v-model="fundSelect"
            filterable
            placeholder="请选择基金产品"
            style="width: 300px"
          >
            <el-option
              v-for="item in funds"
              :key="item.code"
              :value="item.code"
              :label="titleForFund(item)"
            />
          </el-select>

          <el-button
            @click="submitForm2"
          >
            添加
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-tabs
      v-show="isShowFund"
      v-model="fundTabSelect"
      type="card"
      closable
      @tab-remove="removeTab"
    >
      <el-tab-pane
        v-for="(item, index) in fundSettings"
        :key="item.fund_code"
        :label="titleForFundCode(item.fund_code)"
        :name="item.fund_code"
      >
        <fund-setting
          ref="fundSetting"
          :index="index"
          :accounts="accounts"
          :settings="fundSettings[index].accounts"
          @update="handleFundSettingUpdate"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>

import FundSetting from './FundSetting.vue'
export default {
  components: {
    FundSetting
  },
  data () {
    return {
      loading: false,
      settings: {
        enable:                false,
        only_inservice_fund:    false,
        only_inservice_account: false
      },
      funds:    [],
      accounts: [],
      fundSelect: null,
      fundTabSelect: null,
      accountSelect: [], // 每个 fund 有一个
      fundSettings: []
    }
  },
  computed: {
    isShowFund () {
      return this.$settings.fundPermissionAbnormals && this.$settings.fundPermissionAbnormals.enable
    }
  },
  created () {
    this.getSettings()
  },
  methods: {
    getSettings () {
      this.loading = true
      this.$axios.get('admin_api/settings/fund_permission_abnormals')
        .then(response => {
          this.loading      = false
          this.settings     = response.data.settings
          this.accounts     = response.data.accounts
          this.funds        = response.data.funds
          this.fundSettings = response.data.settings.rules

          if (this.fundSettings.length > 0) {
            this.fundTabSelect = this.fundSettings[0].fund_code
          }
        })
        .catch(error => {
          this.loading = false
          switch (error.status) {
            case 456:
              this.$message.error('投资交易系统未找到')
              break
            case 457:
              this.$message.warning('最新时间点数据导入未完成, 请等待数据导入完成后操作')
              break
            default:
              this.$message
          }
        })
    },
    updateConfirm () {
      this.$confirm('此操作将更新系统设置，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.updateSettings()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    updateSettings () {
      this.$axios.post('admin_api/settings/fund_permission_abnormals', {
        settings: Object.assign(this.settings, { rules: this.fundSettings })
      })
        .then(response => {
          this.$message.success('产品权限设置完成')
          window.location.reload(true)
          this.resetForm()
          this.getSettings()
        })
        .catch(() => {})
    },
    resetForm () {
      // 基金产品设置null
      this.fundSelect = null
      // 子组件的基金产品账户全部遍历，设置null
      const accountSelects = this.$refs.fundSetting
      for (let i = 0, len = accountSelects.length; i < len; i++) {
        accountSelects[i].accountSelect = null
      }
    },
    submitForm2 () {
      if (this.fundSelect === null) {
        this.$message.error('请选择基金产品')
      } else {
        this.createNewFund()
      }
    },
    createNewFund () {
      const existFund = this.fundSettings.find(x => x.fund_code === this.fundSelect)

      if (existFund) {
        this.$message.warning('该基金产品已添加')
        return
      }
      this.fundSettings.push({ fund_code: this.fundSelect, accounts: [] })
      this.fundTabSelect = this.fundSelect
    },
    removeTab (code) {
      this.$confirm('删除该基金的全部账号权限设置吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const index = this.fundSettings.findIndex(x => x.fund_code === code)
          this.fundSettings.splice(index, 1)
          this.updateSettings()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    titleForFund (fund) {
      return `${fund.fund_code} - ${fund.name}`
    },

    titleForFundCode (code) {
      const fund = this.funds.find(x => x.code === code)

      return `${fund.fund_code} - ${fund.name}`
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.updateConfirm()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    handleFundSettingUpdate (payload) {
      this.fundSettings[payload.index].accounts = payload.settings
      this.updateConfirm()
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .tool-bar{
    @include vertical_center_between;
  }
  .form-item{
    @include vertical_center_between;
    width: 100%;

    .el-select{
      width: calc(100% - 80px);
    }
  }
  .form-item-first-line{
    // MARK: 为了把表单验证的字露出来
    margin-bottom: 14px;

    .el-input{
      width: 200px;
    }
  }
  .form-item-second-line{
    margin-bottom: 25px;

    .el-select{
      width: 100%;
    }
  }
  .list-complete-item {
    transition: all 0.5s;
  }
  .list-complete-enter, .list-complete-leave-to
  /* .list-complete-leave-active for below version 2.1.8 */ {
    opacity: 0;
    transform: translateY(-30px);
  }
</style>
