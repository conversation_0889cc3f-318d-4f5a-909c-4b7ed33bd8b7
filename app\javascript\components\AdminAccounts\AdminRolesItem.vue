<template>
  <div>
    <div class="button-bar">
      <div class="left">
        <el-input
          v-model="filterText"
          size="small"
          placeholder="搜索角色名称"
          clearable
          prefix-icon="el-icon-search"
          class="search-input"
        />
      </div>
      <div class="right">
        <create-role @update="gettingRoles" />
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="rolesByPage"
      border
    >
      <el-table-column
        prop="id"
        label="角色ID"
        align="center"
        width="80"
      />
      <el-table-column
        align="center"
        property="name"
        label="角色名称"
        sortable
        width="200px"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.edit">
            <el-input
              v-model="scope.row.name"
              placeholder="角色名称"
            />
          </span>
          <span v-else>
            {{ scope.row.name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        property="description"
        label="角色描述"
        min-width="150px"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.edit">
            <el-input
              v-model="scope.row.description"
              placeholder="角色描述"
            />
          </span>
          <span v-else>
            {{ scope.row.description }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        property="admin_ids"
        label="角色成员"
        min-width="300px"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.edit && adminUsersPermission">
            <admin-select
              v-model="scope.row.admin_ids"
              placeholder="请选择角色成员"
              multiple
              clearable
            />
          </span>
          <span v-else>
            {{ admins.filter(x => scope.row.admin_ids.includes(x.id)) ? admins.filter(x => scope.row.admin_ids.includes(x.id)).map(x => x.name).join(' / ') : '' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作"
        width="260px"
      >
        <template slot-scope="scope">
          <el-button
            :disabled="!$store.getters.hasPermission('admin_account_manager.role_admin')"
            size="small"
            @click="
              handleToSetPermissions({
                handle: 'edit',
                roleId: scope.row.id,
                roleName: scope.row.name,
                moduleFunctionIds: scope.row.module_function_ids,
                alertPermissions: scope.row.alert_permissions
              })
            "
          >
            权限设置
          </el-button>
          <el-button
            v-if="!scope.row.edit"
            size="small"
            :disabled="!$store.getters.hasPermission('admin_account_manager.role_admin')"
            @click="scope.row.edit = true"
          >
            编辑
          </el-button>
          <el-button
            v-if="scope.row.edit"
            size="small"
            @click="handleUpdateRole(scope.row)"
          >
            保存
          </el-button>
          <el-button
            v-if="scope.row.edit"
            size="small"
            @click="handleCancel(scope.row)"
          >
            取消
          </el-button>
          <el-button
            v-if="!scope.row.edit"
            size="small"
            type="danger"
            :disabled="!$store.getters.hasPermission('admin_account_manager.role_admin')"
            @click="handleDeleteConfirm(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page.sync="currentPage"
      :page-size="pageSize"
      :total="roles.length"
      background
      layout="total, prev, pager, next, jumper"
      class="roles-pagination"
    />
  </div>
</template>
<script>
import API from '@/api'
import AdminSelect from '@/components/common/AdminSelect.vue'
import CreateRole from './CreateRole.vue'

export default {
  components: {
    AdminSelect,
    CreateRole
  },
  data () {
    return {
      loading: false,
      roles: [],
      admins: [],
      filterText: '',
      currentPage: 1,
      pageSize: 15
    }
  },
  computed: {
    rolesByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end = this.currentPage * this.pageSize
      return this.roles.slice(start, end)
    },
    adminUsersPermission () {
      return this.$store.getters.hasPermission('admin_account_manager.query')
    }
  },
  watch: {
    filterText: {
      handler (filter) {
        this.gettingRoles()
      }
    }
  },
  created () {
    this.gettingRoles()
    this.gettingAdmins()
  },
  methods: {
    gettingRoles () {
      this.loading = true
      API.roles.roles({ filter: this.filterText })
        .then(response => {
          this.loading = false
          this.roles = response.data.map(x => Object.assign(x, { edit: false }))
        })
        .catch(() => {
          this.loading = false
        })
    },
    gettingAdmins () {
      this.$axios.get('/admin_api/admin_accounts')
        .then(response => {
          this.admins = response.data
        })
        .catch(_ => {})
    },
    handleUpdateRole (role) {
      if (!role.name) {
        this.$message.error('角色名称不能为空')
      } else {
        role.edit = false
        this.loading = true
        API.roles
          .update(role.id, role)
          .then(() => {
            this.loading = false
            this.gettingRoles()
            this.$message.success(`${role.name}-角色更新成功`)
          })
          .catch(() => {
            this.gettingRoles()
          })
      }
    },
    handleCancel (role) {
      role.edit = false
      this.gettingRoles()
    },
    handleDeleteConfirm (id) {
      this.$confirm('此操作将删除该角色所有信息，无法恢复，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          this.handleRoleDelete(id)
        })
        .catch(() => {
          this.$message.info('已取消删除操作')
        })
    },
    handleRoleDelete (id) {
      // 二次确认
      this.loading = true
      API.roles
        .destroy(id)
        .then(() => {
          this.loading = false
          this.gettingRoles()
          this.$message.success('删除角色成功')
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleToSetPermissions (data) {
      this.$emit('ToForm', data)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/components/variables';

.button-bar {
  @include vertical_center_between;

  margin-top: 20px;
  margin-bottom: 20px;

  .search-input {
    width: 300px;
  }
}

.el-button + .el-button {
  margin-left: 5px;
}

.el-tag {
  margin-left: 3px;
}

.container {
  padding: 2em;
  background-color: white;
  min-width: 800px;
}

.roles-pagination {
  margin-top: 20px;
}
</style>
