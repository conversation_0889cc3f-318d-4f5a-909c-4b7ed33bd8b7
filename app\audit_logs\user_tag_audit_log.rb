# frozen_string_literal: true

# 员工标签审计日志
class UserTagAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '员工标签管理'
    super
  end

  def create
    set_operation_category
    @operation = '创建标签'
    @comment   = "创建员工标签，名称: #{params[0]}, 颜色: #{params[1]}"
    create_audit_log
  end

  def update
    set_operation_category
    @operation = '修改标签'
    @comment   = "修改员工标签，名称: #{params[0]}, 颜色: #{params[1]}"
    create_audit_log
  end

  def disable
    set_operation_category
    @operation = '修改标签状态'
    @comment   = "修改员工标签状态，状态: #{params[0]}"
    create_audit_log
  end

  def user_set_tag
    set_operation_category
    @operation_module = '全部员工列表'
    @operation_category = '员工详情'
    @operation = '设置员工标签'
    @comment   = "设置标签，员工名称: #{params[0]}, 标签名称: #{params[1].join(',')}"
    create_audit_log
  end

  private

  def set_operation_category
    @operation_category = '员工标签管理'
  end
end
