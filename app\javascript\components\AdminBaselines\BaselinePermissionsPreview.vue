<template>
  <div>
    <div>
      <el-tabs
        v-model="activeName"
        v-loading="loading"
        type="border-card"
      >
        <el-tab-pane
          v-for="item in showPermissionSchema"
          :label="item.name"
          :name="item.data_key"
          :key="item.data_key"
        >
          <div v-if="baseline.id === sourceId && sourceType === 'baseline' && !disableButton">
            <el-button type="primary" @click="showCreateBaseline(item.data_key)" size="mini">添加权限</el-button>
            <hr/>
          </div>

          <el-form-item label="基线对比字段">
            <el-checkbox
              v-for="schema in item.schema"
              v-model="schema.is_diff"
              @change="setPermissionSchemaChange()"
            >
              {{ schema.label }}
            </el-checkbox>
          </el-form-item>
          <hr/>

          <base-table
            :tableData="permissionData[item.data_key]"
            :tableSchema="item.schema"
            :dataKey="item.data_key"
            table-size="mini"
            :isNowBaseline="baseline.id === sourceId && sourceType === 'baseline'"
            :disable-button="disableButton"
            @destroyBaseline="destroyBaseline(arguments)"
            @showUpdateBaseline="showUpdateBaseline(arguments)"
            tableType="baseline"
            filterable
            background
            border
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog
      title="编辑系统基线权限"
      :visible.sync="showEditPermission"
      v-loading="loading"
      width="500px"
      :append-to-body='true'
    >
      <span>
        <el-form
          :model="formPermission"
          size="mini"
        >
          <el-form-item
            v-for="item in formSchema"
            :label="item.label"
          >
            <el-input v-model="formPermission[item.property]"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="showEditPermission = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="permissionSubmit" size="mini">提 交</el-button>
          </el-form-item>
        </el-form>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import BaseTable from '@/components/AccountDetail/TableWithPagination.vue'
import API from '@/api'
export default {
  components: {
    BaseTable
  },
  props: {
    systemId: {
      type: Number,
      required: true
    },
    sourceType: {
      type: String,
      required: true
    },
    baseline: {
      type:    Object,
      default: () => {}
    },
    mode: {
      type: String,
      required: true
    },
    sourceId: {
      validator: function (val) {
        return val === null || typeof val === 'number'
      },
      required: true
    },
    disableButton: {
      type: Boolean,
      default: false
    },
    outputSchemaData: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      permissionTypes: 1,
      permissionData: [],
      permissionSchema: [],
      formSchema: [],
      loading: false,
      activeName: 'data1',
      formPermission: {},
      oldPermission: null,
      dataKey: '',
      showEditPermission: false,
      currentIndex: null
    }
  },
  watch: {
    sourceId () {
      if (!this.sourceId) {
        this.permissionReset()
        return
      }

      this.getPermissions()
    },
    systemId () {
      this.getSystemSettings()
    },
    showPermissionSchema() {
      let actionNameExist = this.showPermissionSchema.find(item => item.data_key === this.activeName)
      if(!actionNameExist && this.showPermissionSchema.length > 0) {
        this.activeName = this.showPermissionSchema[0].data_key
      }
    }
  },
  created () {
    this.getSystemSettings()
  },
  computed: {
    baselinePermissionShow() {
      return this.$settings.baselinePermissionShow || false
    },
    showPermissionSchema() {
      if(this.baselinePermissionShow) {
        return this.permissionSchema.filter((item) => {
          let schemaData = this.outputSchemaData.find(item2 => item2['data_key'] === item['data_key'])
          if(schemaData) {
            return schemaData['is_notice'] === true
          } else {
            return false
          }
        })
      } else {
        return this.permissionSchema
      }
    }
  },
  methods: {
    permissionSubmit () {
      let params_status = false
      for (let i in this.formSchema) {
        if (this.formPermission[this.formSchema[i].property] !== '') {
          params_status = true
        }
      }
      if (!params_status) {
        this.$message.error('权限不能全部为空')
        return
      }
      if(this.oldPermission) {
        this.$set(this.permissionData[this.dataKey], this.currentIndex, this.formPermission)
      } else {
        if(this.permissionData[this.dataKey]) {
          this.permissionData[this.dataKey].push(this.formPermission)
        } else {
          this.permissionData[this.dataKey] = [this.formPermission]
        }
      }
      this.setPermissionChange()
      this.showEditPermission = false
    },
    showCreateBaseline (schemaKey) {
      let fromJson = {}
      this.oldPermission = null
      this.currentIndex = null
      this.dataKey = schemaKey
      this.formSchema = this.permissionSchema.find( x => x.data_key === this.dataKey).schema

      for (let i in this.formSchema) {
        fromJson[this.formSchema[i].property] = ''
      }
      this.formPermission = JSON.parse(JSON.stringify(fromJson))

      this.showEditPermission = true
    },
    showUpdateBaseline (item) {
      this.oldPermission = JSON.parse(JSON.stringify(item[0]))
      this.formPermission = JSON.parse(JSON.stringify(item[0]))
      this.dataKey = item[1]
      this.currentIndex = item[2]
      this.formSchema = this.permissionSchema.find( x => x.data_key === this.dataKey).schema
      this.showEditPermission = true
    },

    destroyBaseline (item) {
      let dataKey = item[1]
      let index = item[2]
      this.permissionData[dataKey].splice(index, 1)
      this.setPermissionChange()
    },
    permissionReset () {
      if (this.permissionTypes === 1) {
        this.permissionData   = { data1: []}
        return
      }
      if (this.permissionTypes === 2) {
        this.permissionData   = { data1: [], data2: [] }
      }
    },
    getPermissions () {
      this.sourceType === 'account'  && this.getAccountPermissions()
      this.sourceType === 'baseline' && this.getBaselinePermissions()
    },

    getBaselinePermissions () {
      this.loading = true
      this.$axios.get(`/api/systems/${this.systemId}/baselines/${this.sourceId}`)
        .then(response => {
          this.loading = false
          this.permissionData   = response.data.output_datas
          this.permissionSchema = response.data.output_schema
          this.setPermissionSchemaChange()
        })
        .catch(() => {
          this.loading = false
        })
    },
    getAccountPermissions () {
      this.getAccountPermissionsData()
    },
    getAccountPermissionsData () {
      this.loading = true
      API.systemAccounts.allDatas({ account_id: this.sourceId, system_id: this.systemId, with_role: true })
        .then(response => {
          this.permissionData = response.data
          // 通过账号权限复制，设置schema 所有字段默认 比对
          // 防止前面选择了复制基线，然后再选择账号，导致账号的字段对比，用的是基线的字段对比
          this.setPermissionSchemaDiff()
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    getSystemSettings () {
      this.$axios.get(`/api/systems/${this.systemId}/settings`, { params: { with_role: 'true' } })
        .then(response => {
          this.permissionTypes = response.data.permission_types
          this.permissionSchema = response.data.account_schema
          if (this.mode === 'create') {
            this.permissionReset()
            this.setPermissionSchemaDiff()
          }
        })
        .catch(() => {})
    },
    setPermissionChange () {
      this.$emit("setPermissionChange")
    },
    // 创建基线时，设置权限schema所有字段对比基线
    setPermissionSchemaDiff () {
      this.permissionSchema.forEach(item => {
        item.schema.forEach(schema => {
          this.$set(schema, 'is_diff', true); // 确保 is_diff 为响应式
        });
      });
      this.setPermissionSchemaChange()
    },
    setPermissionSchemaChange () {
      this.$emit("setPermissionSchemaChange", this.permissionSchema)
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

</style>
