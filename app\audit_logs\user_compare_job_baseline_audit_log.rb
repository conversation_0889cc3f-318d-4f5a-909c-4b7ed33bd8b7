# frozen_string_literal: true

# 用户操作审计
class UserCompareJobBaselineAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = I18n.t('aas.quarter_user_job_baseline_diff.title')
    @operation_category = I18n.t('aas.quarter_user_job_baseline_diff.title')
    super
  end

  def create
    @operation = "创建#{I18n.t('aas.quarter_user_job_baseline_diff.title')}数据"
    @comment   = "创建了 #{params.name} #{I18n.t('aas.quarter_user_job_baseline_diff.title')}数据"
    create_audit_log
  end

  def export_success
    @operation = "#{I18n.t('aas.quarter_user_job_baseline_diff.title')}数据导出"
    @comment   = "#{params.name} #{I18n.t('aas.quarter_user_job_baseline_diff.title')}数据导出成功"
    create_audit_log
  end

  def export_faild
    @operation = "#{I18n.t('aas.quarter_user_job_baseline_diff.title')}数据导出"
    @comment   = "#{params.name} #{I18n.t('aas.quarter_user_job_baseline_diff.title')}数据导出失败"
    create_audit_log
  end
end
