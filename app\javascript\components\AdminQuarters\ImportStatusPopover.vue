<template>
  <span>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="systemDialogVisible"
      width="600px"
      append-to-body
      @open="handleOpenSystemTask"
      @close="clearSystemTaskStatusInterval"
    >
      <div v-loading="systemTaskLoading">
        <el-table
          :data="systemTaskStatuses"
          max-height="500"
          size="small"
        >
          <el-table-column
            property="name"
            label="任务名称"
          />
          <el-table-column
            property="status"
            label="任务跑批状态"
          >
            <template slot-scope="scope">
              <quarter-system-task-status :status="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column
            property="time"
            label="执行时间（秒）"
          />
          <el-table-column
            v-if="showImportFromImporter"
            align="right"
            width="100"
          >
            <template
              slot="header"
              slot-scope="scope"
            >
              <el-button
                size="mini"
                :loading="loadingAllSystemTasks"
                @click="handlePerformAllSystemTask()"
                :disabled="!$store.getters.hasPermission('quarters.run_tasks')"
              >
                重新执行
              </el-button>
            </template>
            <template slot-scope="scope">
              <el-button
                size="mini"
                :loading="scope.row.loading"
                @click="handlePerformSystemTask(scope.row)"
                :disabled="!$store.getters.hasPermission('quarters.run_tasks')"
              >
                重新执行
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="850px"
      append-to-body
      @open="handleOpen"
      @close="clearStatusInterval"
    >
      <div v-loading="loading">
        <hr class="popover-hr">
        <el-tabs v-model="activeName">
          <el-tab-pane
            label="业务系统数据导入状态"
            name="systems"
          >
            <div class="tool-bar">
              <div class="left">
                <el-form
                  :inline="true"
                  size="small"
                >
                  <el-form-item>
                    <el-input
                      v-model="filter.name"
                      placeholder="搜索系统名称"
                      clearable
                      style="width:150px;margin-right: 10px;"
                      size="small"
                    />
                    <el-select
                      v-model="filter.status"
                      placeholder="数据导入状态"
                      size="small"
                      clearable
                      multiple
                      width="120px"
                    >
                      <el-option
                        v-for="x in allStatus"
                        :key="x.value"
                        :label="x.name"
                        :value="x.value"
                      />
                    </el-select>
                    <el-select
                      v-model="filter.operation"
                      size="small"
                      style="width:80px"
                    >
                      <el-option
                        v-for="x in allOperations"
                        :key="x.value"
                        :label="x.name"
                        :value="x.value"
                      />
                    </el-select>
                    <el-select
                      v-model="filter.system_task_status"
                      placeholder="任务跑批状态"
                      size="small"
                      clearable
                      multiple
                      width="120px"
                    >
                      <el-option
                        v-for="x in allSystemTaskStatus"
                        :key="x.value"
                        :label="x.name"
                        :value="x.value"
                      />
                    </el-select>

                    <el-button
                      type="primary"
                      size="small"
                      style="margin-left:3px"
                      @click="runSearch"
                    >
                      搜索
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
              <div class="right">
                <el-button
                  size="mini"
                  type="primary"
                  :loading="loadingAllImportTasks"
                  @click="handleImportAll()"
                  :disabled="!$store.getters.hasPermission('quarters.run_tasks')"
                  class="restart-run-all"
                >
                  重新执行
                </el-button>
              </div>
            </div>

            <el-table
              :data="systemStatuses"
              max-height="500"
              size="small"
            >
              <el-table-column
                property="name"
                label="系统名称"
              />
              <el-table-column
                property="status"
                label="数据导入状态"
              >
                <template slot-scope="scope">
                  <quarter-status :status="scope.row.status" />
                  <el-popover
                    v-if="isExistMsg(scope.row)"
                    trigger="hover"
                    placement="top"
                    width="250px"
                  >
                    <div class="popover-content">
                      <!-- 错误信息 -->
                      <p v-if="isExist(scope.row.error_msg)" class="error-msg">
                        <strong>错误信息：</strong>{{ scope.row.error_msg }}
                      </p>

                      <!-- 告警信息 -->
                      <p v-if="isExist(scope.row.warn_msg)" class="warn-msg">
                        <strong>告警信息：</strong>{{ scope.row.warn_msg }}
                      </p>
                    </div>

                    <!-- 弹出框触发的元素 -->
                    <span slot="reference">
                      <el-link
                        icon="el-icon-warning"
                        :type="iconType(scope.row)">
                      </el-link>
                    </span>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column
                property="system_task_status"
                label="任务跑批状态"
              >
                <template slot-scope="scope">
                  <quarter-system-task-status :status="scope.row.system_task_status" />
                </template>
              </el-table-column>
              <el-table-column
                property="time"
                label="执行时间（秒）"
              />
              <el-table-column
                v-if="showImportFromImporter"
                align="right"
                width="200"
                label="操作"
              >
                <template slot-scope="scope">
                  <el-button
                    v-if="displaySystemTasks(scope.row)"
                    size="mini"
                    slot="reference"
                    @click="handleOpenSystemTaskDialog(scope.row)"
                    :disabled="!$store.getters.hasPermission('quarters.query')"
                  >
                    执行进度
                  </el-button>

                  <el-button
                    size="mini"
                    :loading="scope.row.loading"
                    @click="handleImport(scope.$index, scope.row)"
                    :disabled="!$store.getters.hasPermission('quarters.run_tasks')"
                  >
                    重新执行
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-pagination
              :page-size="pageSize"
              :total="total"
              :current-page.sync="page"
              background
              layout="total, prev, pager, next, jumper"
              style="padding-top: 10px;"
            />

          </el-tab-pane>
          <el-tab-pane
            label="任务执行状态"
            name="tasks"
          >
            <el-table
              :data="taskStatuses"
              max-height="500"
              size="small"
            >
              <el-table-column
                property="name"
                label="任务名称"
              />
              <el-table-column
                property="status"
                align="center"
                label="执行状态"
                width="90"
              >
                <template slot-scope="scope">
                  <TaskStatus :status="scope.row.status" />
                </template>
              </el-table-column>
              <el-table-column
                property="time"
                label="执行时间（秒）"
                width="120"
              />
              <el-table-column
                align="center"
                width="100"
              >
                <template
                  slot="header"
                  slot-scope="scope"
                >
                  <el-button
                    size="mini"
                    :loading="loadingAllSystemTasks"
                    @click="handlePerformAll()"
                    :disabled="!$store.getters.hasPermission('quarters.run_tasks')"
                  >
                    重新执行
                  </el-button>
                </template>
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    :loading="scope.row.loading"
                    @click="handlePerform(scope.$index, scope.row)"
                    :disabled="!$store.getters.hasPermission('quarters.run_tasks')"
                  >
                    执行
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <el-button
      slot="reference"
      size="small"
      @click="handleOpenTaskDialog()"
      :disabled="!$store.getters.hasPermission('quarters.query') || buttonDisabled"
    >
      状态
    </el-button>

  </span>
</template>

<script>
import API                     from '@/api'
import QuarterStatus           from '@/components/AdminQuarters/QuarterStatus'
import QuarterSystemTaskStatus from '@/components/AdminQuarters/QuarterSystemTaskStatus'
import TaskStatus              from '@/components/AdminQuarters/TaskStatus'
export default {
  components: {
    QuarterStatus, TaskStatus, QuarterSystemTaskStatus
  },
  props: {
    quarter: {
      type: Object,
      required: true
    },
    buttonDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      loading: false,
      loadingAllTasks: false,
      loadingAllSystemTasks: false,
      loadingAllImportTasks: false,
      systemTaskLoading: false,
      activeName: 'systems',
      systemStatuses: [],
      taskStatuses:  [],
      systemTaskStatuses: [],
      timer: 0,
      systemTaskTimer: 0,
      dialogVisible: false,
      systemDialogVisible: false,
      name: '',
      filter: {
        name: null,
        status: [],
        system_task_status: [],
        operation: 'and'
      },
      params: {},
      bsId: null,
      pageSize: 10,
      page: 1,
      total: 0,
      allStatus: [], // 数据导入状态，用于搜索
      allSystemTaskStatus: [], // 系统任务跑批状态，用于搜索
      allOperations: [
        { name: '并且', value: 'and' },
        { name: '或', value: 'or' }
      ]
    }
  },
  beforeDestroy () {
    this.clearStatusInterval()
  },
  watch: {
    pageSize () {
      this.getStatus()
    },
    page () {
      this.getStatus()
    }
  },
  computed: {
    // 是否展示 导入数据管理 -> 业务系统数据导入状态 -> 执行、重新执行
    showImportFromImporter () {
      return this.$settings.importFromImporter['enable']
    },
    title () {
      return `${this.quarter.name}跑批任务详情`
    },
    // dialog弹出框标题
    dialogTitle () {
      return `${this.name}系统跑批任务执行状态`
    },
    // filterSystemStatuses () {
    //   if (!API.tool.isBlank(this.filter.name)) {
    //     return this.systemStatuses.filter(x => x.name.indexOf(this.filter.name) >= 0)
    //   } else {
    //     return this.systemStatuses
    //   }
    // },
    // systemTaskStatuses () {
    //   if (this.bsId === null) return []

    //   return this.allSystemTaskStatuses.filter(x => x.bs_id === this.bsId)
    // }
  },
  methods: {
    handleOpen () {
      // 只在手动点击的时候loading为true
      this.loading = true
      this.initStatuses()
      this.getStatusInterval()
    },
    runSearch () {
      this.params = Object.assign({}, this.filter)
      this.loading = true
      this.getStatus()
    },
    getStatus () {
      // this.loading = true
      API.quarters.importStatus(this.quarter.id, { page: this.page, per_page: this.pageSize, filter: this.params })
        .then(response => {
          this.systemStatuses = response.data.systems
          this.taskStatuses   = response.data.tasks
          // this.allSystemTaskStatuses = response.data.system_tasks
          this.loading = false
          this.total = response.data.total
        })
        .catch(() => {
          this.loading = false
        })
    },
    getStatusInterval () {
      this.getStatus()
      this.timer = setInterval(this.getStatus, 8000)
    },
    clearStatusInterval () {
      clearTimeout(this.timer)
    },
    handlePerformAll () {
      this.loadingAllTasks = true
      API.quarters.rakesRestart(this.quarter.id)
        .then(response => {
          this.loadingAllTasks = false
          if (!response.data.success) {
            this.$message.error(response.data.error_message)
          } else {
            this.$message.success(response.data.message)
          }
        })
        .catch(() => {
          this.loadingAllTasks = false
        })
    },
    handlePerform (index, rake) {
      rake.loading = true
      const params = {
        quarter_id: this.quarter.id,
        task_key: rake.key
      }
      API.quarters.rakeRestart(params)
        .then(response => {
          rake.loading = false
          if (!response.data.success) {
            this.$message.error(response.data.error_message)
          } else {
            this.$message.success(response.data.message)
          }
        })
        .catch(() => {
          rake.loading = false
        })
    },
    // 运行系统依赖任务
    handlePerformSystemTask (rake) {
      rake.loading = true
      const params = {
        quarter_id: this.quarter.id,
        task_key:   rake.key,
        bs_id:      rake.bs_id
      }
      API.quarters.rakeSystemRestart(params)
        .then(response => {
          rake.loading = false
          if (!response.data.success) {
            this.$message.error(response.data.error_message)
          } else {
            this.$message.success(response.data.message)
          }
        })
        .catch(() => {
          rake.loading = false
        })
    },
    // 运行指定系统所有依赖任务
    handlePerformAllSystemTask () {
      this.loadingAllSystemTasks = true
      const params = {
        quarter_id: this.quarter.id,
        bs_id:      this.bsId
      }
      API.quarters.rakesSystemRestart(params)
        .then(response => {
          this.loadingAllSystemTasks = false
          if (!response.data.success) {
            this.$message.error(response.data.error_message)
          } else {
            this.$message.success(response.data.message)
          }
        })
        .catch(() => {
          this.loadingAllSystemTasks = false
        })
    },
    handleImportAll () {
      this.loadingAllImportTasks = true
      API.quarters.importsRestart(this.quarter.id)
        .then(response => {
          this.loadingAllImportTasks = false
          if (!response.data.success) {
            this.$message.error(response.data.error_message)
          }else {
            this.$message.success(response.data.message)
          }
        })
        .catch(() => {
          this.loadingAllImportTasks = false
        })
    },
    handleImport (index, rake) {
      rake.loading = true
      const params = {
        quarter_id: this.quarter.id,
        bs_id: rake.bs_id // bs_id为空，则表示导入员工
      }
      API.quarters.importRestart(params)
        .then(response => {
          rake.loading = false
          if (!response.data.success) {
            this.$message.error(response.data.error_message)
          } else {
            this.$message.success(response.data.message)
          }
        })
        .catch(() => {
          rake.loading = false
        })
    },
    // 是否显示执行进度
    displaySystemTasks (row) {
      return row.bs_id !== null
    },
    // 打开系统依赖任务dialog
    handleOpenSystemTask () {
      // 只在手动点击的时候systemTaskLoading为true
      this.systemTaskLoading = true
      this.systemTaskStatuses = []
      this.getSystemTaskStatusInterval()
    },
    handleOpenSystemTaskDialog (row) {
      this.name = row.name
      this.systemDialogVisible = true
      this.bsId = row.bs_id
    },
    getSystemTaskStatusInterval () {
      this.getSystemTasks()
      this.systemTaskTimer = setInterval(this.getSystemTasks, 6000)
    },
    clearSystemTaskStatusInterval () {
      clearTimeout(this.systemTaskTimer)
    },
    handleOpenTaskDialog () {
      this.dialogVisible = true
    },
    // 初始数据导入状态
    initStatuses () {
      API.quarterBusinessSystemImportStatuses.getAllStatuses()
        .then(response => {
          this.allStatus = response.data.all_statuses
          this.allSystemTaskStatus = response.data.all_system_task_statuses
        })
        .catch(() => {
        })
    },
    // 初始化系统依赖任务
    getSystemTasks () {
      if (API.tool.isBlank(this.bsId)) return

      const theParams = { quarter_id: this.quarter.id, bs_id: this.bsId }
      API.quarterSystemTaskStatuses.index(theParams)
        .then(response => {
          this.systemTaskLoading = false
          if (this.bsId == response.data.business_system_id) {
            this.systemTaskStatuses = response.data.data
          }
        })
        .catch(() => {
          this.systemTaskLoading = false
        })
    },
    // 信息是否存在
    isExist (msg) {
      return !API.tool.isBlank(msg)
    },
    // 是否存在错误信息或者告警信息
    isExistMsg(row) {
      return this.isExist(row.error_msg) || this.isExist(row.warn_msg)
    },
    iconType (row) {
      return this.isExist(row.error_msg) ? 'danger' : 'warning'
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .popover-hr{
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .tool-bar {
    @include vertical_center_between;
    align-items: unset;
  }

  .restart-run-all {
    margin-top: 5px;
  }

  .popover-content {
    padding: 10px; /* 内边距 */
    font-size: 14px; /* 字体大小 */
    line-height: 1.5; /* 行高 */
    max-width: 300px; /* 最大宽度 */
  }

  .error-msg {
    color: #f56c6c; /* 错误信息的颜色 */
    background-color: #fde2e2; /* 背景颜色 */
    padding: 5px 10px; /* 内边距 */
    border-radius: 4px; /* 圆角 */
    margin-bottom: 10px; /* 底部间距 */
  }

  .warn-msg {
    color: #e6a23c; /* 告警信息的颜色 */
    background-color: #fff8e1; /* 背景颜色 */
    padding: 5px 10px; /* 内边距 */
    border-radius: 4px; /* 圆角 */
  }
</style>

