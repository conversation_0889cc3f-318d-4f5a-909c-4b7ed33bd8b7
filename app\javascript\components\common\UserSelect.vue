<template>
  <span class="base-span">
    <el-select
      v-model="localUserId"
      :filterable="filterable"
      :placeholder="placeholder"
      :disabled="disabled"
      :size="size"
      :style="selectStyle"
      :multiple="multiple"
      clearable
      v-loading="loading"
    >
      <el-option
        v-for="item in users"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>
  </span>
</template>

<script>
export default {
  model: {
    prop: 'userId',
    event: 'change'
  },
  props: {
    userId: {
      validator: function (val) {
        return val === null || typeof val === 'number' || typeof val === 'object'
      },
      required: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'normal'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    selectStyle: {
      type: String,
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      localUserId: this.userId,
      users: [],
      loading: false
    }
  },
  computed: {
    selectUser () {
      if (!this.localUserId) return {}
      return this.users.find(x => x.id === this.localUserId)
    }
  },
  watch: {
    userId () {
      this.localUserId = this.userId
    },
    localUserId () {
      this.$emit('change', this.localUserId)
      this.$emit('userChange', this.selectUser)
    }
  },
  created () {
    this.getUsers()
  },
  methods: {
    getUsers () {
      this.loading = true
      this.$axios.get('/api/users')
        .then(response => {
          this.loading = false
          this.users = response.data
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-select{
    width: 100%;
  }
</style>
