<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      width="90%"
      append-to-body
    >
      <div>
        <el-card>
          <div
            slot="header"
            class="title-line"
          >
            <div class="title">岗位基线详情</div>
          </div>
          <el-table
            v-loading="loading"
            :data="baselines"
            border
            stripe
            style="width: 100%"
          >
            <el-table-column
              prop="bs_name"
              label="业务系统"
              fixed
            />
            <el-table-column
              prop="name"
              label="系统基线"
            />
            <el-table-column
              prop="name"
              label="系统基线角色清单"
            >
              <template slot-scope="scope">
                {{ getRoleName(scope.row.roles) }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="130"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.id"
                  size="mini"
                  @click="openBaselineDetail(scope.row)"
                >
                  系统基线详情
                </el-button>
                <span
                  v-else="scope.row.id"
                  style="align: center;"
                >
                  该岗位禁止存在账号
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <el-card class="account-div">
          <div
            slot="header"
            class="title-line"
          >
            <div class="title">岗位员工列表</div>
          </div>

          <all-users-page
            :display-search-user='false'
            :job-id="job.id"
            :quarter="currentQuarter"
          />
        </el-card>
      </div>
    </el-dialog>

    <baseline-detail
      ref="baselineDetail"
      :system-id="currentBaseline.business_system_id"
      :baseline="currentBaseline"
    />
  </div>
</template>

<script>
import API            from '@/api'
import BaselineDetail from '@/components/AdminJobs/BaselineDetail.vue'
import AllUsersPage   from '@/components/AllUsers/AllUsersPage.vue'

export default {
  components: {
    BaselineDetail,
    AllUsersPage
  },
  props:      {
    job: {
      type:     Object,
      required: true,
      default: () => {}
    }
  },
  data () {
    return {
      visible: false,
      loading: false,
      baselines: [],
      currentBaseline: {
        id:    null,
        name:  null,
        business_system_id: null
      }
    }
  },
  computed: {
    title () {
      return `${this.job.name}岗位详情`
    },
    currentQuarter () {
      return this.$store.state.current_quarter
    }
  },
  watch:    {
    job () {
      this.getSystemBaselines()
    }
  },
  created () {
  },
  methods: {
    // 获取指定岗位的系统基线
    getSystemBaselines () {
      if (this.job.id === null) return

      this.loading = true
      API.jobBaselines.systemBaselines(this.job.id)
        .then(response => {
          this.baselines = response.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
          this.$message.info('已取消操作')
        })
    },
    // 角色名称
    getRoleName (roles) {
      if(roles) {
        return roles.map(x => x.name).join('、')
      }
      else {
        return ''
      }
    },
    //
    openBaselineDetail (row) {
      this.$refs.baselineDetail.visible = true
      this.currentBaseline = row
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/components/variables";

.account-div {
  margin-top: 20px;
}

.not {
  margin-right: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

.title-line {
  @include vertical_center_between;

  .title {
    font-size: 16px;
  }
}

.card-body {
  margin-top:    10px;
  margin-bottom: 10px;
}
</style>
