<template>
  <div>
    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-table
      ref="table"
      :data="tableData"
      :row-class-name="tableRowClassName"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :selectable="disableSelect"
        type="selection"
        width="55"
      />
      <el-table-column type="expand">
        <template slot-scope="props">
          <div
            v-if="!props.row.user_id"
            class="account-no-link"
          >
            该账号尚未关联员工
          </div>
          <div v-else>
            <user-info
              :userId="props.row.user_id"
              border
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        property="account_code"
        label="账号编码"
        sortable
      >
        <template slot-scope="{ row }">
          {{ row.account_code || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        property="account_name"
        label="账号名称"
        sortable
      >
        <template slot-scope="{ row }">
          {{ row.account_name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        property="user_code"
        label="员工编码"
        sortable
      >
        <template slot-scope="{ row }">
          {{ row.user_code || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        property="user_name"
        label="员工名称"
        sortable
      >
        <template slot-scope="{ row }">
          {{ row.user_name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="200"
      >
        <template slot-scope="scope">
          <div
            style="float:left;"
          >
            <el-button
              :disabled="scope.row.isLinked"
              size="small"
              type="primary"
              @click="handleChangeUser(scope.row)"
            >
              切换员工
            </el-button>
            <el-button
              :type="scope.row.isLinked ? 'success' : 'primary'"
              size="small"
              @click="createOrDeleteLink(scope.row)"
            >
              {{ scope.row.isLinked ? '取消关联' : '确定关联' }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <user-change
      :visible.sync="dialogVisible"
      :ledger="ledgerWillChangeUser"
      :systemId="systemId"
      @update="updateRow"
    />
  </div>
</template>

<script>
import UserInfo   from './UserInfo.vue'
import UserChange from './UserChange.vue'
export default {
  components: {
    UserInfo,
    UserChange
  },
  props: {
    systemId: {
      type: Number,
      required: true
    },
    tableData: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      selection: [],
      dialogVisible: false,
      ledgerWillChangeUser: {}
    }
  },
  methods: {
    handleSelectionChange (selection) {
      this.selection = selection
    },
    disableSelect (row, index) {
      if (row.isLinked) return false

      return true
    },
    clearSelection () {
      this.selection = []
      this.$refs.table.clearSelection()
    },
    // 关联成功后让当前表格的当前row数据变色
    tableRowClassName ({ row, rowIndex }) {
      if (row.isLinked) return 'warning-row'
    },
    createOrDeleteLink (row) {
      if (row.isLinked) return this.deleteLink(row)

      return this.createLink(row)
    },
    createLink (row) {
      this.$axios.put(`/admin_api/ledgers/bs/${this.systemId}/accounts/${row.account_code}`, {
        user_id: row.user_id
      })
        .then(response => {
          this.$message.success('关联成功')
          row.isLinked = true
          this.$emit('updateRow', row)
        })
        .catch(() => {})
    },
    deleteLink (row) {
      this.$axios.delete(`/admin_api/ledgers/bs/${this.systemId}/accounts/${row.account_code}`)
        .then(response => {
          this.$message.success('取消关联成功')
          row.isLinked = false
          this.$emit('updateRow', row)
        })
        .catch(() => {})
    },
    handleChangeUser (row) {
      this.dialogVisible = true
      this.ledgerWillChangeUser = row
    },
    updateRow (row) {
      this.$emit('updateRow', row)
    }

  }
}
</script>

<style lang="scss" scoped>
  .account-no-link{
    height: 60px;
    font-size: 14px;
    color: #a9a7b1;
    line-height: 60px;
    text-align: center;
  }
</style>
