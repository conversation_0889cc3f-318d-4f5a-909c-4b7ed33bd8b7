<script>
// 禁用各种资源的方法，包括用户、机构、支行、角色
export default {
  data () {
    return {
      // 类型需要是复数，匹配 api
      resourceType: 'accounts',
      resourceMode:  'both' // disable 仅停用， both 同时支持停用和启用
    }
  },
  computed: {
    resourceDisplay () {
      switch (this.resourceType) {
        case 'accounts':
          return '用户'
        case 'organizations':
          return '机构'
        case 'branches':
          return '支行'
        case 'roles':
          return '角色'
      }
    },
    resourceUniqueId () {
      switch (this.resourceType) {
        case 'accounts':
          return 'user_id'
        case 'organizations':
          return 'org_id'
        case 'branches':
          return 'org_id'
        case 'roles':
          return 'id'
      }
    }
  },
  methods:  {
    disableButtonName (record) {
      if (this.resourceMode === 'disable') return '停用'

      if (record.status_string === '正常') {
        return '停用'
      } else {
        return '启用'
      }
    },
    disableAction (record) {
      if (this.resourceMode === 'disable') return 'disable'

      if (record.status_string === '正常') {
        return 'disable'
      } else {
        return 'enable'
      }
    },
    handleDisableResource (record) {
      const message = `确认${this.disableButtonName(record)}该${this.resourceDisplay}吗?`
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.operationReviewDisableResource(record)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })

    },
    operationReviewDisableResource (record) {
      const guid = this.$guidGenerate()
      this.$operationReview(this.systemId, guid)
        .then(() => {
          this.disableResource(record, guid)
        })
        .catch(() => {})
    },
    resourceRefresh () {
      // 在资源内中实现
    },
    disableResource (record, guid) {
      const id         = record[this.resourceUniqueId]
      const action     = this.disableAction(record)
      const actionName = this.disableButtonName(record)

      this.$axios.post(`/admin_api/edit_api/${this.systemId}/${this.resourceType}/${id}/${action}?guid=${guid}`)
        .then(response => {
          this.$message.success(`${this.resourceDisplay}已${actionName}`)
          this.resourceRefresh()
        })
        .catch(() => {})
    }
  }
}
</script>
