<template>
  <el-form
    :inline="true"
    class="search-box"
  >
<!--     <el-form-item>
      <el-checkbox
        v-model="search.showNoAccountLinks"
        size="small"
        @change="handleChange"
      >
        仅显示未关联账号的基线
      </el-checkbox>
    </el-form-item>
 -->
    <el-form-item>
      <department-select
        v-model="search.department_ids"
        placeholder="搜索部门"
        :department-id="[]"
        size="small"
        :checkStrictly="true"
        multiple
        collapseTags
        filterable
        clearable
        selectStyle="width:250px;margin-right:0px;"
        @keyup.enter.native="handleChange"
      />
    </el-form-item>
    <el-form-item>
      <el-input
        v-model="search.job_name"
        size="small"
        placeholder="搜索岗位"
        clearable
        class="common-input"
      />
    </el-form-item>
    <el-form-item>
      <el-select
        v-model="search.showJobBaselineLinks"
        placeholder="是否关联岗位基线"
        size="small"
        style="width:150px;"
        @keyup.enter.native="handleChange"
        clearable
        filterable
      >
        <el-option
          v-for="x in jobBaselineLinks"
          :label="x.name"
          :value="x.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item>
      <el-button
        type="primary"
        size="small"
        @click="handleChange"
      >
        检索
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import API from '@/api'
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'

export default {
  components: {
    DepartmentSelect
  },
  data () {
    return {
      search: {
        department_id:        null,
        department_ids:       [],
        job_name:             null,
        showJobBaselineLinks: null,
        inservice:            'true'
      },
      departments: [],
      jobs: [],
      jobBaselineLinks: [
        { name: '已关联岗位基线', value: '1' },
        { name: '未关联岗位基线', value: '0' }
      ]
    }
  },
  created () {
    this.getDepartments()
  },
  methods: {
    handleChange () {
      this.$emit('change', this.$lodash.clone(this.search))
    },
    getDepartments () {
      const params = { without_permission: true }
      API.departments.index(params)
        .then(response => {
          this.departments = response.data
        })
        .catch(() => {
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form-item{
  margin-bottom: 0;
}

// /deep/ .el-cascader .el-cascader__tags {
//   width:90%;
//   right: 1px;
//   top: 1px;
//   transform: unset;
// }
</style>
