# frozen_string_literal: true

# 任务执行用
class QuarterAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = I18n.t('module_name.quarters')
    @operation_category = '手动任务'
    super
  end

  def rakes_restart
    @operation = '执行后台任务'
    @comment   = "后台任务: #{params}"
    create_audit_log
  end

  def rake_restart
    @operation = '执行后台任务'
    @comment   = "后台任务: #{params}"
    create_audit_log
  end

  def rake_system_restart
    @operation = '执行后台系统依赖任务'
    @comment   = "后台任务: #{params}"
    create_audit_log
  end

  def rakes_system_restart
    @operation = '执行后台系统依赖任务'
    @comment   = "后台任务: #{params}"
    create_audit_log
  end

  def imports_restart
    @operation = '执行后台任务'
    @comment   = "导入数据任务: #{params}"
    create_audit_log
  end

  def import_restart
    @operation = '执行后台任务'
    @comment   = "导入数据任务: #{params}"
    create_audit_log
  end

  def create_excel
    @operation = '创建新导入'
    @operation_category = @operation_module
    @comment   = "创建导入「#{params[0]}」#{params[1]}"
    create_audit_log
  end

  def create
    @operation = '创建新导入'
    @operation_category = @operation_module
    @comment   = "创建导入「#{params[0]}」#{params[1]}"
    create_audit_log
  end

  def destroy
    @operation = '数据删除'
    @operation_category = @operation_module
    @comment   = "成功删除了「#{params}」的所有数据"
    create_audit_log
  end

  def update
    @operation = '导入数据更新'
    @operation_category = @operation_module
    @comment   = "修改 ID 为 「#{params[0].id}」的名称为「#{params[0].name}」#{params[1]}"
    create_audit_log
  end
end
