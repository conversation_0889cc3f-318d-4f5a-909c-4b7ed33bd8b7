<template>
  <div>
    <div class="tool-bar">
      <div class="left">
        <search-tool-box
          @change="handleSearch"
        />
      </div>
      <div class="right">
        <!--
        <el-button
          type="primary"
          size="small"
          :disabled="!$store.getters.hasPermission('job_baseline.edit')"
          @click="createBaseline"
        >
          创建基线
        </el-button>
        -->
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="jobs"
      border
      class="baseline-table"
    >
      <el-table-column
        prop="department_full_name"
        label="部门"
      />
      <el-table-column
        prop="level1_name"
        label="一级岗位"
      />
      <el-table-column
        prop="level2_name"
        label="二级岗位"
      />
      <el-table-column
        prop="system_baseline_count"
        label="已关联系统基线数量"
        width="150"
      />
      <el-table-column
        label="员工数量"
        width="100"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.user_count }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        label="操作"
        header-align="center"
        width="290"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            :disabled="!$store.getters.hasPermission('job_baseline.query')"
            @click="openJobDetail(scope.row)"
          >
            岗位详情
          </el-button>
          <el-button
            size="mini"
            :disabled="!$store.getters.hasPermission('job_baseline.edit')"
            @click="editBaseline(scope.row)"
          >
            {{ isJobBindRoles ? '绑定角色' : '设置基线' }}
          </el-button>
          <el-button
            size="mini"
            type="danger"
            :disabled="!$store.getters.hasPermission('job_baseline.delete') || !scope.row.job_baseline_id"
            @click="deleteConfirm(scope.row)"
          >
            删除基线
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-size="pageSize"
      :total="count"
      :current-page.sync="page"
      background
      layout="total, prev, pager, next, jumper"
      class="pagination"
      @current-change="getJobs"
    />
<!--
    <job-baseline-edit
      ref="baselineCreate"
      mode="create"
      @change="getJobs"
    />
 -->
    <job-baseline-bind-roles
      v-if="isJobBindRoles"
      ref="baselineBindRoles"
      :job="currentBindJob"
      @change="getJobs"
    />
    <job-baseline-edit
      v-else
      ref="baselineEdit"
      :job="currentBindJob"
      @change="getJobs"
    />
    <job-detail
      ref="jobDetail"
      :job="currentJob"
    />
  </div>
</template>

<script>
import API from '@/api'
import SearchToolBox from '@/components/AdminJobs/SearchToolBox.vue'
import JobDetail from '@/components/AdminJobs/JobDetail.vue'
import JobBaselineEdit from '@/components/AdminBaselines/JobBaselineEdit.vue'
import JobBaselineBindRoles from '@/components/AdminBaselines/JobBaselineBindRoles.vue'
import jobBaselineBindRoles from "../AdminBaselines/JobBaselineBindRoles.vue";

export default {
  components: {
    SearchToolBox,
    JobBaselineEdit,
    JobDetail,
    JobBaselineBindRoles
  },
  props: {
  },
  data () {
    return {
      loading: false,
      jobs: [],
      count: 0,
      page: 1,
      pageSize: 25,
      filter: {
        department_id:        null,
        department_ids:       [],
        job_name:             null,
        showJobBaselineLinks: null,
        inservice:            'true'
      },
      currentJob: {
        id:    null,
        name:  null
      },
      currentBindJob: {
        id:    null,
        name:  null
      },
      quarter: null
    }
  },
  computed: {
    currentQuarter () {
      return this.$store.state.current_quarter
    },
    isJobBindRoles () {
      return this.$settings.jobBaselineBindRoles
    }
  },
  created () {
    this.getJobs()
    this.getLastQuarterId()
  },
  methods: {
    getJobs () {
      this.loading = true
      const theParams = {
        page:       this.page,
        per_page:   this.pageSize,
        quarter_id: this.currentQuarter.id,
        filter:     this.filter
      }
      API.jobs.list(theParams)
        .then(response => {
          this.loading = false
          this.jobs = response.data.data
          this.count = response.data.count
        })
        .catch(() => {
          this.loading = false
        })
    },
    openJobDetail (job) {
      this.$refs.jobDetail.visible = true
      this.currentJob = job
    },
    deleteConfirm (job) {
      let notice = '是否确认删除该基线?'
      // if (job.user_count > 0) {
      //   notice = `该基线目前关联了${job.user_count}个账号，是否确认删除该基线?`
      // }
      this.$confirm(notice, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteBaseline(job.job_baseline_id)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    deleteBaseline (id) {
      this.loading = true

      API.jobBaselines.destroy(id)
        .then(response => {
          this.loading = false
          this.getJobs()
          this.$message.success('删除基线成功')
        })
        .catch(() => {
          this.loading = false
        })
    },
    editBaseline (row) {
      if (this.isJobBindRoles) {
        this.$refs.baselineBindRoles.visible = true
      } else {
        this.$refs.baselineEdit.visible = true
      }
      this.currentBindJob = row
    },
    handleSearch (payload) {
      this.filter = payload
      this.page = 1
      this.getJobs()
    },
    displayUserLink (row) {
      return row.user_count > 0
    },
    getLastQuarterId () {
      API.quarters.last()
        .then(response => {
          this.quarter = response.data
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .first-button {
    margin-bottom: 12px;
  }
  .tool-bar{
    @include vertical_top_between;
    margin-top: 5px;
    margin-bottom: 20px;

    .right{
      @include vertical_center_right;
      margin-top: 4px;
    }

    .tool{
      margin-left: 10px;
    }
  }
  .baseline-table{
    margin-top: 20px;
    border-top: 1px solid #EBEEF5;
  }
  .pagination{
    margin-top: 20px;
  }
</style>
