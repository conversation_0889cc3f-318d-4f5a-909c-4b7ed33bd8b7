# frozen_string_literal: true

# 外部系统审计日志
class External::BusinessSystemAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '外部系统设置'
    @operation_category = '外部系统设置'
    super
  end

  def create
    @operation = '创建外部系统'
    @comment   = "创建外部系统「#{params.name}」成功，ID：#{params.id}，状态：#{params.inservice_text}"
    create_audit_log
  end

  def update
    @operation = '更新外部系统'
    @comment   = generate_update_log(params)
    create_audit_log
  end

  def destroy
    @operation = '删除外部系统'
    @comment   = "删除外部系统「#{params.name}」，ID：#{params.id}"
    create_audit_log
  end

  def update_settings
    @operation = '更新外部系统设置'
    @comment   = generate_update_settings_log(params)
    create_audit_log if @comment.present?
  end

  protected

  # 生成更新日志
  def generate_update_log(params)
    strs = []
    data = params[1].to_a - params[0].to_a
    data.each do |o|
      field_name = field_name(o[0].to_s)
      strs << "#{field_name}：#{o[1]}" if field_name.present?
    end
    return if strs.blank?

    "更新外部系统 ID：#{params[0]&.[](:id)}，#{strs.join('，')}"
  end

  # 生成系统设置日志
  def generate_update_settings_log(params)
    strs = []
    bs = params[2]
    del_str = allow_del_account_setting(params)
    add_account_strs = add_account_settings(params)
    del_account_strs = del_account_settings(params)
    add_role_strs = add_role_settings(params)
    del_role_strs = del_role_settings(params)
    update_default_account_strs = update_default_account_settings(params)
    update_default_role_strs = update_default_role_settings(params)
    all_strs = [add_account_strs, del_account_strs, add_role_strs, del_role_strs, update_default_account_strs, update_default_role_strs].flatten
    return if all_strs.blank?

    strs << del_str if del_str.present?
    strs << "新增账户字段：#{add_account_strs.join('、')}" if add_account_strs.present?
    strs << "删除账户字段：#{del_account_strs.join('、')}" if del_account_strs.present?
    strs << "更改账户字段：#{update_default_account_strs.join('、')}" if update_default_account_strs.present?
    strs << "新增角色字段：#{add_role_strs.join('、')}" if add_role_strs.present?
    strs << "删除角色字段：#{del_role_strs.join('、')}" if del_role_strs.present?
    strs << "更改角色字段：#{update_default_role_strs.join('、')}" if update_default_role_strs.present?
    "更新外部系统「#{bs.name}」设置，ID：「#{params[0][:id]}」，#{strs.join('，')}"
  end

  # 允许删除账户设置
  def allow_del_account_setting(params)
    before_is_allow_delete = params[0][:is_allow_delete]
    after_is_allow_delete = params[1][:is_allow_delete]
    return nil if before_is_allow_delete == after_is_allow_delete

    "允许删除账号: 「#{after_is_allow_delete ? '启用' : '禁用'}」"
  end

  # 新增账户设置
  def add_account_settings(params)
    strs = []
    before_account_settings = params[0][:account_settings].select { |o| o['is_enable'] }
    after_account_settings = params[1][:account_settings].select { |o| o['is_enable'] }
    add_account_settings = after_account_settings - before_account_settings
    add_account_settings.each do |o|
      field_name = params[2].account_field_name(o['name'].to_s)
      strs << "#{field_name}:「#{o['type']}」" if field_name.present?
    end
    strs
  end

  # 删除账户设置
  def del_account_settings(params)
    strs = []
    before_account_settings = params[0][:account_settings].select { |o| o['is_enable'] }
    after_account_settings = params[1][:account_settings].select { |o| o['is_enable'] }
    del_account_settings = before_account_settings - after_account_settings
    del_account_settings.each do |o|
      field_name = params[2].account_field_name(o['name'].to_s)
      strs << "#{field_name}:「#{o['type']}」" if field_name.present?
    end
    strs
  end

  # 新增角色设置
  def add_role_settings(params)
    strs = []
    before_role_settings = params[0][:role_settings].select { |o| o['is_enable'] }
    after_role_settings = params[1][:role_settings].select { |o| o['is_enable'] }
    add_role_settings = after_role_settings - before_role_settings
    add_role_settings.each do |o|
      field_name = params[2].role_field_name(o['name'].to_s)
      strs << "#{field_name}:「#{o['type']}」" if field_name.present?
    end
    strs
  end

  # 删除角色设置
  def del_role_settings(params)
    strs = []
    before_role_settings = params[0][:role_settings].select { |o| o['is_enable'] }
    after_role_settings = params[1][:role_settings].select { |o| o['is_enable'] }
    del_role_settings = before_role_settings - after_role_settings
    del_role_settings.each do |o|
      field_name = params[2].role_field_name(o['name'].to_s)
      strs << "#{field_name}:「#{o['type']}」" if field_name.present?
    end
    strs
  end

  # 更新默认账户设置
  def update_default_account_settings(params)
    strs = []
    before_default_account_settings = params[0][:default_account_settings]
    after_default_account_settings = params[1][:default_account_settings]
    update_default_account_settings = after_default_account_settings - before_default_account_settings
    update_default_account_settings.each do |o|
      field_name = params[2].account_field_name(o['name'].to_s)
      default_display_name = o['default_display_name']
      strs << "#{default_display_name} 改名为 「#{field_name}」" if field_name.present?
    end
    strs
  end

  # 更新默认角色设置
  def update_default_role_settings(params)
    strs = []
    before_default_role_settings = params[0][:default_role_settings]
    after_default_role_settings = params[1][:default_role_settings]
    update_default_role_settings = after_default_role_settings - before_default_role_settings
    update_default_role_settings.each do |o|
      field_name = params[2].role_field_name(o['name'].to_s)
      default_display_name = o['default_display_name']
      strs << "#{default_display_name} 改名为 「#{field_name}」" if field_name.present?
    end
    strs
  end

  # 根据字段名称返回系统字段中文名称
  def field_name(name)
    case name
    when 'name' then '名称'
    when 'inservice_text' then '状态'
    end
  end
end
