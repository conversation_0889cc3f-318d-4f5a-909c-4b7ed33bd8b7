<template>
  <div class="container">
    <h3>系统岗位管理</h3>
    <hr>
    <div class="tool-bar">
      <div class="left">
        <search-tool-box
          @change="handleSearch"
        />
      </div>
      <div class="right">
        <el-button
          type="primary"
          size="small"
          :disabled="!$store.getters.hasPermission('admin_job_list.edit')"
          @click="createJob()"
        >
          创建岗位
        </el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="jobs"
      border
      class="baseline-table"
    >
      <el-table-column
        prop="department_full_name"
        label="部门"
      />
      <el-table-column
        prop="level1_name"
        label="一级岗位"
      />
      <el-table-column
        prop="level2_name"
        label="二级岗位"
      />
      <el-table-column
        label="员工数量"
        width="100"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.user_count }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="状态"
        width="100"
      >
        <template slot-scope="scope">
          <el-tag :type="tagClass(scope.row.inservice)">
            {{ inserviceText(scope.row.inservice) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        label="操作"
        header-align="center"
        width="290"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            :disabled="!$store.getters.hasPermission('admin_job_list.user_list')"
            @click="openJobUsers(scope.row)"
          >
            员工管理
          </el-button>

          <el-button
            size="mini"
            :disabled="!$store.getters.hasPermission('admin_job_list.edit')"
            @click="editJob(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            :disabled="!$store.getters.hasPermission('admin_job_list.delete')"
            @click="deleteConfirm(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-size="pageSize"
      :total="count"
      :current-page.sync="page"
      background
      layout="total, prev, pager, next, jumper"
      class="pagination"
      @current-change="getJobs"
    />

    <job-form
      ref="jobCreate"
      mode="create"
      @change="getJobs"
    />

    <job-form
      ref="jobEdit"
      mode="update"
      :job="currentJob"
      @change="getJobs"
    />

    <job-user-list
      ref="userList"
      :job="currentJob"
      @change="getJobs"
    />

  </div>
</template>

<script>
import API from '@/api'
import SearchToolBox from '@/components/AdminJobList/SearchToolBox.vue'
import JobForm from '@/components/AdminJobList/JobForm.vue'
import JobUserList from '@/components/AdminJobList/JobUserList.vue'

export default {
  components: {
    SearchToolBox,
    JobForm,
    JobUserList
  },
  props: {
  },
  data () {
    return {
      loading: false,
      jobs: [],
      count: 0,
      page: 1,
      pageSize: 25,
      filter: {
        // department_id:  null,
        department_ids: [],
        job_name:       null
      },
      currentJob: {
        id:    null,
        name:  null
      },
      quarter: null
    }
  },
  computed: {
    currentQuarter () {
      return this.$store.state.current_quarter
    },
    isJobBindRoles () {
      return this.$settings.jobBaselineBindRoles
    }
  },
  created () {
    this.getJobs()
    this.getLastQuarterId()
  },
  methods: {
    getJobs () {
      this.loading = true
      const theParams = {
        page:       this.page,
        per_page:   this.pageSize,
        quarter_id: this.currentQuarter.id,
        filter:     this.filter
      }
      API.jobs.list(theParams)
        .then(response => {
          this.loading = false
          this.jobs = response.data.data
          this.count = response.data.count
        })
        .catch(() => {
          this.loading = false
        })
    },
    openJobUsers (job) {
      this.$refs.userList.visible = true
      this.currentJob = job
    },
    deleteConfirm (job) {
      let notice = '是否确认删除该岗位?'
      this.$confirm(notice, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteJob(job.id)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    deleteJob (id) {
      this.loading = true

      API.jobs.destroy(id)
        .then(response => {
          this.loading = false
          this.getJobs()
          this.$message.success('删除岗位成功')
        })
        .catch(() => {
          this.loading = false
        })
    },
    editJob (row) {
      this.$refs.jobEdit.visible = true
      this.currentJob = row
    },
    createJob () {
      this.$refs.jobCreate.visible = true
    },
    handleSearch (payload) {
      this.filter = payload
      this.page = 1
      this.getJobs()
    },
    getLastQuarterId () {
      API.quarters.last()
        .then(response => {
          this.quarter = response.data
        })
        .catch(() => {})
    },
    inserviceText(inservice) {
      return inservice ? '启用' : '禁用'
    },
    tagClass(inservice) {
      return inservice ? '' : 'danger'
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .container {
    padding:          2em;
    background-color: white;
    min-width:        1000px;
  }

  .tool-bar{
    @include vertical_top_between;
    margin-top: 5px;
    margin-bottom: 20px;

    .right{
      @include vertical_center_right;
      margin-top: 4px;
    }

    .tool{
      margin-left: 10px;
    }
  }

  .el-pagination {
    margin-top: 1em;
  }

</style>
