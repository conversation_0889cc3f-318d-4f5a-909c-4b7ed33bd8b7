<template>
  <!-- eslint-disable vue/attribute-hyphenation -->
  <el-dialog
    :visible.sync="localVisible"
    :close-on-click-modal="false"
    width="420px"
    top="8em"
    title="提示"
    append-to-body
  >
    <template>
      <div class="notice">
        此操作将删除「{{ ledger.account_name }}」账号与当前「{{ ledger.user_name }}」员工的关联关系, 是否继续?
      </div>
    </template>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="localVisible = false">取 消</el-button>
      <el-button
        type="primary"
        @click="deleteLink"
      >
        确 定
      </el-button>
    </span>
  </el-dialog>
  <!-- eslint-enable vue/attribute-hyphenation -->
</template>

<script>
import API from '@/api'

export default {
  props: {
    visible:  {
      type:     Boolean,
      required: true
    },
    ledger:   {
      type:     Object,
      required: true
    },
    systemId: {
      type:     Number,
      required: true
    }
  },
  data () {
    return {
      localVisible:  false
    }
  },
  watch: {
    visible () {
      this.localVisible = this.visible
    },
    localVisible () {
      this.$emit('update:visible', this.localVisible)
    }
  },
  created () {
    this.localVisible = this.visible
  },
  methods: {
    deleteLink () {
      API.ledgers.deleteLink({
        systemId:      this.systemId,
        accountCode:   this.ledger.account_code
      })
        .then(response => {
          if (response.data.account_code) {
            this.localVisible = false
            this.$emit('update')
            this.$message.success('取消关联成功')
          } else {
            this.$message.error(response.data.error_message)
          }
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.notice{

}
.checkbox-container {
  margin-top: 20px;
}
</style>
