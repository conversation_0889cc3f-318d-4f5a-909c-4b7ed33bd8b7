class DepartmentController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!, except: [:jobs]

  def index
    departments = Department.inservice
    data = departments.map(&:output)
    json_respond data: data, count: data.count
  end

  def create
    name = params[:name]

    if Department.find_by_name(name)
      return_data = {success: false, errors: ['部门已存在']}
      json_respond return_data
      return nil
    end

    department           = Department.new
    department.name      = name
    department.inservice = true

    if department.save

      audit_log! department

      return_data = {success: true, data: {id: department.id, name: department.name}}
    else

      audit_log! department, action: :create_failed

      return_data = {success: false, error_message: 'Cannot create department.'}
    end
    json_respond return_data
  end

  def edit
    name = params[:name]
    id   = params[:id].to_i
    begin
      department = Department.find(id)
      authorize department, policy_class: DepartmentPolicy
    rescue ActiveRecord::RecordNotFound => e
      json_custom_respond(404, success: false, error_message: 'Department not found.')
      return nil
    end
    if department.update(name: name)

      audit_log! department

      json_respond(success: true, error_message: nil)
    else

      audit_log! department, action: :edit_failed

      json_respond(success: false, error_message: 'Update department failed.')
    end
  end

  def destroy
    begin
      department = Department.find(params[:id].to_i)
      authorize department, policy_class: DepartmentPolicy
    rescue ActiveRecord::RecordNotFound => e
      json_custom_respond(404, success: false, error_message: 'Department not found.')
      return nil
    end

    audit_log! department

    department.users.update_all(department_id: nil)
    department.destroy
    json_respond(success: true, error_message: nil)
  end

  def jobs
    department = Department.find(params[:id])
    jobs = department.tree_jobs
    json_respond data: jobs, count: jobs.count
  end

  private

  def authenticate_policy!
    authorize Department
  end
end
