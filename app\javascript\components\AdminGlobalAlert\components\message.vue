<template>
  <div class="block">
    <el-timeline>
      <el-timeline-item
        v-for="(item, index) in tableDataDetails"
        :key="index"
        :timestamp="item.created_at"
        placement="top"
      >
        <span
            v-if="!item.targetable_type&&tableDataDetails.length>0"
        >
          监测到告警存在
        </span>
        <span v-if="!item.targetable_type&&tableDataDetails.length==0">
          告警已屏蔽
        </span>
        <span
          v-if="item.targetable_type"
          style="margin-right:20px;display: block;"
        >
          <i class="el-icon-edit" />
          {{ item.name  }}
          <span class="main">
            {{  item.operation +  item.alert_comment }}
          </span>
        </span>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
export default {
  props: {
    tableDataDetails: {
      type: Array,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.main{
   color:#7d7d7d;
   font-size: 12px;
}
</style>
