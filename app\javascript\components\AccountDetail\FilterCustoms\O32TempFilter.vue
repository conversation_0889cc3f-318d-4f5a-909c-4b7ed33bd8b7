<template>
  <span>
    <el-checkbox v-model="checked">仅显示临时授权</el-checkbox>
  </span>
</template>

<script>
export default {
  props: {
    filterData: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      checked: false
    }
  },
  computed: {
    dataAfterFilter () {
      if (this.checked) {
        return this.filterData.filter(x => x.temp_info.is_temp)
      }

      return this.filterData
    }
  },
  watch: {
    dataAfterFilter () {
      this.$emit('change', this.dataAfterFilter)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
