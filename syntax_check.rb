#!/usr/bin/env ruby

# 简单的语法检查脚本
puts "检查文件: config/initializers/start_sidekiq.rb"

begin
  # 尝试解析文件
  code = File.read('config/initializers/start_sidekiq.rb')

  # 使用 Ruby 解析器检查语法
  RubyVM::InstructionSequence.compile(code)

  puts "✅ 语法检查通过！"
rescue SyntaxError => e
  puts "❌ 语法错误："
  puts e.message

  # 尝试提取行号
  if e.message =~ /\(eval\):(\d+)/
    line_num = $1.to_i
    puts "错误位置: 第 #{line_num} 行"

    # 显示错误附近的代码
    lines = code.split("\n")
    start_line = [line_num - 3, 0].max
    end_line = [line_num + 2, lines.length - 1].min

    puts "\n错误附近的代码:"
    (start_line..end_line).each do |i|
      marker = (i + 1 == line_num) ? ">>> " : "    "
      puts "#{marker}#{i + 1}: #{lines[i]}"
    end
  end
rescue => e
  puts "❌ 其他错误："
  puts e.message
  puts e.backtrace.first(5)
end
