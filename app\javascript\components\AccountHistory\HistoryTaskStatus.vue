<template>
  <el-timeline-item
    v-if="!status"
    :color="color"
    icon="el-icon-loading"
    size="large"
  >
    <span :style="{ color: color }">
      {{ quarterName }} {{ messageI18n }}
    </span>
  </el-timeline-item>
</template>

<script>
export default {
  props: {
    quarterName: {
      type:     String,
      required: true
    },
    status:      {
      type:     Boolean,
      required: true
    }
  },
  data () {
    return {
      messageI18n: this.$t('errors.task.history_task_not_success'),
      color:       '#E6A23C'
    }
  }
}
</script>

<style scoped>

</style>
