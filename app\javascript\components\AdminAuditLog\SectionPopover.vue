<template>
  <el-popover
    placement="right-end"
    trigger="click"
    width="1000"
    @show="auditDataChange(section.id)"
  >
    <div v-loading="loading">
      <el-table
        :data="dataByPage"
        border
        stripe
      >
        <el-table-column
          property="id"
          label="操作序号"
          width="80"
        />
        <el-table-column
          property="event_id"
          label="事件 ID"
          width="85"
        />
        <el-table-column
          property="operation"
          label="操作内容"
          width="140"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.download_url"
              type="text"
              @click="handleDownloadUrl(scope.row.download_url, scope.row.job_id)"
            >
              {{ scope.row.operation }}
            </el-button>
            <div v-else> {{ scope.row.operation }}</div>
          </template>
        </el-table-column>
        <el-table-column
          property="comment"
          label="详细信息"
        />
        <el-table-column
          property="operation_time_p"
          label="操作时间"
          width="160"
        />
      </el-table>
      <!-- eslint-disable vue/attribute-hyphenation -->
      <el-pagination
        :page-size.sync="pageSize"
        :page-sizes="[5, 10, 30, 50]"
        :total="log_data.length"
        :current-page.sync="currentPage"
        background
        layout="sizes, prev, pager, next"
      />
      <!-- eslint-enable vue/attribute-hyphenation -->
    </div>
    <div
      slot="reference"
      class="section-div"
    >
      {{ section.description }}
    </div>
  </el-popover>
</template>

<script>
import API from '@/api'
import downloadFile from './downloadFile.vue'

export default {
  mixins: [downloadFile],
  props: {
    section: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      current_section_id: 0,
      currentPage: 1,
      log_data: [],
      pageSize: 5
    }
  },
  computed: {
    dataByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.log_data.slice(start, end)
    }
  },
  methods: {
    auditDataChange (id) {
      this.loading = true
      this.current_section_id = id
      // console.log("show ....")
      this.$axios.get('/admin_api/audit_logs/logs_in_section', { params: { section_id: id } })
        .then(response => {
          this.currentPage = 1
          this.log_data = response.data.logs
          // console.log(this.role_data)
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    }

  }
}
</script>

<style scoped lang="scss">
  @import '~@/components/variables';

  .section-div{
    color: $link-color;

    &:hover{
      cursor: pointer;
    }
  }
  .el-pagination{
    margin-top: 10px;
    margin-left: -10px;
  }
</style>
