import axios from '@/settings/axios'

export const roles = (params) => {
  return axios.get('/api/app_roles', { params: params })
}

export const permissionsList = () => {
  return axios.get('/api/app_roles/module_functions')
}

export const create = (params) => {
  return axios.post('/api/app_roles', params)
}

export const update = (roleId, params) => {
  return axios.put(`/api/app_roles/${roleId}`, params)
}

export const updateMenuPermissions = (roleId, params) => {
  return axios.post(`/api/app_roles/${roleId}/update_role_module_functions`, params)
}

export const updateAlertPermissions = (roleId, params) => {
  return axios.post(`/api/app_roles/${roleId}/update_role_alerts_permissions`, params)
}

export const destroy = (roleId) => {
  return axios.delete(`/api/app_roles/${roleId}`)
}

export const edit = (roleId) => {
  return axios.get(`/api/app_roles/${roleId}/edit`)
}

export const detail = (roleId, systemId) => {
  return axios.get(`/api/roles/${roleId}/detail`, { params: { system_id: systemId } })
}
