<template>
  <div>
    <div v-if="!getResponse">
      <el-alert
        :closable="false"
        :title="responseMessage"
        type="info"
        center
        show-icon
      />
    </div>
    <div v-else>
      <div>
        <!-- eslint-disable vue/attribute-hyphenation -->
        <!-- 基线不空 -->
        <el-alert
          v-if="diffMode === 'compareBaseline' && baselineName !== '' && if_diff_datas_empty"
          :closable="false"
          :title="diffDataSameDisplay"
          type="info"
          description="Data no difference."
          center
          show-icon
        />
        <!-- 基线空 -->
        <el-alert
          v-else-if="diffMode === 'compareBaseline' && baselineName === '' && if_diff_datas_empty"
          :closable="false"
          :title="diffDataSameDisplay"
          type="warning"
          description="No Data."
          center
          show-icon
        />
        <!-- 其他账号 -->
        <el-alert
          v-else-if="diffMode === 'compareAccount' && if_diff_datas_empty"
          :closable="false"
          :title="diffDataSameDisplay"
          type="info"
          description="Data no difference."
          center
          show-icon
        />
        <!-- 历史账号 -->
        <el-alert
          v-else-if="(diffMode === 'compareHistory' || diffMode === '') && if_diff_datas_empty"
          :closable="false"
          :title="diffDataSameDisplay"
          type="info"
          description="Data no difference."
          center
          show-icon
        />
      </div>

      <div>
        <el-collapse
          v-if="diffMode === 'compareBaseline' && baselineName !== '' && compareRule === 'only_add'"
          v-model="activeNames"
          @change="handleChange"
        >
          <el-collapse-item
            v-if="if_has_add_permissions"
            :title="`对比 ${ diffName }，该${ entityName }权限超出了岗位基线范围`"
            name="addPermissions"
          >
            <base-table
              :table-data="diffDatas.add_permissions"
              :table-schema="dataSchema"
              filterable
              border
              background
              added-row
            />
          </el-collapse-item>
          <el-collapse-item
            v-if="if_has_reduce_permissions"
            :title="`对比 ${ diffName }，该${ entityName }权限与岗位基线有差异`"
            name="reducePermissions"
          >
            <base-table
              :table-data="diffDatas.reduce_permissions"
              :table-schema="dataSchema"
              filterable
              border
              background
              reduce-row
            />
          </el-collapse-item>
        </el-collapse>

        <div v-else>
          <div
            v-if="if_has_add_permissions"
            class="diff-table"
          >
            <h4 class="diff-desc">对比 {{ diffName }}，该{{entityName}}增加了以下{{ dataType }}：</h4>
            <base-table
              :table-data="diffDatas.add_permissions"
              :table-schema="dataSchema"
              filterable
              border
              background
              added-row
            />
          </div>
          <div
            v-if="if_has_reduce_permissions"
            class="diff-table"
          >
            <h4 class="diff-desc">对比 {{ diffName }}，该{{entityName}}减少了以下{{ dataType }}：</h4>
            <base-table
              :table-data="diffDatas.reduce_permissions"
              :table-schema="dataSchema"
              filterable
              border
              background
              reduce-row
            />
          </div>
        </div>
      </div>
      <!-- eslint-enable vue/attribute-hyphenation -->
    </div>
  </div>
</template>

<script>
import API from '@/api'
import BaseTableWithPagination from './TableWithPagination.vue'

export default {
  components: {
    'base-table': BaseTableWithPagination
  },
  props: {
    diffDatas: {
      type: Object,
      required: true
    },
    compareWithOther: {
      type: Boolean,
      default: false
    },
    dataSchema: {
      type: Array,
      required: true
    },
    compareName: {
      type: String,
      required: true
    },
    baselineName: {
      type: String,
      default: ''
    },
    isDiffBaseline: {
      type: Boolean,
      default: false
    },
    dataType: {
      type: String,
      default: '权限'
    },
    diffMode: {
      type: String,
      default: 'compareHistory'
    },
    otherAccountCode: {
      type: String,
      default: ''
    },
    systemId:         {
      type:    Number,
      default: 0
    },
    compareRule: {
      type: String,
      default: 'all_contrast'
    },
    getResponse: {
      type: Boolean,
      required: true
    },
    entityName: {
      type: String,
      default: '账号'
    }
  },
  data () {
    return {
      activeNames: ['addPermissions'],
      account_info: {}
    }
  },
  computed: {
    diffName () {
      if (this.diffMode === 'compareAccount') {
        return this.account_info.account_name
      } else if (this.diffMode === 'compareBaseline') {
        return this.baselineName + ' 系统基线'
      } else {
        return this.compareName
      }
    },
    if_diff_datas_empty () {
      return this.diffDatas.add_permissions.length === 0 && this.diffDatas.reduce_permissions.length === 0
    },
    if_has_add_permissions () {
      return this.diffDatas.add_permissions.length > 0
    },
    if_has_reduce_permissions () {
      return this.diffDatas.reduce_permissions.length > 0
    },
    diffDataSameDisplay () {
      switch (this.diffMode) {
        case 'compareBaseline':
          if (!this.baselineName) {
            return '未获取到账号数据'
          } else {
            return `与系统基线范围相同，无${this.dataType}差异。`
          }
        case 'compareHistory':
          if (this.if_diff_datas_empty) {
            return `两时间点数据相同，无${this.dataType}差异。`
          }
          return ''
        case 'compareAccount':
          if (this.if_diff_datas_empty) {
            return `两账号数据相同，无${this.dataType}差异。`
          }
          return ''
        default:
          return '数据相同，无差异。'
      }
    },
    responseMessage () {
      return this.$t('response_message.not_send')
    }
  },
  watch: {
    otherAccountCode: {
      handler (accountCode) {
        if (this.diffMode === 'compareAccount') {
          this.getAccount()
        }
      }
    }
  },
  created () {
    if (this.diffMode === 'compareAccount') {
      this.getAccount()
    }
  },
  updated () {
    this.$nextTick(() => {
      const myEvent = new Event('resize')
      window.dispatchEvent(myEvent)
    })
  },
  methods: {
    handleChange (val) {
      console.log(val)
    },
    getAccount () {
      const theParams = {
        other_account_code: this.otherAccountCode,
        system_id:          this.systemId
      }
      API.systemAccounts.systemAccount(theParams)
        .then(response => {
          this.account_info = response.data.account_info
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
  .diff-desc{
    font-style: italic;
  }
  .diff-table:nth-child(2) {
    margin-top: 20px;
  }
</style>
