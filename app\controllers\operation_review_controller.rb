class OperationReviewController < ApplicationController
  before_action :authenticate_admin!
  before_action :set_business_system
  before_action :verify_reviewer_login, only: [:review]
  before_action :authenticate_policy!

  def review
    audit_log! [@business_system.name, 4, @admin]
    json_respond(success: true)
  end

  private

  def authenticate_policy!
    authorize nil, policy_class: OperationReviewPolicy
  end

  def review_params
    params.require(:reviewer).permit(:email, :password)
  end

  def verify_reviewer_login
    email              = review_params[:email]
    request.request_id = params[:guid]

    if current_admin.email == email
      json_custom_respond 400, error_message: I18n.t('errors.operation_review.not_allow_review_as_current_admin')
      return false
    end

    # @type [Admin] @admin
    @admin = Admin.find_by(email: review_params[:email])

    unless @admin
      json_custom_respond 400, error_message: I18n.t('errors.operation_review.not_found_admin')
      return false
    end

    if @admin.locked_at?
      json_custom_respond 400, error_message: I18n.t('errors.operation_review.admin_was_locked')
      return false
    end

    if @admin.disabled_at.present?
      json_custom_respond 400, error_message: I18n.t('errors.operation_review.account_disabled')
      return false
    end

    unless @admin.valid_password? review_params[:password]
      @admin.valid_for_authentication? { false }

      audit_log! [@business_system.name, 0] if @admin.failed_attempts >= Admin.maximum_attempts

      error = I18n.t('errors.operation_review.password_not_confirmed', failed_attempts: @admin.failed_attempts)
      audit_log! [@business_system.name, 1, @admin]
      json_custom_respond 400, error_message: error
      return false
    end

    # 验证成功，重置失败次数
    @admin.reset_failed_attempts!

    unless @admin.permission?('system_permission_manager.permission_reviewer')
      audit_log! [@business_system.name, 2, @admin]
      json_custom_respond 400, error_message: I18n.t('errors.operation_review.no_permission_reviewer_role')
      return false
    end

    unless @admin.maintain_system? @business_system.id
      audit_log! [@business_system.name, 3, @admin]
      json_custom_respond 400, error_message: I18n.t('errors.operation_review.not_match_system')
      return false
    end

    true
  end
end
