<template>
  <div class="container">
    <div class="tool-bar">
      <el-select
        v-model="selectPermissionIds"
        placeholder="选择权限"
        multiple
        collapse-tags
        filterable
        style="width: 100%; margin-right: 10px;"
      >
        <el-option
          v-for="item in allPermissions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button
        @click="linkPermissions"
      >
          添加关联
      </el-button>
    </div>
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="dataByPage"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="权限编码"
          minWidth="100"
          fixed
        />

        <el-table-column
          prop="name"
          label="权限名称"
          minWidth="180"
        />
        <el-table-column
          label="操作"
          fixed="right"
          width="105"
        >
          <template slot-scope="scope">
            <el-button
              type="danger"
              size="mini"
              @click="handleUnlinkComfirm(scope.row.id)"
            >
              删除关联
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :pageSize.sync="pageSize"
        :total="accountPermissions.length"
        :currentPage.sync="currentPage"
        :style="{ marginTop: '20px' }"
        :pageSizes="[10, 30, 50, 100]"
        background
        layout="total, sizes, prev, pager, next, jumper"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    accountId: {
      type: Number,
      required: true
    },
    systemId: {
      type: Number,
      required: true
    },
    settings: {
      type: Object,
      required: true
    }
  },

  data () {
    return {
      allPermissions:     [],
      accountPermissions: [],
      loading:      false,
      pageSize:     10,
      currentPage:  1,
      selectPermissionIds: []
    }
  },
  computed: {
    dataByPage () {
      let start = (this.currentPage - 1) * this.pageSize
      let end   = this.currentPage * this.pageSize
      return this.accountPermissions.slice(start, end)
    }
  },
  created () {
    this.getSystemPermissions()
    this.getAccountPermissions()
  },
  methods: {
    getSystemPermissions () {
      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/permissions`)
        .then(response => {
          this.loading = false
          this.allPermissions = response.data
        })
        .catch(() => {
          this.loading = false
        })
    },
    getAccountPermissions () {
      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/accounts/${this.accountId}/permissions`)
        .then(response => {
          this.loading = false
          this.accountPermissions = response.data
        })
        .catch(() => {
          this.loading = false
        })
    },
    unlinkPermissions (id) {
      this.$axios.delete(`/admin_api/edit_api/${this.systemId}/accounts/${this.accountId}/permissions/${id}`)
        .then(response => {
          this.$message.success('删除关联成功')
          this.getAccountPermissions()
        })
        .catch(() => {})
    },
    linkPermissions () {
      if (!this.selectPermissionIds) return

      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts/${this.accountId}/permissions`, {
        permission_ids: this.selectPermissionIds
      })
        .then(response => {
          this.$message.success('关联成功')
          this.getAccountPermissions()
        })
        .catch(() => {
          this.$message.error('关联失败')
        })
    },
    handleUnlinkComfirm (id) {
      this.$confirm('确认删除该权限关联吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.unlinkPermissions(id)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";
  .tool-bar{
    @include vertical_center_between;
  }
  .table-container{
    padding-top: 20px;
  }
</style>
