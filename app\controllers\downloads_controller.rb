# frozen_string_literal: true

# 下载相关
class DownloadsController < ApplicationController
  def sms_file
    redis_key = QuarterNotify::SmsAdapter::AttachmentAdapter::Base.sms_file_redis_key(params['redis_file_id'])
    file_path = Redis::Value.new(redis_key).value
    if redis_key && file_path
      send_file_compatible_with_msie file_path
    else
      render inline: '文件不存在'
    end
  end

  def file
    send_file_compatible_with_msie params[:file]
  end
end
