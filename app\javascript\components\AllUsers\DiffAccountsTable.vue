<template>
  <div>
    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-alert
      v-if="if_diff_datas_empty && otherQuarterName !== ''"
      :closable="false"
      :title="diffDataSameDisplay"
      type="info"
      description="Data no difference."
      center
      show-icon
    />
    <!-- eslint-enable vue/attribute-hyphenation -->
    <div v-else>
      <div
        v-if="if_has_increase"
        class="diff-table"
      >
        <h4 class="diff-desc">对比 {{ otherQuarterName }}，该员工增加了以下{{ dataType }}：</h4>
        <base-table
          :table-data="diffDatas.increase"
          :table-schema="dataSchema"
          filterable
          border
          background
          added-row
        />
      </div>
      <div
        v-if="if_has_reduce"
        class="diff-table"
      >
        <h4 class="diff-desc">对比 {{ otherQuarterName }}，该员工减少了以下{{ dataType }}：</h4>
        <base-table
          :table-data="diffDatas.reduce"
          :table-schema="dataSchema"
          filterable
          border
          background
          reduce-row
        />
      </div>
    </div>
  </div>
</template>

<script>
import BaseTableWithPagination from '@/components/AccountDetail/TableWithPagination'

export default {
  components: {
    'base-table': BaseTableWithPagination
  },
  props: {
    diffDatas: {
      type: Object,
      required: true
    },
    dataSchema: {
      type: Array,
      required: true
    },
    otherQuarterName: {
      type: String,
      required: true
    },
    dataType: {
      type: String,
      default: '账号'
    }
  },
  computed: {
    if_diff_datas_empty () {
      return this.diffDatas.increase.length === 0 && this.diffDatas.reduce.length === 0
    },
    if_has_increase () {
      return this.diffDatas.increase.length > 0
    },
    if_has_reduce () {
      return this.diffDatas.reduce.length > 0
    },
    diffDataSameDisplay () {
      return '历史时间点与当前相比，账号数量及状态未发生改变'
    }
  },
  updated () {
    this.$nextTick(() => {
      const myEvent = new Event('resize')
      window.dispatchEvent(myEvent)
    })
  }
}
</script>
<style lang="scss" scoped>
  .diff-desc{
    font-style: italic;
  }
  .diff-table:nth-child(2) {
    margin-top: 20px;
  }
</style>
