<template>
  <el-tabs v-model="tab">
    <el-tab-pane
      v-for="item in tabs"
      :key="item"
      :name="item"
      :label="tabTitle(item)"
    />

    <roles-check v-if="tab === 'role_abnormal'" />
    <funds-check v-if="tab === 'fund_abnormal'" />
  </el-tabs>
</template>

<script>
import RolesCheck from '@/components/AdminSettings/PcCompareO32/RolesCheck'
import FundsCheck from '@/components/AdminSettings/PcCompareO32/FundsCheck'
export default {
  components: {
    RolesCheck,
    FundsCheck
  },
  data () {
    return {
      tab: 'role_abnormal',
      tabs: ['role_abnormal', 'fund_abnormal']
    }
  },
  methods: {
    tabTitle (key) {
      const i18nKey = `aas.pc_o32_comparison.abnormals.${key}`
      return `${this.$t(i18nKey)}设置`
    }
  }
}
</script>
