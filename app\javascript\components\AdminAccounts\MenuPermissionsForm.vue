<template>
  <div class="page-container">
    <el-form>
      <el-form-item>
        <el-checkbox
          v-model="allSelect"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllSelect"
        >
          选择全部权限
        </el-checkbox>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="permissionsList"
      :span-method="moduleSpanMethod"
      border
      style="width: 100%"
    >
      <el-table-column
        align="center"
        prop="category"
        label="模块分类"
        width="100"
      >
        <template slot-scope="scope">
          {{ moduleType[scope.row.category] }}
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="模块名称"
        width="300"
      >
        <template slot-scope="scope">
          <el-row>
            <el-col :span="18">
              {{ scope.row.name }}
            </el-col>
            <el-col :span="6">
              <el-checkbox
                :value="scope.row.allSelect"
                :indeterminate="scope.row.isIndeterminate"
                @change="handleCheckAll(scope.row)"
              >
                全选
              </el-checkbox>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column
        label="权限"
      >
        <template slot-scope="scope">
          <div>
            <el-checkbox-group
              v-model="permissionsIds"
              class="box-group"
            >
              <div v-for="permission in scope.row.permissions">
                <div class="box-button">
                  <el-checkbox
                    :key="permission.id"
                    :label="permission.id"
                    @change="handleChange(scope.row)"
                  >
                    {{ permission.name }}
                  </el-checkbox>
                </div>
              </div>
            </el-checkbox-group>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import API from '@/api'

export default {
  props: {
    roleId: {
      type: [String, Number],
      required: true
    },
    roleName: {
      type: String,
      required: true
    },
    moduleFunctionIds: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      permissionsList: [],
      permissionsIds: [],
      perTypes: [],
      allSelect: false,
      isIndeterminate: false,
      allPermissions: ''
    }
  },
  created () {
    this.gettingPermissionsList()
    this.handleCheckAllSelect()
  },
  computed: {
    // 动态显示权限左侧的标题，施罗德基金显示'稽核中心'为'权限稽核中心'
    moduleType() {
      const title = this.$settings.customerId === 'sldfund' ? '权限稽核中心' : '稽核中心'
      return { query: title, admin: '管理中心', o32_option: '参数稽核中心' }
    }
  },
  methods: {
    gettingPermissionsList () { 
      this.loading = true
      API.roles.permissionsList()
        .then(response => {
          this.loading = false
          this.permissionsList = this.$lodash.orderBy(response.data, ['category', 'order'], ['desc', 'asc'])
          this.allPermissions = this.$lodash.flatten(response.data.map(x => x.permissions.map(y => y.id)))
          this.permissionsIds = this.moduleFunctionIds
          this.permissionType()
          this.initialize()
        })
        .catch(() => {
          this.loading = false
        })
    },
    moduleSpanMethod ({ row, column, rowIndex, columnIndex }) {
      let i = 0
      let span = {}
      if (columnIndex === 0) {
        for (i = 0; i < this.perTypes.length; i++) {
          if (row.category === this.perTypes[i].type && (this.permissionsList[rowIndex - 1] === undefined || this.permissionsList[rowIndex - 1].category !== this.perTypes[i].type)) {
            span = {
              rowspan: Number(this.perTypes[i].count),
              colspan: 1
            }
            break
          } else {
            span = {
              rowspan: 0,
              colspan: 0
            }
          }
        }
        return span
      }
    },
    handleUpdatePermissions () {
      this.loading = true
      API.roles.updateMenuPermissions(this.roleId, { module_function_ids: this.permissionsIds })
        .then(() => {
          this.loading = false
          this.$message.success(`${this.roleName}-角色菜单权限更新成功`)
        })
        .catch(() => {
          this.loading = false
        })
    },
    permissionType () {
      // 找出分类
      const types = this.$lodash.uniqBy(this.permissionsList.map(x => x.category))

      // 创建分类数组[{type: null, count: Number}]
      types.forEach(type => {
        const perType = {}
        perType.count = this.permissionsList.filter(x => x.category === type).length
        perType.type = type
        this.perTypes.push(perType)
      })
    },
    goBack () {
      this.$confirm('未提交的表单数据将不会保存，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          this.$emit('update')
        })
        .catch(() => {
        })
    },
    initialize () {
      // 遍历每个模块，确定allSelect 和 isIndeterminate 的值
      for (const module of this.permissionsList) {
        this.handleChange(module)
      }
    },
    initializeAllSelect () {
      // 初始化最上面的全选
      this.allSelect = this.allPermissions.length === this.permissionsIds.length
      this.isIndeterminate = this.permissionsIds.length > 0 && this.permissionsIds.length < this.allPermissions.length
    },
    handleCheckAll (val) {
      const permissionIds = val.permissions.map(x => x.id)
      val.allSelect = !val.allSelect
      val.isIndeterminate = false
      if (val.allSelect) {
        // true：把该模块的全部功能添加到数组中，并去重
        this.permissionsIds = this.$lodash.uniq(this.$lodash.concat(this.permissionsIds, permissionIds))
        val.allSelect = true
      } else {
        this.permissionsIds = this.$lodash.difference(this.permissionsIds, permissionIds)
        val.allSelect = false
      }
      this.initializeAllSelect()
    },
    handleChange (module) {
      // 模块的 功能的id数组
      const permissionIds = module.permissions.map(x => x.id)
      // 模块的功能id数组 和 选中的功能id的数组 的交集
      const intersection = this.$lodash.intersection(this.permissionsIds, permissionIds)
      // 如果 模块的功能id数组 和 选中的功能id的数组 的交集 等于 模块的功能id数组，那么就是全选
      module.allSelect = intersection.length === permissionIds.length
      // 交集 > 0 && 交集长度小于 功能的id数组的长度
      module.isIndeterminate = intersection.length > 0 && intersection.length < permissionIds.length
      this.initializeAllSelect()
    },
    handleCheckAllSelect (val) {
      this.isIndeterminate = false
      this.permissionsList.map(x => x.isIndeterminate = false)
      if (val) {
        this.permissionsIds = this.allPermissions
        this.permissionsList.map(x => x.allSelect = true)
        this.allSelect = true
      } else {
        this.permissionsIds = []
        this.permissionsList.map(x => x.allSelect = false)
        this.allSelect = false
      }
    },
    allSelectButton () {
    }
  }
}
</script>
<style lang="scss" scoped>
.box-group {
  display: flex;
  flex-wrap: wrap;
}

.box-button {
  width: 16em;
  font-size: 15px;
}

.el-divider--horizontal {
  display: block;
  height: 1px;
  width: 100%;
  margin: 15px 0px;
}
</style>
