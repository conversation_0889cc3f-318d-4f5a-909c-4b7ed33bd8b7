<template>
  <el-container style="height: 100vh;">
    <el-aside width="260px">
      <el-scrollbar style="height: 100%">
        <el-collapse-transition>
          <system-select
            v-if="activeIndex"
            ref="systemSelect"
            :quarterId="currentQuarter.id || 0"
            :comparedQuarterId="comparedQuarterId || 0"
            :default-active="Number(activeIndex)"
            :loadPage="status === 'success'"
            @select="selectItem"
          />
        </el-collapse-transition>
      </el-scrollbar>
    </el-aside>
    <el-main class="mainContainer">
      <div class="container">
        <div class="title-bar">
          <h2
            class="title"
            style="padding-top:15px;"
          >
            {{ title }}账号权限差异情况
          </h2>
          <div class="quarter-select-container">
            <div class="label">
              选择对比时间：
            </div>
            <div class="quarter-select">
              <quarter-select
                ref="quarterSelect"
                v-model="comparedQuarterId"
                :current-quarter-id="currentQuarter.id"
                only-select-less-than-current
                placeholder="请选择待比对时间"
                size="small"
                default-select="next"
                @quarterChange="storeComparedQuarter"
              />
            </div>
            <el-button
              :disabled="!$store.getters.hasPermission('quarter_compared.query')"
              size="small"
              @click="routerBtn"
            >
              权限差异历史查询
            </el-button>
          </div>
        </div>
      </div>
      <hr class="hr">
      <div
        v-if="status === 'not_exist'"
        v-loading="loading"
        class="container in-center"
      >
        <div>未发现这两个时间点的差异索引数据，是否创建？</div>
        <el-button
          :disabled="!$store.getters.hasPermission('quarter_compared.create')"
          type="primary"
          class="create-button"
          @click="dialogVisible = true"
        >
          创建差异数据索引
        </el-button>
      </div>
      <div
        v-else-if="status === 'success'"
        class="container tableContainer"
      >
        <difference-table
          v-if="selectSystem"
          :systemId="selectSystem.id"
          :quarterId="currentQuarter.id || 0"
          :comparedQuarterId="comparedQuarterId || 0"
          :showDisplayStatus="showDisplayStatus"
          :displayStatusList="displayStatusList"
          class="difference-table"
          @refreshStatus="getQuarterDifferenceStatus()"
        />
      </div>
      <div
        v-else
        class="container"
      >
        <div
          v-loading="loading"
          class="diff-processing"
        >
          <div class="desc-line">
            <div class="left">
              <i class="el-icon-loading" />
              <span class="desc-span">
                正在索引 {{ comparedQuarterName }} - {{ currentQuarter.name }} 差异数据...
              </span>
            </div>

            <div>
              <el-button
                type="primary"
                @click="dialogVisible = true"
              >
                重新创建索引
              </el-button>
            </div>
          </div>
          <!-- eslint-disable vue/attribute-hyphenation -->
          <el-progress
            :stroke-width="26"
            :percentage="Number((process.completed * 100 / process.all).toFixed(1))"
            text-inside
            class="progress-bar"
          />
          <!-- eslint-enable vue/attribute-hyphenation -->
        </div>
      </div>
    </el-main>
    <el-dialog
      :visible.sync="dialogVisible"
      title="选择系统"
      width="600px"
    >
      <el-checkbox
        v-model="checkAll"
        :indeterminate="isIndeterminate"
        @change="handleCheckAllChange"
      >
        全选
      </el-checkbox>
      <div style="margin: 15px 0;" />
      <el-checkbox-group
        v-model="businessSystemIds"
        @change="handleCheckedCitiesChange"
      >
        <div class="box">
          <div
            v-for="(system, index) in businessSystems"
            class="item"
          >
            <el-checkbox
              :key="system.id"
              :label="system.id"
              :value="system.id"
            >
              {{ system.name }}
            </el-checkbox>
          </div>
        </div>
      </el-checkbox-group>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="businessSystemIds.length == 0"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
  </el-container>
</template>

<script>
import DifferenceTable from './DifferenceTable.vue'
import QuarterSelect   from '@/components/common/QuarterSelect.vue'
import SystemSelect    from './SystemTreeSelect.vue'
import API             from '@/api'

export default {
  components: {
    DifferenceTable,
    QuarterSelect,
    SystemSelect
  },
  data () {
    return {
      checkAll:          false,
      isIndeterminate:   true,
      dialogVisible:     false,
      businessSystemIds: [],
      activeIndex:       null,
      selectSystem:      null,
      comparedQuarterId: null,
      comparedQuarter:   null,
      businessSystems:   [],
      status:            'success',
      displayStatusList: [],
      showDisplayStatus: false,
      process:           {
        completed: 0,
        all:       1
      },
      timer:             [],
      loading:           false
    }
  },
  computed: {
    currentQuarter () {
      return this.$store.state.current_quarter
    },
    title () {
      return `${this.comparedQuarterName} - ${this.currentQuarter.name} ${this.selectSystemName}`
    },
    comparedQuarterName () {
      if (this.comparedQuarter) {
        return this.comparedQuarter.name
      } else {
        return ''
      }
    },
    // 避免在 selectSystem 未获取到数据时，导致 name 出错的问题
    selectSystemName () {
      if (this.selectSystem) return this.selectSystem.name
      return ''
    }
  },
  watch:    {
    activeIndex () {
      this.selectSystem = this.businessSystems.find(x => x.id === this.activeIndex)
      this.getQuarterDifferenceStatus()
      this.getSystemSettings()
    },
    status () {
      switch (this.status) {
        case 'success':
          this.clearStatusInterval()
          break
        case 'not_exist':
          break
        default:
          this.getStatusInterval()
      }
    },
    // 修改为 quarter 一旦变化，会自动刷新 comparedQuarter
    currentQuarter () {
      this.clearStatusInterval()
      this.$nextTick(() => {
        this.$refs.quarterSelect.selectDefaultQuarter()
      })
    },
    comparedQuarterId () {
      this.clearStatusInterval()
      this.$nextTick(() => {
        this.getSystems()
        // this.getQuarterDifferenceStatus()
      })
    }
  },
  created () {
    this.getSystems()
  },
  beforeDestroy () {
    this.clearStatusInterval()
  },
  methods: {
    routerBtn () {
      this.$router.push('/quarter_compared_history')
    },
    handleCheckAllChange (val) {
      this.businessSystemIds = val ? this.businessSystems.map((item) => item.id) : []
      this.isIndeterminate   = false
    },
    handleCheckedCitiesChange (value) {
      const checkedCount   = value.length
      this.checkAll        = checkedCount === this.businessSystems.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.businessSystems.length
    },
    selectItem (key, keyPath) {
      let [systemId, systemName] = key.split('-')
      this.activeIndex = Number(systemId)
    },
    getSystems () {
      this.$axios.get('/api/systems')
        .then(response => {
          this.businessSystems = response.data

          if (this.businessSystems[0]) {
            this.findSelectSystem()
            this.getQuarterDifferenceStatus()
          }
        })
        .catch(() => {})
    },
    findSelectSystem(){
      const bsId = this.$route.query.business_system_id || this.$store.state.default_system_id
      let storeSystem = this.businessSystems.find(system => system.id === Number(bsId))
      this.selectSystem = storeSystem || this.businessSystems[0]
      this.$store.commit('storeCompareSystem', this.selectSystem.id)
      this.activeIndex  = this.selectSystem.id
    },
    getQuarterDifferenceStatus () {
      if (!this.comparedQuarterId) return

      if (this.currentQuarter.id === this.comparedQuarterId) {
        this.$message.warning('比较相同的时间，结果将为空')
        return
      }

      const bsId = this.$route.query.business_system_id

      API.quarterCompared.comparedStatus({
        quarterId:         this.currentQuarter.id,
        comparedQuarterId: this.comparedQuarterId,
        systemId:          bsId || this.selectSystem.id
      })
        .then(response => {
          this.status = response.data.status

          if (this.status === 'process') {
            this.process.completed = response.data.process_completed
            this.process.all       = response.data.process_all
          }
        })
        .catch(() => {})
    },
    handleConfirm () {
      this.dialogVisible = false
      this.createDiffData()
      this.businessSystemIds = []
    },
    createDiffData () {
      if (this.businessSystemIds.length > 0) {
        if (!this.businessSystemIds.includes(this.activeIndex)) {
          this.activeIndex = this.businessSystemIds[0]
          this.$store.state.default_system_id = this.activeIndex
        }
        this.loading = true
        this.clearStatusInterval()
        API.quarterCompared.createCompare({
          quarterId:         this.currentQuarter.id,
          comparedQuarterId: this.comparedQuarterId,
          businessSystemIds: this.businessSystemIds
        })
          .then(response => {
            this.loading = false
            if (response.data.success) {
              this.getStatusInterval()
            }
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    getStatusInterval () {
      this.getQuarterDifferenceStatus()
      const timer = setInterval(this.getQuarterDifferenceStatus, 3000)
      // 可能某些触发条件下，有可能会启动多个计时器，timer 会被覆盖，仅清除一个无法关闭所有，所以通过数组捕捉所有计时器
      this.timer.push(timer)
    },
    clearStatusInterval () {
      this.timer.forEach(x => clearTimeout(x))
    },
    storeComparedQuarter (payload) {
      this.comparedQuarter = payload
    },
    getSystemSettings () {
      if (this.activeIndex === 0) {
        return null
      }
      this.$axios.get(`/api/systems/${this.activeIndex}/settings`)
        .then(response => {
          this.showDisplayStatus      = response.data.settings.display_status && response.data.settings.display_status.enable
          const object                = response.data.settings.display_status && response.data.settings.display_status.enum
          this.displayStatusList      = this.obejctToArray(object)
        })
        .catch(() => {})
    },
    obejctToArray (object) {
      let array = []
      for(var i in object) {
        array.push({
          key: String(i),
          value: object[i]
        })
      }
      return array
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/components/variables';

.el-aside {
  border-right: 1px solid #EBEEF5;
  height:       calc(100vh - #{$header-height});
}

.hr {
  margin-top: 0;
}

.tableContainer {
  flex: 1;
  height: 100vh;
}

.mainContainer {
  height: calc(100vh - #{$header-height});
  display: flex;
  flex-direction: column;
}

.container {
  margin-left: 20px;
  color:       $font-color;

  .title-bar {
    @include vertical_center_between;
  }

  .progress-bar {
    margin-top: 20px;
  }

  .desc-line {
    @include vertical_center_between;

    .left {
      line-height: 40px;
    }

    .desc-span {
      margin-left: 5px;
    }
  }

  .difference-table {
    margin-top: 20px;
  }

  .quarter-select-container {
    @include vertical_center_right;
    height: 40px;

    .label {
      color: $font-color;
    }

    .quarter-select {
      width: 180px;
      margin-right: 20px;
    }
  }
}

.in-center {
  text-align: center;
  margin-top: 100px;
}

.create-button {
  margin-top: 20px;
}

.box {
  display:         flex;
  flex-wrap:       wrap;
  justify-content: flex-start;
}

.item {
  width:            33%;
  height:           50px;
  margin-bottom:    10px;
}

.placeholder {
  width:  30%;
  height: 0px;
}
</style>
