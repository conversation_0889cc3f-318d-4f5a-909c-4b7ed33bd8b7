<template>
  <div class="container">
    <h2>功能账号管理</h2>
    <hr>

    <el-container>
      <el-aside width="180px">
        <el-scrollbar>
          <el-collapse-transition>
            <sidebar-ledger-systems
              ref="systemSelect"
              v-loading="loading"
              style="height: 920px;overflow-y:auto;"
              :default-active="Number(activeIndex)"
              :systems="systems"
              @select="selectItem"
            />
          </el-collapse-transition>
        </el-scrollbar>
      </el-aside>
      <el-main>
        <public-account-index
          v-if="activeIndex"
          :key="activeIndex"
          :system-id="Number(activeIndex)"
          :systems="systems"
        />
      </el-main>
    </el-container>
  </div>
</template>
<script>
import PublicAccountIndex   from './PublicAccountIndex'
import SidebarLedgerSystems from '@/components/SidebarLedgerSystems'
import API from '@/api'

export default {
  components: {
    SidebarLedgerSystems,
    PublicAccountIndex
  },
  data () {
    return {
      activeIndex: null,
      systems:     [],
      loading:     false
    }
  },
  created () {
    this.getSystems()
  },
  methods: {
    getSystems () {
      this.loading = true
      API.systems.maintainList()
        .then(response => {
          this.loading = false
          this.systems = response.data
          this.activeIndex = String(this.systems[0].id)
        })
        .catch(() => {
          this.loading = false
        })
    },
    selectItem (systemId) {
      this.activeIndex = systemId
    }
  }
}
</script>

<style lang="scss" scoped>
.el-aside {
  border-right: 1px solid #EBEEF5;
}
.el-main{
  padding: 20px 0 0 20px;
}

.container {
  padding:          2em;
  background-color: white;
}
</style>
