import axios from '@/settings/axios'

export const employeeTotal = (quarterId) => {
  return axios.get(`/api/data_statistics/employee_total?quarter_id=${quarterId}`)
}
export const employeeNew   = (quarterId) => {
  return axios.get(`/api/data_statistics/employee_new?quarter_id=${quarterId}`)
}

export const employeeReduce = (quarterId) => {
  return axios.get(`/api/data_statistics/employee_reduce?quarter_id=${quarterId}`)
}

export const accountTotal = (params) => {
  return axios.get('/api/data_statistics/account_total', {
    params: {
      quarter_id: params.quarterId, system_id: params.systemId
    }
  })
}
export const accountNew   = (params) => {
  return axios.get('/api/data_statistics/account_new', {
    params: {
      quarter_id: params.quarterId, system_id: params.systemId
    }
  })
}

export const accountReduce = (params) => {
  return axios.get('/api/data_statistics/account_reduce', {
    params: {
      quarter_id: params.quarterId, system_id: params.systemId
    }
  })
}

