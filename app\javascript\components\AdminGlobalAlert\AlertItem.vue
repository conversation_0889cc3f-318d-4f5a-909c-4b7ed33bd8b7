<template>
  <div>
    <div class="category-item-title">
      <span class="title-item"> {{ alert.title }} </span>
    </div>
    <div>
      <el-progress
        :percentage="percent"
        :text-inside="true"
        :color='progressColor'
      />
    </div>
    <div class="numberSty">
      <span class="numberSpan" :class="finishColor">
        {{ `${alert.processed_count}/${alert.count} ` }}
      </span>
      <span class="numberSpan1" :class="finishColor">
        {{ `${ percent }%` }}
      </span>
    </div>
  </div>
</template>

<script>
import AlertItemMixin from '@/components/AdminGlobalAlert/mixins/AlertItemMixin'
import styles         from '@/components/_variables.scss'

export default {
  mixins: [AlertItemMixin],
  props: {
    alert: {
      type: Object,
      required: true
    }
  },
  methods: {

  },
  data() {
    return {
      styles: styles
    }
  },
  computed: {
    // 完成百分比
    percent () {
      return this.alertPercent(this.alert)
    },
    // 进度颜色
    progressColor () {
      let unFinishColor = styles.progressUnfinishColor
      if (this.$settings.customerId === 'sldfund') {
        unFinishColor = styles.progressSldfundUnfinishColor
      }
      return this.percent === 100 ? styles.progressfinishColor : unFinishColor
    },
    // 未完成颜色
    unFinishColor () {
      const customerId = this.$settings.customerId
      switch (customerId) {
        case 'sldfund':
          return 'sldfund-unfinish-color'
        default :
          return 'unfinish-color'
      }
    },
    finishColor () {
      return this.percent === 100 ? 'finish-color' : this.unFinishColor
    }
  }
}
</script>

<style lang="scss" scoped>
.category-item-title {
  // flex + inline-size 启用容器查询
  display: flex;
  container-type: inline-size;

  overflow: hidden;
  white-space: nowrap;

  &:hover .title-item {
    animation: move 3s linear infinite both alternate;
    //-webkit-animation-play-state: paused;
  }

  @keyframes move {
    to {
      // 需要插值，否则 min 不同单位无法比较 https://github.com/sass/node-sass/issues/2963
      // 0px 转换后会变成 0， 在 css 中报错，因此改成 1px - 1px
      transform: translateX(#{'min(100cqw - 100%, 1px - 1px)'});
    }
  }
}

.numberSpan1,
.numberSty2 {
  font-size: 18px;
  font-weight: bold;
}

.progressSty {
  width: 80%;
  margin: 0 auto;
  position: relative;
  top: 40%;
}

.numberSty2 {
  left: 10%;
  position: absolute;
}

/deep/ .el-progress-bar__innerText {
  display: none;
}

/deep/ .el-progress-bar__outer {
  height: 8px !important;
  background: #ebecef;
  border-radius: inherit;
}

/deep/ .el-progress-bar__inner {
  background: #0e6eff;
  border-radius: inherit;
}

.numberSty {
  display: flex;
  justify-content: space-between;
}

.progressSty {
  width: 100%;
  margin: 0 auto;
}

.numberSpan {
  font-size: 18px;
  font-family: PangMenZhengDao;
  font-weight: bold;
}

.finish-color {
  color: #0e6eff;
}

.unfinish-color {
  color: #12D4FF;
}

.sldfund-unfinish-color {
  color: #e13131;
}
</style>
