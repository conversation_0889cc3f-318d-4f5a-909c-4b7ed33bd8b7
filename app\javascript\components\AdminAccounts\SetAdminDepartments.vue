<template>
  <div>
    <el-form @submit.native.prevent>
      <el-form-item>
        <el-input
          v-model="filterText"
          placeholder="输入部门名称进行过滤"
          clearable
          size="small"
          prefix-icon="el-icon-search"
          style="width: 300px;"
        />
        <el-checkbox
          v-model="isSelected"
          style="padding-left: 24px;"
          @change="filterDepartmentsTree"
        >
          已选中
        </el-checkbox>
      </el-form-item>
      <el-divider class="page-divider" />
      <el-form-item>
        <el-row>
          <el-col :span="12">
            <el-checkbox
              v-model="allDept"
              style="padding-left: 24px;"
              :indeterminate="isIndeterminateDept"
              :disabled="allDepartmentsQuery"
              @change="handleCheckAllDept"
            >
              全选
            </el-checkbox>
          </el-col>
          <el-col :span="12">
            <el-checkbox
              :disabled="!isAllDepartmentsQuery"
              v-model="allDepartmentsQuery"
            >
              所有部门权限
              <el-tooltip class="item" effect="dark" placement="right">
                <span><i class="el-icon-question"></i></span>
                <template slot="content">
                  <div>
                    <div>1. 自动添加新增部门</div>
                    <div>2. 包含未绑定员工的账号</div>
                  </div>
                </template>
              </el-tooltip>
            </el-checkbox>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>

    <el-tree
      v-if="!allDepartmentsQuery"
      ref="tree"
      :data="departmentsTree"
      show-checkbox
      node-key="value"
      :filter-node-method="filterNode"
      :default-checked-keys="adminDepartmentIds"
      :props="defaultProps"
      style="margin-top: 20px;"
      @check="handleCheckChange"
    />
  </div>
</template>
<script>
import API from '@/api'
import { departmentTree } from '@/utils/tools'

export default {
  props: {
    admin: {
      type: Object,
      required: true
    },
    flag: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      // 是否允许显示部门全选
      isAllDepartmentsQuery: false,
      departmentsTree: [],
      departments: [],
      departmentLength: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        disabled: 'disabled'
      },
      filterText: '',
      adminDepartmentIds: [],
      allDepartmentsQuery: false,
      allDept: false,
      isIndeterminateDept: false,
      isSelected: false
    }
  },
  computed: {
    quarter () {
      return this.$store.state.current_quarter
    }
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val)
    },
    admin: {
      handler (admin) {
        this.adminDepartmentIds = []
        this.allDepartmentsQuery = false
        this.gettingAdminDepartments()
      }
    },
    // 切换管理员dialog时，选中状态要重置
    'admin.id'(newId, oldId) {
      this.isSelected = false
    },
    allDepartmentsQuery (val) {
      this.initializeDepartment()
      // this.departmentsTree.map(x => x.disabled = val)
    },
    flag: {
      deep: true,
      immediate: true,
      handler (newVal) {
        const params = {
          all_departments_query: this.allDepartmentsQuery,
          department_ids: this.adminDepartmentIds
        }
        this.$emit('area', params)
      }
    }
  },
  created () {
    this.gettingDepartments()
  },
  methods: {
    gettingDepartments () {
      this.loading = true
      this.$axios
        .get('/api/departments', { params: {without_permission: true, is_admin_permission: true}})
        .then(response => {
          this.departments = response.data
          this.departmentLength = response.data.length
          // tree结构部门
          this.initializeDepartment()
          this.gettingAdminDepartments()
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    initializeDepartment () {
      this.departments.map(x => (x.disabled = this.allDepartmentsQuery || !x.is_query))
      this.departmentsTree = departmentTree(this.departments)
      if (this.isSelected) {
        const checkedNodes = this.$refs.tree.getCheckedNodes();
        // 过滤选中的节点
        this.departmentsTree = this.filterSelectedNodes(this.departmentsTree, checkedNodes);
      }
    },
    filterSelectedNodes(tree, selectedNodes) {
      const selectedIds = selectedNodes.map(node => node.value)
      // 递归过滤函数
      const filterTree = (nodes) => {
        return nodes
          .map(node => {
            if (selectedIds.includes(node.value)) {
              return { ...node }
            }
            if (node.children) {
              const filteredChildren = filterTree(node.children)
              if (filteredChildren.length) {
                return { ...node, children: filteredChildren }
              }
            }
            return null
          })
          .filter(node => node !== null);
      }
      return filterTree(tree);
    },
    filterDepartmentsTree () {
      this.initializeDepartment()
    },
    gettingAdminDepartments () {
      this.loading = false
      API.adminPermissions
        .getAdminDepartments(this.admin.id)
        .then(response => {
          this.loading = false
          this.initialize(response.data)
        })
        .catch(() => {
          this.loading = false
        })
    },
    filterNode (value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    handleCheckChange (data, check) {
      this.adminDepartmentIds = check.checkedKeys
      this.setAllSelect()
    },
    // 系统全选，disabled禁止选择
    handleCheckAllDept (val) {
      this.isIndeterminateDept = false
      if (val) {
        const departmentIds = this.departments.filter(x => !x.disabled).map(x => x.id)
        this.$refs.tree.setCheckedKeys(departmentIds)
        this.adminDepartmentIds = departmentIds
        this.allDept = true
      } else {
        this.$refs.tree.setCheckedKeys([])
        this.adminDepartmentIds = []
        this.allDept = false
      }
    },
    initialize (data) {
      this.isAllDepartmentsQuery = data.is_all_departments_query
      this.allDepartmentsQuery = data.all_departments_query
      this.$refs.tree.setCheckedKeys(data.departments.map(x => x.id))
      this.adminDepartmentIds = data.departments.map(x => x.id)
      this.setAllSelect()
      // 初始化 this.isIndeterminateDept
      // 0 < 选中的长度 < this.departmentLength
      // this.isIndeterminateDept = this.adminDeptLength > 0 && this.adminDeptLength < this.departmentLength
    },
    setAllSelect () {
      this.adminDeptLength = this.adminDepartmentIds.length
      this.allDept = this.adminDeptLength === this.departmentLength
      this.isIndeterminateDept =
        this.adminDeptLength > 0 && this.adminDeptLength < this.departmentLength
    }
  }
}
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  display: block;
  height: 1px;
  width: 100%;
  margin: 10px 0px;
}
</style>
