<template>
  <span>
    <el-button
      class="new-user"
      size="small"
      :disabled="!$store.getters.hasPermission('admin_account_manager.admin')"
      @click="dialogVisible = true"
    >
      创建系统用户
    </el-button>
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="cancel"
      :close-on-click-modal="false"
      title="创建系统用户"
      width="600px"
      center
      append-to-body
    >
      <el-form
        ref="new_user"
        :model="new_user"
        :rules="rules"
        label-width="100px"
        status-icon
        class="new-form"
      >
        <el-form-item
          label="姓名"
          prop="name"
        >
          <el-input
            v-model="new_user.name"
            auto-complete="off"
          />
        </el-form-item>
        <el-form-item
          label="邮箱"
          prop="email"
        >
          <el-input
            v-model="new_user.email"
            auto-complete="off"
          />
        </el-form-item>
        <el-form-item
          label="员工编号"
          prop="code"
        >
          <el-input
            v-model="new_user.code"
          />
        </el-form-item>
        <el-form-item
          label="手机号"
          prop="mobile"
        >
          <el-input
            v-model="new_user.mobile"
            auto-complete="off"
          />
        </el-form-item>
        <el-form-item label="绑定员工" prop="user_id">
          <div class="name-flex">
            <el-cascader
              v-model="new_user.user_id"
              style="width: 100%"
              :options="userTree"
              :props="cascaderProps"
              clearable
              filterable
              auto-complete='off'
            />
          </div>
        </el-form-item>
        <el-form-item
          label="密码"
          prop="password"
        >
          <el-input
            v-model="new_user.password"
            show-password
            auto-complete="new-password"
          />
        </el-form-item>

        <el-form-item
          label="确认密码"
          prop="password_confirmation"
        >
          <el-input
            v-model="new_user.password_confirmation"
            show-password
            auto-complete="off"
          />
        </el-form-item>
        <el-form-item>
          <el-checkbox
            v-model="new_user.allow_notification"
          >
            接收邮件通知
          </el-checkbox>
          <el-checkbox
            v-if="isSmsNotificationEnable"
            v-model="new_user.allow_sms_notification"
          >
            接收短信通知
          </el-checkbox>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="userCreate"
        >
          确 定
        </el-button>
      </span>

    </el-dialog>
  </span>
</template>

<script>
import API from '@/api'
import { validatePhone } from '@/utils/form_validates'
import RegexpTool from "../../utils/regexp_tool";

export default {
  data: function () {
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.new_user.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    const rules         = {
      name:                  [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      email:                 [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      code:                  [
        {required: true, message: '请输入员工编号', trigger: 'blur' }
      ],
      mobile:                [
        { validator: validatePhone, trigger: 'blur' }
      ],
      password:              [
        { required: true, validator: this.generateValidatePass(), trigger: 'blur' }
      ],
      password_confirmation: [
        { required: true, validator: validatePass2, trigger: 'blur' }
      ]
    }
    return {
      dialogVisible: false,
      new_user: {
        email: '',
        mobile: '',
        name: '',
        code: '',
        password: '',
        password_confirmation: '',
        allow_notification: true,
        allow_sms_notification: true,
        user_id: null
      },
      rules: rules,
      security_rule: {},
      cascaderProps: {
        checkStrictly: false, // 支持父子节点互相关联
        emitPath: false, // 返回选中的值为叶子节点的值
      }
    }
  },
  created() {
    this.initSecurityRule()
    this.initUserIdRule()
  },
  computed: {
    isSmsNotificationEnable () {
      return this.$settings.showSmsNotification
    },
    userTree () {
      return this.$store.state.user_tree
    },
    // 如果开启强制绑定员工 并且 没有开启'无限制授权'
    isBindUser () {
      return this.$settings.adminBindUser.enable && !this.$store.getters.hasPermission('admin_account_manager.all_permission')
    }
  },
  methods: {
    // 该方法因客户需求变化，先临时禁用
    cancel (done) {
      const inEdit = this.new_user.email.length + this.new_user.mobile.length + this.new_user.name.length + this.new_user.code.length
      if (inEdit === 0) return done()

      this.$confirm('正在创建账号，确认不创建直接关闭吗？')
        .then(_ => {
          this.dialogVisible = false
          this.$refs.new_user.resetFields()
          done()
        })
        .catch(_ => {})
    },
    userCreate () {
      this.$refs.new_user.validate((valid) => {
        if (valid) {
          API.adminAccounts.create(this.new_user)
            .then(response => {
              this.dialogVisible = false
              this.$refs.new_user.resetFields()
              this.$emit('update')
            })
            .catch(_ => {})
        }
      })
    },
    initSecurityRule () {
      this.$axios.get(`/api/aas_security`)
        .then(res => {
          this.security_rule = res.data
          this.rules.password = [
            { required: true, validator: this.generateValidatePass(), trigger: 'blur' }
          ]
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 强制绑定员工
    initUserIdRule () {
      if (this.isBindUser) {
        this.rules.user_id = [
          { required: true, message: '请绑定员工', trigger: 'change' }
        ]
      }
    },
    generateValidatePass () {
      const validatePassFunction = (rule, value, callback) => {
        if(!this.security_rule.password_complexity) return
        let password_min_length = this.security_rule.password_min_length
        let password_max_length = this.security_rule.password_max_length
        let upper = this.security_rule.password_complexity.upper
        let lower = this.security_rule.password_complexity.lower
        let digit = this.security_rule.password_complexity.digit
        let symbol = this.security_rule.password_complexity.symbol
        switch(true) {
          case value === '':
            callback(new Error('请输入密码'))
            break
          case (password_min_length && value.length < 8):
            callback(new Error(`密码长度最短为 ${password_min_length} 位`))
            break
          case (password_max_length && value.length > 128):
            callback(new Error(`密码长度最长为 ${password_max_length} 位`))
            break
          case (upper && !RegexpTool.matchUpper(value, upper)):
            callback(new Error(`必须包含至少${upper}个大写字母`))
            break
          case (lower && !RegexpTool.matchLower(value, lower)):
            callback(new Error(`必须包含至少${lower}个小写字母`))
            break
          case (digit && !RegexpTool.matchDigit(value, digit)):
            callback(new Error(`必须包含至少${digit}个数字`))
            break
          case (symbol && !RegexpTool.matchPunct(value, symbol)):
            callback(new Error(`必须包含至少${symbol}个特殊符号（!"#$%&\'()*+,-./:;<=>?@[\\]^_‘{|}~)`))
            break
          default:
            if (this.new_user.password_confirmation !== '') {
              this.$refs.new_user.validateField('password_confirmation')
            }
            callback()
            break
        }
      }
      return validatePassFunction
    }
  }
}

</script>

<style lang='scss' scoped>
  .new-user {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .new-form {
    margin-right: 3em;
  }

  .new-input {
    width: 100%;
  }
</style>
