# frozen_string_literal: true

# ApplicationController
class ApplicationController < ActionController::Base
  include DeviseTokenAuth::Concerns::SetUserByToken
  include Pundit
  include AuditLogService
  protect_from_forgery with: :null_session

  # @!method current_admin
  # @return [Admin]
  # 当前管理员用户

  # @!method authenticate_admin!
  # 管理员认证登录

  # @!method audit_log!
  # 审计日志记录

  # @!method before_action
  # 环绕方法（防止 ide 报错）

  rescue_from AppAuthorizationException, with: :admin_not_authorized
  rescue_from Pundit::NotAuthorizedError, with: :pundit_not_authorized
  rescue_from ActiveRecord::RecordNotFound, with: :handle_ar_404
  rescue_from NullQuartersError, with: :handle_null_quarters

  protected

  def aes_decrypt_data(encrypted_data)
    decoded_data = Base64.decode64(encrypted_data)

    cipher = OpenSSL::Cipher.new('AES-128-ECB')
    cipher.decrypt
    cipher.key = Setting.secret_key || 'RhZcP3YPAC1nCbHB'

    begin
      decrypted_data = cipher.update(decoded_data) + cipher.final
      decrypted_data
    rescue OpenSSL::Cipher::CipherError => e
      Rails.logger.error "Decryption error: #{e.message}"
      nil
    end
  end

  def page
    params[:page]&.to_i || 1
  end

  def per_page
    params[:per_page]&.to_i || 15
  end

  def begin_time
    params[:begin_time]
  end

  def end_time
    params[:end_time] || Time.now.to_date
  end

  def current_admin_query_bs
    current_admin.business_systems_in_query
  end

  def current_admin_maintain_bs
    current_admin.business_systems_in_maintain
  end

  def current_admin_query_departments
    current_admin.departments_in_query
  end

  def json_respond(api_data)
    render json: api_data.to_json
  end

  def json_custom_respond(status, api_data)
    render status: status, json: api_data.to_json
  end

  def json_respond_no_content
    head :no_content
  end

  def send_file_compatible_with_msie(file_path, custom_error_i18n = nil)
    error_message = custom_error_i18n ? I18n.t(custom_error_i18n) : 'File not found.'

    return json_custom_respond(404, error_message: error_message) unless file_path

    file_name = File.basename file_path
    file_name = URI.encode_www_form_component(file_name.gsub(/\s+/, ''))
    send_file file_path, type: 'application/octet-stream;charset=utf-8', filename: file_name, x_sendfile: true
  end

  def admin_audit_log_create(args)
    op_category = args[:operation_category]
    operation   = args[:operation]
    comment     = args[:comment]
    op_module   = args[:operation_module] || '管理中心'
    admin_id    = args[:admin_id] || current_admin.id
    targetable  = args[:targetable]

    AdminAuditLog.create(
      admin_id:           admin_id,
      operation:          operation,
      operation_time:     Time.now,
      operation_module:   op_module,
      operation_category: op_category,
      comment:            comment,
      ip_address:         request.ip,
      event_id:           request.request_id,
      host:               request.host,
      agent:              request.user_agent,
      targetable:         targetable
    )
  end

  def admin_not_authorized
    json_custom_respond(403, error_message: 'You have no permission!')
  end

  def set_business_system
    @business_system = BusinessSystem.find(params[:system_id])
  end

  # 需要 set_business_system 首先执行
  def set_system_account
    @account = @business_system.account_class.find(params[:account_id])
  end

  def set_quarter
    @quarter = Quarter.find(params[:quarter_id])
  end

  def set_o32_option_quarter
    @o32_option_quarter = O32OptionQuarter.find(params[:o32_option_quarter_id])
  end

  def pundit_user
    current_admin
  end

  def handle_ar_404(error)
    json_custom_respond(404, error_message: error.message)
  end

  def handle_null_quarters
    json_custom_respond(400, error_message: '在时间范围内未查询到权限差异数据')
  end

  # 前端代码直接将403(返回信息是nil)翻译成：您没有权限操作此功能
  # 传参数的前端直接提示messag信息
  def pundit_not_authorized(exception)
    message = if exception.message.include?('not allowed')
                I18n.t('pundit.no_operation_permission')
              else
                I18n.t('pundit.no_operation_permission_for_parameter', message: exception.message)
              end

    json_custom_respond 403, error_message: message
  end

  # 依赖任务校验
  # @param task_key[Symbol] after_import 中注册的任务
  def check_dependent_task_status(task_key)
    msg = I18n.t('errors.task.not_success', import_name: I18n.t('module_name.quarters'))
    if AfterImport::REGISTERED_SYSTEM_TASKS.include? task_key.to_sym
      return true if QuarterSystemTaskStatus.all_success?(@quarter, task_key)

      task_statuses = QuarterSystemTaskStatus.where(quarter_id: @quarter.id, task: task_key).where.not(status: 2)
      system_names = task_statuses.map { |x| x.business_system&.name }.compact
      dependent_task = @quarter.find_task(task_key, nil)
      error_message = system_names.join('、') + '的 ' + dependent_task.name + msg
    else
      dependent_task = @quarter.find_task(task_key)
      return true if dependent_task.success?

      error_message = dependent_task.name + msg
    end

    json_custom_respond 456, error_message: error_message
  rescue NoMethodError => _e
    raise AfterImport::TaskNotFound, "not found dependent task #{task_key}"
  end

  def should_login!
  #   license_expire_date = AccountAuditSystemInformation.license_expire_date.to_date
  #   if license_expire_date <= Date.today
  #     json_custom_respond(401, success: false, error_message: I18n.t('license.license_overdue'))
  #   end
  # rescue ArgumentError => e
  #   json_custom_respond(401, success: false, error_message: I18n.t('license.license_invalid'))
  end
end
