# frozen_string_literal: true

# 外部系统账号日志
class External::AccountAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '外部系统账号管理'
    @operation_category = '外部系统账号管理'
    super
  end

  def update
    @operation = '更新外部系统账号'
    @comment = generate_update_log(params)
    create_audit_log if @comment.present?
  end

  def create
    @operation = '创建外部系统账号'
    @comment = generate_create_log(params)
    create_audit_log if @comment.present?
  end

  def destroy
    @operation = '删除外部系统账号'
    settings = params.external_business_system.default_account_settings
    code_display_name = settings.find { |o| o['name'] == 'code' }&.[]('display_name')
    name_display_name = settings.find { |o| o['name'] == 'name' }&.[]('display_name')
    @comment   = "删除外部系统「#{params.external_business_system&.name}」的账号，ID：#{params.id}，#{name_display_name}：#{params.name}，#{code_display_name}：#{params.code}"
    create_audit_log
  end

  def destroy_batch
    return if params.blank?

    param = params.first
    @operation = '删除外部系统账号'
    settings = param.external_business_system.default_account_settings
    code_display_name = settings.find { |o| o['name'] == 'code' }&.[]('display_name')
    name_display_name = settings.find { |o| o['name'] == 'name' }&.[]('display_name')
    str = params.map { |x| "ID：#{x.id}，#{name_display_name}：#{x.name}，#{code_display_name}：#{x.code}" }.join('; ')
    @comment   = "批量删除外部系统「#{param.external_business_system&.name}」的账号，#{str}"
    create_audit_log
  end

  def import_success
    @operation = '导入外部系统账号'
    bs         = params[0]
    file       = params[1]
    @comment   = "外部系统「#{bs&.name}」导入账号「#{file.original_filename}」成功"
    create_audit_log
  end

  def import_failed
    @operation = '导入外部系统账号'
    bs         = params[0]
    file       = params[1]
    @comment   = "外部系统「#{bs&.name}」导入账号「#{file.original_filename}」失败"
    create_audit_log
  end

  def export_success
    @operation = '导出外部系统账户模板'
    filename   = params
    @comment   = "导出账号模板「#{filename}」成功"
    create_audit_log
  end

  def export_account_and_permissions_success
    @operation = '导出外部系统账户与权限'
    filename   = params
    @comment   = "导出账号授权权限表「#{filename}」成功"
    create_audit_log
  end

  def export_failed
    @operation = '导出外部系统账户模板'
    filename   = params
    @comment   = "导出账号模板「#{filename}」失败"
    create_audit_log
  end

  def update_roles
    @operation = '外部系统账号更新角色'
    bs_name = params[0]
    account_name = params[1]
    add_role_names = params[2]
    remove_role_names = params[3]
    @comment = "系统名称：#{bs_name}，账号名称：#{account_name}，"
    @comment += "增加角色: #{add_role_names.join('，')}；" if add_role_names.present?
    @comment += "移除角色：#{remove_role_names.join('，')}；" if remove_role_names.present?
    create_audit_log
  end

  def update_permissions
    @operation = '外部系统账号更新权限'
    bs_name = params[0]
    account_name = params[1]
    add_permission_names = params[2]
    remove_permission_names = params[3]
    @comment = "系统名称：#{bs_name}，账号名称：#{account_name}，"
    @comment += "增加权限: #{add_permission_names.join('，')}；" if add_permission_names.present?
    @comment += "移除权限：#{remove_permission_names.join('，')}；" if remove_permission_names.present?
    create_audit_log
  end

  protected

  # 生成创建日志
  def generate_create_log(params)
    strs = []
    bs = params.external_business_system
    output = params.output
    account_settings = bs.account_settings.select { |o| o['is_enable'] }
    code_display_name = bs.account_field_name('code')
    name_display_name = bs.account_field_name('name')
    status_display_name = bs.account_field_name('status')
    user_name_display_name = bs.account_field_name('user_name')
    role_names = output[:external_roles]&.map { |o| o[:name] }
    strs << "#{code_display_name}：#{params.code}" if output[:code].present?
    strs << "#{name_display_name}：#{params.name}" if output[:name].present?
    strs << "#{status_display_name}：#{output[:status_text]}" if output[:status].present?
    strs << "#{user_name_display_name}：#{output[:user_name]}" if output[:user_name].present?
    strs << "角色：#{role_names.join('、')}" if role_names.present?
    account_settings.each do |obj|
      field = obj['name']
      display_name = bs.account_field_name(field)
      value = params.send(field)
      strs << "#{display_name}：#{value}" if value.present?
    end
    return if strs.blank?

    "创建外部系统「#{bs&.name}」的账号成功，ID：#{params.id}，#{strs.join('，')}"
  end

  # 生成更新日志
  def generate_update_log(params)
    strs = []
    data = params[1].to_a - params[0].to_a
    bs = params[2]
    after_role_names = params[1][:external_roles]&.map { |o| o[:name] } || []
    before_role_names = params[0][:external_roles]&.map { |o| o[:name] } || []
    add_role_names = after_role_names - before_role_names
    del_role_names = before_role_names - after_role_names
    data.each do |o|
      field_name = bs.account_field_name(o[0].to_s)
      strs << "#{field_name}: 「#{o[1]}」" if field_name.present?
    end
    strs << "角色增加: 「#{add_role_names.join('、')}」" if add_role_names.present?
    strs << "角色减少: 「#{del_role_names.join('、')}」" if del_role_names.present?
    return if strs.blank?

    "更新外部系统账号 ID：「#{params[0][:id]}」，#{strs.join('，')}"
  end
end
