<template>
  <div class="container">
    <div class="tool-bar">
      <el-form
        ref="localForm"
        v-loading="loading"
        :model="params"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item
          prop="start_at"
          label="初次发送日期"
        >
          <el-date-picker
            v-model="params.start_at"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            size="small"
          />
        </el-form-item>

        <el-form-item
          v-for="(notice, index) in params.notice"
          :key="index"
          :label="getLabel(index)"
        >
          <el-select
            v-model="notice.bs_id"
            :loading="loadingSystems"
            filterable
            clearable
            placeholder="请选择系统(必选)"
            style="width: 238px"
            size="small"
          >
            <el-option
              v-for="item in allBusinessSystems"
              :key="item.id"
              :value="item.id"
              :label="item.name"
              :disabled="item.is_disable"
            />
          </el-select>

          <department-select
            v-model="notice.department_ids"
            placeholder="请选择部门"
            :department-id="[]"
            size="small"
            :checkStrictly="true"
            multiple
            collapseTags
            filterable
            clearable
            selectStyle="width:238px;margin-right:0px;"
          />

          <el-input
            v-model="notice.frequency"
            size="small"
            placeholder="请输入发送频率"
            style="width: 130px"
          />

          <el-button
            icon="el-icon-minus"
            circle
            size="small"
            style="margin-left: 15px;"
            @click="deleteNotice(index)"
          />
        </el-form-item>

        <el-form-item
          prop=""
          label=""
        >
          <el-button
            icon="el-icon-plus"
            circle
            size="small"
            @click="addNotice()"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="small"
            @click="handleComfirm()"
          >
            更新设置
          </el-button>
        </el-form-item>

      </el-form>
    </div>
  </div>
</template>

<script>
import API              from '@/api'
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'
export default {
  components: {
    DepartmentSelect
  },
  props:      {
  },
  data () {
    return {
      loadingSystems:     false,
      loading:            false,
      allBusinessSystems: [],
      params: {
        notice: []
      },
      rules: {
        start_at: [
          { required: true, message: '请输入初次发送日期', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.initAllBuisineSystems()
    this.getSendResetPassword()
  },
  methods:    {
    getLabel (index) {
      return index === 0 ? '发送配置' : ''
    },
    updateSendResetPassword () {
      this.$axios.post('admin_api/settings/send_reset_password', {
        send_reset_password: this.params
      })
        .then(response => {
          this.$message.success('密码通知设置成功')
          this.getSendResetPassword()
        })
        .catch(() => {})
    },
    getSendResetPassword () {
      this.loading = true
      this.$axios.get('admin_api/settings/send_reset_password', {})
        .then(response => {
          this.loading = false
          this.params = response.data.settings
        })
        .catch(() => {})
    },
    handleComfirm () {
      this.$refs.localForm.validate((valid) => {
        if (valid) {
          if (this.blankSystemId().length > 0) {
            this.$message.error('系统为必选项')
            return false
          }
          if (this.blankFrequency().length > 0) {
            this.$message.error('发送频率为必填项')
            return false
          }
          this.updateSendResetPassword()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    // 添加通知
    addNotice () {
      var json =  {
        bs_id:          null,
        department_ids: [],
        frequency:      90
      }
      this.params.notice.push(json)
    },
    // 删除一个通知
    deleteNotice (index) {
      this.params.notice.splice(index, 1)
    },
    // 获取所有系统
    initAllBuisineSystems () {
      API.systems.inserviceSystems()
        .then(response => {
          this.allBusinessSystems = response.data
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    // 是否有系统ID为空
    blankSystemId () {
      return this.params.notice.filter(x => API.tool.isBlank(x.bs_id))
    },
    // 是否有频率为空
    blankFrequency () {
      return this.params.notice.filter(x => API.tool.isBlank(x.frequency))
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";
</style>
