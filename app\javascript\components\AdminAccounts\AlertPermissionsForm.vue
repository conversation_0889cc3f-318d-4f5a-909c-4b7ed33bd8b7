<template>
  <div>
    <el-input
      v-model="filterText"
      placeholder="输入告警类型进行过滤"
      clearable
      prefix-icon="el-icon-search"
      style="width: 300px;"
    />
    <el-table
      ref="categoryTable"
      :data="categories"
      :span-method="groupSpanMethod"
      border
      class="alert-table"
    >
      <el-table-column
          prop="group_name"
          label="分组名称"
          width="200"
      />
      <el-table-column
        prop="name"
        label="告警名称"
        width="200"
      />
      <el-table-column min-width="200px">
        <template
          slot="header"
          slot-scope="scope"
        >
          <el-checkbox
            v-model="allQuery"
            :indeterminate="isIndeterminateQuery"
            @change="handleCheckAllQuery"
          >
            全选
          </el-checkbox>
          <el-checkbox
            v-model="allMaintain"
            :indeterminate="isIndeterminateMaintain"
            @change="handleCheckAllMaintain"
          >
            全选
          </el-checkbox>
        </template>
        <template slot-scope="scope">
          <el-checkbox
            :value="scope.row.query"
            @change="handelChangeQuery(scope.row)"
          >
            查询
          </el-checkbox>
          <el-checkbox
            :value="scope.row.maintain"
            @change="handelChangeMaintain(scope.row)"
          >
            处理
          </el-checkbox>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import API from '@/api'

export default {
  props: {
    roleId: {
      type: [String, Number],
      required: true
    },
    roleName: {
      type: String,
      required: true
    },
    alertPermissions: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      loading:                 false,
      filterText:              '',
      categories:              [],
      rolePermissions:         [],
      perTypes:                [],
      allQuery:                false,
      allMaintain:             false,
      isIndeterminateQuery:    false,
      isIndeterminateMaintain: false,
    }
  },
  watch: {
    filterText: {
      handler (filter) {
        this.filterCategories(filter)
      }
    },
  },
  created () {
    this.getCategories()
    this.rolePermissions = this.alertPermissions
  },
  methods: {
    getCategories () {
      this.loading = true
      const theParams = {
        filter_text: this.filterText
      }
      API.globalAlerts.categoriesList(theParams)
      .then(response => {
        this.loading = false
        this.allCategories = response.data.map(x => Object.assign(x, { maintain: false, query: false }))
        this.filterCategories()
      })
      .catch(() => {
        this.loading = false       
      })
    },
    filterCategories () {
      this.categories = this.filterText ? this.categories.filter(x => x.name.match(this.filterText)) : this.allCategories
      this.categoriesLength = this.categories.length
      this.categories = this.$lodash.sortBy(this.categories, 'group_name')
      this.groupType()
      this.initializeRolePermissions()
    },
    initializeRolePermissions () {
      for (let i in this.rolePermissions) {
        const categories = this.categories.find(x => x.id === this.rolePermissions[i].category_id)
        if (categories) {
          categories.query = this.rolePermissions[i].query
          categories.maintain = this.rolePermissions[i].maintain
        }
      }
      this.setAllSelect()
    },
    handleCheckAllQuery (val) {
      this.isIndeterminateQuery = false
      if (val) {
        this.categories.map(x => x.query = true)
        this.allQuery = true
      } else {
        this.categories.map(x => x.query = false)
        this.allQuery = false
      }
    },
    handleCheckAllMaintain (val) {
      this.isIndeterminateMaintain = false
      if (val) {
        this.categories.map(x => x.maintain = true)
        this.allMaintain = true
      } else {
        this.categories.map(x => x.maintain = false)
        this.allMaintain = false
      }
    },
    handelChangeQuery (category) {
      category.query = !category.query
      this.setAllSelect()
    },
    handelChangeMaintain (category) {
      category.maintain = !category.maintain
      this.setAllSelect()
    },
    setAllSelect () {
      this.queryLength = this.categories.filter(x => x.query === true).length
      this.maintainLength = this.categories.filter(x => x.maintain === true).length

      this.allQuery = (this.queryLength === this.categoriesLength && this.categoriesLength != 0)
      this.isIndeterminateQuery = this.queryLength > 0 && this.queryLength < this.categoriesLength

      this.allMaintain = (this.maintainLength === this.categoriesLength && this.categoriesLength != 0)
      this.isIndeterminateMaintain = this.maintainLength > 0 && this.maintainLength < this.categoriesLength
    },
    handleUpdatePermissions () {
      this.loading = true
      API.roles.updateAlertPermissions(
        this.roleId, 
        { 
          alert_permissions: this.categories.map(x => this.$lodash.pick(x, ['id', 'query', 'maintain'])) 
        })
        .then(() => {
          this.loading = false
          this.$message.success(`${this.roleName}-角色告警权限更新成功`)
        })
        .catch(() => {
          this.loading = false
        })
    },
    groupType() {
      this.perTypes = []
      // 找出分类
      const types = this.$lodash.uniqBy(this.categories.map(x => x.group_name))

      // 创建分类数组[{type: null, count: Number}]
      types.forEach(type => {
        const perType = {}
        perType.count = this.categories.filter(x => x.group_name === type).length
        perType.type = type
        this.perTypes.push(perType)
      })
    },
    groupSpanMethod({ row, column, rowIndex, columnIndex }) {
      let i = 0
      let span = {}
      if (columnIndex === 0) {
        for (i = 0; i < this.perTypes.length; i++) {
          if (row.group_name === this.perTypes[i].type && (this.categories[rowIndex - 1] === undefined || this.categories[rowIndex - 1].group_name !== this.perTypes[i].type)) {
            span = {
              rowspan: Number(this.perTypes[i].count),
              colspan: 1
            }
            break
          } else {
            span = {
              rowspan: 0,
              colspan: 0
            }
          }
        }
        return span
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.alert-table {
  width: 100%;
  margin-top: 20px;
}
</style>
