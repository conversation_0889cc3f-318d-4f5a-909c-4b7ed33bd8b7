# frozen_string_literal: true

class SystemAccountAuditLog < ApplicationAuditLog

  def initialize(user, request, params)
    @operation_module   = '账号查看'
    @operation_category = '系统账号详情'
    super
  end

  def word_export_success
    @operation = '账号数据导出'
    @comment   = "成功导出「#{params[:account]&.the_system&.name} : #{params[:account]&.name}」的「#{params[:content]}」数据"
    create_audit_log
  end

  def word_export_faild
    @operation = '账号数据导出'
    @comment   = "导出「#{params[:account]&.the_system&.name} : #{params[:account]&.name}」的「#{params[:content]}」数据失败"
    create_audit_log
  end

  def excel_export_success
    @operation = '账号数据导出'
    @comment   = "成功导出「#{params[:account]&.the_system&.name} : #{params[:account]&.name}」的「#{params[:content]}」数据"
    create_audit_log
  end

  def excel_export_faild
    @operation = '账号数据导出'
    @comment   = "导出「#{params[:account]&.the_system&.name} : #{params[:account]&.name}」的「#{params[:content]}」数据失败"
    create_audit_log
  end
end
