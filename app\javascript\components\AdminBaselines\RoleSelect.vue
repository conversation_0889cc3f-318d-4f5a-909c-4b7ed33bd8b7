<template>
    <el-select
        v-model="localRoleCodes"
        @change="chooseRoles"
        filterable
        sear
        clearable
        multiple
        placeholder="请选择角色"
    >
      <el-option
        v-for="item in systemRoles"
        :key="item.id"
        :label="item.name"
        :value="item.code">
      </el-option>
    </el-select>
</template>

<script>
import API from '@/api'

export default {
  props: ['system_id', 'role_codes'],
  data () {
    return {
      loading: false,
      systemRoles: [],
      localRoleCodes: []
    }
  },
  created() {
    this.localRoleCodes = this.role_codes || []
    this.initRoles()
  },
  watch: {
    system_id() {
      this.localRoleCodes = []
      this.initRoles()
    }
  },
  methods: {
    initRoles() {
      if(this.system_id === null) return
      const params = { system_id: this.system_id }
      this.$axios.get('/api/roles', { params: params })
          .then(response => {
            this.systemRoles = response.data.roles
          })
          .catch(() => {
            this.systemRoles = []
          })
    },
    chooseRoles() {
      this.$emit('chooseRoles', this.localRoleCodes)
    }
  }
}
</script>
