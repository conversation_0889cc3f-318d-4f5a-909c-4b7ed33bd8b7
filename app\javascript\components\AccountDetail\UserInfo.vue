<template>
  <el-card>
    <div
      slot="header"
      class="title-line"
    >
      <div class="title">账号基本信息</div>
      <div class="button-group">
        <el-button
          size="medium"
          :disabled="!$store.getters.hasPermission('ledgers.edit')"
          @click="handleChangeUser"
        >
          关联责任人
        </el-button>

        <el-button
          :disabled="!$store.getters.hasPermission('public_accounts.edit')"
          size="medium"
          @click="handleAddPublicAccount()"
        >
          添加至功能账号
        </el-button>

        <account-history
          :system-id="systemId"
          :account-id="account.account_id"
        />
        <export-buttons
          :account="account"
          :account-id="account.account_id"
          :system-id="systemId"
          :disabled="exportDisable"
          :other-quarter="otherQuarter"
          size="medium"
          type=""
        />
      </div>
    </div>

    <el-descriptions  v-loading="loading" title="" direction="vertical" :column="describeColumn"   border>
      <el-descriptions-item label="账号编码">
        {{ accountInfo.display_account_code || accountInfo.account_code }}
      </el-descriptions-item>
      <el-descriptions-item label="账号名称">
        {{ accountInfo.account_name }}
      </el-descriptions-item>
      <el-descriptions-item label="账号状态">
        {{ accountInfo.account_status }}
      </el-descriptions-item>
      <el-descriptions-item label="系统基线">
        {{ accountInfo.system_baseline ? accountInfo.system_baseline.name : "" }}
      </el-descriptions-item>
      <el-descriptions-item :label="displayStatusText" v-if="accountInfo.display_status != null">
        {{ accountInfo.display_status }}
      </el-descriptions-item>
      <el-descriptions-item label="最后登录时间" v-if="showLastLoginAt">
        {{ accountInfo.last_login_at }}
      </el-descriptions-item>
      <el-descriptions-item label="机构编码" v-if="showOrganizationCode">
        {{ accountInfo.organization_code }}
      </el-descriptions-item>
      <el-descriptions-item label="邮箱" v-if="showEmail">
        {{ accountInfo.last_login_at }}
      </el-descriptions-item>
      <el-descriptions-item label="员工工号" v-if="showObjid">
        {{ accountInfo.objid }}
      </el-descriptions-item>
      <el-descriptions-item
        v-for="(item, index) in accountInfoSchema" :label="item.name"
        :key="index"
      >
        {{ account[item.key] }}
      </el-descriptions-item>
      <el-descriptions-item label="所属用户组或角色" :span="fillLeftSpace('account')" contentStyle="max-width:400px;">
        {{ accountInfo.roles }}
      </el-descriptions-item>
      <template v-if="account.is_public">
        <el-descriptions-item :label="$t('activerecord.attributes.public_account.label_name')">
          {{ publicAccount[0] ? publicAccount[0].label : '' }}
        </el-descriptions-item>
        <el-descriptions-item label="使用人">
          {{ publicAccount[0] ? publicAccount[0].used_user_names : '' }}
        </el-descriptions-item>
        <el-descriptions-item label="有效期">
          {{ publicAccount[0] ? publicAccount[0].valid_date : '' }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <span v-show="publicAccount[0] && publicAccount[0].status === true">有效</span>
          <span
              v-show="publicAccount[0] && publicAccount[0].status === false"
              style="color: red;font-weight: bold;"
          >
            过期
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="fillLeftSpace('publicAccount')">
          {{ publicAccount[0] ? publicAccount[0].remark : '' }}
        </el-descriptions-item>
      </template>
      <template v-if="account.user_id && account.user_id !== 0">
        <el-descriptions-item
            v-for="(item, index) in userInfoColumns"
            :key="index"
            :label="item.label"
            :span="(index === userInfoColumns.length - 1) ? fillLeftSpace('user') : 1"
        >
          {{ showUserInfo(item) }}
        </el-descriptions-item>
        <el-descriptions-item v-if="accountTags.size > 0">
          <template slot="label">
            标 签
          </template>
          <div
            v-for="item in accountTags"
            :key="item.id"
            style="margin-top: 5px;"
          >
            <el-tag
              :style="`color: ${item.color};background-color: ${generateRgba(item.color)};`"
              size="small"
            >
              {{ item.name }}
            </el-tag>
          </div>
        </el-descriptions-item>
      </template>
    </el-descriptions>

    <el-alert
      v-if="!(account.user_id && account.user_id !== 0)"
      type="info"
      center
      show-icon
      title="该账号尚未关联员工"
      :closable="false"
    />
    <user-change
      :visible.sync="userChangeDiaLogVisible"
      :ledger="account"
      :system-id="systemId"
    />

    <public-account-form
      ref="publicAccount"
      :account="currentAccount"
      :system-id="systemId"
      :systems="systems"
      :mode="mode"
      @update="handleUpdate"
    />
  </el-card>
</template>

<script>
import API               from '@/api'
import UserChange        from '@/components/AdminLedgers/UserChange.vue'
import ExportButtons     from '@/components/common/ExportButtons.vue'
import AccountHistory    from '@/components/AccountHistory'
import PublicAccountForm from '@/components/AdminPublicAccounts/AccountForm.vue'
import { hexToRgb }             from '@/utils/color_util'

export default {
  components: {
    AccountHistory,
    ExportButtons,
    UserChange,
    PublicAccountForm
  },
  props:      {
    account:           {
      type:     Object,
      required: true
    },
    accountInfoSchema: {
      type:     Array,
      required: true
    },
    systemId:          {
      type:     Number,
      required: true
    },
    systemSettings: {
      type: Object,
      default:  {}
    },
    system:  {
      type:     Object,
      required: true
    },
    otherQuarter:      {
      type:     Object,
      required: true
    }
  },
  data () {
    return {
      loading:              false,
      user_informations:    [],
      display_status:       false,
      userChangeDiaLogVisible: false,
      account_informations: [],
      accountInfo: {
        account_code:         this.account.account_code,
        account_name:         this.account.account_name,
        account_status:       this.$t('enumerize.account.status')[this.account.status],
        roles:                this.account.roles.map(x => x.name).join(' / '),
        display_status:       this.account.display_status,
        display_account_code: this.account.display_account_code,
        system_baseline:      this.account.system_baseline,
        last_login_at:        this.account.last_login_at
      },
      systems: null,
      mode: 'ledger',
      currentAccount: this.account,
      publicAccount: Array(this.account.public_account),
      displayStatusText: this.$t('account.display_status')
    }
  },
  computed:   {
    exportDisable () {
      const accountNotAllowExport = this.account.is_common_account
      const adminNoPermission = !this.$store.getters.hasPermission('system_summary.export')
      return accountNotAllowExport || adminNoPermission
    },
    userInfoColumns () {
      return JSON.parse(JSON.stringify(this.$settings.userInfoColumns)).filter((item) => item["show_in_account_page"])
    },
    currentQuarter () {
      return this.$store.state.current_quarter
    },
    // 获取最大的列数， 取账号信息和员工信息展示的数量，取最大的
    describeColumn () {
      let userSize = this.userInfoColumns.length
      let accountSize = this.accountInfoSize + this.accountInfoSchema.length
      let columnSize = this.$lodash.max([userSize, accountSize])
      return columnSize > 8 ? 6 : columnSize
    },
    // display_status为null的时候，不展示业务系统状态
    accountInfoSize () {
      return this.accountInfo.display_status === null ? 5: 6
    },
    // 是否展示最后登录时间
    showLastLoginAt () {
      const settings = this.systemSettings
      if (API.tool.isBlank(settings)) {
        return false
      }
      return settings.show_last_login_at || settings.show_last_sign_in_at
    },
    showOrganizationCode () {
      const settings = this.systemSettings
      if (API.tool.isBlank(settings)) {
        return false
      }
      return settings.organization_code
    },
    showEmail () {
      const settings = this.systemSettings
      if (API.tool.isBlank(settings)) {
        return false
      }
      return settings.email
    },
    showObjid () {
      const settings = this.systemSettings
      if (API.tool.isBlank(settings)) {
        return false
      }
      return settings.objid
    },
    accountTags () {
      if (this.account.info && this.account.info.tags) {
        return this.account.info.tags
      } else {
        return []
      }
    }
  },
  watch:      {
    account: {
      handler (_newV, _oldV) {
        this.getAccountInformations()
        this.dataUpdate()
      },
      deep: true
    }
  },
  created () {
    this.getAccountInformations()
    this.dataUpdate()
    this.getSystems()
  },
  methods:    {
    // account会变化，需要重新复制，所以从data改为了方法
    getAccountInformations () {
      this.account_informations = [{
        info: {
          account_code:   this.account.account_code,
          account_name:   this.account.account_name,
          account_status: this.$t('enumerize.account.status')[this.account.status],
          roles:          this.account.roles.map(x => x.name).join(' / '),
          display_status: this.account.display_status,
          display_account_code: this.account.display_account_code,
          system_baseline:      this.account.system_baseline
        }
      }]
    },
    dataUpdate () {
      this.loading = true
      if (this.account.user_id === 0 || !this.account.user_id) {
        this.unknownUser()
      } else {
        this.$axios.get(`/api/users/${this.account.user_id}/quarter_user_info?quarter_id=${this.currentQuarter.id}`)
          .then(response => {
            const info             = response.data
            this.user_informations = [{ info: info }]
            this.loading           = false
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    getSystems () {
      API.systems.maintainList()
        .then(response => {
          this.systems = response.data
        })
    },
    unknownUser () {
      const info             = {
        name:       '',
        manager:    '',
        department: '',
        position:   '',
        inservice:  '未知'
      }
      this.user_informations = [{ info: info }]
      this.loading              = false
    },
    handleChangeUser () {
      this.userChangeDiaLogVisible = true
    },
    handleAddPublicAccount () {
      if (this.account.is_public) {
        this.mode = 'edit'
        this.currentAccount = this.account.public_account
      }
      this.$refs.publicAccount.visible = true
    },
    handleUpdate (val) {
      this.publicAccount = Array(val)
    },
    // 基于最大的列数, 账号信息，员工信息，公共账号信息的最后一列合并剩下的单元格
    fillLeftSpace(infoType) {
      switch (infoType) {
        case 'account':
          return (this.describeColumn - this.accountInfoSize - (this.accountInfoSchema.length - 1))
        case 'user':
          return (this.describeColumn - (this.userInfoColumns.length - 1))
        case 'publicAccount':
          return this.describeColumn - 3
        default:
          return 1
      }
    },
    showUserInfo(item) {
      if(this.user_informations.length > 0) {
        let info = this.user_informations[0].info
        let prop = item.prop.replace(/info./g, '')
        return info[prop]
      } else {
        return ""
      }
    },
    generateRgba(color) {
      let [r, g, b] = hexToRgb(color)
      return `rgba(${r}, ${g}, ${b}, 0.1)`
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/components/_variables';

.title-line {
  @include vertical_center_between;

  .title {
    font-size: 16px;
  }
}

.role-descriptions-item {
  width:400px;
}

.card-body {
  margin-top:    10px;
  margin-bottom: 10px;
}
</style>
