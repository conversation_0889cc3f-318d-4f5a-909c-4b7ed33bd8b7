<template>
  <!-- eslint-disable vue/attribute-hyphenation -->
  <el-upload
    :action="uploadUrl"
    :headers="headers"
    :file-list="fileList"
    :on-success="handleSuccess"
    :on-error="handleError"
    :on-remove="handleRemove"
    :data="the_params"
    :multiple="multiple"
    class="new-quarter-upload"
  >
    <el-button
      v-loading="loading"
      :disabled="disabled"
      class="upload-button"
      size="small"
      type="primary"
    >
      点击上传{{ systemName }}数据
    </el-button>
  </el-upload>
  <!-- eslint-enable vue/attribute-hyphenation -->
</template>

<script>
import API from '@/api'
export default {
  props: {
    action: {
      type: String,
      required: true
    },
    systemName: {
      type: String,
      required: true
    },
    systemId: {
      type: Number,
      required: true
    },
    quarter: {
      type: Object,
      required: true
    },
    describe: {
      type: String,
      default: '只能上传一个 xlsx 文件'
    },
    disabled: {
      type: <PERSON><PERSON>an,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data () {
    const headers = API.tool.getToken()

    return {
      fileList: [],
      headers: headers,
      loading: false
    }
  },
  computed: {
    the_params () {
      return { quarter_id: this.quarter.id, business_system_id: this.systemId }
    },
    uploadUrl () {
      return API.tool.absoluteUrlFor(this.action)
    }
  },
  watch: {
    quarter () {
      this.gettingExistList()
    }
  },
  created () {
    this.gettingExistList()
  },
  methods: {
    gettingExistList () {
      this.loading = true
      this.$axios.get('/admin_api/list_upload_data', { params: this.the_params })
        .then(response => {
          this.loading = false
          this.fileList = response.data.files
        })
        .catch(() => {
          this.loading = false
          this.$message.error('获取文件列表时发生错误')
        })
    },
    handleRemove (file, fileList) {
      this.$axios.delete('/admin_api/delete_uploaded_data',
        { params:
          {
            quarter_id: this.quarter.id,
            business_system_id: this.systemId,
            file: file.name
          }
        })
        .then(response => {
          if (response.data.success) {
            this.$message.success(`${file.name}已删除`)
          } else {
            this.$message.error(`删除${file.name}时发生错误`)
          }
        })
        .catch(() => {
          this.$message.error(`删除${file.name}时发生错误`)
        })
    },
    handlePreview (file) {
      console.log(file)
    },
    handleSuccess (response, file, fileList) {
      this.$message.success(`${this.systemName}数据已成功更新!`)
    },
    handleExceed (files, fileList) {
      console.log(files, fileList)
    },
    handleError (err, file, fileList) {
      const errorMsg = JSON.parse(err.message)
      this.$message.error('文件上传失败，服务器返回：' + errorMsg.error_message)
    }
  }
}
</script>

<style lang="scss">
  .new-quarter-upload{
    margin: 2em;

    .upload-button{
      width: 300px;
      height: 50px;
    }
    .el-upload-tip{
      font-size: 11px;
    }
    .el-upload-list{
      margin-top: 10px;
      height: 200px;
      overflow-y: auto;
    }
  }

</style>
