# frozen_string_literal: true

# oa日志
class OaApprovalFlowAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = 'OA 流程审批'
    super
  end

  def generate
    @operation_category = '流程创建'
    @operation = '自动生成成功'
    @comment   = "#{params[0]}: 执行了自动生成 OA 流程，成功创建了 #{params[1]} 个 OA 流程"
    create_audit_log
  end

  def batch_start_oa_flows
    @operation_category = '流程发起'
    @operation = '批量发起成功'
    @comment   = "#{params[0]}: 执行了批量发起 OA 流程，相关 OA 流程为：#{params[1]}"
    create_audit_log
  end

  def batch_start_oa_flows_fail
    @operation_category = '流程发起'
    @operation = '批量发起失败'
    @comment   = "#{params[0]}: 执行了批量发起 OA 流程，连接 OA 系统接口失败，相关 OA 流程为：#{params[1]}"
    create_audit_log
  end

  def batch_revoke_oa_flows
    @operation_category = '流程撤销'
    @operation = '批量撤销成功'
    @comment   = "#{params[0]}: 执行了批量撤销 OA 流程，相关 OA 流程为：#{params[1]}"
    create_audit_log
  end

  def batch_destory_oa_flows
    @operation_category = '流程删除'
    @operation = '批量删除成功'
    @comment   = "#{params[0]}: 执行了批量删除 OA 流程，相关 OA 流程为：#{params[1]}"
    create_audit_log
  end

  def create
    @operation_category = '流程创建'
    @operation = '流程创建成功'
    @comment   = "#{params[0]}: 创建了 OA 流程，参数为：#{params[1]}"
    create_audit_log
  end

  def create_fail
    @operation_category = '流程创建'
    @operation = '流程创建失败'
    @comment   = "#{params[0]}创建了 OA 流程失败: #{params[1]}"
    create_audit_log
  end

  def update
    @operation_category = '流程修改'
    @operation = '流程修改成功'
    @comment   = "#{params[0]}: 修改 OA 流程：修改前：#{params[1]}，修改后：#{params[2]}"
    create_audit_log
  end

  def update_fail
    @operation_category = '流程创建'
    @operation = '流程创建失败'
    @comment   = "#{params[0]}修改 OA 流程 #{params[1]} 失败: #{params[2]}"
    create_audit_log
  end

  def destroy
    @operation_category = '流程删除'
    @operation = '流程删除成功'
    @comment   = "#{params[0]}删除了 OA 流程: #{params[1]}"
    create_audit_log
  end

  def destroy_fail
    @operation_category = '流程创建'
    @operation = '流程删除失败'
    @comment   = "#{params[0]}删除 OA 流程 #{params[1]} 失败: #{params[2]}"
    create_audit_log
  end

  def start_oa_flow
    @operation_category = '流程发起'
    @operation = '流程发起成功'
    @comment   = "#{params[0]}: 发起了 OA 流程「#{params[1]}」"
    create_audit_log
  end

  def start_oa_flow_fail
    @operation_category = '流程发起'
    @operation = '流程发起失败'
    @comment   = "#{params[0]}: 发起 OA 流程 #{params[1]} 失败: #{params[2]}"
    create_audit_log
  end

  def revoke_oa_flow
    @operation_category = '流程撤销'
    @operation = '流程撤销成功'
    @comment   = "#{params[0]}: 撤销了「#{params[1]}」的 OA 流程"
    create_audit_log
  end

  def revoke_oa_flow_fail
    @operation_category = '流程撤销'
    @operation = '流程撤销失败'
    @comment   = "#{params[0]}: 撤销 OA 流程「#{params[1]}」失败：#{params[2]} "
    create_audit_log
  end
end
