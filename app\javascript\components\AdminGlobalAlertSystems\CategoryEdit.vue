<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    title="修改告警信息"
    width="600px"
    appendToBody
    @close="dialogVisible = false"
  >
    <el-form
      ref="categoryForm"
      :model="categoryForm"
      :rules="rules"
      labelWidth="120px"
      statusIcon
      class="new-form"
    >
      <el-form-item label="告警名称：">
        <el-input v-model="categoryForm.name" maxlength="255" />
      </el-form-item>
      <el-form-item label="告警分组：">
        <el-select
          v-model="categoryForm.group_id"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            :value="null"
            label="不选择分组"
          />
          <el-option
            v-for="group in groups"
            :key="group.id"
            :label="group.name"
            :value="group.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="顺序号：">
        <el-input-number
          :min=0
          :step=1
          :step-strictly="true"
          v-model="categoryForm.order_number"
        />
      </el-form-item>
      <el-form-item label="最大处理时限：">
        <el-input-number
            placeholder="时间间隔"
            :min=0
            :step=1
            :step-strictly="true"
            v-model="categoryForm.max_deal_interval"
        />
        <el-select
            v-model="categoryForm.max_deal_time_type"
            clearable
            placeholder="请选择时间类型"
            style="width: 150px;"
        >
          <el-option
              v-for="item in max_deal_time_types"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <div>
          <comment-bar>设置为 0，表示不限制处理时限</comment-bar>
        </div>
      </el-form-item>
      <template v-if="categoryForm.advance_delay_setting.can_advance && categoryForm.advance_delay_setting.can_delay">
         <el-form-item
             label="提前或延迟告警"
         >
             <el-radio v-model="showAdvance" label="1">提前</el-radio>
             <el-radio v-model="showAdvance" label="2">延迟</el-radio>
         </el-form-item>
         <template v-if="showAdvance === '1'">
            <el-form-item label="提前告警天数: ">
              <el-input-number
                  :min=0
                  :step=1
                  :step-strictly="true"
                  v-model="categoryForm.advance_delay_setting.advance_days"
              />
              <el-select
                v-model="categoryForm.advance_delay_setting.day_type"
                clearable
                placeholder="请选择日期类型"
                style="width: 150px;"
              >
                <el-option
                  key="0"
                  label="日"
                  value="0"
                />
                <el-option
                  key="1"
                  label="工作日"
                  value="1"
                />
              </el-select>
            </el-form-item>
         </template>
         <template v-else>
            <el-form-item label="延迟告警天数: ">
              <el-input-number
                  :min=0
                  :step=1
                  :step-strictly="true"
                  v-model="categoryForm.advance_delay_setting.delay_days"
              />
              <el-select
                v-model="categoryForm.advance_delay_setting.day_type"
                clearable
                placeholder="请选择日期类型"
                style="width: 150px;"
              >
                <el-option
                  key="0"
                  label="日"
                  value="0"
                />
                <el-option
                  key="1"
                  label="工作日"
                  value="1"
                />
              </el-select>
            </el-form-item>
         </template>
      </template>
      <el-form-item label="提前告警天数: " v-else-if="categoryForm.advance_delay_setting.can_advance">
        <el-input-number
          :min=0
          :step=1
          :step-strictly="true"
          v-model="categoryForm.advance_delay_setting.advance_days"
        />
        <el-select
          v-model="categoryForm.advance_delay_setting.day_type"
          clearable
          placeholder="请选择日期类型"
          style="width: 150px;"
        >
          <el-option
            key="0"
            label="日"
            value="0"
          />
          <el-option
            key="1"
            label="工作日"
            value="1"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="延迟告警天数: " v-else-if="categoryForm.advance_delay_setting.can_delay">
        <el-input-number
          :min=0
          :step=1
          :step-strictly="true"
          v-model="categoryForm.advance_delay_setting.delay_days"
        />
        <el-select
          v-model="categoryForm.advance_delay_setting.day_type"
          clearable
          placeholder="请选择日期类型"
          style="width: 150px;"
        >
          <el-option
            key="0"
            label="日"
            value="0"
          />
          <el-option
            key="1"
            label="工作日"
            value="1"
          />
        </el-select>
      </el-form-item>
      <el-divider></el-divider>
      <el-form-item label="告警升级配置：">
        <el-button
          size="small"
          icon="el-icon-plus"
          circle
          @click="addBlankConfig"
          style="float: right"
        />
      </el-form-item>
      <el-form-item
        v-for="(config, index) in categoryForm.notify_configs"
        label-width="10px"
        :key="index"
      >
        <el-input-number
          placeholder="时间间隔"
          :min=0
          :step=1
          :step-strictly="true"
          v-model="config.interval"
        />
        <el-select
          v-model="config.time_type"
          clearable
          placeholder="请选择时间类型"
          style="width: 150px;"
        >
          <el-option
            v-for="item in time_types"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <admin-select
          multiple
          v-model="config.admin_ids"
          placeholder="请选择通知人"
          selectStyle="width: 150px;"
        />
        <el-button
          icon="el-icon-minus"
          size="small"
          circle
          style="float: right"
          @click="removeConfig(index)"
        />
      </el-form-item>
      <span v-if="categoryForm.code === 'business_account_has_admin_role'" >
        <el-divider />
        <el-form-item label="管理员角色设置："></el-form-item>
        <el-form-item
          v-for="(setting, index) in categoryForm.settings"
          :key="index"
          :label="getSystemName(setting.system_id)"
        >
          <el-select
            v-model="setting.role_codes"
            multiple
            filterable
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="(role, index) in getSystemRoles(setting.system_id)"
              :key="index"
              :label="role.name"
              :value="role.code"
            />
          </el-select>
        </el-form-item>
      </span>
      <span v-if="categoryForm.code === 'intern_account'" >
        <el-divider />
        <el-form-item
          label="告警系统设置：">
          <el-switch
            v-model="categoryForm.settings.is_all"
            active-text="全部系统"
            inactive-text="部分系统">
          </el-switch>
        </el-form-item>
        <el-form-item
          label="">
          <el-select
            v-show="!categoryForm.settings.is_all"
            v-model="categoryForm.settings.system_ids"
            filterable
            clearable
            multiple
            placeholder="请选择系统"
          >
            <el-option
              v-for="(x, index) in allSystems"
              :key="index"
              :label="x.name"
              :value="x.id"
            />
          </el-select>
        </el-form-item>
      </span>
      <span v-if="categoryForm.code === 'unusing_account'" >
        <el-divider />
        <el-form-item label="超时告警阈值：">
          <el-input-number
            v-model="categoryForm.settings.unusing_quantity"
            :min=1
            :step=1
            :step-strictly="true"
          />
          <el-select
            v-model="categoryForm.settings.unit"
            clearable
            placeholder="请选择时间类型"
            style="width: 150px;"
          >
            <el-option
              v-for="item in unusing_account_units"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </span>
      <span v-if="categoryForm.code === 'ledgers_stock'">
        <el-divider />
        <el-form-item
          :label="`排除${$t('activerecord.attributes.public_account.label_name')}：`"
        >
          <el-select
            v-model="categoryForm.settings.public_account_type_ids"
            :placeholder="$t('activerecord.attributes.public_account.label_name')"
            clearable
            multiple
            style="width: 100%"
          >
            <el-option
              v-for="(label, index) in publicAccountLabels"
              :key="index"
              :label="label.label"
              :value="label.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </span>
    </el-form>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="primary"
        @click="updateCategory"
      >
        更新
      </el-button>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </span>
  </el-dialog>
</template>

<script>

import AdminSelect from '@/components/common/AdminSelect.vue'
import CommentBar  from "../common/CommentBar.vue";
import API         from '@/api'

export default {
  components: {
    CommentBar,
    AdminSelect
  },
  props: {
    category: {
      type:     Object,
      required: true
    },
    groups:   {
      type:     Array,
      required: true
    }
  },
  data () {
    return {
      allSystems: [],
      system_roles: [],
      dialogVisible: false,
      showAdvance: '1',
      unusing_account_units: [
        {
           "label": "日",
           "value": 'day'
         },
         {
           "label": "工作日",
           "value": 'workday'
         },
         {
           "label": "月",
           "value": 'month'
         },
         {
           "label": "年",
           "value": 'year'
         }
      ],
      max_deal_time_types: [
        {
          "label": "时",
          "value": 1
        },
       {
          "label": "日",
          "value": 2
        },
        {
         "label": "工作日",
         "value": 5
        },
        {
          "label": "月",
          "value": 3
        },
        {
          "label": "年",
          "value": 4
        },
      ],
      time_types: [
        {
          "label": "日",
          "value": 1
        },
        {
           "label": "工作日",
           "value": 4
         },
        {
          "label": "月",
          "value": 2
        },
        {
          "label": "年",
          "value": 3
        },
      ],
      categoryForm:  {
        name:           '',
        code:           '',
        group_id:       null,
        order_number:   null,
        notify_configs: [],
        max_deal_time_type: 1,
        max_deal_interval: 0,
        advance_delay_setting: {
          can_advance: false,
          can_delay: false,
          advance_days: 0,
          delay_days: 0,
          day_type: 0
        },
        settings: []
      },
      rules:         {
        name: [
          { required: true, message: '告警分类名称不能为空', trigger: 'blur' }
        ]
      },
      publicAccountLabels: []
    }
  },
  watch: {
    category () {
      this.categoryInitialize()
    },
    showAdvance () {
      if (this.showAdvance === '1' && this.category.advance_delay_setting) {
          this.categoryForm.advance_delay_setting.delay_days = 0
      } else {
          this.categoryForm.advance_delay_setting.advance_days = 0
      }
    }
  },
  created () {
    this.getAccountRoleSettings()
    this.categoryInitialize()
    this.initAllBuisineSystems()
    this.gettingPublicAccountType()
  },
  methods: {
    gettingPublicAccountType() {
      this.loading = true
      API.publicAccountType.list()
          .then(response => {
            this.loading = false
            this.publicAccountLabels = response.data
          })
          .catch(_ => {
            this.loading = false
          })
    },
    getSystemName (systemId) {
      return this.system_roles.find(x => x.system_id === systemId).name+'：'
    },
    getSystemRoles (systemId) {
      return this.system_roles.find(x => x.system_id === systemId).roles
    },
    getAccountRoleSettings () {
      this.$axios.get('admin_api/global_alert_categories/account_role_settings', {})
        .then(response => {
          this.system_roles = response.data.system_roles
        })
        .catch(() => {})
    },
    categoryInitialize () {
      this.categoryForm.name           = this.category.name
      this.categoryForm.code           = this.category.code
      this.categoryForm.group_id       = this.category.group_id
      this.categoryForm.order_number   = this.category.order_number
      this.categoryForm.notify_configs = this.category.notify_configs
      this.categoryForm.max_deal_interval = this.category.max_deal_interval
      this.categoryForm.max_deal_time_type = this.category.max_deal_time_type || 1
      this.categoryForm.settings = this.category.settings
      // 如果实习生告警类型没有设置告警设置，默认赋予空值
      if (this.category.code === 'internal_account' && API.tool.isBlank(this.categoryForm.settings)) {
        this.categoryForm.settings = { is_all: true, system_ids: [] }
      }
      if(this.category.advance_delay_setting) {
        this.categoryForm.advance_delay_setting = this.category.advance_delay_setting
        if (this.category.advance_delay_setting.advance_days > 0) {
          this.showAdvance = '1'
        } else {
          this.showAdvance = '2'
        }
      }
    },
    updateCategory () {
      if (this.categoryForm.code === 'unusing_account') {
        if (API.tool.isBlank(this.categoryForm.settings.unusing_quantity)) {
          return this.$message.error('超时告警阈值不能为空')
        }
        if (API.tool.isBlank(this.categoryForm.settings.unit)) {
          return this.$message.error('超时告警阈值单位不能为空')
        }
      }
      if (!this.categoryForm.name) return this.$message.error('告警分类名称不能为空')
      let params = {
        name:           this.categoryForm.name,
        group_id:       this.categoryForm.group_id,
        order_number:   this.categoryForm.order_number || null,
        notify_configs: this.categoryForm.notify_configs,
        advance_delay_setting: this.categoryForm.advance_delay_setting,
        max_deal_interval: this.categoryForm.max_deal_interval,
        max_deal_time_type: this.categoryForm.max_deal_time_type,
        settings:           this.categoryForm.settings
      }
      if (this.categoryForm.notify_configs.length > 0) {
        for (let i = 0; i < this.categoryForm.notify_configs.length; i++) {
          let config = this.categoryForm.notify_configs[i]
          if(!config.interval) return this.$message.error('时间间隔须为大于0的数字')
          if(!config.time_type) return this.$message.error('时间类型不能为空')
          if(!config.admin_ids || config.admin_ids.length === 0) return this.$message.error('通知人不能为空')
        }
      }
      this.$axios.put(`/admin_api/global_alert_categories/${this.category.id}`, { category: params })
        .then(response => {
          this.$emit('update')
          this.$message.success(`更新${this.categoryForm.name}成功`)
          this.dialogVisible = false
        })
        .catch(() => {})
    },
    addBlankConfig() {
      let blankConfig = {
        interval: null,
        time_type: null,
        admin_ids: null
      }
      this.categoryForm.notify_configs.push(blankConfig)
    },
    removeConfig(index) {
      this.categoryForm.notify_configs.splice(index, 1)
    },
    // 获取所有系统
    initAllBuisineSystems() {
      API.systems.all()
        .then(response => {
          this.allSystems = response.data
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    }
  }
}

</script>
