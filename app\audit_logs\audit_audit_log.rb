# frozen_string_literal: true

# 业务系统维护的审计日志
class AuditAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '审计日志'
    @operation_category = '审计日志搜索条件'
    super
  end

  def create_search_record
    @operation = '保存搜索条件'
    @comment   = "审计日志搜索条件：创建记录「#{params.name},#{JSON.parse(params[:content]).join('，')}」"
    create_audit_log
  end

  def delete_search_record
    @operation = '删除搜索条件'
    @comment   = "审计日志搜索条件：删除记录「#{params.name},#{JSON.parse(params[:content]).join('，')}」"
    create_audit_log
  end

  def export_audit_logs_success
    @operation = '审计日志导出'
    @comment   = "审计日志导出成功"
    create_audit_log
  end

  def export_audit_logs_faild
    @operation = '审计日志导出'
    @comment   = "审计日志导出失败"
    create_audit_log
  end
end
