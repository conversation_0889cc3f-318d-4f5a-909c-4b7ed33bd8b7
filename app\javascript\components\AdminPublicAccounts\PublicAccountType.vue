<template>
  <div>
    <div class="pa-container">
      <div class="toolbar">
          <div class="right">
          <el-form
            :inline="true"
            class="search-box"
          >
            <el-form-item>
              <el-button
                size="small"
                type="primary"
                :disabled="!$store.getters.hasPermission('public_account_type_manager.edit')"
                @click="handleCreateAccountType"
              >
                添加账号类型
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="types"
      border
    >
      <el-table-column
        property="id"
        label="序号"
      />
      <el-table-column
        property="key"
        label="类型编码"
      />
      <el-table-column
        property="label"
        label="类型名称"
      >
        <template slot-scope="scope">
          <el-input
            v-if="scope.row.edit"
            v-model="scope.row.label"
          />
          <span v-else>{{ scope.row.label }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="272"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.edit"
            size="mini"
            icon="el-icon-close"
            @click="cancelEdit(scope.row)"
          />
          <el-button
            v-if="scope.row.edit"
            size="mini"
            type="primary"
            icon="el-icon-check"
            @click="typeEditConfirm(scope.row)"
          />
          <el-button
            v-else
            size="mini"
            :disabled="!$store.getters.hasPermission('public_account_type_manager.edit')"
            @click="scope.row.edit = true"
          >
            编 辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            :disabled="!$store.getters.hasPermission('public_account_type_manager.delete') || deleteDisabled(scope.row.key)"
            @click="handleDestroyComfirm(scope.$index, scope.row)"
          >
            删 除
          </el-button>
          <el-button
            v-if="displaySetting(scope.row)"
            size="mini"
            :disabled="!$store.getters.hasPermission('public_account_type_manager.edit')"
            @click="handleSettingAccountType(scope.row)"
          >
            设 置
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :visible.sync="visible"
      :title="dialogTitle"
      :close-on-click-modal="false"
      width="600px"
      append-to-body
    >
      <el-form
        ref="ruleForm"
        :model="type"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item
          label="类型编码"
          prop="key"
        >
          <el-input
            v-model="type.key"
          />
        </el-form-item>
        <el-form-item
          label="类型名称"
          prop="label"
        >
          <el-input
            v-model="type.label"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="handleCommit">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="settingVisible"
      :title="settingDialogTitle"
      :close-on-click-modal="false"
      width="600px"
      append-to-body
    >
      <el-form
        :model="settingParams"
        label-width="120px"
      >
        <span v-if="settingParams.key === 'intern'">
          <el-form-item
            v-for="(bs, index) in settingParams.settings.systems"
            :label="bs.name"
          >
            <el-select
              v-model="bs.ignore_account_codes"
              remote
              multiple
              filterable
              clearable
              auto-complete="off"
              placeholder="请输入账号编码或名称"
              style="width: 100%;"
              :remote-method="data => getSearchAccounts(data, bs)"
              :loading="accountLoading"
            >
              <el-option
                v-for="(x, index) in bs.options"
                :key="`${bs.id}-${x.code}`"
                :label="getLable(x)"
                :value="x.code"
              />
            </el-select>
          </el-form-item>
        </span>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="settingVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleUpdateSetting">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import API from '@/api'

export default {
  data () {
    return {
      loading: false,
      accountLoading: false,
      visible: false,
      settingVisible: false,
      types:    [],
      settingParams: {
        id:       null,
        key:      null,
        settings: {}
      },
      type: {
        key:   '',
        label: ''
      },
      rules: {
        key: [
          { required: true, message: '请输入类型编码', trigger: 'blur' }
        ],
        label: [
          { required: true, message: '请选择类型名称', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle () {
      return this.$t('activerecord.attributes.public_account.label_name')
    },
    settingDialogTitle () {
      const key = this.settingParams.key
      return this.$t(`activerecord.attributes.public_account.setting_title.${key}`)
    },
    // 含有告警的功能账号类型
    alertAccountType () {
      return ['privilege', 'default', 'epiboly', 'intern']
    }
  },
  created () {
    this.gettingPublicAccountType()
  },
  methods: {
    gettingPublicAccountType () {
      this.loading = true
      API.publicAccountType.list()
        .then(response => {
          this.loading = false
          this.types = response.data.map(x =>
            Object.assign(x, { edit: false})
          )
        })
        .catch(_ => {
          this.loading = false
        })
    },
    handleCreateAccountType () {
      this.visible = true
    },
    handleCommit () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleCreate()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    handleCreate () {
      this.loading = true
      API.publicAccountType.create(this.type)
        .then(_ => {
          this.loading = false
          this.gettingPublicAccountType()
          this.visible = false
          this.$message.success('账号类型创建成功')
        })
        .catch(_ => {
          this.loading = false
        })
    },
    cancelEdit (row) {
      row.edit = false
      this.gettingPublicAccountType()
    },
    typeEditConfirm (row) {
      if (!row.label) {
        this.$message.error('请填写类型名称')
      } else {
        this.handleUpdate(row)
      }
    },
    handleUpdate (row) {
      this.loading = true
      API.publicAccountType.update(row)
        .then(_ => {
          this.loading = false
          this.$message.success('账号类型已更新')
          this.gettingPublicAccountType()
        })
        .catch(_ => {
          this.loading = false
        })
    },
    handleDestroyComfirm (index, row) {
      if (row.account_count > 0) return this.$message.warning('存在使用该类型的功能账号，不能删除')
      this.handleDelete(index, row)
    },
    handleDelete (_, row) {
      API.publicAccountType.destroy({id: row.id})
        .then(_ => {
          this.$message.success('删除成功')
          this.gettingPublicAccountType()
        })
        .catch(_ => {})
    },
    deleteDisabled (key) {
      return this.alertAccountType.includes(key)
    },
    // 是否展示设置按钮
    displaySetting (row) {
      return row.key === 'intern'
    },
    handleSettingAccountType (row) {
      this.settingParams.id = row.id
      this.settingParams.key = row.key
      this.settingParams.settings = row.settings
      this.settingVisible = true
    },
    handleUpdateSetting () {
      this.loading = true
      API.publicAccountType.update(this.settingParams)
        .then(_ => {
          this.loading = false
          this.gettingPublicAccountType()
          this.settingVisible = false
          this.$message.success('账号类型设置成功')
        })
        .catch(_ => {
          this.loading = false
        })
    },
    getSearchAccounts(query, bs) {
      if (query !== '') {
        this.accountLoading = true
        setTimeout(() => {
          const params = { systemId: bs.id, query: query }
          API.systemAccounts.searchAccounts(params)
            .then(response => {
              this.accountLoading = false
              bs.options          = response.data.data
            })
            .catch(() => {
              this.accountLoading = false
              bs.options          = []
            })
        }, 200);
      } else {
        this.options = [];
      }
    },
    getLable (row) {
      return `名称：${row.name}，编码：${row.code}`
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';
  .pa-container{
    background-color: white;
    min-width: 600px;

    .toolbar{
      @include vertical_top_between;
      margin-bottom: 0px;
    }
    .pagination{
      margin-top: 20px;
    }

    .right {
      margin-left: auto;
    }
  }
</style>
