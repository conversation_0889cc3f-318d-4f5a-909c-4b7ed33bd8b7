import axios                           from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const list = (params) => {
  const url = '/api/user_compare_job_baseline'
  return axios.get(url, {params: params})
}

export const create = (params) => {
  const url = '/api/user_compare_job_baseline'
  return axios.post(url, params)
}

export const diffsExport        = (params) => {
  const url = '/api/user_compare_job_baseline/export'
  return axios.get(url, {
    responseType: 'blob',
    params:       params
  })
    .then(response => {
      const fileName = parseFileName(response, 'user_compare_job_baseline.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch(() => {})
}
