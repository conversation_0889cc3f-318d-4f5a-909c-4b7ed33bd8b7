class AnalysisReportExportController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!, except: %i[export_file_status download_export_file]
  before_action :systems_in_query, except: %i[export_file_status download_export_file]

  def global_base_report
    options = params.to_unsafe_h.symbolize_keys
    quarter_id = params[:quarter_id].to_i
    quarter = Quarter.find(quarter_id)
    other_quarter_id = params[:other_quarter_id].to_i
    options.merge!({
      system_ids:       @systems.pluck(:id),
      other_quarter_id: other_quarter_id
    })
    DownloadExport::GlobalBaseExport.new(quarter, current_admin, options).async_export

    audit_log! ({ quarter_id: quarter_id, other_quarter_id: other_quarter_id }), action: :global_base_report_success
    json_respond(success: true)
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! ({ quarter_id: quarter_id, other_quarter_id: other_quarter_id }), action: :global_base_report_failed
    json_custom_respond(500, success: false, error_message: e.message)
  end

  def diff_global_base_report
    options = params.to_unsafe_h.symbolize_keys
    quarter_id = params[:quarter_id].to_i
    quarter = Quarter.find(quarter_id)
    other_quarter_id = params[:other_quarter_id].to_i
    options.merge!({
      system_ids:       @systems.pluck(:id),
      other_quarter_id: other_quarter_id
    })
    DownloadExport::DiffGlobalBaseExport.new(quarter, current_admin, options).async_export

    audit_log! ({ quarter_id: quarter_id, other_quarter_id: other_quarter_id }), action: :diff_global_base_report_success
    json_respond(success: true)
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! ({ quarter_id: quarter_id, other_quarter_id: other_quarter_id }), action: :diff_global_base_report_failed
    json_custom_respond(500, success: false, error_message: e.message)
  end

  def global_inspect_report
    options = params.to_unsafe_h.symbolize_keys
    quarter_id = params[:quarter_id].to_i
    quarter = Quarter.find(quarter_id)
    other_quarter_id = params[:other_quarter_id].to_i
    options.merge!({
      system_ids:       @systems.pluck(:id),
      other_quarter_id: other_quarter_id || 0
    })
    DownloadExport::GlobalInspectExport.new(quarter, current_admin, options).async_export

    audit_log! ({ quarter_id: quarter_id, other_quarter_id: other_quarter_id }), action: :global_inspect_report_success
    # json_respond(success: true, data: { export_file_path: file.export_file_path, job_id: job_id })
    json_respond(success: true)
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! ({ quarter_id: quarter_id, other_quarter_id: other_quarter_id }), action: :global_inspect_report_success
    json_custom_respond(500, success: false, error_message: e.message)
  end

  def export_file_status
    job_status = Sidekiq::Status.status(params[:job_id])
    case job_status
    when :complete
      json_respond(success: true)
    when :queued, :working, :retrying
      json_respond(success: false, message: I18n.t('audit_report.errors.response_202'))
    else
      json_custom_respond(500, success: false, message: I18n.t('audit_report.errors.response_404'))
    end
  end

  def download_export_file
    send_file_compatible_with_msie params[:file_path]
  end

  private

  def authenticate_policy!
    authorize nil, policy_class: ReportExportPolicy
  end

  def systems_in_query
    @systems = current_admin.business_systems_in_query
    @systems -= BusinessSystem.where(id: [32]).to_a if Setting.customer_id == 'mfcteda'
    @systems
  end
end
