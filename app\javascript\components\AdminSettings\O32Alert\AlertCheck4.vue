<template>
  <div v-loading="loading">
    <div class="title">
      <div class="placeholder-box"></div>
      <h3 class="title-label">非组合经理、投票账户存在指令下达权限</h3>
    </div>
    <el-form
      ref="form"
      inline
      label-position="top"
    >
      <el-form-item
        label="排除角色："
      >
        <div class="form-item-container">
          <el-select
            v-model="permissions_1"
            placeholder="请选择"
            clearable
            multiple
            filterable
            style="width:400px"
          >
            <el-option
              v-for="role in jiaoyiRoles"
              :label="role"
              :value="role"
            />
          </el-select>
        </div>
      </el-form-item>
      <el-form-item
        label="告警权限："
      >
        <div class="form-item-container">
          <el-select
            v-model="permissions_2"
            placeholder="请选择权限"
            clearable
            multiple
            filterable
            style="width:400px"
          >
            <el-option
              v-for="menu in jiaoyiNames"
              :label="menu"
              :value="menu"
            />
          </el-select>
        </div>
      </el-form-item>
    </el-form>
    <el-button
      size="small"
      type="primary"
      @click="subSettings()"
    >
      保存
    </el-button>
  </div>
</template>
<script>
export default {
  components: {
  },
  props: {
    alertSetting: {
      type: Object,
      default: () => {},
      required: true
    }
  },
  data () {
    return {
      loading:       false,
      permissions_1: [],
      permissions_2: []
    }
  },
  created () {
    this.getSettings()
  },
  computed: {
    jiaoyiNames () { return this.alertSetting.menus },
    jiaoyiRoles () { return this.alertSetting.roles }
  },
  methods: {
    getSettings () {
      this.loading = true
      this.$axios.get('/admin_api/global_alerts/o32_alert_settings/4')
        .then(response => {
          this.permissions_1 = response.data.permissions_1
          this.permissions_2 = response.data.permissions_2
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    subSettings () {
      this.loading = true
      if (this.permissions_1.length === 0 || this.permissions_2.length === 0) {
        this.loading = false
        return this.$message.error('请选择权限')
      }
      const theParams = {
        permissions_1: this.permissions_1,
        permissions_2: this.permissions_2
      }
      this.$axios.post('/admin_api/global_alerts/o32_alert_settings/4', theParams)
        .then(response => {
          this.$message.success('添加成功')
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.title{
  @include  vertical_center_left;
  margin-bottom: 20px;

  .placeholder-box{
    @include placeholder_box;
  }
  .title-label{
    margin-top: 0;
  }
}
</style>
