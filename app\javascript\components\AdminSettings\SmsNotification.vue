<template>
  <div>
    <el-form-item
      label=""
    >
      <el-button
        size="small"
        :disabled="!$store.getters.hasPermission('app_settings.notification')"
        @click="createNewSms"
      >
        添加
      </el-button>
    </el-form-item>
    <transition-group name="list-complete">
      <div
        v-for="(item, index) in smsSettings"
        :key="`sms${index}`"
        class="list-complete-item"
      >
        <el-form-item
          :prop="`smses.${index}.mobile`"
          :label="`手机号 ${index + 1}`"
          :rules="rules.mobile"
          class="form-item-first-line"
        >
          <el-input
            v-model="smsSettings[index].mobile"
            size="small"
          />
          <el-button
            size="small"
            :disabled="!$store.getters.hasPermission('app_settings.notification')"
            @click="handleDeleteSms(index)"
          >
            删除
          </el-button>
        </el-form-item>
        <el-form-item
          :prop="'smses.' + index + '.templates'"
          :rules="rules.templates"
          class="form-item-second-line"
        >
          <el-select
            v-model="smsSettings[index].templates"
            :disabled="!$store.getters.hasPermission('app_settings.notification')"
            multiple
            size="small"
            placeholder="请选择通知模板"
          >
            <el-option
              v-for="template in templates"
              :key="template.key"
              :value="template.key"
              :label="template.name"
            />
          </el-select>
        </el-form-item>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { validatePhone } from '@/utils/form_validates'

export default {
  props: {
    outerSmsSettings: {
      type: Array,
      default: () => [],
      required: true
    },
    outerTemplates: {
      type: Array,
      default: () => [],
      required: true
    }
  },
  data () {
    return {
      smsSettings: [],
      templates: [],
      rules: {
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        templates: [
          { required: true, message: '请选择通知模板', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    outerSmsSettings () {
      this.initializeSmsSettings()
    },
    outerTemplates () {
      this.initializeTemplates()
    }
  },
  computed: {

  },
  methods: {
    createNewSms () {
      const itemData = { mobile: '', templates: [] }
      const length   = this.smsSettings.length
      this.$set(this.smsSettings, length, itemData)
    },
    handleDeleteSms (index) {
      this.$delete(this.smsSettings, index)
    },
    initializeSmsSettings () {
      this.smsSettings = this.outerSmsSettings
    },
    initializeTemplates () {
      this.templates = this.outerTemplates
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .form-item-first-line {
    // MARK: 为了把表单验证的字露出来
    margin-bottom: 14px;

    .el-input {
      width: 200px;
    }
  }

  .form-item-second-line {
    margin-bottom: 25px;

    .el-select {
      width: 100%;
    }
  }

  .list-complete-item {
    transition: all 0.5s;
  }

  .error {
    color: #E54D42;
    font-weight: bold;
  }

</style>