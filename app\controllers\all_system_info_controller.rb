# frozen_string_literal: true

# 全部系统相关控制器
class AllSystemInfoController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!
  before_action :set_all_system_info, only: %i[update delete]

  def create
    all_system_info = AllSystemInfo.new(all_system_info_params)

    if all_system_info.save
      audit_log! all_system_info
      json_respond all_system_info.output
    else
      json_custom_respond(:unprocessable_entity, error_message: all_system_info.errors.full_messages.join('; '))
    end
  end

  def update
    if @all_system_info.update(all_system_info_params)
      audit_log! @all_system_info
      json_respond @all_system_info
    else
      json_custom_respond(:unprocessable_entity,
                          error_message: @all_system_info.errors.full_messages.join('; '))
    end
  end

  def delete
    @all_system_info.destroy
    audit_log! @all_system_info
  end

  def all_list
    result = AllSystemInfo.all.map(&:output)
    not_online_system_count = AllSystemInfo.where(business_system_id: nil).count

    json_respond ({
      all_system_info:           result,
      online_system_count:       result.size - not_online_system_count,
      not_online_system_count:   not_online_system_count,
      system_count:              result.size
    })
  end

  def import
    importer = DataImport::AllSystemInfoImport.new(params[:file])
    importer.import
    audit_log! action: :import_success
    json_respond importer.errors
  rescue DataImport::ValidateFailure => e
    audit_log! action: :import_failed
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    json_custom_respond 400, error_message: e.message
  end

  # TODO 之前未做登录验证，后期一同修改
  def export
    audit_log! action: :export_success
    exporter = DataExport::AllSystemInfoExport.new(params[:system_ids])
    send_data(
      exporter.export,
      filename: URI.encode_www_form_component("全部系统数据导出.xlsx".gsub(/\s+/, '')),
      type:     'application/octet-stream;charset=utf-8'
    )
  end

  private

  def all_system_info_params
    params.require(:all_system_info).permit(:name, :company, :version, :admin, :email, :business_system_id)
  end

  def set_all_system_info
    @all_system_info = AllSystemInfo.find_by(id: params[:system_id])
  rescue ActiveRecord::RecordNotFound => e
    json_custom_respond(404, error_message: e.message.to_s)
  end

  def authenticate_policy!
      authorize AllSystemInfo
    end
end
