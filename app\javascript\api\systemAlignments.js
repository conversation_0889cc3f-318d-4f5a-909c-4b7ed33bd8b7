import axios                           from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const index     = (data) => {
  return axios.get('/api/system_alignment', { params: data })
}
export const exportData = (data) => {
  return axios.get(`/api/system_alignment/export`, {
    responseType: 'blob',
    params: data
  })
    .then(response => {
      const fileName = parseFileName(response, 'system-alignment-export.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch(() => {})
}

export const fundList = (data) => {
  return axios.get('/api/system_alignment/fund_list', { params: data })
}


export const exportStatusDiff = (data) => {
  return axios.get(`api/system_alignment/export_status_diff`, {
    responseType: 'blob',
    params: data
  })
    .then(response => {
      const fileName = parseFileName(response, 'system-alignment-export-status-diff.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch(() => {})
}
