import axios from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const index = (params = {}) => {
  return axios.get('/api/o32_option_differences', { params: params })
}

// 参数变更历史记录
export const list = (params = {}) => {
  return axios.get('/api/o32_option_differences/list', { params: params })
}

// 导出时间点对比
export const exportQuarterData = (params = {}) => {
  return axios.get(`/api/o32_option_differences/export_quarter`, { responseType: 'blob', params: params })
    .then(response => {
      const fileName = parseFileName(response, 'o32_option_differences.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

// 导出基线对比
export const exportBaselineData = (params = {}) => {
  return axios.get(`/api/o32_option_differences/export_baseline`, { responseType: 'blob', params: params })
    .then(response => {
      const fileName = parseFileName(response, 'o32_option_differences.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

// 更新员工
export const update = (id, userId) => {
  return axios.put(`/api/o32_option_differences/${id}`, { o32_option_difference: { user_id: userId } })
}