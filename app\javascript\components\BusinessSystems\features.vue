<template>
  <div class="container">
    <el-tabs
      v-model="tab"
      tabPosition="left"
    >
      <el-tab-pane
        v-for="system in systems"
        :key="system.id"
        :name="system.id.toString()"
        :label="system.name"
      >
      </el-tab-pane>
      <system-features :systemId="systemId"/>

    </el-tabs>
  </div>
</template>

<script>
import SystemFeatures from './SystemFeatures.vue'
export default {
  components: {
    SystemFeatures
  },
  data () {
    return {
      systems: [],
      tab: null
    }
  },
  computed: {
    systemId () {
      return Number(this.tab)
    }
  },
  created () {
    this.getSystems()
  },
  methods: {
    getSystems () {
      this.$axios.get('/api/systems/all')
        .then(response => {
          this.systems = response.data
          let firstSystem = this.systems[0]
          if (firstSystem) this.tab = firstSystem.id.toString()
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .container{
    padding: 20px;
  }
</style>
