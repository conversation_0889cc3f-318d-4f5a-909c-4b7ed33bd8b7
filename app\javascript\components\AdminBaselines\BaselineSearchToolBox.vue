<template>
  <el-form
    :inline="true"
    class="search-box"
  >
    
    <el-form-item>
      <el-select
        v-model="search.property"
        placeholder="检索条件"
        size="small"
        style="width:120px;"
      >
        <el-option
          label="基线 ID"
          value="baseline_id"
        />
        <el-option
          label="基线名称"
          value="baseline_name"
        />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-input
        v-model="search.value"
        placeholder="输入搜索内容"
        size="small"
        style="width:150px;"
        @keyup.enter.native="handleChange"
        clearable
      />
    </el-form-item>
    <el-form-item>
      <department-select
        v-model="search.department_ids"
        placeholder="搜索部门"
        :department-id="[]"
        size="small"
        :checkStrictly="true"
        multiple
        collapseTags
        filterable
        clearable
        selectStyle="width:250px;margin-right:0px;"
      />
    </el-form-item>
    <el-form-item>
      <el-select
        v-model="search.showBaselineLinks"
        placeholder="是否关联账号"
        size='small'
        style="width:150px;"
        clearable
      >
        <el-option
          label="已关联"
          value="true"
        />
        <el-option
          label="未关联"
          value="false"
        />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        size="small"
        @click="handleChange"
      >
        检索
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'

export default {
  components: {
    DepartmentSelect
  },
  data () {
    return {
      search: {
        showBaselineLinks:  null, // 只显示未关联账号的系统基线
        property:           'baseline_name',
        value:              null,
        department_ids:     []
      }
    }
  },
  methods: {
    handleChange () {
      this.$emit('change', this.$lodash.clone(this.search))
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form-item{
  margin-bottom: 0;
}
</style>
