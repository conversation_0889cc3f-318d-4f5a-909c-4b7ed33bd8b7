#!/usr/bin/env ruby

# 检查 Ruby 文件的结构
def check_ruby_structure(filename)
  content = File.read(filename)
  lines = content.split("\n")
  
  stack = []
  errors = []
  
  lines.each_with_index do |line, index|
    line_num = index + 1
    stripped = line.strip
    
    case stripped
    when /^(def|class|module|if|unless|case|while|until|for|begin)\b/
      keyword = $1
      stack.push({keyword: keyword, line: line_num, content: line.strip})
    when /^rescue\b/, /^ensure\b/, /^elsif\b/, /^else\b/, /^when\b/
      # 这些关键字不需要对应的 end，但需要有对应的开始
      if stack.empty?
        errors << "第 #{line_num} 行: #{stripped} 没有对应的开始语句"
      end
    when /^end\b/
      if stack.empty?
        errors << "第 #{line_num} 行: 多余的 'end'"
      else
        popped = stack.pop
        puts "匹配: #{popped[:keyword]} (第 #{popped[:line]} 行) -> end (第 #{line_num} 行)"
      end
    end
  end
  
  # 检查未关闭的语句
  stack.each do |item|
    errors << "第 #{item[:line]} 行: '#{item[:keyword]}' 没有对应的 'end' - #{item[:content]}"
  end
  
  if errors.empty?
    puts "✅ 结构检查通过！"
  else
    puts "❌ 发现结构错误："
    errors.each { |error| puts "  #{error}" }
  end
  
  errors.empty?
end

# 检查文件
puts "检查 Ruby 文件结构..."
check_ruby_structure('config/initializers/start_sidekiq.rb')
