import axios from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

// 更新Owner
export const updateDepartments = (code, departmentIds) => {
  return axios.put('/api/o32_option_configs/update_departments', { o32_option_config: { code: code, department_ids: departmentIds } })
}


// 更新操作人，o32_option_header
export const updateDepartment = (code, departmentId) => {
  return axios.put('/api/o32_option_configs/update_department', { o32_option_config: { code: code, department_id: departmentId } })
}

// 更新操作人
export const updateUser = (code, userId) => {
  return axios.put('/api/o32_option_configs/update_user', { o32_option_config: { code: code, user_id: userId } })
}

export const downloadTemplate = () => {
  return axios.get(`/api/o32_option_configs/download_template`,{
    responseType: 'blob'
  })
    .then(response => {
      const fileName = parseFileName(response, 'O32参数配置.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}