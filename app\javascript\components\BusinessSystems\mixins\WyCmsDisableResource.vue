<script>
// 禁用各种资源的方法，包括用户、机构、支行、角色
export default {
  data () {
    return {
      // 类型需要是复数，匹配 api
      resourceType: 'accounts',
      resourceMode:  'both' // disable 仅停用， both 同时支持停用和启用
    }
  },
  computed: {
    resourceDisplay () {
      switch (this.resourceType) {
        case 'accounts':
          return '用户'
        case 'organizations':
          return '机构'
        case 'branches':
          return '支行'
        case 'roles':
          return '角色'
      }
    },
    resourceUniqueId () {
      switch (this.resourceType) {
        case 'accounts':
          return 'user_id'
        case 'organizations':
          return 'org_id'
        case 'branches':
          return 'org_id'
        case 'roles':
          return 'id'
      }
    }
  },
  methods:  {
    disableButtonName (record) {
      if (this.resourceMode === 'disable') return '停用'

      if (record.status_string === '正常') {
        return '停用'
      } else {
        return '启用'
      }
    },
    disableAction (record) {
      if (this.resourceMode === 'disable') return 'disable'

      if (record.status_string === '正常') {
        return 'disable'
      } else {
        return 'enable'
      }
    },
    handleDisableResource (record) {
      console.log(record)
      console.log(23232)
      const message = `确认${this.disableButtonName(record)}该用户吗?`
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.operationReviewDisableResource(record)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })

    },
    operationReviewDisableResource (record) {
      this.$operationReview(this.systemId)
        .then(() => {
          this.disableResource(record)
        })
        .catch(() => {})
    },
    resourceRefresh () {
      // 在资源内中实现
    },
    disableResource (record) {
      const teller_seq         = record.teller_seq
      const teller_state     = this.disableAction(record)
      const actionName = this.disableButtonName(record)

      this.$axios.put(`/admin_api/edit_api/${this.systemId}/accounts/${teller_seq}`, {
        account: {
            teller_seq: teller_seq,
            system_id: this.systemId,
            teller_state: teller_state
          }
      })
        .then(response => {
          this.$message.success(`用户已${actionName}`)
          this.resourceRefresh()
        })
        .catch(() => {})
    }
  }
}
</script>
