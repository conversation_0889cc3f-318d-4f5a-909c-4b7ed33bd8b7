
<template>

  <div>
    <div class="quick-operator">
      <div
        style="margin-left: 50px;margin-right: 16px;"
        v-if="showOperator(fromData.category_code)"
      >快捷操作</div>
      <div>
        <el-button
          v-if="fromData.category_code === 'ledgers_stock'"
          size="small"
          :disabled="!$store.getters.hasPermission('ledgers.edit')"
          @click="handleChangeUser"
        >
          关联责任人
        </el-button>
        <el-button
          v-if="fromData.category_code === 'baseline_differences'"
          size="small"
          :disabled="!$store.getters.hasPermission('system_baseline.edit')"
          @click="confirmUpdateSystemBaseline"
        >
          更新基线
        </el-button>
        <el-button
          v-if="fromData.category_code === 'public_account_expiration'"
          size="small"
          :disabled="!$store.getters.hasPermission('public_accounts.edit')"
          @click="updatePublicAccount"
        >
          功能账号设置
        </el-button>
      </div>
    </div>
    <hr>
    <div>
      <el-form
        ref="fromData"
        :model="fromData"
        label-width="120px"
        class="demo-ruleForm"
        :rules="rules"
      >
        <el-form-item
          label='附件'
        >
          <el-col :span="20">
            <upload
              ref="upload"
              :alert="fromDatas"
              :maintain="maintain"
              @updateFromData="updateFromData"
            />
          </el-col>
        </el-form-item>
        <el-form-item
          label="处理状态"
          prop="deal_status"
        >
          <el-checkbox
            v-model="statusValue"
            :disabled="fromData.deal_status || !maintain || closeStatusValue"
          >
            {{ statusValue === true ? '已处理' :'未处理' }}
          </el-checkbox>
        </el-form-item>
        <el-form-item
            label="关闭告警"
            prop="close_status"
        >
          <el-checkbox
              v-model="closeStatusValue"
              :disabled="(recoverStatus !== 0) || !maintain || statusValue"
          >
            {{ closeStatusValue ? '已关闭' :'未关闭' }}
          </el-checkbox>
          <el-tooltip class="item" effect="dark" placement="right">
            <span><i class="el-icon-question"></i></span>
            <template slot="content">关闭告警，若问题未解决，会产生新的告警</template>
          </el-tooltip>
        </el-form-item>
        <el-form-item
            v-if="closeStatusValue"
            label="关闭原因"
            prop="close_reason"
            :rules="{
              required: true, message: '请输入关闭原因', trigger: 'change'
            }"
            :disabled="!maintain"
        >
          <el-col :span="20">
            <el-input
                v-model="fromData.close_reason"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 6}"
                placeholder="请输入关闭原因"
                :disabled="!maintain"
            />
          </el-col>
        </el-form-item>
        <el-form-item
          v-if="!closeStatusValue"
          label="是否屏蔽告警"
          prop="enable"
        >
          <el-switch
            v-model="fromData.enable"
            :disabled="!maintain"
          />
        </el-form-item>
        <el-form-item
          v-if="fromData.enable==true && !closeStatusValue"
          label="解除屏蔽时间"
          prop="enable_at"
        >
          <el-col :span="20">
            <el-date-picker
              v-model="fromData.enable_at"
              type="date"
              placeholder="选择日期"
              :picker-options="pickerOptions"
              @change="change"
              style="width: 100%;"
            />
          </el-col>
        </el-form-item>
        <el-form-item
          v-if="fromData.enable==true && !closeStatusValue"
          label="备注"
          prop="comment"
          :rules="{
            required: true, message: '请输入备注内容', trigger: 'change'
          }"
          :disabled="!maintain"
        >
          <el-col :span="20">
            <el-input
              v-model="fromData.comment"
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 6}"
              placeholder="请输入备注内容"
            />
          </el-col>
        </el-form-item>
        <template v-else>
          <el-form-item
              label="备注"
              prop="comment"
          >
            <el-col :span="20">
              <el-input
                  v-model="fromData.comment"
                  type="textarea"
                  :autosize="{ minRows: 4, maxRows: 6}"
                  placeholder="请输入备注内容"
                  :disabled="!maintain"
              />
            </el-col>
          </el-form-item>

        </template>

        <el-form-item>
          <el-button
            size="small"
            @click="cancelBtn"
          >
            取消
          </el-button>
          <el-button
            type="primary"
            size="small"
            :disabled="!maintain"
            @click="confirmBtn('fromData')"
          >
            确定
          </el-button>
        </el-form-item>
      </el-form>
      <user-change
        v-if="fromData.category_code === 'ledgers_stock'"
        :visible.sync="userChangeDiaLogVisible"
        :ledger="account"
        :system-id="fromData.bs_id"
        @update="handleUserChangeUpdate"
      />
      <public-account-form
        v-if="fromData.category_code === 'public_account_expiration'"
        ref="publicAccountEdit"
        :account="fromData.public_account"
        :system-id="fromData.bs_id"
        @update="handlePublicAccount"
      />
    </div>
  </div>
</template>

<script>
import upload from "./upload"
import UserChange from '@/components/AdminLedgers/UserChange.vue'
import PublicAccountForm from '@/components/AdminPublicAccounts/AccountForm.vue'
export default {
  components: {
    upload,
    UserChange,
    PublicAccountForm
  },
  props: {
    fromDatas: {
      type: Object,
      required: true
    },
  },
  data () {
    return {
      fromData: {
        category_code: '',
        account_name: '',
        account_code: '',
        sub_bs_name: '',
        deal_status: '',
        updated_at: '',
        enable: '',
        enable_at: '',
        prev_quarter_id: 0,
        bs_id: 0,
        account_id: 0,
        comment: '',
        close_status: '',
        close_reason: '',
        maintain: true,
        recover_status: 0,
        public_account: null
      },
      localFormData: {},
      statusValue: false,
      userChangeDiaLogVisible: false,
      closeStatusValue: false,
      recoverStatus: 0, // { not_recovered: 0, recovered: 1, closed: 2 }
      rules: {
        enable_at: [
          {  required: true, message: '请选择日期', trigger: 'change' }
        ]
      },
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now()
        },
        shortcuts: [{
          text: '明天',
          onClick (picker) {
            const date = new Date()
            date.setTime(date.getTime() + 3600 * 1000 * 24)
            picker.$emit('pick', date)
          }
        }, {
          text: '三天后',
          onClick (picker) {
            const date = new Date()
            date.setTime(date.getTime() + 3600 * 1000 * 24 * 3)
            picker.$emit('pick', date)
          }
        }, {
          text: '一周后',
          onClick (picker) {
            const date = new Date()
            date.setTime(date.getTime() + 3600 * 1000 * 24 * 7)
            picker.$emit('pick', date)
          }
        }, {
          text: '永久',
          onClick (picker) {
            const date = 'Thu Dec 31 2099 00:00:00 GMT+0800 (中国标准时间)'
            picker.$emit('pick', date)
          }
        }]
      }
    }
  },
  computed: {
    maintain() {
      return this.fromData.maintain
    },
    account () {
      return { account_code: this.fromData.account_code, user_id: this.fromData.user_id }
    }
  },
  watch: {
    fromDatas (newVal, oldVal) {
      this.localFormData = { ...newVal }
      this.fromData = newVal
      this.fromData.enable = !this.fromData.enable
      this.statusValue = this.fromData.deal_status
      this.closeStatusValue = this.fromData.close_status
      this.recoverStatus = this.fromData.recover_status
    },
    closeStatusValue(newVal, oldVal) {
      if(newVal) {
        this.resetFormField()
      }
    }
  },
  created () {
  },
  methods: {
    cancelBtn () {
      this.$emit('drawervals', this.fromDatas.id)
    },
    change () {
    },
    resetFormField() {
      this.fromData.enable = !this.localFormData.enable
      this.fromData.enable_at = this.localFormData.enable_at
      this.statusValue = this.localFormData.deal_status
    },
    confirmBtn (fromData) {
      this.$refs[fromData].validate((valid) => {
        if (valid) {
          if (this.closeStatusValue) {
            this.$confirm('您正在操作关闭告警，若问题未解决，会产生新的告警，是否继续？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.updateGlobalAlert()
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消处理'
              })
            })
          } else {
            this.updateGlobalAlert()
          }
        }
      })
    },
    updateGlobalAlert() {
      this.$axios.put(`admin_api/global_alerts/${this.fromDatas.id}`,
        {
          enable: !this.fromData.enable,
          enable_at: this.fromData.enable_at,
          comment: this.fromData.comment,
          deal_status: this.statusValue === false ? 0 : 1,
          close_status: this.closeStatusValue === false ? 0 : 1,
          close_reason: this.fromData.close_reason
        }
      ).then(res => {
        this.$emit('drawervals',  this.fromDatas.id)
        this.$emit('detailId', this.fromDatas.id)
      }).catch((err) => {
        console.log(err)
      })
      this.$message({
        type: 'success',
        message: '处理成功!'
      })
    },
    updateFromData () {
      this.$emit('handleEdit', this.fromDatas)
    },
    handleChangeUser () {
      this.userChangeDiaLogVisible = true
    },
    handleUserChangeUpdate (payload) {
      this.$emit('handleEdit', this.fromDatas)
    },
    updateSystemBaseline () {
      let theParams = { account_id: this.fromDatas.account_id }
      this.$axios.put(`/api/systems/${this.fromData.bs_id}/baselines/update_with_account`, theParams)
          .then(response => {
            if(response.data.success) {
              this.$message.success("更新系统基线成功")
            } else {
              this.$message.error("更新系统基线失败")
            }
          })
          .catch(error => {
            console.log(error)
          })
    },
    confirmUpdateSystemBaseline () {
      this.$confirm('确定更新基线吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            this.updateSystemBaseline()
          })
          .catch(() => {
            this.$message.info('已取消操作')
          })
    },
    updatePublicAccount () {
      this.$refs.publicAccountEdit.visible = true
    },
    handlePublicAccount () {
      console.log("handle public account edit")
    },
    showOperator(category_code) {
      const operatorCategoryCodes = ['ledgers_stock', 'baseline_differences', 'public_account_expiration']
      if (operatorCategoryCodes.includes(category_code)) {
        return true
      } else {
        return false
      }
    }
  }
}

</script>

<style scoped lang="scss">
/deep/ .el-upload-list__item {
  transition: none !important;
}
@import "~@/components/variables";
.quick-operator{
  @include vertical_center_left;
}
</style>
