# frozen_string_literal: true

# 后台账号管理审计日志
class SystemScaffoldAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '自定义业务系统管理'
    @operation_category = '自定义业务系统管理'
    super
  end

  def update
    @operation = '编辑业务系统'
    @comment   = "更新了业务系统 名称为：「#{params.name}」, 厂商为：「#{params.company}」, 版本为：「#{params.version}」，ID为：「#{params.id}」, 前缀为：「#{params.prefix}」, 维度数为：「#{params.schemas.count}」"
    create_audit_log
  end

  def create
    operating_description = params.state == 'unpublished' ? '暂存' : '创建'
    @operation = "#{operating_description}业务系统"
    @comment   = "#{operating_description}了业务系统 名称为：「#{params.name}」, 厂商为：「#{params.company}」, 版本为：「#{params.version}」, ID为：「#{params.id}」, 前缀为：「#{params.prefix}」, 维度数为：「#{params.schemas.count}」"
    create_audit_log
  end

  def destroy
    @operation = '删除业务系统'
    @comment   = "删除了业务系统 名称为：「#{params.name}」, 厂商为：「#{params.company}」, 版本为：「#{params.version}」, ID为：「#{params.id}」, 前缀为：「#{params.prefix}」, 维度数为：「#{params.schemas.count}」"
    create_audit_log
  end

  def import_success
    @operation = '导入业务系统'
    @comment   = "导入业务系统成功 名称为：「#{params.name}」, 厂商为：「#{params.company}」, 版本为：「#{params.version}」, ID为：「#{params.id}」, 前缀为：「#{params.prefix}」, 维度数为：「#{params.schemas.count}」"
    create_audit_log
  end

  def import_faild
    @operation = '导入业务系统'
    @comment   = "导入业务系统失败 名称为：「#{params.name}」, 厂商为：「#{params.company}」, 版本为：「#{params.version}」, ID为：「#{params.id}」, 前缀为：「#{params.prefix}」, 维度数为：「#{params.schemas.count}」"
    create_audit_log
  end

  def export_success
    @operation = '导出业务系统'
    @comment   = "导出业务系统成功 名称为：「#{params.name}」, 厂商为：「#{params.company}」, 版本为：「#{params.version}」, ID为：「#{params.id}」, 前缀为：「#{params.prefix}」, 维度数为：「#{params.schemas.count}」"
    create_audit_log
  end

  def export_faild
    @operation = '导出业务系统'
    @comment   = "导出业务系统失败 名称为：「#{params.name}」, 厂商为：「#{params.company}」, 版本为：「#{params.version}」, ID为：「#{params.id}」, 前缀为：「#{params.prefix}」, 维度数为：「#{params.schemas.count}」"
    create_audit_log
  end
end
