<template>
  <div class="container">
    <el-tabs
      v-model="tab"
      tabPosition="top"
    >
      <el-tab-pane
        v-for="system in systems"
        :key="system.id"
        :name="system.id.toString()"
        :label="system.name"
      >
      </el-tab-pane>
      <system-accounts :systemId="systemId" />

    </el-tabs>
  </div>
</template>

<script>
import SystemAccounts from './SystemAccounts/index'

export default {
  components: {
    SystemAccounts
  },
  data () {
    return {
      systems: [],
      tab: null
    }
  },
  computed: {
    systemId () {
      return Number(this.tab)
    }
  },
  created () {
    this.getSystems()
  },
  methods: {
    getSystems () {
      this.$axios.get('/api/systems/systems_in_maintain')
        .then(response => {
          this.systems = response.data
          let firstSystem = this.systems[0]
          if (firstSystem) this.tab = firstSystem.id.toString()
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .container{
    padding: 20px;
  }
</style>
<style>
  .el-tabs__nav {
    overflow-y: hidden;
  }
</style>