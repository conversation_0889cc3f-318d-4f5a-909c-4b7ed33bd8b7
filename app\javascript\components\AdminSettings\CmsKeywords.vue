<template>
  <div
    v-loading="loading"
    class="container"
  >
    <div class="tool-bar">
      <el-input
        v-model="query"
        placeholder="搜索机构编号或名称"
        prefix-icon="el-icon-search"
        clearable
        style="width: 250px"
        size="small"
      />
    </div>
    <el-table
      :data="dataByPage"
      :defaultSort="{ prop: 'code', order: 'ascending' }"
      border
      stripe
    >
      <el-table-column
        sortable
        property="code"
        label="机构编号"
        width="105"
      />
      <el-table-column
        sortable
        property="main_frame_org_id"
        label="网点号"
        width="95"
      />
      <el-table-column
        sortable
        property="org_type"
        label="机构类型"
        width="105"
      >
        <template slot-scope="scope">
          <span v-show="scope.row.org_type === 'org'">机构</span>
          <span v-show="scope.row.org_type === 'branch'">支行</span>
        </template>
      </el-table-column>
      <el-table-column
        sortable
        property="full_name"
        label="机构名称"
      />
      <el-table-column
        sortable
        property="org_keyword"
        label="机构关键字"
      >
        <template slot-scope="scope">
          <el-input
            v-if="scope.row.edit"
            v-model="scope.row.org_keyword"
            size="small"
          />
          </el-input>
          <span v-else>{{ scope.row.org_keyword }}</span>
        </template>
      </el-table-column>
      <el-table-column
        sortable
        property="branch_keyword"
        label="支行关键字"
      >
        <template slot-scope="scope">
          <el-input
            v-if="scope.row.edit && scope.row.org_type === 'branch'"
            v-model="scope.row.branch_keyword"
            size="small"
          />
          </el-input>
          <span v-else>{{ scope.row.branch_keyword }}</span>
        </template>
      </el-table-column>
      <el-table-column
        sortable
        property="status"
        label="状态"
        width="75"
      >
        <template slot-scope="scope">
          <span v-show="scope.row.status === 'locked'">锁定</span>
          <span v-show="scope.row.status === 'enable'">正常</span>
          <span v-show="scope.row.status === 'disable'">停用</span>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="150"
      >
        <template slot-scope="scope">

          <el-button
            v-if="scope.row.edit"
            size="mini"
            type="primary"
            @click="keywordEdit(scope.row)"
          >
            确定
          </el-button>
          <el-button
            v-if="scope.row.edit"
            size="mini"
            @click="getKeywords"
          >
            取消
          </el-button>
          <el-button
            v-if="!scope.row.edit"
            size="mini"
            @click="scope.row.edit = true"
          >
            修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :pageSize="pageSize"
      :total="dataByQuery.length"
      :currentPage.sync="currentPage"
      background
      layout="prev, pager, next"
      style="margin-top: 20px;"
    />
  </div>
</template>

<script>
export default {
  name:     'CmsKeywordsVue',
  data () {
    return {
      keywords:    [],
      loading:     false,
      pageSize:    25,
      currentPage: 1,
      query:       ''
    }
  },
  computed: {
    dataByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.dataByQuery.slice(start, end)
    },
    dataByQuery () {
      if (!this.query) return this.keywords

      let escapeCode = this.$lodash.escapeRegExp(this.query)
      const query = new RegExp(escapeCode)
      return this.keywords.filter(x => x.full_name.match(query) || x.code.match(query))
    }
  },
  created () {
    this.getKeywords()
  },
  methods:  {
    getKeywords () {
      this.loading = true
      this.$axios.get('/api/cms_keywords')
        .then(response => {
          this.loading  = false
          this.keywords = response.data.map(x => Object.assign(x, { edit: false }))
        })
        .catch(() => {
          this.loading = false
        })
    },
    keywordEdit (row) {
      this.$axios.put(`/api/cms_keywords/${row.id}`, { keyword: row })
        .then(response => {
          row.edit = false
          this.$message.success('关键字已成功更新')

        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  .tool-bar {
    margin-bottom: 20px;
  }
</style>
