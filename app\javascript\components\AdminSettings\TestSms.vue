<template>
  <div>
    <el-button
      size="small"
      @click="dialogVisible = true"
    >
      短信发送测试
    </el-button>
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="700px"
      title="发送短信测试"
    >
      <el-form
        :model="form"
        v-loading="loading"
        ref="sms_form"
        label-width="100px"
      >
        <el-form-item
          :rules="rules.mobile"
          prop="mobile"
          label="手机号"
        >
          <el-input
            v-model="form.mobile"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item
          :rules="rules.content"
          prop="content"
          label="短信内容"
        >
          <el-input
            :rows="2"
            type="textarea"
            v-model="form.content"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item
          v-show="error"
          prop="error"
          label="错误消息"
        >
          <div class="error">
            {{ error }}
          </div>
        </el-form-item>
        <el-form-item
          v-show="message"
          prop="message"
          label="调试消息"
        >
          {{ message }}
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">
          关闭
        </el-button>
        <el-button
          type="primary"
          @click="sendTestSms"
        >
          发送
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { validatePhone } from '@/utils/form_validates'

export default {
  props: {
  },
  data () {
    return {
      dialogVisible: false,
      testSms:       null,
      error:         null,
      message:       null,
      loading:       false,
      form: {
        mobile:      null,
        content:     null
      },
      rules: {
        content: [
          { required: true, message: '请输入短信内容', trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {

  },
  methods: {
    sendTestSms () {
      this.$refs.sms_form.validate((valid) => {
        if(valid){
          this.loading = true
          this.$axios.post('admin_api/settings/notification/test_sms', {
            mobile: this.form.mobile,
            content: this.form.content
          })
            .then(response => {
              this.loading = false
              this.error   = response.data.error
              this.message = response.data.message
              if (response.data.success) {
                this.$message.success('短信已发送')
              } else {
                this.$message.error('短信发送失败')
              }
            })
            .catch(() => {
              this.$message.error('短信发送失败')
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .error {
    color: #E54D42;
    font-weight: bold;
  }

  .el-form-item .el-form-item {
    margin-bottom: 22px;
  }
</style>
