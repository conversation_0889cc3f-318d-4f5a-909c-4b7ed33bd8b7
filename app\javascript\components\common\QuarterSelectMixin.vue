<script>
export default {
  model: {
    prop: 'quarterId',
    event: 'change'
  },
  props: {
    quarterId: {
      validator: function (val) {
        return val === null || typeof val === 'number'
      },
      required: true
    },
    defaultSelect: {
      type: String,
      default: 'first' // none / first / second
    },
    filterable: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'normal'
    },
    currentQuarterId: {
      type: Number,
      default: 0
    },
    excludeCurrentQuarter: {
      type: Boolean,
      default: false
    },
    onlySelectLessThanCurrent: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isEmpty () {
      return this.quarters.length === 0
    },
    selectQuarters () {
      if (this.excludeCurrentQuarter) {
        return this.quarters.filter(x => x.id !== this.currentQuarterId)
      }
      if (this.onlySelectLessThanCurrent) {
        return this.quarters.filter(x => x.id < this.currentQuarterId)
      }
      return this.quarters
    },
    reloadQuarter () {
      return this.$store.state.reloadQuarter
    }
  },
  watch: {
    reloadQuarter () {
      if (this.reloadQuarter) this.getQuarters()
    }
  },
  created () {
    this.getQuarters()
  },
  methods: {
    selectDefaultQuarter () {
      if (this.selectQuarters.length === 0) return

      if (this.defaultSelect === 'none') return

      if (this.selectQuarters.length === 1) {
        this.quarterConvert(this.selectQuarters[0])
        return
      }

      let nextQuarter = null
      switch (this.defaultSelect) {
        case 'first':
          this.quarterConvert(this.selectQuarters[0])
          break
        case 'second':
          this.quarterConvert(this.selectQuarters[1])
          break
        case 'next':
          nextQuarter = this.findNextQuarter()
          if (nextQuarter) this.quarterConvert(nextQuarter)
          break
      }
    },
    getQuarters () {
      this.$axios.get('/api/quarters')
        .then(response => {
          if (this.reloadQuarter) this.$store.commit('disableReloadQuarter')


          this.quarters = response.data
          if (this.quarters.length === 0){
            this.$store.state.current_quarter = {
             id:   0,
             name: '未发现时间点'
            }
          }
          this.selectDefaultQuarter()
        })
        .catch(error => {
          this.quarters = [{ name: 'Read data error!' }]
          switch (error.status) {
            case 401:
              break
            default:
              this.$message.error('获取时间列表失败')
          }
        })
    },
    quarterConvert (quarter) {
      // 此方法应当被子类替换
    },
    findNextQuarter () {
      const currentQuarterIndex = this.quarters.findIndex(x => x.id === this.currentQuarterId)
      return this.quarters[currentQuarterIndex + 1]
    }
  }
}
</script>
