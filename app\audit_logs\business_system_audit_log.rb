# frozen_string_literal: true

# 后台账号管理审计日志
class BusinessSystemAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '业务分组管理'
    super
  end

  def category_create
    set_account_category
    @operation = '创建'
    @comment   = "创建 分组：#{params[0]}; 业务系统：#{params[1].join(', ')}；上级分组：#{params[2]}"
    create_audit_log
  end

  def category_update
    set_account_category
    @operation = '更新'
    @comment   = "更新 分组：#{params[0]}; 业务系统：#{params[1].join(', ')}；上级分组：#{params[2]}"
    create_audit_log
  end

  def category_destroy
    set_account_category
    @operation = '删除'
    @comment   = "删除 分组：#{params}"
    create_audit_log
  end

  def update
    set_system_category
    @operation = '更新'
    @comment   = "更新：#{params[0]}, 分组为：#{params[1]}"
    create_audit_log
  end

  def disable
    set_system_category
    @operation = '禁用'
    @comment   = "禁用：#{params.name}"
    create_audit_log
  end

  def enable
    set_system_category
    @operation = '启用'
    @comment   = "启用：#{params.name}"
    create_audit_log
  end

  def export_batch_success
    set_operation
    @operation = '账号数据导出'
    @comment   = "成功导出「#{params[:quarter_name]}」的账号：「#{params[:content]}」的「#{params[:file_names]&.join("，")}」数据"
    create_audit_log
  end

  def export_batch_faild
    set_operation
    @operation = '账号数据导出'
    @comment   = "「#{params[:quarter_name]}」的账号「#{params[:content]}」,的「#{params[:file_names]&.join("，")}」数据导出失败"
    create_audit_log
  end

  def export_role_batch_success
    set_role_operation
    @operation = '角色数据导出'
    @comment   = "成功导出「#{params[:quarter_name]}」的角色：「#{params[:content]}」的数据"
    create_audit_log
  end

  def export_role_batch_faild
    set_role_operation
    @operation = '角色数据导出'
    @comment   = "「#{params[:quarter_name]}」的角色「#{params[:content]}」的数据导出失败"
    create_audit_log
  end

  private

  def file_names
    params.flatten.uniq.map { |file| File.basename(file.to_s) }
  end

  def set_operation
    @operation_module = '系统账号管理'
    @operation_category = '账号管理'
  end

  def set_role_operation
    @operation_module = '系统角色管理'
    @operation_category = '角色管理'
  end

  def set_account_category
    @operation_category = '分组管理'
  end

  def set_system_category
    @operation_category = '业务系统管理'
  end
end
