<template>
  <div class="matcher-container">
    <el-button
      type="primary"
      @click="clickAIMatchBtn"
      size='small'
      :disabled="!$store.getters.hasPermission('ledgers.edit')"
    >
      智能匹配
    </el-button>
    <el-dialog
      :visible.sync="dialogVisible"
      :beforeClose="closeDialog"
      title="智能匹配信息"
      width="70%"
    >
      <template class="table-container">
        <ai-table
          v-loading="loading"
          ref="aiMatchTable"
          :systemId="systemId"
          :tableData="ledgers"
          @updateRow="updateRow"
        />
      </template>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="clickLinkBatch"
        >
          批量关联
        </el-button>
        <el-button @click="closeDialog">关  闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import AiTable from './AIMatchTable.vue'

export default {
  components: {
    AiTable
  },
  props: {
    systemId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      dialogVisible: false,
      loading: false,
      ledgers: [],
      isRowChange: false // 检查是否有单条更新操作，通知父组件
    }
  },
  methods: {
    getAIData () {
      this.loading = true
      this.$axios.get(`/admin_api/ledgers/bs/${this.systemId}/matchers`)
        .then(response => {
          for (const item of response.data) {
            item.isLinked = false
          }
          this.ledgers = response.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    linkBatch () {
      let selection = this.$refs.aiMatchTable.selection

      if (selection.length === 0) {
        this.$message.success('您还未选择任何员工')
        return
      }
      selection.forEach(x => { x.isLinked = true })

      this.$axios.post(`/admin_api/ledgers/bs/${this.systemId}/accounts/bulk`, selection)
        .then(response => {
          if (response.status === 200) {
            this.$emit('dataUpdate')
            this.$message.success('批量关联成功')
            this.$refs.aiMatchTable.clearSelection()
          }
        })
        .catch(() => {})
    },
    clickAIMatchBtn () {
      this.dialogVisible = true
      this.getAIData()
    },
    closeDialog () {
      if (this.isRowChange) this.$emit('dataUpdate')
      this.dialogVisible = false
    },
    clickLinkBatch () {
      this.linkBatch()
    },
    updateRow (row) {
      this.isRowChange = true
      let index = this.ledgers.findIndex(x => x.account_code === row.account_code)
      this.ledgers.splice(index, 1, row)
    }
  }
}
</script>

<style lang="scss" scoped>

  .new-department{
    margin-bottom: 2em;
    width: 15%
  }
  .table-container{
    padding: 1em;
  }

</style>
