#### 附加禅道的需求链接或 Bug 链接

- (禅道需求链接 1)

#### 问题描述

- [ ] 「MR 标题」是否清楚描述了这个 MR 解决了什么问题? 
- [ ] 「禅道」备注中是否附上了该 MR 的链接? 

#### 代码自审

- [ ] 对于复杂逻辑，代码中是否添加了足够清晰的注释和单元测试？
- [ ] 后端代码是否通过了 Rubocop 验证？
- [ ] 前端代码是否通过了 EsLint 验证？

#### BUG 相关（如不是可删除下方内容）

- [ ] 「禅道」是否标注了产生 BUG 的代码在何时引入，并影响到哪些系统版本？
- [ ] 「禅道」是否描述了这个 BUG 当前的错误行为是什么？
- [ ] 「禅道」是否描述了这个 BUG 预期的正确行为是什么？
- [ ] 「禅道」是否附上了相关的日志或截图？

#### 前端样式调整（如不是可删除下方内容）

涉及到前端样式调整的 MR，是否已在禅道需求中附上了调整后的样式在不同分辨率中的截图？

- [ ] 笔记本分辨率截图（1366x768）
- [ ] 大分辨率截图（1920x1080）

#### 功能配置开关（如不是可删除下方内容）

- [ ] 如果增加了配置项开关，是否在`app.yml.example` 中增加了相关说明？
- [ ] 是否在涉及到开关的客户配置中进行了启用？
- [ ] 是否在未添加该开关的客户中已测试不会出现错误？

#### 功能模块（如不是可删除下方内容）

- [ ] 「禅道」如果是功能模块，是否已附上相关的技术方案或逻辑说明？

#### 公共接口或代码（如不是可删除下方内容）

- [ ] 「禅道」如果涉及到公共接口或代码，是否添加了因代码需要测试的功能范围？

