<template>
  <div>
    <el-form @submit.native.prevent>
      <el-form-item>
        <el-input
          v-model="filterText"
          placeholder="输入系统名称进行过滤"
          clearable
          size="small"
          prefix-icon="el-icon-search"
          style="width: 300px;"
        />
        <el-checkbox
          v-model="isSelected"
          style="padding-left: 24px;"
        >
          已选中
        </el-checkbox>
      </el-form-item>
      <el-divider class="page-divider" />
      <el-form-item>
        <el-checkbox
          v-model="allSystemsQuery"
          :disabled="!isAllSystemsQuery"
        >
          所有系统查询（自动添加新增系统）
        </el-checkbox>
        <el-checkbox
          v-model="allSystemsMaintain"
          :disabled="!isAllSystemsMaintain"
        >
          所有系统操作（自动添加新增系统）
        </el-checkbox>
      </el-form-item>
    </el-form>
    <el-table
      ref="systemTable"
      :data="filterShowTree"
      row-key="id"
      border
      style="width: 100%"
      default-expand-all
      :tree-props="{children: 'children'}">
    >
      <el-table-column
        prop="name"
        label="系统分类"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
          <span style="float: right;">
            <span
              v-if="displaySelect(scope.row)"
              style="margin-right: 5px;"
            >
              <el-checkbox
                v-model="scope.row.all_query"
                :disabled="allSystemsQuery || isTopObjectQuery(scope.row.id)"
              />
              查询
            </span>
            <span v-if="displaySelect(scope.row)">
              <el-checkbox
                v-model="scope.row.all_maintain"
                :disabled="allSystemsMaintain || isTopObjectMaintain(scope.row.id)"
              />
              操作
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="系统名称"
      >
        <template slot-scope="scope">
          <div
            v-for="item in scope.row.business_systems.filter(x => filterText == '' || x.name.indexOf(filterText) > -1)"
            :key="item.id"
          >
            <div class="between">
              <div><span>{{ item.name }}</span></div>
              <div>
                <span>
                  <el-checkbox
                    v-model="item.query"
                    :disabled="allSystemsQuery || !(item.permission.is_query || isAllSystemsQuery) || scope.row.all_query || isTopObjectQuery(scope.row.id)"
                  />
                  查询
                </span>
                <span>
                  <el-checkbox
                    v-model="item.maintain"
                    :disabled="allSystemsMaintain || !(item.permission.is_maintain || isAllSystemsMaintain) || scope.row.all_maintain || isTopObjectMaintain(scope.row.id)"
                  />
                  操作
                </span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import API from '@/api'

export default {
  props: {
    admin: {
      type:     Object,
      required: true
    },
    flag: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      sysTypes: [],
      permissions: [
        {
          id: 1,
          name: '查看'
        },
        {
          id: 2,
          name: '操作'
        }
      ],
      allSystemsQuery: false,
      allSystemsMaintain: false,
      isAllSystemsQuery: false,
      isAllSystemsMaintain: false,
      filterText: '',
      showTreeIds: [],
      showTree: [],
      openTreeIds: [],
      allChildren: {},
      isSelected: false
    }
  },
  computed: {
    // 过滤已选中的tree数据
    filterShowTree() {
      if (!this.isSelected) return this.showTree
      // 递归过滤节点
      const filterNode = (node) => {
        // 检查当前节点或其 children 是否符合条件
        const hasChildren = node.children && node.children.length > 0;
        const filteredChildren = hasChildren
          ? node.children.map(filterNode).filter((child) => child !== null)
          : [];

        // 检查 business_systems 数组中符合条件的对象
        const filteredBusinessSystems = node.business_systems.filter(
          (system) => node.all_query || node.all_maintain || system.query || system.maintain
        );

        // 节点是否需要保留
        const isValid =
          node.all_query ||
          node.all_maintain ||
          filteredChildren.length > 0 ||
          filteredBusinessSystems.length > 0;

        // 如果符合条件，返回一个新的对象，否则返回 null
        if (isValid) {
          return {
            ...node,
            children: filteredChildren,
            business_systems: filteredBusinessSystems,
          };
        } else {
          return null;
        }
      };

      // 对数据数组进行过滤
      return this.showTree.map(filterNode).filter((node) => node !== null);
    }
  },
  watch: {
    admin: {
      handler (admin) {
        this.gettingSystems()
      }
    },
    // 切换管理员dialog时，选中状态要重置
    'admin.id'(newId, oldId) {
      this.isSelected = false
    },
    flag: {
      deep: true,
      immediate: true,
      handler (newVal) {
        const params = {
          all_systems_query: this.allSystemsQuery,
          all_systems_maintain: this.allSystemsMaintain,
          systems_permissions: this.getSystemsPermissions(this.showTree),
          systems_category_permissions: this.getSystemsCategoryPermissions(this.showTree)
        }
        this.$emit('systems', params)
      }
    },
    filterText() {
      this.startFilter()
      this.openTreeIds = this.filterText == "" ? [] : this.showTreeIds.map(x=> 'category-'+x)
    }
  },
  created () {
    this.gettingSystems()
  },
  methods: {
    // 递归方法，用于在树形结构中查找节点并返回顶级对象
    // 该方法会返回当前targetId对应的对象
    findTopLevelObject(tree, targetId, parentId = null) {
      for (const node of tree) {
        // 如果找到了目标节点
        if (node.id === targetId) {
          node.parent_id = parentId; // 添加 parent_id
          return node;
        }

        // 如果有子节点，递归查找
        if (node.children && node.children.length > 0) {
          const result = this.findTopLevelObject(node.children, targetId, node.id);
          if (result) {
            node.parent_id = parentId; // 为当前节点添加 parent_id
            return node; // 返回最顶级对象
          }
        }
      }
      return null; // 未找到目标节点
    },
    // 树形结构中查找节点并返回顶级对象
    findTopObject(tree, targetId, parentId = null) {
      const node = this.findTopLevelObject(tree, targetId, parentId = null)
      if (node.id === targetId) {
        return null
      } else {
        return node
      }
    },
    // row顶级对象是否有query权限
    isTopObjectQuery(id) {
      const topNode = this.findTopObject(this.showTree, id)
      if (API.tool.isBlank(topNode)) {
        return false
      }
      return topNode.all_query
    },
    // row顶级对象是否有maintain权限
    isTopObjectMaintain(id) {
      const topNode = this.findTopObject(this.showTree, id)
      if (API.tool.isBlank(topNode)) {
        return false
      }
      return topNode.all_maintain
    },
    startFilter() {
      let filterData
      this.showTreeIds = []
      this.showTree = []
      this.scanTree(this.allSystems)
      filterData = JSON.parse(JSON.stringify(this.allSystems));
      filterData = filterData.filter(x=> this.showTreeIds.indexOf(x.id) > -1)
      filterData.forEach( x => {
        x.children = x.children.filter(y=> this.showTreeIds.indexOf(y.id) > -1)
        this.showTree.push(x)
      })
    },
    verifyTree (data) {
      if (this.filterText == "" || data.business_systems.filter(x => x.name.indexOf(this.filterText) > -1).length > 0) {
        this.showTreeIds.push(data.id)
        let parentTree = this.allChildren[data.parent_id]
        while(parentTree){
          this.showTreeIds.push(parentTree.id)
          parentTree = this.allChildren[parentTree.parent_id]
        }
      }
      if (data.children.length > 0){
        this.scanTree(data.children)
      }
    },
    scanTree (data) {
      data.forEach((item, index) => {
        this.allChildren[item.id] = { id : item.id, parent_id : item.parent_id }
        this.verifyTree(item)
      })
    },
    filterData(data) {
      return data.map(item => {
        let filteredItem = {
          ...item,
          business_systems: this.filterBusinessSystems(item.business_systems),
          children: this.filterData(item.children)
        };
        return filteredItem;
      }).filter(item => {
        return item.business_systems.length > 0 || item.children.length > 0;
      });
    },
    filterBusinessSystems(businessSystems) {
      return businessSystems.filter(sys => sys.name.includes(this.searchName));
    },
    gettingSystems () {
      this.$axios.get('/api/systems/inservices', { params: { filter: '',
        admin_id: this.admin.id } })
        .then(response => {
          this.loading = false
          this.allSystems = response.data.data
          this.allSystemsQuery = response.data.all_systems_query
          this.allSystemsMaintain = response.data.all_systems_maintain
          this.startFilter()
          this.gettingAdminSystems()
        })
        .catch(() => {
          this.loading = false
        })
    },
    gettingAdminSystems () {
      this.loading = false
      API.adminPermissions.getAdminSystems(this.admin.id)
        .then(response => {
          this.loading = false
          this.allSystemsQuery = response.data.all_systems_query
          this.allSystemsMaintain = response.data.all_systems_maintain
          this.isAllSystemsQuery = response.data.is_all_systems_query
          this.isAllSystemsMaintain = response.data.is_all_systems_maintain
        })
        .catch(() => {
          this.loading = false
        })
    },
    groupSelectQuery(groupData) {
      groupData.business_systems.forEach(bs => {
        if(groupData.all_query) {
          bs.query = true
        } else {
          bs.query = false
        }
      })
    },
    groupSelectMaintain(groupData) {
      groupData.business_systems.forEach(bs => {
        if(groupData.all_maintain) {
          bs.maintain = true
        } else {
          bs.maintain = false
        }
      })
    },
    getSystemsPermissions(data) {
      let businessSystems = []

      function traverse(nodes) {
        for (const node of nodes) {
          if (node.business_systems) {
            for (const system of node.business_systems) {
              const { id, query, maintain } = system
              businessSystems.push({id: id, permission: { query: query, maintain: maintain }})
            }
          }
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        }
      }

      traverse(data)
      return businessSystems
    },
    getSystemsCategoryPermissions(data) {
      let businessSystemCategories = []

      function traverse(nodes) {
        for (const node of nodes) {
          if (node.id !== 0) {
            businessSystemCategories.push({ id: node.id, permission: { query: node.all_query, maintain: node.all_maintain }})
          }
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        }
      }
      traverse(data)
      return businessSystemCategories
    },
    displaySelect (row) {
      return row.id !== 0
    }
  }
}

</script>
<style lang="scss" scoped>
  @import "~@/components/variables";
  .systems-pagination{
    margin-top: 20px;
  }
  .el-divider--horizontal {
    display: block;
    height: 1px;
    width: 100%;
    margin: 10px 0px;
  }
  .between {
    @include vertical_top_between;
  }
  .right {
    @include vertical_top_between;
  }
</style>
