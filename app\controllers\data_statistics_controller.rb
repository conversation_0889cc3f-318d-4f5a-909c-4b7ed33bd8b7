# frozen_string_literal: true

# 数据统计
class DataStatisticsController < ApplicationController
  before_action :authenticate_admin!
  before_action :quarters, only: %i[employee_total employee_new employee_reduce]
  before_action :system_quarters, only: %i[account_total account_new account_reduce]
  before_action :business_systems, only: %i[alarm_ledger alarm_baseline alarm_dimission]
  before_action :authenticate_policy!, only: %i[employee_total employee_new employee_reduce account_total account_new account_reduce]

  # TODO: 这里在前端界面应当能支持多级部门列表的情形，通过上级部门进行分类
  def department
    department_datas       = DataStatistic.user_quantity
    group_department_datas = department_datas.in_groups_of(8).map(&:compact)
    data                   = []
    group_department_datas.each_with_index do |obj, index|
      data << { id: index.succ, name: "分组#{index.succ}", children: obj }
    end
    json_respond data
  end

  # 在职员工统计
  def employee_total
    data = @quarters.map do |quarter|
      { name: quarter.name, value: DataStatistic.total_employee_quantity(quarter) }
    end
    data = data.reject { |obj| obj[:value].nil? }
    json_respond data
  end

  # 新员工统计
  def employee_new
    json_respond([]) if @quarters.blank?
    data = @quarters.map do |quarter|
      { name: quarter.name, value: DataStatistic.new_employee_quantity(quarter) }
    end
    data = data.reject { |obj| obj[:value].nil? }
    json_respond data
  end

  # 离职员工统计
  def employee_reduce
    json_respond([]) if @quarters.blank?
    data = @quarters.map do |quarter|
      { name: quarter.name, value: DataStatistic.reduce_employee_quantity(quarter) }
    end
    data = data.reject { |obj| obj[:value].nil? }
    json_respond data
  end

  # 正常状态账号总数统计
  def account_total
    data = @quarters.map do |quarter|
      # system_id为0，表示选择全部
      if params[:system_id].to_i == 0
        value = DataStatistic::QuarterAccountTotalCount.get(quarter.id)
      else
        value = DataStatistic.total_account_quantity(params[:system_id], quarter)
      end
      { name: quarter.name, value: value }
    end
    json_respond data
  end

  # 新增账号统计
  def account_new
    data = @quarters.map do |quarter|
      # system_id为0，表示选择全部
      if params[:system_id].to_i == 0
        value = DataStatistic::QuarterAccountNewCount.get(quarter.id)
      else
        value = DataStatistic.new_account_quantity(params[:system_id], quarter)
      end
      { name: quarter.name, value: value }
    end
    data = data.reject { |obj| obj[:value].nil? }
    json_respond data
  end

  # 禁用账号统计
  def account_reduce
    data = @quarters.map do |quarter|
      if params[:system_id].to_i == 0
        value = DataStatistic::QuarterAccountReduceCount.get(quarter.id)
      else
        value = DataStatistic.reduce_account_quantity(params[:system_id], quarter)
      end
      { name: quarter.name, value: value }
    end
    data = data.reject { |obj| obj[:value].nil? }
    json_respond data
  end

  # 未关联台账
  def alarm_ledger
    data = @systems.map do |the_system|
      { name: the_system.name, value: DataStatistic.alarm_ledger_quantity(the_system) }
    end
    data = data.select { |obj| obj[:value].positive? }
    json_respond data
  end

  # 基线权限未匹配
  def alarm_baseline
    data = @systems.map do |the_system|
      { name: the_system.name, value: DataStatistic.alarm_baseline_quantity(the_system, params[:quarter_id]) }
    end
    data = data.select { |obj| obj[:value].positive? }
    json_respond data
  end

  # 离职未禁用
  def alarm_dimission
    data = @systems.map do |the_system|
      { name: the_system.name, value: DataStatistic.alarm_dimission_quantity(the_system, params[:quarter_id]) }
    end
    data = data.select { |obj| obj[:value].positive? }
    json_respond data
  end

  protected

  def authenticate_policy!
    authorize nil, policy_class: DataStatisticsPolicy
  end

  def business_systems
    @systems = BusinessSystem.inservice
  end

  # 获取当前时间点最近的10个时间点
  def quarters
    set_quarter
    @quarters = DataStatistic.relation_quarters(@quarter, 15)
  end

  def system_quarters
    set_quarter
    if params[:system_id].to_i == 0
      # 传过来的system_id为0时，表示全部，获取最近的quarters，传system_id为nil
      @quarters = DataStatistic.relation_quarters(@quarter, 15, nil)
    else
      @quarters = DataStatistic.relation_quarters(@quarter, 15, params[:system_id].to_i)
    end
  end
end
