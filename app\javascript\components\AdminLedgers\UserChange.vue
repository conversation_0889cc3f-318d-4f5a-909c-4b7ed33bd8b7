<template>
  <!-- eslint-disable vue/attribute-hyphenation -->
  <el-dialog
    v-loading="loading"
    :visible.sync="localVisible"
    :close-on-click-modal="false"
    width="45%"
    top="8em"
    title="请选择要关联的员工"
    append-to-body
    @open="handleOpen"
    @close="handleClose"
  >
    <template>
      <el-cascader
        v-model="selectData"
        :options="userTree"
        filterable
        class="user-cascader"
        @change="handleChange"
      />
    </template>
    <template>
      <user-info
        v-if="userId && showUserDetail"
        ref="userDetail"
        :userId="userId"
        border
        style="margin-top: 2em"
        @get="getUserDetail"
      />
    </template>

    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="localVisible = false">取 消</el-button>
      <el-button
        type="primary"
        @click="createLink"
      >
        关联员工
      </el-button>
    </span>
  </el-dialog>
  <!-- eslint-enable vue/attribute-hyphenation -->
</template>

<script>
import UserInfo      from './UserInfo.vue'
import API           from '@/api'

export default {
  components: {
    UserInfo
  },
  props:      {
    visible:  {
      type:     Boolean,
      required: true
    },
    ledger:   {
      type:     Object,
      required: true
    },
    systemId: {
      type:     Number,
      required: true
    }
  },
  data () {
    return {
      userTree:      [],
      selectData:    [],
      localVisible:  false,
      userId:        null,
      loading:       false,
      showUserDetail: false,
    }
  },
  watch: {
    visible () {
      this.localVisible = this.visible
    },
    localVisible () {
      this.$emit('update:visible', this.localVisible)
    },
    ledger (val, oldVal) {
      this.userId = this.ledger.user_id
      if (oldVal && oldVal.account_code !== val.account_code && val.user_id == null) {
        this.selectData = []
      }
    }
  },
  created () {
    this.localVisible = this.visible
    this.loadUserTree()
  },
  methods: {
    loadUserTree () {
      this.userTree = this.$store.state.user_tree
    },
    createLink () {
      if (this.selectData.length === 0) {
        this.$message.error('关联失败，您尚未选择需要关联的员工，请重新操作')
        return
      }
      this.loading = true
      API.ledgers.link({
        systemId:      this.systemId,
        accountCode:   this.ledger.account_code,
        userId:        this.userId
      })
        .then(response => {
          if (response.data.account_code) { // 成功返回数据会带有account_code数据
            this.$message.success('关联成功')
            this.loading = false
            this.localVisible = false
            this.$emit('update', Object.assign(response.data, { isLinked: true }))
            this.$EventBus.$emit('account:change')
          }
        })
        .catch(() => {
          this.loading = false
          this.$message.error('关联失败，请重新关联')
        })
    },
    handleChange () {
      this.userId = this.selectData[this.selectData.length - 1]
    },
    getUserDetail (payload) {
      if (payload.tree_ids) this.selectData = payload.tree_ids
    },
    handleOpen() {
      this.showUserDetail = true
    },
    handleClose() {
      this.showUserDetail = false
    }
  }
}
</script>

<style lang="scss" scoped>
.user-cascader {
  max-width: 650px;
  width:     100%;
}

.checkbox-container {
  margin-top: 20px;
}
</style>
