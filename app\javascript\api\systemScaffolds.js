import axios from '@/settings/axios'
import { absoluteUrlFor, download } from './tool'

export const update = (id, params) => {
  return axios.put(`/api/system_scaffolds/${id}`, params)
}

export const updateExpireDays = (params) => {
  return axios.put(`/api/system_scaffolds/update_expire_days`, params)
}

export const create = (params) => {
  return axios.post('/api/system_scaffolds', params)
}

export const list = () => {
  return axios.get('/api/system_scaffolds')
}

export const destroy = (id) => {
  return axios.delete(`/api/system_scaffolds/${id}`)
}

export const detail = (id) => {
  return axios.get(`/api/system_scaffolds/${id}`)
}

// 导入类型
export const importerTypes = () => {
  return axios.get(`/api/system_scaffolds/importer_types`)
}

// 下载EXCEL路径
export const downloadExcelPath = (params) => {
  return axios.post(`/api/system_scaffolds/download_excel_path`, params)
}

// 下载excel模板
export const downloadExcel = (mappings, schemas) => {
  downloadExcelPath({mappings: mappings, schemas: schemas})
    .then(response => {
      window.location.href = absoluteUrlFor('/downloads/file?file=' + response.data)
    })
    .catch(() => {})
}

// 下载导出json
export const exportJson = (row) => {
  download(`/api/system_scaffolds/${row.id}/export`, `${row.name}.json`, {})
    .then(response => {})
    .catch(() => {})
}

export const isNeedGenerateSqlCode = (id, params) => {
  return axios.post(`/api/system_scaffolds/${id}/is_need_generate_sql_code`, params)
}
