# frozen_string_literal: true

# 任务执行用
class PublicAccountTypeAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '功能账号类型'
    @operation_category = '类型管理'
    super
  end

  def create
    @operation = '创建功能账号类型'
    @comment = "成功创建功能账号类型「类型名称：#{params.label || '-'}, 类型编码：#{params.key || '-'}」"
    create_audit_log
  end

  def update
    @operation = '更新功能账号类型'
    @comment = "成功更新功能账号类型「类型名称：#{params.label || '-'}, 类型编码：#{params.key || '-'}」"
    create_audit_log
  end

  def destroy
    @operation = '删除功能账号类型'
    @comment = "成功删除功能账号类型「类型名称：#{params.label || '-'}, 类型编码：#{params.key || '-'}」"
    create_audit_log
  end
end
