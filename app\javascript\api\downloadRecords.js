import axios from '@/settings/axios'
import { MyMessageCreator } from '@/utils/my_message'
import { parseFileName, downloadBlob } from './tool'

// 下载文件
export const downloadFiles = () => {
  return axios.get('/api/download_records/download', 
    { responseType: 'blob' })
    .then(response => {
      if (response.data.size > 0) {
        const fileName = parseFileName(response, 'audit.rar')
        downloadBlob(response.data, fileName)
      }
    })
    .catch((error) => {
      throw error
    })

}

// 下载文件
export const downloadAsync = (url, params = {}, method = 'get') => {
  if (method === 'get') {
    return axios.get(url, { params: params })
  } else {
    return axios.post(url, params)
  }
}

// 下载文件
export const downloadFailureFiles = (params = {}) => {
  return axios.get('/api/download_records/download_failure', { params: params })
}