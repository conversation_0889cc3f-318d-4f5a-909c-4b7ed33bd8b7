# frozen_string_literal: true

# 权限配置审计日志

class PermissionEditableAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '业务系统权限管理'
    super
  end

  def cms_user_excel_upload
    @operation_category = params[0]
    @operation = '数据检查'
    case params[2]
    when 0
      @comment = "上传文件「#{params[1]}」成功"
    when 1
      @comment = "上传文件「#{params[1]}」失败"
    when 2
      @comment = "解析成功「#{params[1]}」,解析内容为 #{params[3]}"
    when 3
      @comment = "解析失败「#{params[1]}」, 错误：#{params[3]}"
    end
    create_audit_log
  end

  def cms_user_excel_cancel
    @operation_category = 'CMS 系统'
    @operation = '取消数据导入'
    @comment   = "取消数据导入「#{params}」"
    create_audit_log
  end
end
