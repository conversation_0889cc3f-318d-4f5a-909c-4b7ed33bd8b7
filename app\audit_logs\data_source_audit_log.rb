class DataSourceAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '数据源管理'
    super
  end

  def create
    data_source_category
    @operation = '创建数据源'
    @comment   = "创建了数据源：「#{bundle_log_comment(params)}」"
    create_audit_log
  end

  def update
    data_source_category
    @operation = '编辑数据源'
    @comment   = "更新了数据源 ：「#{bundle_log_comment(params[0])}」更新为 「#{bundle_log_comment(params[1])}」"
    create_audit_log
  end

  def destroy
    data_source_category
    @operation = '删除数据源'
    @comment   = "删除了数据源 ：「#{bundle_log_comment(params)}」"
    create_audit_log
  end

  private

  def data_source_category
    @operation_category = '数据源管理'
  end

  def bundle_log_comment(data)
    text = ''
    data.each_with_index do |field, index|
      comma = index == 0 ? '' : ', '
      text += "#{comma}#{field[0].to_s}：#{field[1]}"
    end
    text
  end
end
