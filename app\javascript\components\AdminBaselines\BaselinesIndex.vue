<template>
  <div>
    <el-tabs v-model="activeTab">
      <el-tab-pane
        :disabled="!baselineLinkPermission"
        label="系统基线关联"
        name="account"
      >
        <system-account-baselines
          v-if="activeTab === 'account'"
          :system-id="systemId"
        />
      </el-tab-pane>
      <el-tab-pane
        :disabled="!baselineManagePermission"
        label="系统基线管理"
        name="baseline"
      >
        <system-baselines
          v-if="activeTab === 'baseline'"
          :system-id="systemId"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import SystemAccountBaselines from './SystemAccountBaselines.vue'
import SystemBaselines        from './SystemBaselines.vue'

export default {
  components: {
    SystemAccountBaselines,
    SystemBaselines
  },
  props: {
    systemId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      activeTab: this.getActiveName()
    }
  },
  computed: {
    baselineLinkPermission () {
      return this.$store.getters.hasPermission('system_baseline.accounts_query')
    },
    baselineManagePermission () {
      return this.$store.getters.hasPermission('system_baseline.query')
    }
  },
  methods: {
    getActiveName () {
      if (this.$store.getters.hasPermission('system_baseline.accounts_query')) { return 'account' }
      if (this.$store.getters.hasPermission('system_baseline.query')) { return 'baseline' }
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .tool-bar{
    @include vertical_center_between;
    height: 40px;

    .right{
      @include vertical_center_right;
      height: 40px;
    }

    .tool{
      margin-left: 10px;
    }
  }
</style>
