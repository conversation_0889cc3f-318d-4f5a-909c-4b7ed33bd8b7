<template>
  <diff-datas-table
    :diff-datas="roleDiff"
    :data-schema="roleSchema"
    :compare-name="compareName"
    :is-diff-baseline="false"
    :other-account-code="otherAccountCode"
    :diff-mode="diffMode"
    :system-id="systemId"
    :get-response="getResponse"
    data-type="角色"
  />
</template>

<script>
import DiffDatasTable from '@/components/AccountDetail/DiffDatasTable'
import API from '@/api'
export default {
  components: { DiffDatasTable },
  props:      {
    accountId:        {
      type:     Number,
      required: true
    },
    systemId:         {
      type:     Number,
      required: true
    },
    show:             {
      type:    Boolean,
      default: true
    },
    otherQuarterId:   {
      type:    Number,
      default: 0
    },
    compareName: {
      type:    String,
      default: ''
    },
    otherAccountCode: {
      type: String,
      default: ''
    },
    diffMode: {
      type: String,
      default: 'compareHistory'
    }
  },
  data () {
    return {
      roleDiff:   {
        add_permissions:    [],
        reduce_permissions: []
      },
      roleSchema: [],
      getResponse: false
    }
  },
  watch: {
    accountId () {
      this.getDiffWithQuarter()
    },
    otherQuarterId () {
      this.getDiffWithQuarter()
    },
    otherAccountCode () {
      this.getDiffWithAccount()
    },
    diffMode () {
      this.handleDiffModeChange(this.diffMode)
    },
    roleDiff () {
      this.$emit('changeRoleDiff', this.roleDiff)
    }
  },
  created () {
    this.handleDiffModeChange(this.diffMode)
  },
  methods: {
    // 历史差异
    getDiffWithQuarter () {
      if (this.otherQuarterId === 0) return

      API.systemAccounts.diffRoles({
        system_id: this.systemId,
        account_id: this.accountId,
        other_quarter_id: this.otherQuarterId
      })
        .then(response => {
          this.roleSchema  = response.data.schema
          this.roleDiff    = response.data.diff
          this.getResponse = true
        })
    },
    // 账号差异
    getDiffWithAccount () {
      API.systemAccounts.diffRolesWithAccount({
        system_id: this.systemId,
        account_id: this.accountId,
        other_account_code: this.otherAccountCode
      })
        .then(response => {
          this.roleSchema  = response.data.schema
          this.roleDiff    = response.data.diff
          this.getResponse = true
        })
    },
    handleDiffModeChange (payload) {
      this.getResponse = false
      this.diffMode = payload
      switch (payload) {
        case 'compareAccount':
          return this.getDiffWithAccount()
        case 'compareHistory':
          return this.getDiffWithQuarter()
        default:
          return ''
      }
    }
  }
}
</script>
