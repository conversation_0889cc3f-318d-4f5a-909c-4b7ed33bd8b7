# frozen_string_literal: true

# 业务系统权限管理
class OperationReviewAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '业务系统权限管理'
    super
  end

  def review
    @operation_category = params[0]
    @operation = '双人复核'
    case params[1]
    when 0
      @comment = '账号连续输入错误次数过多，账号被锁定'
    when 1
      @comment = "复核人#{params[2].name}(#{params[2].uid}) 验证失败，密码错误，错误尝试次数#{params[2].failed_attempts}"
    when 2
      @comment = "复核人#{params[2].name}(#{params[2].uid}) 验证失败，该用户不是权限复核员"
    when 3
      @comment = "复核人#{params[2].name}(#{params[2].uid}) 验证失败，该用户权限范围不包含该系统"
    when 4
      @comment = "复核人#{params[2].name}(#{params[2].uid}) 验证成功"
    end
    create_audit_log
  end
end