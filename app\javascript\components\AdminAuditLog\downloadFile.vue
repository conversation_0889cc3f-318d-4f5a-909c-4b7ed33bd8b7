<script>
import API from '@/api'

export default {
  data () {
    return {}
  },
  methods: {
    handleDownloadUrl (url, jid) {
      const theParams = {
        url: url,
        jid: jid
      }
      API.auditExport.auditExportStatus(theParams)
        .then(response => {
          if (response.data.success) {
            const downloadUrl = response.data.data.download_url
            API.auditExport.auditExport(downloadUrl)
              .then(() => {})
              .catch(error => {
                error.data.text().then((res) => {
                  this.$message.error(JSON.parse(res).error_message)
                })
              })
          } else {
            this.$message.warning(response.data.message)
          }
        })
        .catch(() => {})
    }
  }
}
</script>