<template>
  <div class="container">
    <h2>权限基线管理</h2>
    <hr>

    <!-- 启用岗位基线按照tab显示 -->
    <el-tabs
      type="border-card"
      v-model="activeTab"
      v-if="isJobBaselineEnable"
    >
      <el-tab-pane
        :disabled="!jobBaselineManagePermission"
        label="岗位基线"
        name="job-baseline"
      >
        <admin-jobs v-if="activeTab === 'job-baseline'" />
      </el-tab-pane>
      <el-tab-pane
        label="系统基线"
        name="system-baseline"
      >
        <system-and-baselines-Index v-if="activeTab === 'system-baseline'"/>
      </el-tab-pane>
    </el-tabs>
    <system-and-baselines-Index v-else />
  </div>
</template>

<script>
import AdminJobs                from '@/components/AdminJobs'
import SystemAndBaselinesIndex  from './SystemAndBaselinesIndex.vue'
import API from '@/api'
export default {
  components: {
    AdminJobs,
    SystemAndBaselinesIndex
  },
  data () {
    return {
      activeTab: 'job-baseline'
    }
  },
  created () {
    this.initActiveName()
  },
  computed: {
    jobBaselineManagePermission () {
      return this.$store.getters.hasPermission('job_baseline.query')
    },
    isJobBaselineEnable () {
      return this.$settings.jobBaseline.enable
    }
  },
  methods: {
    initActiveName () {
      if (this.jobBaselineManagePermission) {
        this.activeTab = 'job-baseline'
      } else {
        this.activeTab = 'system-baseline'
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.container {
  padding:          2em;
  background-color: white;
}
</style>
