<template>
  <div class="login-container">
    <el-row>
      <el-col
        :span="11"
        class="login-col"
      >
        <div class="title-container">
          <div class="title">
            {{ appName }}
          </div>
          <div class="sub-title">
            欢迎使用{{ appName }}
          </div>
        </div>
        <div class="form-container">
          <el-form
            ref="login"
            :model="form"
            label-position="top"
            label-width="4em"
            class="login-form"
          >
            <el-form-item
              :label="loginLabel"
              class="login-label"
            >
              <el-input
                v-model="form.email"
                :placeholder="placeholder"
                @keyup.enter.native="onSubmit"
              />
            </el-form-item>
            <el-form-item
              label="密码"
              class="login-label"
            >
              <el-input
                v-model="form.password"
                placeholder="请输入密码"
                type="password"
                auto-complete="off"
                @keyup.enter.native="onSubmit"
              />
            </el-form-item>
            <div>
              <el-link
                  class="primary"
                  style="float: right;color: #aaaaaa;"
                  @click="onForgetPassword"
              >忘记密码?
              </el-link>
            </div>
            <el-button
              type="primary"
              class="login-submit"
              @click="onSubmit"
            >
              <span class="icon-container">
                <i
                  v-show="localLoading"
                  class="el-icon-loading"
                />
              </span>
              登录
            </el-button>
          </el-form>
        </div>
      </el-col>
      <el-col
        :span="13"
        class="login-col"
      >
        <div class="banner-container" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  props:   {
    appName:     {
      type:     String,
      required: true
    },
    loginLabel:  {
      type:     String,
      required: true
    },
    placeholder: {
      type:     String,
      required: true
    },
    form:        {
      type:     Object,
      required: true
    },
    loading: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      localLoading: false
    }
  },
  watch: {
    loading () {
      this.localLoading = this.loading
    }
  },
  created () {
    this.localLoading = this.loading
  },
  methods: {
    onSubmit () {
      this.localLoading = true
      this.$emit('submit', this.form)
    },
    onForgetPassword () {
      this.$emit('forgetPassword', this.form)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.login-container {
  width:            100%;
  height:           100vh;
  background-color: #F7F9FA;
}

.login-col {
  height: 100vh;
}

.banner-container {
  width:                100%;
  height:               100%;
  background-color:     #ffffff;
  background-image:     url('~../../../assets/images/aas-banner.png');
  background-repeat:    no-repeat;
  background-position:  center;
  background-size:      85% auto;
  -moz-background-size: 85% auto;
}

.title-container {
  width:       400px;
  height:      100px;
  border:      1px;
  margin-left: 20%;
  margin-top:  calc(100vh / 6);
}

.title {
  font-size:   xx-large;
  font-weight: bolder;
  color:       #2E2E2E;
}

.sub-title {
  margin-top: 5px;
  font-size:  normal;
  color:      #979797;
}

.form-container {
  width:       350px;
  height:      270px;
  border:      1px;
  margin-left: 20%;
  margin-top:  calc(100vh / 2 - (100vh / 6 + 100px) - 130px);

  .login-form {
    margin-top: 50px;
  }

  .login-submit {
    @include vertical_center;
    width:            100%;
    margin-top:       47px;
    background-color: #3A93FC;
    border-color:     #3A93FC;

    .icon-container {
      margin-left: -18px;
      display:     inline-block;
      width:       18px;
    }
  }
}

</style>

<style lang="scss">

.login-label {
  margin-bottom: 10px !important;

  .el-form-item__label {
    line-height: 35px;
    color:       #0c0c0c;
    padding:     0 0 !important;
  }

}
</style>
