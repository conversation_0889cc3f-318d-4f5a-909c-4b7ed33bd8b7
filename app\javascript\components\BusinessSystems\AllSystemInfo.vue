<template>
  <div class="container">
    <div class="tool-bar">
      <el-button
        size="small"
        type="primary"
        class="left-button"
        @click="handleCreateAllSystemInfo"
        :disabled="!$store.getters.hasPermission('all_system_info.system_info_edit')"
      >
        添加业务系统信息
      </el-button>
      <all-system-info-export
        :system-ids="systemIds"
        class="right-button"
      />
      <all-system-info-upload
        class="right-button"
        @update="gettingAllSystemInfo"
      />
    </div>
    <el-table
      ref="multipleTable"
      :data="allSystemInfoByPage"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
      />
      <el-table-column
        prop="name"
        label="系统名称"
        sortable
      />
      <el-table-column
        prop="company"
        label="系统厂商"
        sortable
      />
      <el-table-column
        prop="version"
        label="系统版本"
        sortable
      />
      <el-table-column
        prop="admin"
        label="管理员姓名"
        sortable
      />
      <el-table-column
        prop="email"
        label="邮箱"
        sortable
      />
      <el-table-column
        prop="inservice"
        label="状态"
        width="80"
      >
        <template slot-scope="scope">
          <span :class="statusClass.find(x => x.status === scope.row.status).class">
            {{ scope.row.status }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        label="操作"
        width="160"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="handleEdit(scope.row)"
            :disabled="!$store.getters.hasPermission('all_system_info.system_info_edit')"
          >
            编辑
          </el-button>
          <el-button
            :disabled="scope.row.status === '已对接' && !$store.getters.hasPermission('all_system_info.system_info_edit')"
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page.sync="currentPage"
      :page-size="pageSize"
      :total="allSystemInfo.length"
      background
      layout="total, prev, pager, next, jumper"
      class="all-system-pagination"
    />
    <el-dialog
      :title="title"
      :visible.sync="dialogFormVisible"
      width="500px"
      :before-close="handleClose"
    >
      <el-form
        ref="ruleForm"
        :model="system"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item
          label="系统名称"
          prop="name"
        >
          <el-input
            v-model="system.name"
            :disabled="system.status === '已对接'"
            autocomplete="off"
            style="width: 80%;"
          />
        </el-form-item>
        <el-form-item
          label="系统厂商"
          prop="company"
        >
          <el-input
            v-model="system.company"
            autocomplete="off"
            style="width: 80%;"
          />
        </el-form-item>
        <el-form-item
          label="系统版本"
          prop="version"
        >
          <el-input
            v-model="system.version"
            autocomplete="off"
            style="width: 80%;"
          />
        </el-form-item>
        <el-form-item
          label="管理员"
          prop="admin"
        >
          <el-input
            v-model="system.admin"
            :disabled="system.status === '已对接'"
            autocomplete="off"
            style="width: 80%;"
          />
          </el-select>
        </el-form-item>
        <el-form-item
          label="管理员邮箱"
          prop="email"
        >
          <el-input
            v-model="system.email"
            :disabled="system.status === '已对接'"
            autocomplete="off"
            style="width: 80%;"
          />
        </el-form-item>
        <el-form-item
          label="关联已对接系统"
          prop="business_system_id"
        >
          <el-select
            v-model="system.business_system_id"
            :disabled="system.status === '已对接'"
            placeholder="未对接系统无需选择"
            style="width: 80%;"
            clearable
          >
            <el-option
              v-for="system in businessSystems"
              :key="system.id"
              :label="system.name"
              :value="system.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="handleClose">取 消</el-button>
        <el-button
          type="primary"
          @click="submitForm"
        >
          {{ button }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AllSystemInfoUpload from './AllSystemInfoUpload.vue'
import AllSystemInfoExport from './AllSystemInfoExport.vue'

export default {
  components: {
    AllSystemInfoUpload,
    AllSystemInfoExport
  },
  data () {
    return {
      loading: false,
      currentSystem: {},
      allSystemInfo: [],
      businessSystems: [],
      systemIds: [],
      users: [],
      dialogFormVisible: false,
      title: '',
      button: '',
      formMethod: '',
      rules: {
        name: [
          { required: true, message: '请输入系统名称', trigger: 'blur' }
        ],
        admin: [
          { required: true, message: '请输入管理员姓名', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入管理员邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ]
      },
      updateSystem: {},
      system: {
        id: '',
        business_system_id: '',
        name: '',
        company: '',
        version: '',
        admin: '',
        email: '',
        status: ''
      },
      statusClass: [
        { status: '未对接', class: 'not-docking' },
        { status: '已对接', class: 'is-docked' }],
      currentPage: 1,
      pageSize: 15
    }
  },
  computed: {
    allSystemInfoByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.allSystemInfo.slice(start, end)
    }
  },
  created () {
    this.gettingAllSystemInfo()
    this.gettingBusinessSystems()
  },
  methods: {
    gettingAllSystemInfo () {
      this.$axios.get('/api/all_system_info/all_list')
        .then(response => {
          this.allSystemInfo = response.data.all_system_info
        })
        .catch(error => {
          this.$message
        })
    },
    gettingBusinessSystems () {
      this.$axios.get('/api/systems/all')
        .then(response => {
          this.businessSystems = response.data
        })
        .catch(() => {})
    },
    handleCreateAllSystemInfo () {
      this.formMethod = 'create'
      this.button = '创 建'
      this.title = '创建未导入系统'
      this.dialogFormVisible = true
    },
    handleEdit (system) {
      this.formMethod = 'edit'
      this.button = '确 定'
      this.title = '编辑未导入系统'
      this.system = JSON.parse(JSON.stringify(system))
      this.dialogFormVisible = true
    },
    submitForm () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.selectApi()
        } else {
          return false
        }
      })
    },
    selectApi () {
      if (this.formMethod === 'create') {
        this.createAllSystemInfo()
      } else {
        this.editAllSystemInfo()
      }
    },
    createAllSystemInfo () {
      this.$axios.post('/api/all_system_info/create', this.system)
        .then(response => {
          this.$message.success('系统创建成功')
          this.gettingAllSystemInfo()
        })
      this.resetForm()
    },
    editAllSystemInfo () {
      this.$axios.post(`/api/all_system_info/update/${this.system.id}`, this.system)
        .then(response => {
          this.$message.success('系统更新成功')
          this.gettingAllSystemInfo()
        })
      this.resetForm()
    },
    handleDelete (system) {
      this.$axios.delete(`/api/all_system_info/delete/${system.id}`)
        .then(response => {
          this.$message.success('系统已删除')
          this.gettingAllSystemInfo()
        })
    },
    resetForm () {
      this.button = ''
      this.system = {
        name: '',
        company: '',
        version: '',
        admin: '',
        email: ''
      }
      this.dialogFormVisible = false
    },
    handleClose () {
      this.$refs.ruleForm.resetFields()
      this.resetForm()
    },
    handleSelectionChange (systems) {
      this.systemIds = systems.map(x => x.id)
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .container{
    padding: 20px;
  }
  .el-table{
    width: 100%;
  }
  .not-docking {
    color: #F56C6C;
  }
  .is-docked{
    color: #409EFF;
  }
  .tool-bar {
    margin-bottom: 20px;

    .left-button {
      float: left;
      margin-bottom: 20px;
    }
    .right-button {
      float: right;
      margin-left: 10px;
      margin-bottom: 20px;
    }
  }

  .all-system-pagination {
    margin-top: 20px;
  }
</style>
