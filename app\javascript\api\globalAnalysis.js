import axios from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'
import API                             from '@/api'

export const exportGlobalAnalysis = (quarterId, otherQuarterId) => {
  const url = '/api/reports/global_base_report'
  const theParams = { quarter_id: quarterId, other_quarter_id: otherQuarterId }
  return API.downloadRecords.downloadAsync(url, theParams)
  // return axios.get(url, { responseType: 'blob', params: { quarter_id: quarterId, other_quarter_id: otherQuarterId } })
  //   .then(response => {
  //     const fileName = parseFileName(response)
  //     downloadBlob(response.data, fileName)
  //   })
  //   .catch(() => {})
}

export const diffGlobalAnalysis = (quarterId, otherQuarterId) => {
  const url = '/api/reports/diff_global_base_report'
  const theParams = { quarter_id: quarterId, other_quarter_id: otherQuarterId }
  return API.downloadRecords.downloadAsync(url, theParams)
  // return axios.get('/api/reports/diff_global_base_report', { responseType: 'blob', params: { quarter_id: quarterId, other_quarter_id: otherQuarterId } })
  //   .then(response => {
  //     const fileName = parseFileName(response)
  //     downloadBlob(response.data, fileName)
  //   })
  //   .catch(() => {})
}

export const exportGlobalInspect = (quarterId, otherQuarterId) => {
  const url = '/api/reports/global_inspect_report'
  const theParams = { quarter_id: quarterId, other_quarter_id: otherQuarterId }
  return API.downloadRecords.downloadAsync(url, theParams)
  // return axios.get('/api/reports/global_inspect_report', { responseType: 'blob', params: { quarter_id: quarterId, other_quarter_id: otherQuarterId } })
  //   .then(response => {
  //     const fileName = parseFileName(response)
  //     downloadBlob(response.data, fileName)
  //   })
  //   .catch(() => {})
}

export const generateGlobalInspect = (quarterId, otherQuarterId) => {
  return axios.get('/api/reports/global_inspect_report', { params: { quarter_id: quarterId, other_quarter_id: otherQuarterId } })
}

export const downloadExportFile = (params) => {
  return axios.get('/api/reports/download_export_file', { responseType: 'blob', params: { file_path: params.file_path, job_id: params.job_id } })
    .then(response => {
      const fileName = parseFileName(response)
      downloadBlob(response.data, fileName)
    }
    )
    .catch(error => {
      error.data.text().then((res) => {
        this.$message.error(JSON.parse(res).error_message)
      })
    })
}

export const exportFileStatus = (params) => {
  return axios.get('/api/reports/export_file_status', { params: params })
}
