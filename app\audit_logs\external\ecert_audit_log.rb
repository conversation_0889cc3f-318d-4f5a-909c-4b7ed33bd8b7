# frozen_string_literal: true

# 外部系统电子证书日志
class External::EcertAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '外部系统电子证书管理'
    @operation_category = '外部系统电子证书管理'
    super
  end

  def update
    @operation = '更新外部系统电子证书'
    @comment = generate_update_log(params)
    create_audit_log if @comment.present?
  end

  def destroy
    @operation = '删除外部系统电子证书'
    @comment = "删除外部系统电子证书：系统名称「#{params[0]}」，序号：「#{params[1]}」"
    create_audit_log
  end

  def import_success
    @operation = '导入外部系统电子证书'
    file       = params[0]
    @comment   = "外部系统导入电子证书「#{file.original_filename}」成功"
    create_audit_log
  end

  def import_failed
    @operation = '导入外部系统电子证书'
    file       = params[0]
    @comment   = "外部系统导入电子证书「#{file.original_filename}」失败"
    create_audit_log
  end

  def export_success
    @operation = '导出外部系统电子证书模板'
    filename   = params
    @comment   = "导出电子证书模板「#{filename}」成功"
    create_audit_log
  end

  def export_failed
    @operation = '导出外部系统电子证书模板'
    filename   = params
    @comment   = "导出电子证书模板「#{filename}」失败"
    create_audit_log
  end

  private

  def generate_update_log(params)
    update_params_hash = params[0]
    account_settings = params[1]
    comment = "外部系统电子证书更新："
    update_params_hash.each do |key, value|
      setting = account_settings.find { |item| item["name"] == key}
      if setting.present?
        display_name = setting["display_name"]
        comment += "#{display_name}：#{value[1]} ，"
      end
      if key == "user_id"
        comment += "使用人：#{User.find_by(id: value[1])&.name}"
      end
    end
    comment
  end
end
