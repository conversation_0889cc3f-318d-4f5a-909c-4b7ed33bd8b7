<template>
  <div class="container">
    <el-descriptions
      v-loading="loading"
      class="info-class"
      :column="1"
      border
    >
      <el-descriptions-item>
        <template slot="label" class="label-class">系统名称</template>
        {{ aasInfo.systemName }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label" class="label-class">系统版本号</template>
        {{ aasInfo.systemVersion }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label" class="label-class">版本标识符</template>
        {{ aasInfo.commitHash }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label" class="label-class">授权客户名称</template>
        {{ aasInfo.customerName }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label" class="label-class">
          <span :class="licenseDateClass()">
            授权到期时间
          </span>
        </template>
        <span
          v-if="licenseDateOverdue"
          style="color: red;"
        >
          {{ aasInfo.licenseExpireDate }}
        </span>
        <span v-else>
          {{ aasInfo.licenseExpireDate }}
        </span>
      </el-descriptions-item>
    </el-descriptions>
    <upload-license
      @update="getAasInfo"
    />
  </div>
</template>

<script>
import API     from '@/api'
import AasInfo from '@/components/common/AasInfo'
import UploadLicense from './UploadLicense'

export default {
  components: {
    UploadLicense
  },
  mixins: [AasInfo],
  watch: {
  },
  created () {
    this.getAasInfo()
  },
  methods: {
    licenseDateClass () {
      return this.licenseDateOverdue ? 'red-class' : 'normal-class'
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";
.red-class {
  color: red;
}
.normal-class {
  color: #909399;
}
.info-class {
  margin-top: 10px;
  width: 400px;
}
.label-class {
  display: flex;
  justify-content: flex-end;
}
</style>
