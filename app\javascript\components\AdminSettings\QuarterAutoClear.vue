<template>
  <div class="container">
    <div class="tool-bar">
      <el-form ref="form" size="small" label-width="80px">
        <el-form-item label="自动清理">
          <el-select  v-model="clearYear" placeholder="请选择" @change="selectYear">
            <el-option
              key="0"
              label="不清理数据"
              value="0">
            </el-option>
            <el-option
              key="1"
              label="一年前的数据"
              value="1">
            </el-option>
            <el-option
              key="2"
              label="两年前的数据"
              value="2">
            </el-option>
            <el-option
              key="3"
              label="三年前的数据"
              value="3">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="清理类型" v-if="clearYear != '0'">
          <el-select v-model="clearType" placeholder="请选择" @change="selectType">
            <el-option
              key="all"
              label="全部清除"
              value="all">
            </el-option>
            <el-option
              key="month"
              label="按月保留"
              value="month">
            </el-option>
            <el-option
              key="week"
              label="按周保留"
              value="week">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="保留日期" v-if="clearType === 'month'">
          <el-select v-model="selectDays" multiple placeholder="请选择">
            <el-option
              v-for="count in 31"
              :key="count"
              :label="count"
              :value="count">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="保留日期" v-if="clearType === 'week'">
          <el-select v-model="selectDays" multiple placeholder="请选择">
            <el-option
              key="0"
              label="星期日"
              value="0"
            />
            <el-option
              key="1"
              label="星期一"
              value="1"
            />
            <el-option
              key="2"
              label="星期二"
              value="2"
            />
            <el-option
              key="3"
              label="星期三"
              value="3"
            />
            <el-option
              key="4"
              label="星期四"
              value="4"
            />
            <el-option
              key="5"
              label="星期五"
              value="5"
            />
            <el-option
              key="6"
              label="星期六"
              value="6"
            />

          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="updateClearData">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
  },
  data () {
    return {
      clearYear: "0",
      clearType: "",
      selectDays: [],

    }
  },
  watch: {
  },
  created () {
    this.getClearData()
  },
  methods: {
    selectYear () {
      this.selectDays = []
      this.clearType = ''
    },
    selectType () {
      this.selectDays = []
    },
    updateClearData () {
      this.$axios.post('admin_api/settings/quarter_auto_clear', {
        clear_year: this.clearYear,
        clear_type: this.clearType,
        select_days: this.selectDays
      })
        .then(response => {
          this.$message.success('保存成功')
          this.getSettings()
        })
        .catch(() => {})
    },
    getClearData() {
      this.$axios.get('admin_api/settings/quarter_auto_clear', {})
        .then(response => {
          this.clearYear = response.data.clear_year
          this.clearType = response.data.clear_type
          this.selectDays = response.data.select_days
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";
</style>
