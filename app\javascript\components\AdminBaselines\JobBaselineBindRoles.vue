<template>
  <el-dialog
      :visible.sync="visible"
      :title="title"
      width="960px"
      @open="handleOpen"
      @close="handleClose"
      :close-on-click-modal="false"
  >
    <div class="dialog-container">
      <div class="toolbar">
        <div class="float-right">
          <el-button size="small" @click="addSystemBaseline">添加</el-button>
        </div>
      </div>

      <el-table
          v-loading="loading"
          :data="systemBaselines"
          border
          style="width: 100%">
        <el-table-column
            prop="name"
            label="系统名称"
        >
        </el-table-column>
        <el-table-column
            label="角色"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.edit">
              <role-select :system_id="scope.row.id" :role_codes="scope.row.role_codes"
                           @chooseRoles="chooseRoles(scope.row, $event)"></role-select>
              <el-checkbox v-model="copy_role_permissions" style="margin-top: 8px;">复制角色权限到基线</el-checkbox>
            </div>
            <div v-else>
              <ul>
                <li v-for="item in scope.row.roles">
                  {{ item.name }}
                </li>
              </ul>
            </div>
          </template>
        </el-table-column>
        <el-table-column
            label="操作"
            width="180"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.edit">
              <el-button
                  type="primary"
                  size="small"
                  :disabled="checkDisabled(scope.row)"
                  @click="handleUpdateBaseline(scope.row)"
              >
                  确认
              </el-button>
              <el-button
                  size="small"
                  :disabled="checkDisabled(scope.row)"
                  @click="toggleEdit(scope.row)"
              >
                  取消
              </el-button>
            </span>
            <span v-else>
              <el-button
                  size="small"
                  :disabled="checkDisabled(scope.row)"
                  @click="toggleEdit(scope.row)"
              >
                  修改
              </el-button>
              <el-button
                  type="danger"
                  size="small"
                  :disabled="checkDisabled(scope.row)"
                  @click="confirmDeleteBaseline(scope.row)"
              >
                  删除
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <span
        slot="footer"
        class="dialog-footer"
    >

    </span>
    <job-baseline-bind-roles-create
        ref="createDialog"
        :businessSystems="filterAllBusinessSystems"
        @create="createSystemBaseline"
    ></job-baseline-bind-roles-create>
  </el-dialog>
</template>

<script>
import API from '@/api'
import RoleSelect from '@/components/AdminBaselines/RoleSelect.vue'
import JobBaselineBindRolesCreate from "./JobBaselineBindRolesCreate.vue"

export default {
  components: {
    RoleSelect,
    JobBaselineBindRolesCreate
  },
  props: {
    job: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      visible: false,
      loading: false, // 默人加载中，需要确保所有系统选项加载完，才能展示
      systemBaselines: [],
      allBusinessSystems: [],
      params: {
        id: null
      },
      system: {},
      copy_role_permissions: false
    }
  },
  computed: {
    filterAllBusinessSystems() {
      var allBusinessSystems = this.$lodash.clone(this.allBusinessSystems)
      var selectedSystemIds = this.selectedSystemIds()
      var systems = allBusinessSystems.map(x => {
        x.is_disable = selectedSystemIds.indexOf(x.id) >= 0
        return x
      })
      return systems
    },
    title() {
      return `岗位绑定角色-${this.job.name}`
    }
  },
  watch: {
  },
  created() {
    this.initAllBuisineSystems()
  },
  methods: {
    handleOpen() {
      this.initJobBaseline()
      this.getJobBaselineSystemBaselines()
    },
    handleClose() {
      this.systemBaselines = []
      this.$emit('change')
    },
    initJobBaseline() {
      var id = this.job.id
      if (API.tool.isBlank(id)) {
        return
      }
      API.jobBaselines.detail(id)
          .then(response => {
            this.params = response.data
          })
          .catch(() => {
          })
    },
    getJobBaselineSystemBaselines() {
      if (typeof (this.job.id) != 'number') return
      this.loading = true
      API.jobBaselines.outputSystemBaselines(this.job.id)
          .then(response => {
            this.loading = false
            // default_role_codes 记录默认角色编码，用于取消后再打开显示之前的角色编码
            this.systemBaselines = response.data.map(item =>
                Object.assign(item, {edit: false, default_role_codes: item.role_codes})
            )
          })
          .catch(() => {
            this.loading = false
            this.$message.info('已取消操作')
          })
    },
    // 获取所有系统
    initAllBuisineSystems() {
      API.systems.all()
          .then(response => {
            this.allBusinessSystems = response.data
          })
          .catch(() => {
            this.$message.info('已取消操作')
          })
    },
    // 已选择的系统ID
    selectedSystemIds() {
      return this.systemBaselines.filter(x => !API.tool.isBlank(x.id)).map(x => x.id)
    },
    checkDisabled(systemBaseline) {
      let currentEdit = this.systemBaselines.find(x => x.edit)
      if(!currentEdit) return false
      return systemBaseline.id !== currentEdit.id
    },
    chooseRoles(system, role_codes) {
      system.role_codes = role_codes
    },
    toggleEdit(system_baseline) {
      if(!system_baseline.edit) {
        system_baseline.role_codes = system_baseline.default_role_codes
      }
      system_baseline.edit = !system_baseline.edit
    },
    handleUpdateBaseline(system_baseline) {
      if (this.copy_role_permissions){
        this.$confirm('基线权限将被覆盖，确认将角色权限同步到系统基线权限吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          this.updateBaseline(system_baseline)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
      }
      else {
        this.updateBaseline(system_baseline)
      }
    },
    updateBaseline(system_baseline) {
      if (system_baseline.role_codes.length === 0) {
        this.$message.error("请选择至少一个角色")
        return
      }
      
      this.loading = true
      let updateParams = {
        id: this.params.id,
        job_id: this.params.job_id,
        job_name: this.params.job_name,
        system_baseline_id: system_baseline.system_baseline_id,
        business_system_id: system_baseline.id,
        role_codes: system_baseline.role_codes,
        copy_role_permissions: this.copy_role_permissions
      }
      API.jobBaselines.bindRolesUpdate(updateParams)
          .then(response => {
            this.loading = false
            if (response.data.success) {
              this.getJobBaselineSystemBaselines()
              this.$message.success(`更新成功`)
            } else {
              this.$message.error(response.data.error_message)
            }
          })
          .catch(() => {
            this.loading = false
          })
    },
    confirmDeleteBaseline(system_baseline) {
      this.$confirm('确认删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            this.deleteBaseline(system_baseline)
          })
          .catch(() => {
            this.$message.info('已取消操作')
          })
    },
    deleteBaseline(system_baseline) {
      this.loading = true
      let deleteParams = {
        id: this.params.id,
        job_id: this.params.job_id,
        job_name: this.params.job_name,
        system_baseline_id: system_baseline.system_baseline_id
      }
      API.jobBaselines.bindRolesDelete(deleteParams)
          .then(response => {
            this.loading = false
            if (response.data.success) {
              this.getJobBaselineSystemBaselines()
              this.$message.success(`删除成功`)
            } else {
              this.$message.error(response.data.error_message)
            }
          })
          .catch(() => {
            this.loading = false
          })
    },
    addSystemBaseline() {
      this.$refs.createDialog.visible = true
    },
    createSystemBaseline(localForm) {
      this.loading = true
      let createParams = {
        id: this.params.id,
        job_id: this.params.job_id,
        job_name: this.params.job_name,
        business_system_id: localForm.business_system_id,
        role_codes: localForm.role_codes,
        copy_role_permissions: localForm.copy_role_permissions
      }
      API.jobBaselines.bindRolesCreate(createParams)
          .then(response => {
            this.loading = false
            if (response.data.success) {
              this.$refs.createDialog.cancel()
              this.getJobBaselineSystemBaselines()
              this.$message.success('添加成功')
            } else {
              this.$message.error(response.data.error_message)
            }
          })
          .catch(() => {
            this.loading = false
          })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.red {
  color: #f56c6c;
  margin-left: 15px;
}

.float-right {
  @include vertical_center_right;
}

.dialog-container {
  margin-top: 0;
}

.toolbar {
  margin-bottom: 20px;
}

</style>
