# frozen_string_literal: true

# 业务系统相关控制器
class BusinessSystemsController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!, except: %i[list list_with_inservice tree_with_category
                                                  all_list all_list_with_admins systems_for_permissions upload_type_list
                                                  auto_import_type_list category_list all_accounts
                                                  settings advanced_search_datas user_accounts_exist_list
                                                  search_user output_role_schema jiaoyi_menu_names systems_with_roles systems_with_accounts]

  before_action :set_business_system, only: %i[settings update enable disable
                                               summary summary_in_system_mode
                                               account_history user_accounts_exist_list
                                               search_user all_accounts advanced_search_datas
                                               export_batch output_role_schema
                                               destroy custom_export fund_status_change_export]
  before_action :set_business_system_category, only: %i[category_update category_destroy]
  before_action :set_system_account, only: %i[account_history]
  before_action :set_quarter, only: %i[summary summary_in_system_mode]
  before_action :set_departments, only: %i[summary]
  before_action :check_import_status, only: %i[summary summary_in_system_mode]

  # 业务系统管理 列表页面 和 业务分组管理
  def list
    json_respond current_admin.business_systems_in_query.map(&:output)
  end

  # 业务系统权限管理使用
  def systems_in_maintain
    json_respond current_admin.business_systems_in_maintain.map(&:output)
  end

  # 未禁用系统（职务权限规则设置使用）
  def list_with_inservice
    json_respond BusinessSystem.inservice.map(&:output)
  end

  def tree_with_category
    has_query_bs_ids = current_admin.business_systems_in_query.ids
    tree_output = BusinessSystemCategory.arrange_serializable do |parent, children|
      bs_list = parent.business_systems.inservice.where(id: has_query_bs_ids).map { |item| { id: item.id, name: item.name, category: 'bs' }}
      {
        id: parent.id,
        name: parent.name,
        parent_id: parent.parent_id,
        children: children,
        business_systems: bs_list,
        category: 'category'
      }
    end
    no_category_bs_list = BusinessSystem.where(business_system_category_id: nil).inservice
                                        .where(id: has_query_bs_ids).map { |bs| { id: bs.id, name: bs.name, category: 'bs' }}
    no_category_bs_list.each { |x| tree_output << x }
    # 此处当分组下没有系统时，不要展示
    tree_output = tree_output.select { |item| item[:category] == 'bs' || item[:business_systems].size > 0 }
    business_systems = collect_business_systems(tree_output)
    # 展示默认系统
    default_bs_id = business_systems.first&.[](:id)
    open_tree_ids = tree_ids(default_bs_id)
    json_respond data: tree_output, default_bs_id: default_bs_id, open_tree_ids: open_tree_ids
  end

  # 获取所有业务系统
  def all_list
    # TODO: 需要确认这个接口前端都在哪调用，应当限制超级管理员才能访问
    json_respond BusinessSystem.all.map(&:output)
  end

  def all_list_with_admins
    json_respond BusinessSystem.all.map(&:output_with_admins)
  end

  # 后台账号管理 配置业务系统权限用,返回的是使用中的并且按条件筛选来的系统
  def systems_for_permissions
    admin = Admin.find_by(id: params[:admin_id])
    data = BusinessSystemCategory.arrange_serializable do |parent, children|
      bs_list = sort_business_systems(parent.business_systems.inservice)
      bs_list = bs_list.map do |bs|
        {
          id: bs.id,
          name: bs.name,
          query: admin.admin_system_permissions.query.exists?(business_system_id: bs.id),
          maintain: admin.admin_system_permissions.maintain.exists?(business_system_id: bs.id),
        }
      end
      add_admin_system_permission(bs_list)
      {
        id: parent.id,
        name: parent.name,
        parent_id: parent.parent_id,
        business_systems: bs_list,
        children: children,
        all_query: admin.admin_system_category_permissions.query.exists?(business_system_category_id: parent.id),
        all_maintain: admin.admin_system_category_permissions.maintain.exists?(business_system_category_id: parent.id)
      }
    end
    no_bs_list = sort_business_systems(BusinessSystem.where(business_system_category_id: nil))
    no_category_bs_list = no_bs_list.map do |bs|
      {
        id: bs.id,
        name: bs.name,
        query: admin.admin_system_permissions.query.exists?(business_system_id: bs.id),
        maintain: admin.admin_system_permissions.maintain.exists?(business_system_id: bs.id),
      }
    end
    add_admin_system_permission(no_category_bs_list)
    if no_category_bs_list.present?
      data.push({ id: 0, name: "未分类",parent_id: nil, business_systems: no_category_bs_list, children: [],
                  all_query: false,
                  all_maintain: false })
    end
    all_systems_maintain = admin.admin_special_permission&.all_systems_maintain || false
    all_systems_query = admin.admin_special_permission&.all_systems_query || false
    json_respond({ data: data, all_systems_maintain: all_systems_maintain, all_systems_query: all_systems_query })
  end

  # 输出业务系统 和 业务系统的角色
  def systems_with_roles
    json_respond BusinessSystem.inservice.map(&:output_with_roles)
  end

  # 输出业务系统 和 业务系统的账号
  def systems_with_accounts
    json_respond BusinessSystem.inservice.map(&:output_with_accounts)
  end

  # 通过 Excel 上传方式的业务系统
  def upload_type_list
    json_respond current_admin.business_systems_in_maintain.inservice.upload_type.order(:name).map(&:output)
  end

  # 通过 Excel 上传方式的业务系统,和自助系统配合
  def excel_list
    json_respond current_admin
                   .business_systems_in_maintain
                   .select(&:excel?)
                   .sort_by { |x| Pinyin.t(x[:name]) }
                   .map(&:output)
  end

  # 通过自动导入的业务系统,如 数据库，api 等
  def auto_import_type_list
    json_respond BusinessSystem.inservice.auto_import_type.order(:name).map(&:output)
  end

  def category_list
    json_respond BusinessSystemCategory.all.map(&:output)
  end

  def category_list_tree
    json_respond BusinessSystemCategory.tree
  end

  def category_create
    category = BusinessSystemCategory.new(name: category_params[:name],
                                          order_number: category_params[:order_number],
                                          parent_id: category_params[:parent_id])
    if category.save
      systems      = category.update_system_ids(category_params[:business_system_ids])
      systems_name = systems.pluck(:name)
      audit_log! [category_params[:name], systems_name, category.parent&.name]
      json_custom_respond(:created, category.output)
    else
      json_custom_respond(:unprocessable_entity, error_message: category.errors.full_messages.join('; '))
    end
  end

  def category_update
    update_success = @category.update(name: category_params[:name],
                                      order_number: category_params[:order_number],
                                      parent_id: category_params[:parent_id])
    category_update_systems = @category.update_system_ids(category_params[:business_system_ids])
    if update_success || category_update_systems
      systems_name = @category.business_systems.pluck(:name)
      audit_log! [category_params[:name], systems_name, @category.parent&.name]
      json_custom_respond(:ok, @category.output)
    else
      json_custom_respond(:unprocessable_entity, error_message: @category.errors.full_messages.join('; '))
    end
  end

  def category_destroy
    @category.destroy
    audit_log! @category.name
    json_respond_no_content
  end

  def all_accounts
    json_respond @business_system.account_class.pluck(:code, :name).uniq
  end

  # 查询业务系统账号，返回包含关键词的系统及其数量
  def search_systems
    query      = params[:q]
    quarter_id = params[:quarter_id].to_i
    page = params[:page] || 1
    per_page = params[:per_page] || 15

    system_data = BusinessSystem.search_systems_by_account(query, quarter_id, current_admin)
    system_id = system_data.first&.[](:system)&.[](:id)
    bs = BusinessSystem.find_by(id: system_id)
    system_accounts = if bs
                        bs.search_accounts(query, quarter_id, page, per_page)
                      else
                        []
                      end
    json_respond system_data: system_data, system_id: system_id, system_accounts: system_accounts
  end

  def search_accounts
    bs = BusinessSystem.find_by(id: params[:system_id])
    if params[:account_ids].present?
      quarter_id = params[:quarter_id] || Quarter.last.id
      @accounts = bs.account_class.where(quarter_id: quarter_id)
      if params[:account_ids].present?
        @accounts = @accounts.where(id: params[:account_ids])
      end
      if params[:filter]
        filter = JSON.parse(params[:filter]).deep_symbolize_keys
        if filter[:query].present?
          @accounts = @accounts.where('name like ? or code like ?', filter[:query], filter[:query])
        end
        if filter[:status].present?
          @accounts = @accounts.where(status: filter[:status].to_s === 'true')
        end
      end
      @accounts = @accounts.page(params[:page] || 1).per(params[:per_page] || 25)
      account_list = @accounts.map(&:output_baseinfo)
      json_respond data: account_list, count: @accounts.total_count
    else
      query      = params[:q]
      quarter_id = params[:quarter_id].to_i
      page = params[:page] || 1
      per_page = params[:per_page] || 15
      system_accounts = []
      bs = BusinessSystem.find_by(id: params[:system_id])
      system_accounts = bs.search_accounts(query, quarter_id, page, per_page) if bs
      json_respond system_accounts: system_accounts
    end
  end

  def settings
    # 可能 role_class.output_schema 不存在
    role_class         = @business_system.role_class
    role_output_schema = role_class.output_schema if role_class.respond_to?(:output_schema)
    account_schema     = BusinessSystem.compat_with_old_schema(@business_system.account_class.output_schema)
    if params[:with_role].to_s == 'true'
      account_schema = BusinessSystem.add_role_schema(account_schema, @business_system)
    end
    json_respond(
      permission_types:         @business_system.permission_types,
      account_info_schema:      @business_system.account_info_schema,
      disable_role_button:      @business_system.disable_role_button,
      settings:                 @business_system.settings || {},
      model_type:               @business_system.prefix,
      account_schema:           account_schema,
      role_schema:              role_output_schema,
      output_schema_data:       SystemBaseline.calculate_output_schema_data(account_schema),
      advanced_search_editable: @business_system.settings ? @business_system.settings[:advanced_search_editable] : nil,
      model_names:              @business_system.model_names || []
    )
  end

  def advanced_search_datas
    json_respond @business_system.search_permission_datas(params[:quarter_id])
  end

  def update
    if @business_system.update(update_business_system_params)
      audit_log! [@business_system.name, @business_system.business_system_category&.name || '无', @business_system&.order_number]
      if Setting.create_system&.[]('enable')
        SystemScaffold.find_by(business_systems_id: @business_system.id)&.update(name: @business_system.name)
      end
      json_respond @business_system.output
    else
      json_custom_respond(:unprocessable_entity, error_message: @business_system.errors.full_messages.join('; '))
    end
  end

  # 部门视角（默认）
  def summary
    not_founds = User.get_not_found_accounts(@quarter, @business_system, filter_params)
    json = User.system_summary(@quarter, @business_system, 'department', current_admin, filter_params)
    json[:not_found] = not_founds
    json_respond json
  end

  # 系统视角
  def summary_in_system_mode
    json_respond User.system_summary(@quarter, @business_system, 'system', current_admin, filter_params)
  end

  # 自定义权限导出
  def custom_export
    if params[:method] == 'export_fund_status_change_excel'
      custom_export_file = DataExport::FundStatusChangeExport.new(@business_system, params[:first_quarter_id],
                                                                  params[:second_quarter_id]).export
    elsif params[:method].include?("efunds_")
      custom_export_file = DataExport::EfundsExport.new(@business_system, params[:quarter_id], params[:method], params[:quarter_id], params[:first_quarter_id]).export
    else
      custom_export_file = @business_system.account_class.send(params[:method], params[:quarter_id],
                                                               params[:first_quarter_id])
    end

    unique = "_#{current_admin.id}_#{Time.now.to_i}"
    DataExport.batch_zip([custom_export_file].flatten, unique)
    json_respond(success: true, data: { download_url: "/api/download_batch_zip?unique=#{unique}" })
  rescue StandardError => e
    json_respond(success: false, error_message: e.message)
  end

  # 系统批量数据导出
  def export_batch
    options = params.to_unsafe_h.symbolize_keys
    quarter = Quarter.find(params[:quarter_id])
    system_id = params[:system_id].to_i
    bs        = BusinessSystem.find(system_id)
    case params[:export_type]
    when 'all'
      log_content = { filter_params: filter_params, bs: bs, quarter_id: quarter.id }
    else
      ids = params[:ids] || []
      account_ids = ids.map(&:to_i)
      accounts = bs.account_class.where(id: account_ids)
      log_content = { account_name: accounts.pluck(:name) }
    end
    # 日志
    log = log_params(quarter&.name, log_content)
    options.merge!(filter_params: filter_params, log: log)
    DownloadExport::AccountExport.new(quarter, current_admin, bs, options).async_export

    json_respond(status: true)
    # json_respond(success: true, data: { download_url: "/api/download_batch_zip?unique=#{unique}" })
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    # audit_log! log_params(quarter&.name, log_content), action: :export_batch_faild
    # json_respond(success: false, error_message: e.message)
    json_custom_respond(500, error_message: e.message)
  end

  # 系统角色批量数据导出
  def export_role_batch
    options = params.to_unsafe_h.symbolize_keys
    system_id = params[:system_id].to_i
    bs        = BusinessSystem.find(system_id)
    quarter   = Quarter.find(params[:quarter_id])
    case params[:export_type]
    when 'all'
      log_content = { filter_params: filter_role_params, bs: bs, quarter_id: quarter.id }
    else
      role_ids = params[:ids] || []
      role_ids = role_ids.map(&:to_i)
      roles    = bs.role_class.where(id: role_ids)
      log_content = { role_name: roles.pluck(:name) }
    end
    log = log_role_params(quarter&.name, log_content)
    options.merge!(filter_role_params: filter_role_params, log: log)
    DownloadExport::RoleExport.new(quarter, current_admin, bs, options).async_export

    json_respond(status: true)
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    # audit_log! log_role_params(quarter&.name, log_content), action: :export_role_batch_faild
    json_custom_respond(500, error_message: e.message)
  end

  # 导出基金状态变化报表
  def fund_status_change_export
    export_file_path = DataExport::FundStatusChangeExport.new(@business_system, params[:begin_quarter_id],
                                                              params[:end_quarter_id]).export
    unique = "_#{current_admin.id}_#{Time.now.to_i}"
    DataExport.batch_zip([export_file_path].flatten, unique)
    json_respond(success: true, data: { download_url: "/api/download_batch_zip?unique=#{unique}" })
  rescue StandardError => e
    json_respond(success: false, error_message: e.message)
  end

  def user_accounts_exist_list
    user = User.find(params[:user_id].to_i)
    json_respond user.business_system_accounts_exist_list(@business_system)
  end

  def search_user
    user_id =
      if params[:account_code] && @business_system.account_class.new.respond_to?(:code)
        @business_system
          .account_class
          .where(code: params[:account_code])
          .where.not(user_id: nil)
          .map(&:user_id)
          .uniq
          .last
      end
    if user_id
      json_respond User.find(user_id).information
    else
      json_respond nil
    end
  end

  def disable
    audit_log! @business_system
    json_respond(success: @business_system.update(inservice: false))
  end

  def enable
    audit_log! @business_system
    json_respond(success: @business_system.update(inservice: true))
  end

  def output_role_schema
    role_class = @business_system.role_class
    schema = role_class.respond_to?(:output_schema) ? role_class.output_role_schema : []
    json_respond schema
  end

  private

  def authenticate_policy!
    authorize BusinessSystem
  end

  def filter_params
    @filter_params ||= JSON.parse params[:account_filter], symbolize_names: true
  end

  # 角色搜索参数
  def filter_role_params
    @filter_params ||= JSON.parse params[:role_filter], symbolize_names: true
  end

  def update_business_system_params
    params.require(:system).permit(:name, :business_system_category_id, :order_number)
  end

  def business_system_params
    params.require(:system).permit(:name, :business_system_category_id)
  end

  def category_params
    params.require(:category).permit(:name, :order_number, :parent_id, business_system_ids: [])
  end

  def set_business_system_category
    @category = BusinessSystemCategory.find(params[:category_id])
  rescue StandardError => e
    json_custom_respond(404, error_message: e.message)
  end

  def set_departments
    @departments = Department.where(id: filter_params[:departments])
  rescue StandardError => e
    json_custom_respond(404, error_message: e.message)
  end

  def check_import_status
    dependent_task =
      @quarter
        .business_system_import_statuses
        .find_by(business_system_id: @business_system.id)

    return if dependent_task&.success?

    msg = I18n.t('errors.system_import.not_success', import_name: I18n.t('module_name.quarters'))
    json_custom_respond 456, error_message: @business_system.name + msg
  end

  # 格式化参数，将维度参数格式化为可以直接保存的格式
  def format_params
    schemas_attributes = params['system']['schemas_attributes']
    return unless schemas_attributes.present?

    obj = {}
    schemas_attributes.each_with_index do |attrs, index|
      mapping_obj         = {}
      mappings_attributes = attrs['mappings_attributes']
      mappings_attributes.each_with_index do |mapping_attrs, mapping_index|
        mapping_obj[mapping_index.to_s] = mapping_attrs
      end
      attrs['mappings_attributes'] = mapping_obj
      obj[index.to_s]              = attrs
    end
    params['system']['schemas_attributes'] = obj
  end

  # 账户日志需要的信息
  def log_params(quarter_name, log_content)
    files = { checked_permission: '操作权限列表', checked_examine: '用户权限审核表', checked_overview: '系统账号概览表',
checked_compose_permission: '系统权限表（合并）' }
    file_names = files.select { |key, _value| params[key] }.values
    {
      quarter_name: quarter_name,
      content:      content(log_content),
      file_names:   file_names
    }
  end

  # 角色日志需要的信息
  def log_role_params(quarter_name, log_content)
    {
      quarter_name: quarter_name,
      content:      role_content(log_content)
    }
  end

  def content(log_content)
    if log_content.keys == [:account_name]
      log_content[:account_name].join('、')
    else
      # 注意： 这里要复制（clone）
      options               = log_content[:filter_params].clone
      options[:role]        =
        log_content[:bs].role_class.find_by(quarter_id: log_content[:quarter_id], id: options[:role])&.name
      options[:department]  = Department.find_by(id: options[:department])&.name
      options[:status]      = I18n.t("enumerize.user.inservice.#{options[:status]}") if options[:status].present?
      options[:user_status] = I18n.t("enumerize.user.inservice.#{options[:user_status]}") if options[:user_status].present?

      options = options.reject { |_key, value| value.blank? }

      filter_items = { query: '名字或编号', status: '账号状态', user_status: '员工状态', role: '角色', department: '部门',
position: '职务' }
      filters = filter_items.select { |key, _value| log_content[:filter_params].has_key? key }.values

      connects = filters.map do |filter|
        value = options&.[] filter_items.index(filter.to_s).to_sym

        "#{filter}：#{value.present? ? value : '空'}"
      end

      '筛选条件为：' + connects.compact.join('，')
    end
  end

  def role_content(log_content)
    if log_content.keys == [:role_name]
      log_content[:role_name].join('、')
    else
      options = log_content[:filter_params].clone
      options.reject { |_key, value| value.blank? }

      filter_items = { query: '角色名称或编号' }
      filters = filter_items.select { |key, _value| log_content[:filter_params].has_key? key }.values

      connects = filters.map do |filter|
        value = options&.[] filter_items.index(filter.to_s).to_sym

        "#{filter}：#{value.present? ? value : '空'}"
      end

      '筛选条件为：' + connects.compact.join('，')
    end
  end

  def custom_titles
    Setting.frontendSettings['overviewCustomtitles']
  end

  # 按照order_number倒序、名称拼音正序排序
  def sort_business_systems(business_systems = [])
    business_systems.sort do |a, b|
      order_number_a = a.order_number || -Float::INFINITY # nil 视为最小值
      order_number_b = b.order_number || -Float::INFINITY
      category_id_a = a.business_system_category_id || -Float::INFINITY
      category_id_b = b.business_system_category_id || -Float::INFINITY

      order_category_id_comparison = category_id_b <=> category_id_a
      order_number_comparison = order_number_b <=> order_number_a
      order_category_id_comparison.zero? ? (order_number_comparison.zero? ? Pinyin.t(a.name.downcase) <=> Pinyin.t(b.name.downcase) : order_number_comparison) : order_category_id_comparison
    end
  end

  def is_all_permission?
    current_admin.permission? 'admin_account_manager.all_permission'
  end

  # 添加当前用户是否有系统查询和操作权限，用于授权时，判断只允许授权的系统
  def add_admin_system_permission(bs_list = [])
    if is_all_permission?
      bs_list.each do |x|
        x[:permission] = { is_query: true, is_maintain: true }
      end
    else
      query_bs_ids = current_admin.business_systems_in_query.pluck(:id)
      maintain_bs_ids = current_admin.business_systems_in_maintain.pluck(:id)
      bs_list.each do |x|
        is_query = query_bs_ids.include?(x[:id])
        is_maintain = maintain_bs_ids.include?(x[:id])
        x[:permission] = {
          is_query:    is_query,
          is_maintain: is_maintain
        }
      end
    end
  end

  # 获取树形分类里的系统
  def collect_business_systems(nodes)
    systems = []

    nodes.each do |node|
      systems.concat(node[:business_systems]) if node[:business_systems]
      systems.concat(collect_business_systems(node[:children])) if node[:children]
    end
    systems.concat(nodes.select { |x| x[:children].blank? })

    systems
  end

  # 打开指定树形菜单，前端用于展开分组
  def tree_ids(default_bs_id)
    return [] if default_bs_id.blank?

    bs = BusinessSystem.find_by(id: default_bs_id)
    return [] if bs.blank?
    return ['category-0'] if bs.business_system_category.blank?

    bs.business_system_category.path.map { |x| "category-#{x.id}" }
  end
end
