<template>
  <span>
    <el-upload
      :action="uploadUrl"
      :headers="headers"
      :fileList="fileList"
      :onSuccess="handleSuccess"
      :onError="handleError"
      :show-file-list="false"
      :disabled="!$store.getters.hasPermission('system_permission_manager.user_excel_upload')"
    >
      <el-button
        v-loading="loading"
        :disabled="!$store.getters.hasPermission('system_permission_manager.user_excel_upload')"
      >
        Excel 导入
      </el-button>
    </el-upload>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="Excel 导入数据检查"
      width="950px"
    >
      <el-divider />
      <div
        v-for="(account, index) in accounts"
        :key="index"
        class="form-line"
      >
        <el-form
          :ref="`form${index}`"
          :inline="true"
          :model="account"
          :rules="rulesFor(index)"
          label-width="100px"
          size="small"
        >
          <div class="line1">
            <el-form-item
              label=""
              prop=""
            >
              <el-tag
                v-show="account.method === 'create'"
                size="mini"
                type="success"
                :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
              >
                创建
              </el-tag>
              <el-tag
                v-show="account.method === 'update'"
                size="mini"
                type="warning"
                :disabled="!$store.getters.hasPermission('system_permission_manager.update_account')"
              >
                更新
              </el-tag>

            </el-form-item>

            <el-form-item
              label="所属机构"
              prop="org_id"
            >
              <el-select
                v-model="account.org_id"
                filterable
                placeholder="请选择机构"
                class="form-item"
                @change="handleChangeOrg($event, index, 'org_id')"
              >
                <el-option
                  v-for="org in all_orgs"
                  :key="org.org_id"
                  :label="`${org.main_frame_org_id} - ${org.org_name}`"
                  :value="org.org_id"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="用户编号"
              prop="user_id"
            >
              <el-input
                v-model="account.user_id"
                placeholder="用户编号"
                class="form-item"
                @change="handleChangeOrg($event, index, 'user_id')"
              />
            </el-form-item>
            <el-form-item
              label="用户名称"
              prop="user_name"
            >
              <el-input
                v-model="account.user_name"
                placeholder="用户名称"
                class="form-item"
                @change="handleChangeOrg($event, index, 'user_name')"
              />
            </el-form-item>
          </div>

          <div class="line2">
            <el-form-item
              label="岗位基线"
              prop="baseline_id"
            >
              <el-select
                v-model="account.baseline_id"
                filterable
                placeholder="请选择岗位基线"
                class="form-item"
                @change="handleChangeOrg($event, index, 'baseline_id')"
              >
                <el-option
                  v-for="baseline in all_baselines"
                  :key="baseline.id"
                  :label="baseline.name"
                  :value="baseline.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="所选角色"
              prop="role_ids"
            >
              <el-select
                v-model="account.role_ids"
                filterable
                multiple
                placeholder="请选择额外角色"
                class="form-item"
                @change="handleChangeOrg($event, index, 'role_ids')"
              >
                <el-option
                  v-for="role in all_roles"
                  :key="role.id"
                  :label="`${role.id} - ${role.name}`"
                  :value="role.id"
                />
              </el-select>
            </el-form-item>
          </div>
        </el-form>
        <el-divider />
      </div>

      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="handleCancel">取 消</el-button>
        <el-button
          type="primary"
          @click="submitConfirm"
        >
          确认导入
        </el-button>
      </span>
    </el-dialog>
  </span>
</template>

<script>
import { catchError } from '@/utils/axios_utils'
import API from '@/api'
export default {
  name:    'AccountsImportVue',
  props:   {
    systemId: {
      type:     Number,
      required: true
    }
  },
  data () {
    const headers = API.tool.getToken()

    return {
      uploadUrl:     `/admin_api/edit_api/${this.systemId}/accounts/upload`,
      fileList:      [],
      headers:       headers,
      loading:       false,
      visible:       false,
      accounts:      [],
      all_orgs:      [],
      all_roles:     [],
      all_baselines: [],
      formStatus:    [],
      requestId:     null
    }
  },
  methods: {
    check () {
      this.accounts.forEach((account, index) => {
        const formName = `form${index}`
        this.$refs[formName][0].validate(valid => {
          this.formStatus[index] = !!valid
        })
      })
    },
    submitConfirm () {
      this.check()
      if (this.formStatus.every(x => x)) {
        this.operationReview()
      } else {
        this.$message.error('请检查必填项')
        return false
      }
    },
    operationReview () {
      const guid = this.requestId
      // 二次复核
      this.$operationReview(this.systemId, guid)
        .then(() => {
          this.submit(guid)
        })
        .catch(() => {})
    },
    submit (guid) {
      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts/upload/submit`, {
        guid:     guid,
        accounts: this.accounts
      })
        .then(() => {
          this.visible = false
          this.$message.success('数据导入成功')
          this.$emit('update')
        })
        .catch(error => {
          switch (error.status) {
            case 456:
              break
            case 457:
              this.accounts = error.data.data
              break
            default:
              break
          }
        })
    },
    handleSuccess (response, _file, _fileList) {
      this.visible   = true
      this.accounts  = response.data
      this.requestId = response.request_id
      this.fileName  = response.file_name
      this.getOptionData()
    },
    handleExceed (files, fileList) {
      console.log(files, fileList)
    },
    handleError (err, _file, _fileList) {
      const errorMsg = JSON.parse(err.message)
      this.$message.error('文件上传失败，服务器返回：' + errorMsg.error_message)
    },
    handleClose (done) {
      this.$confirm('确认直接关闭吗？未上传的数据将会丢失')
        .then(_ => {
          this.cancelAndLog(done)
        })
        .catch(() => {})
    },
    cancelAndLog (done) {
      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts/upload/cancel`, {
        request_id: this.requestId,
        file_name:  this.fileName
      })
        .then(() => {
          done()
        })
        .catch(() => {})
    },
    handleCancel () {
      const done = () => {
        this.visible = false
      }
      this.handleClose(done)
    },
    getOptionData () {
      this.getAllOrgs()
      this.getAllRoles()
      this.getBaselines()
    },
    getAllOrgs () {
      if (this.systemId === 0) return

      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/organizations`, {
        params: {
          parent_id: '1'
        }
      })
        .then(response => {
          this.loading  = false
          this.all_orgs = response.data
        })
        .catch(error => {
          this.loading = false
          catchError(error, '获取机构列表数据失败')
        })
    },
    getAllRoles () {
      if (this.systemId === 0) return

      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/roles`)
        .then(response => {
          this.loading   = false
          this.all_roles = response.data
        })
        .catch(error => {
          this.loading = false
          catchError(error, '获取角色列表失败')
        })
    },
    getBaselines () {
      this.$axios.get(`/api/systems/${this.systemId}/baselines/all`)
        .then(response => {
          this.all_baselines = response.data
        })
        .catch(error => {
          catchError(error, '获取基线列表失败')
        })
    },
    handleChangeOrg (value, index, prop) {
      this.accounts[index].status[prop] = true
    },

    rulesFor (index) {
      const account        = this.accounts[index]
      const validateStatus = (rule, value, callback) => {
        const field = rule.field
        if (account.status[field] === false) {
          callback(new Error(account.messages[field]))
        } else {
          callback()
        }
      }
      return {
        org_id:      [
          { validator: validateStatus, trigger: 'blur' }
        ],
        user_id:     [
          { required: true, message: '用户编号不能为空', trigger: 'blur' },
          { validator: validateStatus, trigger: 'blur' }
        ],
        user_name:   [
          { required: true, message: '用户姓名不能为空', trigger: 'blur' },
          { validator: validateStatus, trigger: 'blur' }
        ],
        baseline_id: [
          { validator: validateStatus, trigger: 'blur' }
        ],
        role_ids:    [
          { validator: validateStatus, trigger: 'blur' }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .form-item {
    width: 170px;
  }

  .line1 {
    padding-bottom: 40px;
  }

  .line2 {
    margin-left: 50px;
  }
</style>
