<template>
  <div
      v-loading="loading"
      class="container"
  >
    <el-form
        ref="form"
        labelWidth="200px"
        :model="{rules: rules}"
    >
      <el-form-item
          label="开启基金状态异常检测"
      >
        <el-switch
            v-model="enable"
            active-text="开"
            inactive-text="关"
        />
      </el-form-item>

      <el-divider />
      <transition-group name="list-complete">
        <div
            v-for="(item, index) in rules"
            :key="index"
            class="list-complete-item form-item"
        >
          <div class="title">
            {{ `规则 ${index + 1}` }}
          </div>
          <el-form-item
              :label="`${system_names.reference_system}基金产品状态`"
              style="width: 430px;"
              label-width="270"
          >
            <el-select size="small" v-model="rules[index].status_display" disabled>
              <el-option :value="rules[index].status_display"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
              :label="`${system_names.alignment_system}基金产品状态`">
            <el-select size="small" v-model="rules[index].o32_status">
              <el-option
                  v-for="item in o32FundStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
            </el-select>
          </el-form-item>
        </div>
      </transition-group>
      <div class="submit-buttons" v-if="showUpdateSetting">
        <el-button
            size="small"
            type="primary"
            @click="updateConfirm"
        >
          更新设置
        </el-button>
        <el-button
            size="small"
            type="primary"
            @click="recheckComparison"
        >
          重新检查当前时间点数据
        </el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import RecheckButton from '@/components/AdminSettings/SystemAlignment/RecheckButton'
export default {
  mixins: [RecheckButton],
  data () {
    return {
      loading:         false,
      enable:          false,
      rules:           [],
      system_names:    {},
      o32FundStatusOptions: [{
        value: 1,
        label: "已注销"
      }, {
        value: 2,
        label: "正常"
      }, {
        value: 3,
        label: "未找到"
      }]
    }
  },
  computed: {
    showUpdateSetting() {
      return this.$store.getters.hasPermission('app_settings.fund_status_compare_rule_settings')
    },
  },
  created () {
    this.getSettings()
  },
  methods: {
    getSettings () {
      this.loading = true
      this.$axios.get('/admin_api/settings/system_alignment/fund_status_check')
          .then(response => {
            this.loading         = false
            this.enable          = response.data.enable
            this.rules           = response.data.rules
            this.system_names    = response.data.system_names
          })
          .catch(() => {
            this.loading = false
          })
    },
    updateConfirm () {
      this.$confirm('此操作将更新系统设置，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
          .then(() => {
            this.updateSettings()
          })
          .catch(() => {
            this.$message.info('已取消操作')
          })
    },
    updateSettings () {
      this.$axios.post('/admin_api/settings/system_alignment/fund_status_check', {
          enable: this.enable,
          rules:  this.rules
      })
          .then(() => {
            this.$message.success('设置已更新')
            this.getSettings()
          })
          .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.form-item {
  @include vertical_center_between;
  height: 63px;

}
.el-select{
  width: 160px;
}
.title{
  width: 60px;
  color: $font-color;
  line-height: 40px;
  margin-bottom: 22px;
}
.delete-button{
  margin-left: 10px;
  line-height: 40px;
  margin-bottom: 22px;

}
.list-complete-item {
  transition: all 0.5s;
}

.error {
  color: #E54D42;
  font-weight: bold;
}

.list-complete-enter, .list-complete-leave-to
  /* .list-complete-leave-active for below version 2.1.8 */
{
  opacity: 0;
  transform: translateY(-30px);
}
.submit-buttons{
  margin-top: 20px;
}
</style>
