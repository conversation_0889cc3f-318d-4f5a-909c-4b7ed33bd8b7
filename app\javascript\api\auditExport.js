import axios from '@/settings/axios'
import { parseFileName, downloadBlob   } from './tool'

export const auditExport = (params) => {
  return axios.get(`${params}`,
    { responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'audit.rar')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

export const auditExportStatus = (params) => {
  return axios.get('/api/audit_export_status', { params: params })
}

// 预览文件
export const auditReportPreview = (params) => {
  return axios.get('/api/audit_report_records/preview',
    {
      responseType: 'arraybuffer',
      params: params
    })
}

// 下载文件前判断
export const beforeDowload = (params) => {
  return axios.post('/api/audit_report_records/download', params)
}

// 审计报告文件列表
export const recordList = () => {
  return axios.get('/api/audit_report_records/list')
}

// 审计报告文件信息
export const recordInfo = (params) => {
  return axios.get('/api/audit_report_records', { params: params })
}

// 审计报告文件系统详细编辑
export const detailEdit = (params) => {
  return axios.put('/api/audit_report_records', params)
}

// 审计报告文件记录创建
export const recordGenerate = (params) => {
  return axios.post('/api/audit_report_records', params)
}

// 通过记录创建审计报告
export const recordCreateReport = (params) => {
  return axios.post('/api/audit_report_records/create_report', params)
}

// 直接下载， 没有文件提示创建
export const recordDowland = (params) => {
  return axios.post('/api/audit_report_records/download_audit_zip', params)
}

// 删除记录
export const recordDestroy = (params) => {
  return axios.delete(`/api/audit_report_records/${params.unique}`)
}
