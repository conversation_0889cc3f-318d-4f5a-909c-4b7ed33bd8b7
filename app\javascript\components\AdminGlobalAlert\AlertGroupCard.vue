<template>
  <el-card :class="{ 'alert-group-card': true, 'alert-group-card-large': biggerStyle }">
    <div slot="header" class="clearfix">
      <div class="place-box"></div>
      <div class="header-container">
        <div class="group-title">
          <span class="title-item">{{ item.group_name }}</span>
        </div>
        <div class="numberSpan">
          {{ `${item.processed_count}/${item.count}` }}
        </div>
      </div>
    </div>
    <el-scrollbar>
      <div class="group-card" style="height: 300px;">
        <div>
          <div
            v-for="alert in item.category_alerts"
            :key="alert.title"
            :class="elCardClass(alert)"
            @click="changeCategory(alert)"
            style="background: rgba(220, 232, 251, 0.3);"
          >
            <alert-item :alert="alert" class="alert-item" style='cursor: pointer;' />
          </div>
        </div>
      </div>
    </el-scrollbar>
  </el-card>
</template>

<script>
import AlertItem      from '@/components/AdminGlobalAlert/AlertItem.vue'
import AlertItemMixin from '@/components/AdminGlobalAlert/mixins/AlertItemMixin'

export default {
  components: {
    AlertItem
  },
  mixins: [AlertItemMixin],
  props: {
    item: {
      type: Object,
      required: true
    },
    currentType: {
      type: String,
      required: true
    },
    biggerStyle: {
      type: Boolean,
      default: false
    }
  },
  computed: {
  },
  methods: {
    elCardClass (alert) {
      let clazz = 'alert-container'
      if (this.currentType == alert.type) {
        clazz += ' active'
      }
      if (this.isFinishStyle(alert)) {
        clazz += ' finish-alert-card'
      }
      if (this.$settings.customerId === 'sldfund') {
        clazz += ' sldfund-alert-card'
      }
      return clazz
    },
    changeCategory(alert) {
      this.$emit('changeCategory', alert)
    },
    isFinishStyle (alert) {
      return this.alertPercent(alert) === 100
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/components/_variables';

.alert-container {
  padding: 15px;
  margin-top: 13px;
  border-radius: 4px;
  border: 1px solid transparent;
}

.alert-group-card-large {
  width: calc((100vw - 100px) / 3) !important;
}

.alert-group-card {
  width: 400px;
  height: 400px;

  .header-container {
    @include vertical_center_between;
    width: 100%;

    .numberSpan {
      font-weight: bolder;
      font-size: 17px;
      color: #0e6eff;
      margin-left: 10px;
    }

    .group-title {
      // flex + inline-size 启用容器查询
      display: flex;
      container-type: inline-size;
      width: 100%;
      height: 20px;
      overflow: hidden;
      white-space: nowrap;

      &:hover .title-item {
        animation: move 3s linear infinite both alternate;
        //-webkit-animation-play-state: paused;
      }

      @keyframes move {
        to {
          // 需要插值，否则 min 不同单位无法比较 https://github.com/sass/node-sass/issues/2963
          // 0px 转换后会变成 0， 在 css 中报错，因此改成 1px - 1px
          transform: translateX(#{'min(100cqw - 100%, 1px - 1px)'});
        }
      }
    }
  }
}

.alert-item {
  width: 100%;
}
</style>
