<template>
  <span>
    <span v-show="status === null">
      <i class="el-icon-warning" />
      任务尚未执行
    </span>
    <span v-show="status === 'ready'">
      <i class="el-icon-warning" />
      任务即将执行
    </span>
    <span v-show="status === 'process'">
      <i class="el-icon-loading" />
      任务正在执行
    </span>
    <span v-show="status === 'success'" >
      <i class="el-icon-success" />
      任务执行成功
    </span>
    <span v-show="status === 'failed'">
      <i class="el-icon-error" />
      任务执行失败
    </span>
  </span>
</template>

<script>
export default {
  props: {
    status: {
      validator: function (val) {
        return val === null || typeof val === 'string'
      },
      required: true
    }
  }
}
</script>

<style scoped>
  @import './status.scss';
</style>
