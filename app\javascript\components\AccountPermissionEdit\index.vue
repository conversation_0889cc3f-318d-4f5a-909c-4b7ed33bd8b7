<template>
  <span>
    <el-button
      v-if="isPermissionEditable && settings.editable"
      @click="dialogVisible = true"
    >
      <span style="color: red;">* </span>
      账号权限修改
    </el-button>
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      title="账号权限修改"
      width="50%"
      append-to-body
    >
      <el-tabs
        v-if="isRoleTabActive || isPermissionTabActive"
        v-model="tab"
        type="border-card"
      >
        <el-tab-pane
          v-if="isRoleTabActive"
          name="roles"
          label="角色管理"
        >
          <roles-edit
            :systemId="systemId"
            :accountId="accountId"
            :settings="settings"
          />
        </el-tab-pane>
        <el-tab-pane
          v-if="isPermissionTabActive"
          name="permissions"
          label="单独授权"
        >
          <permissions-edit
            :systemId="systemId"
            :accountId="accountId"
            :settings="settings"
          />
        </el-tab-pane>
        <el-tab-pane
          v-if="isPermissionTabActive"
          name="orgs"
          label="组织架构修改"
        >
        </el-tab-pane>

        <el-tab-pane
          v-if="isPermissionTabActive"
          name="password"
          label="密码重置"
        >
        </el-tab-pane>

      </el-tabs>
      <el-alert
        v-else
        :closable="false"
        title="该系统没有可修改的权限模块"
        type="error"
        center
        show-icon>
      </el-alert>
    </el-dialog>
  </span>
</template>

<script>
import RolesEdit from './roles.vue'
import PermissionsEdit from './permissions.vue'

export default {
  components: {
    RolesEdit,
    PermissionsEdit
  },
  props: {
    accountId: {
      type: Number,
      required: true
    },
    systemId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      dialogVisible: false,
      tab: '',
      isRoleTabActive: false,
      isPermissionTabActive: false,
      settings: {}
    }
  },
  computed: {
    isPermissionEditable () {
      // return this.$settings.permissionEditable.enable
      return false
    }
  },
  created () {
    this.getSystemSettings()
  },
  methods: {
    getSystemSettings () {
      this.$axios.get(`/api/systems/${this.systemId}/settings`)
        .then(response => {
          this.settings = response.data.settings || {}
          this.tabDefaultOpen()
        })
        .catch(() => {})
    },
    tabDefaultOpen () {
      if (this.settings.account_link_roles && this.settings.account_link_roles.enable) {
        this.isRoleTabActive = true
      }
      if (this.settings.account_personal_permissions && this.settings.account_personal_permissions.enable) {
        this.isPermissionTabActive = true
      }

      this.tab = this.isRoleTabActive ? 'roles' : 'permissions'
    }

  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

</style>
