import axios from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const list = (params) => {
  return axios.get('admin_api/global_alerts', { params: params })
}

export const handleRemove = (params) => {
  return axios.delete(`/admin_api/global_alerts/${params.alertId}/attachments/${params.attachmentId}`)
}

export const handleDownload = (params) => {
  return axios.get(`/admin_api/global_alerts/${params.alertId}/attachments/${params.attachmentId}`,
    { responseType: 'blob' })
    .then(response => {
      const fileName = parseFileName(response, 'alert')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

export const categoriesList = (params) => {
  return axios.get('/admin_api/global_alerts/categories_list', { params: params })
}

// 获取最近指定天数的最近的日期
export const getDateRange = (defaultQueryDate) => {
  let end                = new Date()
  let start              = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * defaultQueryDate)
  end          = end.getFullYear() + '-' + (end.getMonth() + 1) + '-' + end.getDate()
  start        = start.getFullYear() + '-' + (start.getMonth() + 1) + '-' + start.getDate()
  return [start, end]
}