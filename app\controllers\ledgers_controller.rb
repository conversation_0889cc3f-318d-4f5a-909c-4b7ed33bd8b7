# frozen_string_literal: true

# 台账 api 控制器
class LedgersController < ApplicationController
  before_action :authenticate_admin!
  before_action :set_business_system, except: %i[systems]
  before_action :set_ledger, only: %i[ledger_update ledger_delete edit_ignore_matcher]
  before_action :set_user, only: %i[ledger_update]

  def systems
    # authorize Ledger
    json_respond current_admin.business_systems_in_maintain.map(&:output)
  end

  def search
    authorize Ledger.new( business_system_id: @business_system.id )
    params_page     = params[:page] || 1
    params_per_page = params[:per_page] || 25

    filter = params[:filter] ? JSON.parse(params[:filter], symbolize_names: true) : {}

    params_only_no_user = filter[:onlyShowNoLinkAccount]
    search_field        = filter[:property]
    query               = filter[:value]
    account_status      = filter[:account_status]
    public_account_status = filter[:public_account_status]
    user_status         = filter[:user_status]
    role_ids            = filter[:role_ids]
    department_ids      = filter[:department_ids] || []
    position            = filter[:position]
    prop                = params[:prop]
    order               = params[:order]

    if account_status.to_s.blank? && public_account_status.to_s.blank? && query.to_s.blank? && user_status.blank? && role_ids.blank? && department_ids.blank? && position.blank?
      @ledgers = Ledger.where(business_system_id: @business_system.id).includes(:user)
      if params_only_no_user.to_s == 'true'
        @ledgers = @ledgers.where(user_id: nil)
      end
      @ledgers = @ledgers.page(params_page).per(params_per_page)
      json_respond(
        data:  @ledgers.map(&:simple_output),
        count: @ledgers.total_count
      )
    else
      search_result, search_count =
        Ledger.search(
          business_system: @business_system,
          search_field:    search_field,
          query:           query,
          only_no_user:    params_only_no_user,
          account_status:  account_status,
          public_account_status: public_account_status,
          user_status:     user_status,
          role_ids:        role_ids,
          department_ids:  department_ids,
          position:        position,
          params_page:     params_page,
          params_per_page: params_per_page,
          prop:            prop,
          order:           order
        )

      json_respond(
        data:  search_result.compact,
        count: search_count
      )
    end
  rescue QuarterNotFound => _e
    # 不调用 默认消息通知，因此不传 error_message
    json_custom_respond 404, {}
  end

  def edit_ignore_matcher
    authorize @ledger
    if @ledger.update(ignore_matcher:  params[:ignore_matcher])
      audit_log! [@business_system, @ledger, params[:ignore_matcher]], action: :ignore_matcher_change
      json_respond @ledger.output
    else
      json_custom_respond(:unprocessable_entity, error_message: @ledger.errors.full_messages.join('; '))
    end
  end

  def ledger_update
    authorize @ledger
    old_user = User.find_by(id: @ledger.user_id)

    if @ledger.update(
      user_id:         @user.id,
      user_code_field: User.unique_code_field,
      user_code_value: @user.send(User.unique_code_field)
    )

      audit_log! [@business_system, @ledger, @user]
      json_respond @ledger.output
    else
      json_custom_respond(:unprocessable_entity, error_message: @ledger.errors.full_messages.join('; '))
    end
  end

  def ledger_delete
    authorize @ledger
    # params 传参需要转换
    old_user       = User.find_by(id: @ledger.user_id)

    if @ledger.update(
      user_id:         nil,
      user_code_field: nil,
      user_code_value: nil
    )

      audit_log! [@business_system, @ledger, old_user]
      json_respond @ledger.output
    else
      json_custom_respond(:unprocessable_entity, error_message: @ledger.errors.full_messages.join('; '))
    end
  end

  def matchers
    authorize Ledger.new( business_system_id: @business_system.id )
    json_respond Ledger.matcher(@business_system)
  end

  def ledger_update_bulk
    authorize Ledger.new( business_system_id: @business_system.id )
    @error_result = []
    @result       = []
    ActiveRecord::Base.transaction do
      ledgers_params.each do |lp|
        update_one_account(lp)
      end
    end

    return json_custom_respond(400, error_message: '批量关联失败，请重新操作') unless @error_result.empty?

    audit_log! [@business_system, @result]
    json_respond(status: true)
  end

  private

  def set_ledger
    @account_code = params[:account_code]
    # @type [Ledger]
    @ledger =
      Ledger.find_by(
        business_system_id: @business_system.id,
        account_code_value: @account_code
      )

    json_custom_respond(404, error_message: 'Not found ledger record') unless @ledger
  end

  def ledgers_params
    # TODO: 转 Struct
    JSON.parse(request.body.read)
  end

  def update_one_account(record)
    account_code = record['account_code']
    user_id      = record['user_id']

    ledger = Ledger.find_by(business_system_id: @business_system.id, account_code_value: account_code)
    user   = User.find(user_id)

    if ledger.update(
      user_id:         user.id,
      user_code_field: User.unique_code_field,
      user_code_value: user.send(User.unique_code_field)
    )
      @result << { ledger: ledger, user: user }
    else
      @error_result << record
    end
  end

  def set_user
    @user = User.find(params[:user_id])
  rescue ActiveRecord::RecordNotFound => e
    json_custom_respond(404, error_message: e.message)
  end
end
