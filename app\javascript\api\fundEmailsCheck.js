import axios                           from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const reportExportExcel = (data) => {
  return axios.get(`/api/fund_emails_check/reports/export_excel`, {
    responseType: 'blob',
    params: data
  })
    .then(response => {
      const fileName = parseFileName(response, '产品报表.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch(() => {})
}

export const alertExportExcel = (data) => {
  return axios.get(`/api/fund_emails_check/alerts/export_excel`, {
    responseType: 'blob',
    params: data
  })
    .then(response => {
      const fileName = parseFileName(response, '报表异常告警.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch(() => {})
}