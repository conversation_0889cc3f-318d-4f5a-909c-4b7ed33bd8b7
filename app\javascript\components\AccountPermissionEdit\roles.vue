<template>
  <div class="container">
    <div class="tool-bar">
      <el-select
        v-model="selectRoleId"
        placeholder="选择角色"
        filterable
        style="width: 100%; margin-right: 10px;"
      >
        <el-option
          v-for="item in allRoles"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button
        @click="linkRole"
      >
          添加关联
      </el-button>
    </div>
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="dataByPage"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="角色编码"
          minWidth="100"
          fixed
        />

        <el-table-column
          prop="name"
          label="角色名称"
          minWidth="180"
        />
        <el-table-column
          label="操作"
          fixed="right"
          width="105"
        >
          <template slot-scope="scope">
            <el-button
              type="danger"
              size="mini"
              @click="handleUnlinkComfirm(scope.row.id)"
            >
              删除关联
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :pageSize.sync="pageSize"
        :total="accountRoles.length"
        :currentPage.sync="currentPage"
        :style="{ marginTop: '20px' }"
        :pageSizes="[10, 30, 50, 100]"
        background
        layout="total, sizes, prev, pager, next, jumper"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    accountId: {
      type: Number,
      required: true
    },
    systemId: {
      type: Number,
      required: true
    },
    settings: {
      type: Object,
      required: true
    }
  },

  data () {
    return {
      allRoles:     [],
      accountRoles: [],
      loading:      false,
      pageSize:     10,
      currentPage:  1,
      selectRoleId: null
    }
  },
  computed: {
    dataByPage () {
      let start = (this.currentPage - 1) * this.pageSize
      let end   = this.currentPage * this.pageSize
      return this.accountRoles.slice(start, end)
    }
  },
  created () {
    this.getSystemRoles()
    this.getAccountRoles()
  },
  methods: {
    getSystemRoles () {
      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/roles`)
        .then(response => {
          this.loading = false
          this.allRoles = response.data
        })
        .catch(() => {
          this.loading = false
        })
    },
    getAccountRoles () {
      this.loading = true
      this.$axios.get(`/admin_api/edit_api/${this.systemId}/accounts/${this.accountId}/roles`)
        .then(response => {
          this.loading = false
          this.accountRoles = response.data
        })
        .catch(() => {
          this.loading = false
        })
    },
    unlinkRole (id) {
      this.$axios.delete(`/admin_api/edit_api/${this.systemId}/accounts/${this.accountId}/roles/${id}`)
        .then(response => {
          this.$message.success('删除关联成功')
          this.getAccountRoles()
        })
        .catch(() => {
          this.$message.error('删除关联失败')
        })
    },
    linkRole () {
      if (!this.selectRoleId) return

      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts/${this.accountId}/roles/${this.selectRoleId}`)
        .then(response => {
          this.$message.success('关联成功')
          this.getAccountRoles()
        })
        .catch(() => {
          this.$message.error('关联失败')
        })
    },
    handleUnlinkComfirm (id) {
      this.$confirm('确认删除该角色关联吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        this.unlinkRole(id)
      })
      .catch(() => {
        this.$message.info('已取消操作')
      })
    },
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";
  .tool-bar{
    @include vertical_center_between;
  }
  .table-container{
    padding-top: 20px;
  }
</style>
