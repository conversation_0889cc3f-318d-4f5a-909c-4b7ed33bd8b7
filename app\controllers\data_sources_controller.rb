# frozen_string_literal: true

# 数据源
class DataSourcesController < ApplicationController
  before_action :authenticate_admin!
  before_action :set_data_source, only: %i[show destroy update database_status check_sql]
  before_action :authenticate_policy!, except: %i[check_sql database_status]
  before_action :password_decrypt, only: %i[create update]

  def index
    output_data_source = DataSource.order('id DESC').map(&:output)
    output_data_source = output_data_source.reject { |data_source| data_source[:is_data_center] } unless Setting.create_system&.[]('data_center')&.[]('enable')
    json_respond output_data_source
  end

  def create
    params[:data_source][:password] = Tool::Cryptology.encrypt_if_env(params[:data_source][:password])
    @data_source = DataSource.new(data_sources_params)
      
    if @data_source.save
      DataSource.update_importer_databases
      audit_log! @data_source.log_output
      json_respond @data_source.output
    else
      json_custom_respond(400, error_message: @data_source.errors.full_messages.join('; '))
    end
  end

  def show
    json_respond @data_source.output
  end

  def destroy
    unless @data_source.data_center?
      audit_log! @data_source.log_output if @data_source.destroy
    end
    DataSource.update_importer_databases
    json_respond_no_content
  end

  def update
    before_update_source = @data_source.log_output
    if @data_source.password != params[:data_source][:password]
      params[:data_source][:password] = Tool::Cryptology.encrypt_if_env(params[:data_source][:password])
    end
    if @data_source.update(data_sources_params)
      DataSource.update_importer_databases
      audit_log! [before_update_source, @data_source.log_output]
      json_respond @data_source.output
    else
      json_custom_respond(:unprocessable_entity, error_message: @data_source.errors.full_messages.join('; '))
    end
  end

  def check_sql
    json_respond @data_source.db_select(params[:sql], 20)
  end

  def database_status
    json_respond(status: @data_source.connect_status)
  end

  private

  def password_decrypt
    params[:data_source][:password] = aes_decrypt_data(params[:data_source][:password]) if params[:data_source][:password]
  end

  def set_data_source
    @data_source = DataSource.find(params[:id])
  end

  def data_sources_params
    params.require(:data_source).permit(:name, :db_type, :db_name, :host, :port, :version, :username, :password)
  end

  def authenticate_policy!
    authorize DataSource
  end
end
