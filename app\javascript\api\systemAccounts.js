import axios from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

export const accounts = (params) => {
  return axios.get(`/api/systems/${params.system_id}/accounts`, { params: params })
}

export const diffDatas = (params) => {
  return axios.get(`/api/systems/${params.system_id}/accounts/${params.account_id}/diff_datas`, {
    params: { other_quarter_id: params.other_quarter_id }
  })
}

export const allDatas = (params) => {
  return axios.get(`/api/systems/${params.system_id}/accounts/${params.account_id}/all_datas`,{
    params: { with_role: params.with_role }
  })
}

export const history = (params) => {
  return axios.get(`/api/systems/${params.system_id}/accounts/${params.account_id}/history`, {
    params: { page: params.page }
  })
}

export const diffRoles = (params) => {
  return axios.get(`/api/systems/${params.system_id}/accounts/${params.account_id}/diff_roles`, {
    params: { other_quarter_id: params.other_quarter_id }
  })
}

export const diffRolesWithAccount = (params) => {
  return axios.get(`/api/systems/${params.system_id}/accounts/${params.account_id}/diff_roles_with_account`, {
    params: { other_account_code: params.other_account_code }
  })
}

export const compareWithAccount = (params) => {
  return axios.get(`/api/systems/${params.system_id}/accounts/${params.account_id}/compare_with_account`, {
    params: { other_account_code: params.other_account_code }
  })
}

export const systemAccount = (params) => {
  return axios.get(`/api/systems/${params.system_id}/accounts/account`, {
    params: { other_account_code: params.other_account_code, account_id: params.account_id }
  })
}

export const exportWord = (params) => {
  return axios.get(`/api/systems/${params.systemId}/accounts/${params.accountId}/word_export`,
    { responseType: 'blob', params: { need_code: true } })
    .then(response => {
      const fileName = parseFileName(response, 'account.docx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
export const exportExcel = (params) => {
  return axios.get(`/api/systems/${params.systemId}/accounts/${params.accountId}/excel_export`,
    { responseType: 'blob', params: {need_code: true, otherQuarterId: params.otherQuarterId}})
    .then(response => {
      const fileName = parseFileName(response, 'account.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

export const searchAccounts = (params = {}) => {
  return axios.get(`/api/systems/${params.systemId}/search_accounts`, { params: params })
}

