<template>
  <div
    v-loading="loading"
    class="container"
  >
    <div class="tool-bar">
      <el-form
        ref="form"
        inline
        size="small"
      >
        <el-form-item>
          <system-roles-select
            clearable
            @change="updateSystemRole"
            size="small"
          />
        </el-form-item>
        <el-form-item
          style="float: right; margin-right: 0px;"
        >
          <div class="button-group">
            <el-button
              :disabled="!$store.getters.hasPermission('system_role_conflicts.conflicts_admin')"
              @click="addConflict"
              class="right-button"
            >
              添加规则
            </el-button>
            <rule-upload
              class="right-button"
              @update="gettingConflicts"
            />
            <rule-export
              class="right-button"
            />
            <el-button
              :disabled="!$store.getters.hasPermission('system_role_conflicts.query')"
              class="right-button"
              style="margin-left: 30px"
              @click="conflictUsersShow('system_role_conflicts')"
            >
              {{ conflictUsers }}
            </el-button>
          </div>


        </el-form-item>
      </el-form>
    </div>
    <el-table
      :data="conflictsByPage"
      border
      style="width: 100%"
    >
      <el-table-column
        prop="bs_name"
        label="系统名称"
        width="300"
      />
      <el-table-column
        prop="role_name"
        label="角色名称"
        width="300"
      >
      <template slot-scope="scope">
        <el-tag
          type="danger"
          size="small"
        >
          {{ scope.row.role_name }}
        </el-tag>
      </template>
      </el-table-column>
      <el-table-column
        prop="conflict_bs_name"
        label="冲突系统"
        width="300"
      />
      <el-table-column
        prop="conflict_role_name"
        label="冲突角色"
        width="300"
      >
        <template slot-scope="scope">
          <el-tag
            type="danger"
            size="small"
          >
            {{ scope.row.conflict_role_name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            :disabled="!$store.getters.hasPermission('system_role_conflicts.conflicts_admin')"
            size="small"
            type="danger"
            @click="destroyConfirm(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page.sync="currentPage"
      :page-size="pageSize"
      :total="conflicts.length"
      background
      layout="total, prev, pager, next, jumper"
      class="conflicts-pagination"
    />
    <conflict-form
      ref="conflictForm"
      @update="gettingConflicts"
    />
  </div>
</template>
<script>
import API               from '@/api'
import SystemRolesSelect from './systemRolesSelect'
import ConflictForm      from './conflictForm'
import RuleExport        from './ruleExport'
import RuleUpload        from './ruleUpload'

export default {
  components: {
    SystemRolesSelect,
    ConflictForm,
    RuleExport,
    RuleUpload
  },
  data () {
    return {
      loading:             false,
      localSystem:         [],
      localRole:           '',
      conflicts:           [],
      currentPage:         1,
      pageSize:            15,
      dialogVisible:       false
    }
  },
  computed: {
    conflictUsers () {
      return `跳转至${this.$t('system_role_conflicts.module_title')}功能`
    },
    conflictsByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.conflicts.slice(start, end)
    }
  },
  watch: {
    localSystem (val) {
      this.gettingConflicts()
    },
    localRole (val) {
      this.gettingConflicts()
    }
  },
  created () {
    this.gettingConflicts()
  },
  methods: {
    gettingConflicts () {
      this.loading = true
      const params = {
        system_id: this.localSystem,
        role_code: this.localRole,
        all:       true
      }
      API.systemRoleConflicts.conflicts(params)
        .then(response => {
          this.loading = false
          this.conflicts = response.data
        })
        .catch(_ => {
          this.loading = false
        })
    },
    addConflict () {
      this.$refs.conflictForm.dialogVisible = true
    },
    updateSystemRole (data) {
      this.localSystem = data.system_id
      this.localRole = data.role_code
    },
    conflictUsersShow (routerName) {
      this.$router.push({ name: routerName })
    },
    destroyConfirm (val) {
      this.$confirm('此操作将删除关联该冲突规则的所有信息，无法恢复，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          this.handleDelete(val)
        })
        .catch(() => {
          this.$message.info('已取消删除操作')
        })
    },
    handleDelete (val) {
      this.loading = true
      const params = {
        conflict_id: val
      }
      API.systemRoleConflicts.destroy(params)
        .then(response => {
          this.loading = false
          this.$message.success('成功删除该冲突规则')
          this.gettingConflicts()
        })
        .catch(_ => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
  @import '~@/components/variables';
  .conflicts-pagination{
    margin-top: 20px;
  }
  .button-group{
    @include vertical_center_right;
  }
  .right-button{
    margin-left: 10px;
  }
</style>
