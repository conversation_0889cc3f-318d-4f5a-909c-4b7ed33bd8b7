# frozen_string_literal: true

# 业务系统相关控制器
class AppRolesController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!, except: %i[list]

  before_action :set_app_role, only: %i[update destroy update_role_module_functions role_module_functions update_role_alerts_permissions]

  def index
    data = AppRole
             .enabled
             .filter_name(params[:filter])
             .map(&:output)
    add_admin_permission(data) if params[:is_admin_permission] == 'true'
    json_respond data
  end

  def list
    json_respond AppRole.enabled.map(&:select_list)
  end

  def create
    app_role = AppRole.new(app_role_params)

    if app_role.save
      audit_log! app_role.to_str
      json_respond app_role
    else
      json_custom_respond(400, error_message: app_role.errors.full_messages.join('; '))
    end
  end

  def update
    before_update_role = @app_role.to_str
    if @app_role.update(app_role_params)
      audit_log!([before_update_role, @app_role.to_str])
      json_respond @app_role
    else
      json_custom_respond(400, error_message: @app_role.errors.full_messages.join('; '))
    end
  end

  def destroy
    delete_role = @app_role
    @app_role.update(disabled: true)
    audit_log! delete_role.to_str
    json_respond_no_content
  end

  def module_functions
    json_respond AppModule.enabled.order(order: :asc).map(&:output)
  end

  def update_role_module_functions
    before_permissions = @app_role.module_functions&.pluck(:id)
    @app_role.module_functions = AppModuleFunction.where(id: params[:module_function_ids])

    audit_log! ({ before_permissions: before_permissions, role: @app_role }), audit_log_class: AppRoleAuditLog
    json_respond_no_content
  end

  def update_role_alerts_permissions
    before_permissions = @app_role.role_alert_permissions.map(&:output)
    @app_role.update_role_alerts_permissions(alert_permissions[:alert_permissions])

    audit_log! ({ before_permissions: before_permissions, role: @app_role }), audit_log_class: AppRoleAuditLog
    json_respond_no_content
  end

  private

  def authenticate_policy!
    authorize AppRole
  end

  def set_app_role
    @app_role = AppRole.enabled.find_by(id: params[:id])
  rescue StandardError => e
    json_custom_respond(404, error_message: e.message)
  end

  def app_role_params
    params.permit(:name, :description, admin_ids: [])
  end

  def alert_permissions
    params.permit(alert_permissions: [:id, :query, :maintain]).to_h
  end

  def add_admin_permission(data)
    is_all_permission = current_admin.permission? 'admin_account_manager.all_permission'
    if is_all_permission
      data.each { |x| x[:is_query] = true }
    else
      role_ids = current_admin.role_ids
      data.each { |x| x[:is_query] = role_ids.include?(x[:id]) }
    end
  end
end
