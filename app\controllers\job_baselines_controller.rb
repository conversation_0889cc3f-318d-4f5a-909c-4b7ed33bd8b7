# frozen_string_literal: true

# 数据中心
class JobBaselinesController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!
  before_action :set_job, only: %i[system_baselines output_system_baselines detail]
  before_action :set_job_baseline_and_name, only: %i[bind_roles_delete bind_roles_update bind_roles_create]
  after_action :delete_blank_job_baselines, only: :update

  def index
    json = begin
      JSON.parse(params[:filter])
    rescue StandardError
      {}
    end
    baselines = JobBaseline
                  .includes(:system_baselines, job: :users)
                  .ransack(search_params(json))
                  .result
                  .order('id DESC')
                  .page(params[:page])
                  .per(per_page)
    json_respond(data: baselines.map(&:output), count: baselines.total_count)
  end

  def create
    baseline = JobBaseline.new(job_baseline_params)
    system_baseline_ids  = params[:system_baselines].map { |o| o['system_baseline_id'] }.compact.uniq
    system_baselines     = SystemBaseline.where(id: system_baseline_ids)
    ban_system_ids       = params[:system_baselines].select { |o| o['ban'] }.map { |j| j['id'] }.compact.uniq
    ban_business_systems = BusinessSystem.where(id: ban_system_ids)
    if baseline.save
      system_baselines.each { |o| baseline.system_baselines << o }
      ban_business_systems.each { |o| baseline.ban_systems << o }
      AccountAutoLinkBaselineJob.perform_later(baseline.job_id)

      audit_log! baseline
      json_respond(success: true)
    else
      audit_log! baseline, action: :create_fail
      json_respond(success: false, error_message: baseline.errors.full_messages.join('; '))
    end
  end

  def update
    @baseline = JobBaseline.find(params[:id])
    system_baseline_ids = params[:system_baselines].map { |o| o['system_baseline_id'] }.compact.uniq
    ban_system_ids = params[:system_baselines].select { |o| o['ban'] }.map { |j| j['id'] }.compact.uniq

    system_baselines = SystemBaseline.where(id: system_baseline_ids)
    ban_business_systems = BusinessSystem.where(id: ban_system_ids)

    before_baseline = JobBaseline.find_by(id: params[:job_baseline][:id])
    before_system_baselines_ids = before_baseline.system_baselines.pluck(:id)
    before_ban_systems_ids = before_baseline.ban_systems.pluck(:id)

    if @baseline
      before_job_baseline = {
        system_baselines: SystemBaseline.where(id: before_system_baselines_ids),
        ban_systems:      BusinessSystem.where(id: before_ban_systems_ids)
      }

      @baseline.system_baselines.clear
      @baseline.ban_systems.clear

      system_baselines.each { |o| @baseline.system_baselines << o }
      ban_business_systems.each { |o| @baseline.ban_systems << o }

      after_job_baselines = {
        system_baselines: system_baselines,
        ban_systems:      ban_business_systems
      }
      AccountAutoLinkBaselineJob.perform_later(@baseline.job_id)

      audit_log! [@baseline, before_job_baseline, after_job_baselines]
      json_respond(success: true)
    else
      audit_log! @baseline, action: :update_fail
      json_respond(success: false, error_message: @baseline.errors.full_messages.join('; '))
    end
  end

  def destroy
    baseline = JobBaseline.find(params[:id])
    job_id = baseline.job_id
    baseline.job_baseline_ban_systems.destroy
    job_baseline_bind_role_enable = Setting.frontendSettings&.[]('jobBaselineBindRoles') || false
    baseline.system_baselines.each(&:destroy) if job_baseline_bind_role_enable
    baseline.destroy
    AccountAutoLinkBaselineJob.perform_later(job_id)
    audit_log! baseline
    json_respond(success: true)
  end

  # 返回岗位基线的系统基线
  def system_baselines
    job_baseline = @job&.job_baseline
    data =
      if job_baseline.present?
        baseline_data = job_baseline.system_baselines.map(&:output_detail)
        job_baseline.ban_systems.present? ? job_baseline.push_ban_systems_baseline(baseline_data) : baseline_data
      else
        []
      end
    json_respond data
  end

  # 获取指定岗位基线的系统基线，用于岗位基线表单，没有岗位基线就创建
  def output_system_baselines
    json_respond(@job.job_baseline_output_system_baselines)
  end

  # 返回所有系统及其系统基线，如果有id参数，返回各个系统的岗位基线
  def detail
    json_respond(@job.job_baseline_output_simple)
  end

  # # 导出岗位基线
  # def export
  #   audit_log!
  #   exporter = DataExport::SystemBaselineExport.new
  #   begin
  #     baselines_data = exporter.export
  #   rescue StandardError => e
  #     return json_custom_respond 400, error_message: e.message
  #   end
  #   send_data(
  #     baselines_data,
  #     filename: URI.encode_www_form_component("岗位基线导出.xlsx".gsub(/\s+/, '')),
  #     type:     'application/octet-stream;charset=utf-8'
  #   )
  # end

  def bind_roles_delete
    job_id = @job_baseline.job_id
    system_baseline_id = bind_roles_params[:system_baseline_id]
    system_name = SystemBaseline.find(system_baseline_id).business_system.name
    @job_baseline.system_baselines.where(id: system_baseline_id).destroy_all
    @job_baseline.destroy if @job_baseline.system_baselines.blank?

    AccountAutoLinkBaselineJob.perform_later(job_id)

    audit_log! [@job_name, system_name, system_baseline_id]
    json_respond(success: true)
  end

  def bind_roles_update
    system_baseline = SystemBaseline.find(bind_roles_params[:system_baseline_id])

    business_system = system_baseline.business_system
    role = get_role_by_codes(business_system, bind_roles_params[:role_codes])
    system_baseline.update(output_datas: system_baseline.output_datas.merge({ role: role }))
    system_baseline.copy_role_permissions if bind_roles_params[:copy_role_permissions]
    system_baseline.generate_history(current_admin.id)
    role_desc = role.map { |item| "#{item[:name]}(#{item[:code]})" }.join('、')
    audit_log! [@job_name, business_system.name, system_baseline.id, role_desc]
    json_respond(success: true)
  end

  def bind_roles_create
    business_system = BusinessSystem.find(bind_roles_params[:business_system_id])
    account_output_schema = BusinessSystem.compat_with_old_schema(business_system.account_class.output_schema)
    output_schema = SystemBaseline.init_schema_diff(BusinessSystem.add_role_schema(account_output_schema, business_system))
    role = get_role_by_codes(business_system, bind_roles_params[:role_codes])
    role_desc = role.map { |item| "#{item[:name]}(#{item[:code]})" }.join('、')
    system_baseline = SystemBaseline.find_or_initialize_by(
      business_system_id: business_system.id,
      name:               @job_name
    )
    system_baseline.output_schema = output_schema
    output_schema_data = system_baseline.calculate_output_schema_data&.each do |item|
      item[:is_notice] = false unless item[:data_key].to_s == 'role'
    end
    system_baseline.update(
      output_schema_data: output_schema_data,
      compare_rule:       'all_contrast',
      output_datas:       { role:  role }
    )
    system_baseline.copy_role_permissions if bind_roles_params[:copy_role_permissions]
    system_baseline.generate_history(current_admin.id)
    @job_baseline.system_baselines << system_baseline
    AccountAutoLinkBaselineJob.perform_later(@job_baseline.job_id)
    audit_log! [@job_name, business_system.name, role_desc]
    json_respond(success: true)
  end

  # 专门供鹏华基金使用，code是通过部门名称和岗位名称拼接的
  def system_baselines_with_job_name
    job = Job.find_by(code: "#{params[:department_name]}-#{params[:job_name]}")
    job_baseline = job&.job_baseline
    json_respond [] and return if job_baseline.blank?
    baseline_data = job_baseline.system_baselines.map(&:output_detail)
    data = job_baseline.ban_systems.present? ? job_baseline.push_ban_systems_baseline(baseline_data) : baseline_data
    if Setting.frontendSettings&.[]('baselinePermissionShow')
      data.each do |output_detail|
        notice_data_keys = output_detail[:output_schema_data].select { |item| item[:is_notice] }.pluck(:data_key)
        output_detail[:output_datas] = output_detail[:output_datas].select { |key, _| notice_data_keys.include?(key.to_s) }
        output_detail[:output_schema] = output_detail[:output_schema].select { |item| notice_data_keys.include?(item[:data_key].to_s) }
      end
    end
    json_respond data
  end

  private

  def job_baseline_params
    params.require(:job_baseline).permit(:name, :job_id)
  end

  def bind_roles_params
    params.require(:job_baseline).permit(:system_baseline_id, :business_system_id,
                                         :id, :job_id, :job_name, :copy_role_permissions, role_codes: [])
  end

  def set_job
    @job = Job.find_by(id: params[:job_id])
  end

  def authenticate_policy!
    authorize JobBaseline
  end

  # 转换为ransack的搜索参数
  def search_params(json)
    return unless %w[id_eq name_cont].include? json['property']

    { json['property'] => json['value'] }
  rescue StandardError
    nil
  end

  def set_job_baseline_and_name
    @job_baseline = if bind_roles_params[:id].present?
                      JobBaseline.find(bind_roles_params[:id])
                    else
                      JobBaseline.create_with(name: bind_roles_params[:job_name])
                        .find_or_create_by(job_id: bind_roles_params[:job_id])
                    end
    @job_name = @job_baseline.job.name
  end

  def get_role_by_codes(business_system, role_codes)
    last_quarter_id = business_system.quarters_after_import.last.id
    role_keys = business_system.account_class.new.roles_schema.pluck(:property).map(&:to_sym)
    if business_system.role_class.column_names.include?('code')
      roles = business_system.role_class.where(quarter_id: last_quarter_id, code: role_codes)
    else
      roles = business_system.role_class.where(quarter_id: last_quarter_id, name: role_codes)
    end
    roles.map { |role| role.attributes.deep_symbolize_keys.slice(*role_keys) }.uniq
  end

  # 删除相关的空岗位基线
  def delete_blank_job_baselines
    return unless @baseline.system_baselines.count.zero? && @baseline.ban_systems.count.zero?

    @baseline.destroy
  end
end
