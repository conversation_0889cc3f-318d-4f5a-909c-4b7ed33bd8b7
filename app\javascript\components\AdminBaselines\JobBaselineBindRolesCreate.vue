<template>
  <el-dialog
      :visible.sync="visible"
      title="添加"
      append-to-body
      width="400px"
      :close-on-click-modal="false"
  >
    <el-container>
      <el-form ref="form" :model="localForm" :rules="rules" label-width="80px">
        <el-form-item label="系统名称" prop="business_system_id">
          <el-select
              v-model="localForm.business_system_id"
              placeholder="请选择系统"
          >
            <el-option
                v-for="item in businessSystems"
                :key="item.id"
                :disabled="item.is_disable"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="角色" prop="role_codes">
          <role-select :system_id="localForm.business_system_id" @chooseRoles="chooseRoles"></role-select>
          <el-checkbox v-model="copy_role_permissions" style="margin-top: 8px;">复制角色权限到基线</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSubmit">确认</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-container>
  </el-dialog>
</template>

<script>

import RoleSelect from "./RoleSelect.vue";
export default {
  components: {
    RoleSelect
  },
  props: ['businessSystems'],
  data() {
    return {
      visible: false,
      copy_role_permissions: false,
      localForm: {
        business_system_id: null,
        role_codes: [],
        copy_role_permissions: false
      },
      rules: {
        business_system_id: [
          { required: true, message: '系统不能为空', trigger: 'blur' }
        ],
        role_codes: [
          { required: true, message: '必须选择角色', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    chooseRoles(role_codes) {
      this.localForm.role_codes = role_codes
    },
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.localForm.copy_role_permissions = this.copy_role_permissions
          this.$emit('create', this.localForm)
        } else {
          return false;
        }
      });
    },
    handleSubmit () {
      if (this.copy_role_permissions){
        this.$confirm('基线权限将被覆盖，确认将角色权限同步到系统基线权限吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          this.onSubmit()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
      }
      else {
        this.onSubmit()
      }
    },
    cancel() {
      this.visible = false
      this.localForm = {
        business_system_id: null,
        role_codes: []
      }
    }
  }
}
</script>
