import axios from '@/settings/axios'

import * as quarters from './quarters'
import * as systems from './systems'
import * as auth from './auth'
import * as systemAccounts from './systemAccounts'
import * as systemBaselines from './systemBaselines'
import * as systemScaffolds from './systemScaffolds'
import * as schemas from './schemas'
import * as mappings from './mappings'
import * as mappingLedgers from './mappingLedgers'
import * as dataSources from './dataSources'
import * as dataCenterGroups from './dataCenterGroups'
import * as dataCenters from './dataCenters'
import * as tool from './tool'
import * as users from './users'
import * as dataStatistics from './dataStatistics'
import * as adminAccounts from './adminAccounts'
import * as quarterCompared from './quarterCompared'
import * as quarterRoleCompared from './quarterRoleCompared'
import * as ledgers from './ledgers'
import * as systemAlignments from './systemAlignments'
import * as auditLogs from './auditLogs'
import * as customerAuditLogs from './customerAuditLogs'
import * as jobBaselines from './jobBaselines'
import * as jobs from './jobs'
import * as departments from './departments'
import * as globalAlerts from './globalAlerts'
import * as fundEmailsCheck from './fundEmailsCheck'
import * as auditExport from './auditExport'
import * as PcCompareGuzhi from './pcComapreGuzhi'
import * as PcCompareO32 from './pcComapreO32'
import * as externalAccounts from './externalAccounts'
import * as externalRoles from './externalRoles'
import * as externalPermissions from './externalPermissions'
import * as externalRolePermissions from './externalRolePermissions'
import * as externalBusinessSystems from './externalBusinessSystems'
import * as fdfgCompareGzyss45 from './fdfgCompareGzyss45'
import * as roles from './roles'
import * as adminPermissions from './adminPermissions'
import * as jiaoyiTemporaryPermission from './jiaoyiTemporaryPermission'
import * as globalAnalysis from './globalAnalysis'
import * as quarterUserJobBaselineDiff from './quarterUserJobBaselineDiff'
import * as dimissionAccountFiles from './dimissionAccountFiles'
import * as publicAccountType from './publicAccountType'
import * as externalEcerts from './externalEcerts'
import * as systemRoleConflicts from './systemRoleConflicts'
import * as oaRecords from './oaRecords'
import * as ledgerDepartments   from './ledgerDepartments'
import * as downloadRecords   from './downloadRecords'
import * as o32Options from './o32Options'
import * as o32OptionQuarters    from './o32OptionQuarters'
import * as o32OptionDifferences from './o32OptionDifferences'
import * as o32OptionConfigs from './o32OptionConfigs'
import * as quarterBusinessSystemImportStatuses from './quarterBusinessSystemImportStatuses'
import * as quarterSystemTaskStatuses from './quarterSystemTaskStatuses'
import * as o32OptionBaselines from './o32OptionBaselines'

export const aasInfo = () => {
  return axios.get('/api/aas_info')
}

export const updateAlert = (params) => {
  return axios.put('/api/aas_info', params)
}
export default {
  auth,
  quarters,
  systems,
  systemBaselines,
  systemScaffolds,
  schemas,
  mappings,
  mappingLedgers,
  dataSources,
  dataCenterGroups,
  dataCenters,
  tool,
  users,
  dataStatistics,
  adminAccounts,
  quarterCompared,
  quarterRoleCompared,
  ledgers,
  systemAlignments,
  systemAccounts,
  auditLogs,
  customerAuditLogs,
  aasInfo,
  updateAlert,
  roles,
  adminPermissions,
  jiaoyiTemporaryPermission,
  globalAnalysis,
  jobBaselines,
  jobs,
  departments,
  globalAlerts,
  fundEmailsCheck,
  auditExport,
  externalAccounts,
  externalRoles,
  externalPermissions,
  externalRolePermissions,
  externalBusinessSystems,
  externalEcerts,
  PcCompareGuzhi,
  PcCompareO32,
  fdfgCompareGzyss45,
  quarterUserJobBaselineDiff,
  dimissionAccountFiles,
  publicAccountType,
  systemRoleConflicts,
  oaRecords,
  ledgerDepartments,
  downloadRecords,
  o32Options,
  o32OptionQuarters,
  o32OptionDifferences,
  o32OptionConfigs,
  quarterBusinessSystemImportStatuses,
  quarterSystemTaskStatuses,
  o32OptionBaselines
}
