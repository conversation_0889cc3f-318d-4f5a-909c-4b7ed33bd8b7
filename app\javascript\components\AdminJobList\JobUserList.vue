<template>
  <span>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :title="title"
      width="900px"
      :before-close="handleClose"
      @open="handleOpen"
    >
      <el-form :disabled="!loadingAll ? false :true">
        <div class="dialog-container">
          <div class="toolbar">
            <div class="search-box-link-bulk">
              <!--
              <el-cascader
                v-model="local_departments"
                v-loading='loadingDepartments'
                element-loading-spinner="el-icon-loading"
                :options="treeDepartments"
                :props="cascaderDepartmentProps"
                size="small"
                filterable
                clearable
                placeholder="搜索部门"
                :collapse-tags="true"
                style="width:280px;margin-right:10px;"
              >
              </el-cascader>
              -->
              <department-select
                v-model="local_departments"
                placeholder="搜索部门"
                :department-id="[]"
                size="small"
                :checkStrictly="true"
                multiple
                collapseTags
                filterable
                clearable
                selectStyle="width:280px;margin-right:0px;"
              />

              <el-select
                v-model="local_status"
                placeholder="请选择状态"
                size="small"
                clearable
              >
                <el-option
                  key="true"
                  :label="getUserInserviceString(true)"
                  value="true"
                />
                <el-option
                  key="false"
                  :label="getUserInserviceString(false)"
                  value="false"
                />
              </el-select>
<!--
              <el-select
                v-model="local_jobs"
                placeholder="请选择岗位"
                size="small"
                clearable
                filterable
                multiple
              >
                <el-option
                  v-for="(x, index) in jobs"
                  :key="index"
                  :label="x"
                  :value="x"
                />
              </el-select>
 -->
              <el-input
                v-model="userName"
                size="small"
                placeholder="请输入员工名称"
              />
              <el-input
                v-model="userCode"
                size="small"
                placeholder="请输入员工编号"
              />
            </div>
            <el-button
              type="primary"
              size="small"
              class="search-box-button"
              @click="runSearch"
            >
              搜索
            </el-button>
          </div>

          <div style="display:flex;justify-content: space-between;margin-top: 20px; ">
            <el-table
              ref="table"
              v-loading="loadingAll"
              :data="screenAccounts.slice((currentPage-1)*pageSize,currentPage*pageSize)"
              border
              style="width: 30%;"
              size="mini"
              height="450"
              :row-key="(row) => { return row.id }"
              @select="handleSelectionChange"
              @select-all="handleSelectionChangeAll"
            >
              <el-table-column
                type="selection"
                :reserve-selection="true"
                width="55"
              />
              <el-table-column
                prop="name"
                label="员工名称"
              >
                <template slot-scope="scope">
                  <span slot="reference">{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="inservice"
                label="状态"
                width="100"
              >
                <template slot-scope="scope">
                  <el-tag
                    :type="scope.row.inservice ? 'success' : 'primary'"
                    disable-transitions
                  >
                    {{ getUserInserviceString(scope.row.inservice) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin:auto 20px">
              <el-button
                type="primary"
                class="btn"
                :disabled="tableLeft.length <= 0"
                @click="btnClickLeft"
              >
                <span><i class="el-icon-arrow-left" /></span>
              </el-button>
              <el-button
                type="primary"
                class="btn"
                :disabled="tableRight.length <= 0"
                @click="btnClickRight"
              >
                <span><i class="el-icon-arrow-right" /></span>
              </el-button>
            </div>
            <el-table
              ref="tableRight"
              v-loading="loadingRight"
              :data="screenAccountsRights"
              border
              :row-key="(row) => { return row.id }"
              style="width: 30%"
              size="mini"
              height="450"
              @select="handleSelectionChangeRight"
              @select-all="handleSelectionChangeRightAll"
            >
              <el-table-column
                type="selection"
                width="55"
                :reserve-selection="true"
              />
              <el-table-column
                prop="name"
                label="员工名称"
              >
                <template slot-scope="scope">
                  <span slot="reference">{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="inservice"
                label="状态"
                width="100"
              >
                <template slot-scope="scope">
                  <el-tag
                    :type="scope.row.inservice ? 'success' : 'primary'"
                    disable-transitions
                  >
                    {{ getUserInserviceString(scope.row.inservice) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[1,5,10,20]"
            style="margin-top: 20px;"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="screenAccounts.length"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="visible = false"
        >关 闭
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="handleComfirm"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
  </span>
</template>

<script>
import API from '@/api'
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'

export default {
  components: {
    DepartmentSelect
  },
  props: {
    job: {
      type:     Object,
      required: true,
      default: () => {}
    }
  },
  data () {
    return {
      visible:              false,
      loadingAll:           false,
      loadingRight:         false,
      linkIds:              [],
      allAccounts:          [],
      screenAccounts:       [],
      screenAccountsRights: [],
      tableRight:           [],
      tableLeft:            [],
      value:                '',
      userName:             '',
      userCode:             '',
      local_jobs:           '',
      local_departments:    [],
      local_status:         '',
      currentPage:          1,  // 当前页码
      total:                '', // 总条数
      pageSize:             10, // 每页的数据条数
      jobs:                 [],
      departments:          [],
      loadingDepartments:   false,
      treeDepartments:      [],
      cascaderDepartmentProps: {
        multiple:      true
        // emitPath:      true,
        // checkStrictly: true
      }
    }
  },
  computed: {
    title () {
      return `${this.job.name}员工管理`
    }
  },
  watch: {
  },
  created () {
    this.getTreeDepartments()
  },
  mounted () {
  },
  methods: {
    handleOpen () {
      this.getJobUsers()
    },
    getTreeDepartments () {
      const params = { mode: 'tree' }
      this.loadingDepartments = true
      API.departments.index(params)
        .then(response => {
          this.loadingDepartments = false
          this.treeDepartments = response.data
        })
        .catch(() => {
          this.loadingDepartments = false
        })
    },
    runSearch () {
      this.screenAccounts = []
      const departments = this.local_departments
      for (var i = 0; i < this.allAccounts.length; i++) {
        if (this.allAccounts[i].inservice == null) {
          this.allAccounts[i].inservice = true
        }
        if (this.local_jobs.length > 0 && this.allAccounts[i].roles.filter(item => { return this.local_jobs.includes(item) }).length !== this.local_jobs.length) {
          continue
        }
        if (this.local_status && this.allAccounts[i].inservice.toString() !== this.local_status.toString()) {
          continue
        }
        if (departments.length !== 0 && departments.indexOf(this.allAccounts[i].department_id) < 0) {
          continue
        }
        if (this.userName && !this.allAccounts[i].name.includes(this.userName)) {
          continue
        }
        if (this.userCode && !this.allAccounts[i].code.includes(this.userCode)) {
          continue
        }
        this.screenAccounts[this.screenAccounts.length] = this.allAccounts[i]
      }
    },
    handleClose (done) {
      this.screenAccountsRights = []
      done()
    },
    getJobUsers () {
      this.loadingAll = true
      this.loadingRight = true
      this.tableLeft.length = 0
      this.screenAccounts = []
      this.screenAccountsRights = []
      API.jobs.users(this.job.id)
        .then(response => {
          // 这个判断非常重要，避免连续打开员工管理弹框，前面打开的后返回请求，导致数据被覆盖
          if (this.job.id === response.data.job.id){
            const department_tree_ids = response.data.job.department_tree_ids
            this.loadingAll     = false
            this.loadingRight   = false
            this.screenAccountsRights = response.data.job_users
            this.allAccounts    = response.data.users
            this.screenAccounts = this.allAccounts
            this.$refs.tableRight.clearSelection()
            this.$refs.table.clearSelection()
            // 默认展示当前部门的员工
            this.local_departments = department_tree_ids.length == 0 ? [] : [department_tree_ids]
            this.runSearch()
          }
        })
        .catch(() => {
          this.loadingAll = false
          this.loadingRight = false
        })
    },
    handleSubmit () {
      const userIds = this.screenAccountsRights.map(e => e.id)
      this.$axios.post(`/api/job_users`, {
        job_id:   this.job.id,
        user_ids: userIds
      })
        .then(response => {
          this.$message.success('成功保存岗位员工')
          this.$emit('change')
        })
        .catch(() => {})
    },
    handleComfirm () {
      this.handleSubmit()
      this.visible = false
    },
    handleSizeChange (val) {
      this.currentPage = 1
      this.pageSize = val
    },
    handleCurrentChange (val) {
      this.currentPage = val
    },
    handleSelectionChange (e) {
      this.tableRight = e
    },
    handleSelectionChangeAll (e) {
      this.tableRight = e
    },
    handleSelectionChangeRight (e) {
      this.tableLeft = e
    },
    handleSelectionChangeRightAll (e) {
      this.tableLeft = e
    },
    btnClickLeft () {
      this.$refs.table.clearSelection()
      this.screenAccounts = [...new Set([...this.tableLeft, ...this.screenAccounts])]
      this.screenAccountsRights = [...new Set(this.resArr(this.screenAccountsRights, this.tableLeft))]
      this.tableLeft = []
    },
    btnClickRight () {
      this.$refs.table.clearSelection()
      this.$refs.tableRight.clearSelection()
      this.screenAccountsRights.push(...this.tableRight)
      this.screenAccounts = this.resArr(this.screenAccounts, this.screenAccountsRights)
      this.tableRight = []
    },
    resArr (arr1, arr2) {
      return arr1.filter((v) => arr2.every((val) => val.id !== v.id))
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/components/variables';

.dialog-container {
  padding-left:  20px;
  padding-right: 0px;

  .toolbar {
    @include vertical_center_between;
    margin-bottom: 1.5em;
    margin-left:   0px;

    .search-box-link-bulk {
      .el-input {
        width:        130px;
        margin-right: 10px;
      }

      .el-select {
        width:        130px;
        margin-right: 10px;
      }
    }

    .search-box-button {
      margin-right: 25px;
    }
  }
}
.dialog-footer{
  margin-right: 25px;
}
</style>

<style lang="scss" scoped>
@import '~@/components/variables';

.btn {
  width: 56px;
  height: 40px;
}
</style>
