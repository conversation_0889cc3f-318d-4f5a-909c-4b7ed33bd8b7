<script>
import API from '@/api'
export default {
  methods: {
    getMyFunctions (headers, isOnlyPersonalUser) {
      API.auth.myFunctions(headers)
        .then(response => {
          this.$store.commit('myFunctionsSetter', response.data)
          this.redirectToRouter(isOnlyPersonalUser)
        })
        .catch(error => {
          console.error(error)
        })
    },

    getAdminRouters (headers) {
      API.auth.myAdminRouters(headers)
        .then(response => {
          this.$store.commit('adminRoutersSetter', response.data.admin)
          this.$store.commit('queryRoutersSetter', response.data.query)
          this.$store.commit('alertRoutersSetter', response.data.alert)
          this.$store.commit('o32OptionRoutersSetter', response.data.o32_option)
        })
        .catch(error => {
          console.error(error)
        })
    },

    getNewestQuarter (headers) {
      API.quarters.list(headers)
        .then(response => {
          const quarters = response.data
          // 可能为空
          if (quarters.length !== 0) this.$store.commit('quarterChange', quarters[0])
        })
        .catch(error => {
          console.error(error)
        })
    },

    getAdminPrivileges (headers) {
      API.auth.myAdminPrivileges(headers)
        .then(response => {
          this.$store.commit('adminPrivilegesSetter', response.data)
        })
        .catch(error => {
          console.error(error)
        })
    },

    getUserTree (headers) {
      API.users.departmentTreeWithUsers(headers)
        .then(response => {
          this.$store.commit('storeUserTree', response.data.tree_user)
          this.$store.commit('storeInserviceUserCount', response.data.inservice_user_count)
        })
        .catch(() => {})
    },

    getGlobalAlertSearch (headers) {
      this.$axios.get('admin_api/settings/system_general_settings', { headers: headers })
        .then(response => {
          const setting = response.data.globalAlertSettings
          this.$store.commit('StoreGlobalAlertSearchStatus', setting.search.status)
          this.$store.commit('StoreGlobalAlertSearchRecoverStatus', setting.search.recover_status)
        })
        .catch(() => {})
    },

    getPositions (headers) {
      API.users.getPositions(headers)
        .then(response => {
          this.$store.commit('storePositions', response.data)
        })
        .catch(() => {})
    },

    redirectToRouter (isOnlyPersonalUser) {
      if (this.$store.state.myFunctions.length == 0) {
        return this.$router.push({ name: 'welcome' })
      }
      // 外部系统不需要重置密码，但是配置首页就会失效
      if (['ldap','sso','external'].includes(this.authMethod)) {
        this.$router.push({ name: 'home' })
        return
      }
      const needChangePassword = this.$store.state.admin.need_change_password
      const homePage = this.$store.state.admin.home_page

      if (needChangePassword) {
        return this.$router.push({ name: 'password_change', params: { message: '您的密码已过期，请修改密码' } })
      }
      if (isOnlyPersonalUser) {
        this.$router.push({ name: 'personal-page' })
      } else {
        this.$router.push({ name: homePage })
      }
    },

    noticeMessage (headers) {
      if (!this.$settings.oaRecord.enable) {
        return
      }
      // 如果没有oa流程查询权限，不通知
      if (!this.$store.getters.hasPermission('oa_record.query')) {
        return
      }
      API.oaRecords.notice(headers)
        .then(response => {
          if (response.data.is_notice) {
            this.noticeOaRecord()
          }
        })
        .catch(() => {})
    },

    noticeOaRecord () {
      const h = this.$createElement;
      this.$notify.info({
        title: 'OA 流程临时权限通知',
        dangerouslyUseHTMLString: true,
        duration: 0,
        // message: '有即将到期或已经过期的未处理临时权限需要处理，<a @click="redirectToOaRecord()">点击</a> 跳转'
        message: h (
          "span",
          {},
          [
            h("span", {}, '有即将到期或已经过期的未处理临时权限需要处理，点击'),
            h("a",
              {
                class: 'link', // 自定义class
                on: {
                  click: this.redirectToOaRecord
                },
              },
              "跳转"
            )
          ]
        )
      });
    },

    redirectToOaRecord () {
      this.$router.push({ path: `/admin/oa_record` })
    },

    // 获取最新o32参数时间点
    getNewestO32OptionQuarter (headers) {
      API.o32OptionQuarters.newList(headers)
        .then(response => {
          const quarters = response.data.data
          // 可能为空
          if (quarters.length !== 0) this.$store.commit('o32OptionQuarterChange', quarters[0])
          // this.$store.commit('o32OptionQuarterChange', quarters[0])
        })
        .catch(error => {
          console.error(error)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.link {
  cursor: pointer;
}
</style>
