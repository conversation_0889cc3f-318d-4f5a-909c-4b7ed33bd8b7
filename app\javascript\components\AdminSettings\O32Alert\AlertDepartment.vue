<template>
  <div v-loading="loading">
    <div class="title">
      <div class="placeholder-box"></div>
      <h3 class="title-label">部门总、投资总监、分管领导组合查询权限</h3>
      <div  style="margin-left: 30px">
        <el-button
          icon="el-icon-plus"
          type="text"
          @click="addPermission"
        >
          新增稽核账号
        </el-button>
      </div>
    </div>
    <el-form
      ref="form"
      inline
    >

      <div v-for="(item,index) in permissions_1">
        <el-form-item
          label="选择账号"
        >
          <div class="form-item-container">
            <el-select
              v-model="item.accounts"
              placeholder="请选择"
              clearable
              multiple
              filterable
              style="width:300px"
            >
              <el-option
                v-for="account in accounts"
                :label="account.name"
                :value="account.code"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item
          label="选择部门"
        >
          <div class="form-item-container">
            <el-select
              v-model="item.departments"
              placeholder="请选择"
              clearable
              multiple
              filterable
              style="width:500px"
            >
              <el-option
                v-for="department in departments"
                :label="department"
                :value="department"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-button
          type="text"
          @click="deletePermission(index)"
          style="margin-left: 20px"
        >
          删除
        </el-button>
      </div>
      <div>
        <el-button
          size="small"
          type="primary"
          @click="subSettings()"
        >
          保存
        </el-button>
      </div>

    </el-form>
  </div>
</template>
<script>
export default {
  components: {
  },
  props: {
    alertSetting: {
      type: Object,
      default: () => {},
      required: true
    }
  },
  data () {
    return {
      loading:       false,
      permissions_1: []
    }
  },
  computed: {
    departments () { return this.alertSetting.departments },
    accounts () { return this.alertSetting.accounts }
  },
  created () {
    this.getSettings()
  },
  methods: {
    deletePermission(index) {
      this.permissions_1.splice(index, 1)
    },
    addPermission() {
      this.permissions_1.push({
        accounts: [],
        departments: []
      })
    },

    getSettings () {
      this.loading = true
      this.$axios.get('/admin_api/global_alerts/o32_alert_settings/5')
        .then(response => {
          this.permissions_1 = response.data.permissions_1
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    subSettings () {
      this.loading = true
      const theParams = {
        permissions_1: this.permissions_1
      }
      this.$axios.post('/admin_api/global_alerts/o32_alert_settings/5', theParams)
        .then(response => {
          this.$message.success('添加成功')
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.title{
  @include  vertical_center_left;
  margin-bottom: 20px;
  height: 40px;

  .placeholder-box{
    @include placeholder_box;
  }
  .title-label{
    margin-top: 0;
    margin-bottom: 0;
  }
}
</style>
