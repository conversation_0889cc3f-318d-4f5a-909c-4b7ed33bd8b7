import axios from '@/settings/axios'
import { absoluteUrlFor, download } from './tool'

export const index = (params = {}) => {
  return axios.get(`/api/oa_records`, { params: params })
}

export const detail = (id) => {
  return axios.get(`/api/oa_records/${id}`, {})
}

export const update = (id, params) => {
  return axios.put(`/api/oa_records/${id}`, params)
}

export const options = () => {
  return axios.get(`/api/oa_records/options`, {})
}

export const notice = () => {
  return axios.get(`/api/oa_records/notice`, {})
}