<template>
  <div
    v-loading="loading"
    class="info-container"
  >
    <el-descriptions
      :column="3"
      :border="border"
    >
      <el-descriptions-item
        v-for="column in userColumns"
        :key="column.prop"
      >
        <template slot="label">
          {{ column.label }}
        </template>
        {{ column.value }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>

export default {
  props: {
    userId: {
      type:     Number,
      required: true
    },
    border: {
      type:    Boolean,
      default: false
    }
  },
  data () {
    return {
      userColumns: [],
      loading:     false
    }
  },
  watch: {
    userId () {
      this.getUserInfo()
    }
  },
  created () {
    this.getUserInfo()
  },
  methods: {
    getUserInfo () {
      this.loading = true
      this.$axios.get(`/api/users/${this.userId}/info`)
        .then(response => {
          this.userColumns = this.convertTableData(response.data)
          this.$emit('get', response.data)
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    convertTableData (userObject) {
      let userColumns = this.$settings.userInfoColumns
      return userColumns.map(x => {
        return {
          label: x.label,
          value: userObject[x.prop.replace(/info\./, '')]
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
