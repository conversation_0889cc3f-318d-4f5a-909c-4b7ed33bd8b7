import axios from '@/settings/axios'
import { absoluteUrlFor, download } from './tool'

export const index = (params = {}) => {
  return axios.get(`/api/jobs`, { params: params })
}

export const list = (params = {}) => {
  return axios.get(`/api/jobs/list`, { params: params })
}

export const detail = (id) => {
  return axios.get(`/api/jobs/${id}`)
}

export const update = (id, params = {}) => {
  return axios.put(`/api/jobs/${id}`, params)
}

export const create = (params = {}) => {
  return axios.post(`/api/jobs`, params)
}

export const destroy = (id) => {
  return axios.delete(`/api/jobs/${id}`)
}

export const users = (id, params = {}) => {
  return axios.get(`/api/jobs/${id}/users`, { params: params })
}