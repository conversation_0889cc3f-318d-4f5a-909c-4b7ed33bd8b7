<template>
  <el-drawer
    :title="dialogTitle"
    :visible.sync="visible"
    :before-close="handleClose"
    size="800px"
    direction="rtl"
    destroy-on-close
    class="edit-permission-api-drawer"
  >
    <div class="container">
      <div class="button-bar">
        <el-button
          v-if="dialogFlag === 'show'"
          @click="dialogFlag = 'edit'"
        >
          修改用户
        </el-button>
        <el-button
          v-if="dialogFlag === 'show'"
          @click="handleLinkRolesReadonly"
        >
          角色详情
        </el-button>
        <el-button
          v-if="dialogFlag === 'show'"
          @click="handleLinkRoles"
        >
          角色授权
        </el-button>
      </div>

      <el-form
        ref="form"
        :model="localAccount"
        :rules="rules"
        label-width="140px"
      >
        <el-form-item
          prop="id"
          label="ID"
          v-if="dialogFlag !== 'create'"
        >
        <el-input
            v-model="localAccount.id"
            :disabled="true"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="login_name"
          label="登录名"
        >
          <el-input
            v-model="localAccount.login_name"
            :disabled="dialogFlag !== 'create'"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="account_name"
          label="用户姓名"
        >
          <el-input
            v-model="localAccount.account_name"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="department_id"
          label="所属机构"
        >
          <el-select
            v-model="localAccount.department_id"
            :disabled="dialogFlag === 'show'"
            filterable
            class="form-item"
          >
            <el-option
              v-for="item in orgs"
              :key="item.org_id"
              :value="item.org_id"
              :label="item.org_name"
            />
          </el-select>
        </el-form-item>

<!--         <el-form-item
          prop="department_id"
          label="所属机构"
        >
          <el-select
            v-model="localAccount.department_id"
            :disabled="dialogFlag === 'show'"
            filterable
            class="form-item"
          >
            <el-option
              v-for="item in departments"
              :key="item.department_id"
              :value="item.department_id"
              :label="item.department_name"
            />
          </el-select>
        </el-form-item>
 -->
        <el-form-item
          prop="role_ids"
          label="角色"
          v-if="dialogFlag === 'create'"
        >
          <el-select
            v-model="localAccount.role_ids"
            filterable
            class="form-item"
          >
            <el-option
              v-for="item in roles"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          prop="account_status"
          label="状态"
        >
          <el-select
            v-model="localAccount.account_status"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          >
            <el-option
              v-for="item in accountState"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          prop="account_phone"
          label="电话"
        >
          <el-input
            v-model="localAccount.account_phone"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>

        <el-form-item
          prop="account_email"
          label="电子邮箱"
        >
          <el-input
            v-model="localAccount.account_email"
            :disabled="dialogFlag === 'show'"
            class="form-item"
          />
        </el-form-item>
      </el-form>
      <div class="button-bar">
        <el-button
          v-if="dialogFlag !== 'show'"
          @click="visible = false"
        >
          取 消
        </el-button>
        <el-button
          v-if="dialogFlag !== 'show'"
          type="primary"
          @click="handleCommit"
        >
          确 定
        </el-button>
      </div>
      <roles
        v-if="dialogFlag !== 'create'"
        ref="roles"
        :system-id="systemId"
        :account-id="localAccount && localAccount.login_name"
        :login-name="localAccount && localAccount.login_name"
        :current-account="localAccount"
      />
      <roles-table
        v-if="dialogFlag !== 'create'"
        ref="rolesTable"
        :system-id="systemId"
        :account-id="localAccount && localAccount.login_name"
        :login-name="localAccount && localAccount.login_name"
      />
    </div>
  </el-drawer>
</template>

<script>
import Roles         from './roles.vue'
import RolesTable    from '@/components/BusinessSystems/SystemAccounts/PocSpm/rolesTable'
import MyPermissions from '@/components/mixins/MyPermissions'
import { catchError }          from '@/utils/axios_utils'
import { validateSpecialChars } from '@/utils/form_validates'

export default {
  components: {
    Roles,
    RolesTable
  },
  mixins: [MyPermissions],
  props:      {
    systemId: {
      type:     Number,
      required: true
    },
    account:  {
      validator: function (val) {
        return val === null || typeof val === 'object'
      },
      required:  true
    },
    orgs: {
      type: Array,
      required: true
    },
    roles: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      visible:        false,
      dialogFlag:     'create',
      localAccount:   this.account,
      accountState: [{ id: 'ENABLE', name: '正常' }, { id: 'DISABLE', name: '禁用' }],
      rules:          {
        login_name:     [
          { required: true, message: '登录名不能为空', trigger: 'blur' },
          { validator: validateSpecialChars, trigger: 'blur' }
        ],
        account_name: [
          { required: true, message: '账号姓名不能为空', trigger: 'blur' },
          { validator: validateSpecialChars, trigger: 'blur' }
        ],
        now_account_name: [
          { required: true, message: '当前登录用户不能为空', trigger: 'blur' },
          { validator: validateSpecialChars, trigger: 'blur' }
        ],
        account_status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
        role_ids:      [{ required: true, message: '角色不能为空', trigger: 'blur' }],
      },
      AccountDefault: {
        login_name:     null,
        account_name:   null,
        account_phone:  null,
        account_status: null,
        org_id:         null,
        role_ids:       null,
        department_id:  null,
      }

    }
  },
  computed:   {
    // eslint-disable-next-line vue/return-in-computed-property
    dialogTitle () {
      if (this.dialogFlag === 'create') return '创建用户'
      if (this.dialogFlag === 'edit') return '修改用户'
      if (this.dialogFlag === 'show') return '用户详情'
    }
  },
  watch:      {
    systemId () {
      this.dialogFlag = 'create'
      this.initializeLocalAccount()
    },
    account () {
      this.initializeLocalAccount()
    }
  },
  created () {
    this.initializeLocalAccount()
  },
  methods:    {
    initializeLocalAccount () {
      if (this.account) {
        this.localAccount = this.$lodash.clone(this.account)
      } else {
        this.localAccount = this.$lodash.clone(this.AccountDefault)
      }
    },

    handleClose (done) {
      if (this.dialogFlag === 'show') return done()

      this.$confirm('确认直接关闭吗？未保存的数据将会丢失')
        .then(_ => {
          done()
        })
        .catch(_ => {})
    },
    handleCommit () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.dialogFlag === 'create') return this.operationReviewCreate()
          if (this.dialogFlag === 'edit') return this.operationReviewUpdate()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    resetForm () {
      this.$refs.form.resetFields()
    },
    operationReviewCreate () {
      this.$operationReview(this.systemId)
        .then(() => {
          this.createRole()
        })
        .catch(() => {})
    },
    operationReviewUpdate () {
      this.$operationReview(this.systemId)
        .then(() => {
          this.updateRole()
        })
        .catch(() => {})
    },
    createRole () {
      const newAccount = this.localAccount
      console.log(newAccount)
      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts`, {
        account: newAccount
      })
        .then(response => {
          this.$message.success('用户已创建')
          this.$emit('change')
          this.visible = false
        })
        // .catch(() => {})
    },
    updateRole () {
      this.$axios.put(`/admin_api/edit_api/${this.systemId}/accounts/${this.localAccount.org_id}`, {
        account: this.localAccount
      })
        .then(response => {
          this.$message.success('用户已修改')
          this.$emit('change')
          this.visible = false
        })
        .catch(() => {})
    },
    handleLinkRoles () {
      const form   = this.$refs.roles
      form.visible = true
    },
    handleLinkRolesReadonly () {
      const form   = this.$refs.rolesTable
      form.visible = true
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .container {
    padding: 20px;
    height: calc(100vh - 80px);
    overflow-y: auto;
  }

  .button-bar {
    @include vertical_center_right;
    margin-bottom: 20px;
  }

  .form-item {
    width: 100%;
  }
</style>
