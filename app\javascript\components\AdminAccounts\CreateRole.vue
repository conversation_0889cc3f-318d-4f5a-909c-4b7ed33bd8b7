<template>
  <div>
    <el-button
      size="small"
      type="primary"
      :disabled="!$store.getters.hasPermission('admin_account_manager.role_admin')"
      @click="dialogVisible = true"
    >
      创建角色
    </el-button>
    <el-dialog
      title="创建角色"
      :visible.sync="dialogVisible"
      width="600px"
      append-to-body
      @close="handleCancel('roleCreate')"
    >
      <el-form
        ref="roleCreate"
        :model="localRole"
        :rules="rules"
        label-width="100px"
        class="new-form"
      >
        <el-form-item
          label="角色名称"
          prop="name"
        >
          <el-input
            v-model="localRole.name"
            placeholder="请输角色名称"
          />
        </el-form-item>

        <el-form-item
          label="角色成员"
          prop="admin_ids"
        >
          <admin-select
            v-model="localRole.admin_ids"
            placeholder="请选择角色成员"
            multiple
            clearable
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          label="角色描述"
          prop="description"
        >
          <el-input
            v-model="localRole.description"
            type="textarea"
            :rows="2"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="handleCancel('roleCreate')">取 消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit('roleCreate')"
        >
          创建
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import API         from '@/api'
import AdminSelect from '@/components/common/AdminSelect.vue'

export default {
  components: {
    AdminSelect
  },
  data () {
    return {
      loading: false,
      dialogVisible: false,
      localRole: {
        name: '',
        admin_ids: [],
        description: ''
      },
      rules: {
        name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleCancel (formName) {
      this.dialogVisible = false
      this.$refs[formName].resetFields()
    },
    handleSubmit (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.handleCreate()
        } else {
          return false
        }
      })
    },
    handleCreate () {
      this.loading = true
      API.roles.create(this.localRole)
        .then(() => {
          this.loading = false
          this.handleGetRoles()
          this.dialogVisible = false
          this.$message.success('创建角色成功')
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleGetRoles () {
      this.$emit('update')
    }
  }
}
</script>
<style lang='scss' scoped>
  .new-form {
    margin-right: 3em;
  }
</style>
