<template>
  <el-dialog
    :visible.sync="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    width="700px"
    append-to-body
  >
    <el-form
      ref="form"
      v-loading="loading"
      :model="localAccount"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item
        prop="system_id"
        label="选择系统"
      >
        <el-select
          v-model="localAccount.system_id"
          disabled
          placeholder="选择系统"
          class="form-item"
        >
          <el-option
            v-for="item in systems"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="mode === 'create'"
        prop="name"
        label="选择账号"
      >
        <!--el-select
          v-model="localAccount.account"
          value-key="id"
          filterable
          multiple
          placeholder="选择账号"
          class="form-item"
          @change="handleSelectAccount"
        >
          <el-option
            v-for="(item, index) in allAccounts"
            :key="index"
            :label="`名称：${item.name}，编码：${item.code}`"
            :value="item"
            :disabled="item.is_public"
          />
        </el-select-->
        <el-form :inline="true" size='mini' style="line-height: 0px;">
          <el-form-item>
            <el-input v-model="filterName" placeholder="账号编码或名称" style="width:126px;"></el-input>
          </el-form-item>
          <el-form-item>
            <el-select v-model="filterStatus" placeholder="账号状态" style="width:106px;" clearable>
              <el-option key="true" label="正常" value="true"></el-option>
              <el-option key="false" label="禁用" value="false"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="filterLedgerStatus" placeholder="是否关联台账" style="width:121px;" clearable>
              <el-option key="true" label="已关联" value="true"></el-option>
              <el-option key="false" label="未关联" value="false"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="filterPublicStatus" placeholder="是否为功能账号" style="width:141px;" clearable>
              <el-option key="true" label="是" value="true"></el-option>
              <el-option key="false" label="否" value="false"></el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <el-table
          ref="multipleTable"
          size="mini"
          border
          :data="dataByPage"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          :row-key="row => row.id"
        >
          <el-table-column
            type="selection"
            reserve-selection
            width="55">
          </el-table-column>
          <el-table-column
            label="账号编码"
          >
            <template slot-scope="scope">{{ scope.row.code }}</template>
          </el-table-column>
          <el-table-column
            label="账号名称"
          >
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column
            label="账号状态"
            width="80">
            <template slot-scope="scope">{{ scope.row.status ? '正常' : '禁用' }}</template>
          </el-table-column>
          <el-table-column
            label="是否关联台账"
            width="120">
            <template slot-scope="scope">{{ scope.row.ledger_status ? '已关联' : '未关联' }}</template>
          </el-table-column>
          <el-table-column
            label="是否为功能账号"
            width="120">
            <template slot-scope="scope">{{ scope.row.is_public ? '是' : '否' }}</template>
          </el-table-column>
        </el-table>
        <el-pagination
          style="margin-top:10px;"
          layout=" prev, pager, next,total,sizes "
          :page-size.sync="pageSize"
          :page-sizes="[10, 50, 100, 200]"
          :current-page.sync="currentPage"
          :total="filterAccounts.length"
          size="mini"
        />
      </el-form-item>
      <el-form-item
        v-if="mode === 'edit' || mode === 'ledger'"
        prop="name"
        label="账号名称"
      >
        <el-input
          v-model="localAccount.name"
          disabled
          class="form-item"
        />
      </el-form-item>

      <el-form-item
        v-if="mode === 'create' || mode==='ledger'"
        prop="code"
        label="账号编码"
      >
        <el-select
          v-model="localAccount.code"
          disabled
          multiple
          class="form-item"
        />
      </el-form-item>

      <el-form-item
        v-else
        prop="code"
        label="账号编码"
      >
        <el-input
          v-model="localAccount.code"
          disabled
          class="form-item"
        />
      </el-form-item>

      <el-form-item
        prop="type_id"
        :label="$t('activerecord.attributes.public_account.label_name')"
      >
        <el-select
          v-model="localAccount.type_id"
          :placeholder="$t('activerecord.attributes.public_account.label_name')"
          class="form-item"
        >
          <el-option
            v-for="(label, index) in labels"
            :key="index"
            :label="label.label"
            :value="label.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <template v-if="mode === 'edit'">
        <el-form-item
          prop="label"
          label="责任人"
        >
          <template>
            <el-select
              v-if="isSyncSelect"
              v-model="localAccount.user_id"
              filterable
              remote
              clearable
              reserve-keyword
              placeholder="请输入关键词"
              style="width: 480px;"
              :remote-method="getUsers"
              :loading="userLoading"
              @change="refreshData"
            >
              <el-option
                v-for="user in allUsers"
                :key="user.code"
                :label="user.full_name"
                :value="user.id"
              />
            </el-select>

            <el-cascader
              v-else
              v-model="selectUserData"
              :props="getCascaderProps"
              :options="userTree"
              filterable
              clearable
              class="user-cascader"
              style="width: 480px;"
              @change="handleUserChange"
            />
          </template>
        </el-form-item>

        <el-form-item
          prop="label"
          label="使用人"
        >
          <template>
            <el-select
              v-if="isSyncSelect"
              v-model="localAccount.used_user_ids"
              multiple
              filterable
              remote
              :remote-method="getUsedUsers"
              :loading="usedUserLoading"
              clearable
              reserve-keyword
              placeholder="请输入关键词"
              style="width: 480px;"
              @change="refreshData"
            >
              <el-option
                v-for="user in allUsedUsers"
                :key="user.code"
                :label="user.full_name"
                :value="user.id"
              />
            </el-select>
            <el-cascader
              v-else
              v-model="selectUsedUserData"
              :props="getMultipleCascaderProps"
              :options="userTree"
              filterable
              clearable
              collapse-tags
              class="user-cascader"
              style="width: 480px;"
              @change="handleUsedUserChange"
            />
          </template>
        </el-form-item>

      </template>
      <el-form-item
        prop="valid_date"
        label="有效期至"
        class="custom-form-item"
      >
        <el-date-picker
         v-model="localAccount.valid_date"
         :picker-options="pickerOptions"
         type="date"
         value-format="yyyy-MM-dd"
         placeholder="选择日期"
         class="form-item"
         style="width: 480px;"
        />
        <div class="comment">如不选择日期，功能账号永久生效</div>
      </el-form-item>
      <el-form-item
        prop="remark"
        label="备注"
      >
        <el-input
          v-model="localAccount.remark"
          type="textarea"
          :rows="3"
          class="form-item"
          maxlength="255"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleCommit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import API from '@/api'
import * as Message from '@/utils/general_messages'
import UserChange from '@/components/AdminLedgers/UserChange.vue'

export default {
  components: { UserChange },
  props: {
    account: {
      type: Object,
      default: () => {}
    },
    mode: {
      type: String,
      default: 'edit'
    },
    systemId: {
      type: Number,
      required: true
    },
    systems: {
      type: Array,
      default: () => []
    },
  },
  data () {
    return {
      visible: false,
      allAccounts: [],
      allUsers:    [],
      allUsedUsers: [],
      localAccount: {},
      selectUserData: [],
      selectUsedUserData: [],
      userTree: [],
      loading: false,
      userLoading: false,
      usedUserLoading: false,
      labels: [],
      pageSize: 10,
      currentPage: 1,
      filterName: '',
      filterStatus: '',
      filterLedgerStatus: '',
      filterPublicStatus: '',
      rules: {
        system_id: [
          { required: true, message: '业务系统不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '账号名称不能为空', trigger: 'blur' }
        ],
        type_id: [
          { required: true, message: '账号类型不能为空', trigger: 'blur' }
        ]
      },
      pickerOptions: {
        disabledDate: (date) => {
          return date.getTime() < Date.now() - 86400 * 1000
        }
      },
      cascaderProps: {},
      cascaderSyncProps: {
        lazy: true,
        lazyLoad (node, resolve) {
          setTimeout(() => {
            // const nodes = this.$lodash.find(this.userTree, function(x){
            //   x.value == 'department_1630'
            // })['children']
            const nodes = this.userTree
            // 通过调用resolve将子节点数据返回，通知组件数据加载完成
            resolve(nodes);
          }, 100);
        }
      }
    }
  },
  computed: {
    dataByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.filterAccounts.slice(start, end)
    },
    dialogTitle () {
      if (this.mode === 'edit')   return '修改功能账号'
      if (this.mode === 'create') return '添加功能账号'
      if (this.mode === 'ledger') return '添加到功能账号'
    },
    // 员工数超过1000，异步加载
    isSyncLoad () {
      return this.$store.state.inservice_user_count > 1000
    },
    // 员工数大于3000，则远程搜索
    isSyncSelect () {
      const count = this.$store.state.inservice_user_count
      return count > 2000
    },
    getCascaderProps () {
      return this.isSyncLoad ? this.cascaderSyncProps : this.cascaderProps
    },
    getMultipleCascaderProps () {
      const options = this.$lodash.clone(this.getCascaderProps)
      options.multiple = true
      return options
    },
    filterAccounts () {
      return this.allAccounts.filter(x => (this.filterName == '' || x.name.includes(this.filterName) || x.code.includes(this.filterName)) && (this.filterStatus == '' || this.filterStatus === x.status.toString()) && (this.filterLedgerStatus == '' || this.filterLedgerStatus == x.ledger_status.toString()) && (this.filterPublicStatus == '' || this.filterPublicStatus == x.is_public.toString()))
    }
  },
  watch: {
    account () {
      this.initializeData()
    },
    systemId () {
      this.getAllAccounts()
    },
    visible () {
      if (this.visible) {
        this.getAllAccounts()
        this.getInitUsedUsers()
        this.getInitUsers()
      }
    },
    mode () {
      this.initializeData()
    }
  },
  created () {
    // this.initializeData()
    this.gettingPublicAccountType()
    this.loadUserTree()
  },
  methods: {
    refreshData (val) {
      this.$forceUpdate()
    },
    getUsers (query) {
      if (query !== '') {
        this.userLoading = true
        const params = { query: query }
        API.users.searchUsers(params)
          .then(response => {
            this.userLoading = false
            this.allUsers    = response.data
          })
          .catch(() => {
            this.userLoading = false
            this.allUsers    = []
          })
      }
    },
    getUsedUsers (query) {
      if (query !== '') {
        this.usedUserLoading = true
        const params = { query: query }
        API.users.searchUsers(params)
          .then(response => {
            this.usedUserLoading = false
            this.allUsedUsers    = response.data
          })
          .catch(() => {
            this.usedUserLoading = false
            this.allUsedUsers    = []
          })
      }
    },
    // 打开编辑页面初始化责任人
    getInitUsers() {
      const params = { user_ids: [this.localAccount.user_id] }
      API.users.searchUsers(params)
        .then(response => {
          this.allUsers = response.data
        })
        .catch(() => {
          this.allUsers = []
        })
    },
    // 打开编辑页面初始化使用人
    getInitUsedUsers() {
      const params = { user_ids: this.localAccount.used_user_ids }
      API.users.searchUsers(params)
        .then(response => {
          this.allUsedUsers = response.data
        })
        .catch(() => {
          this.allUsedUsers = []
        })
    },
    handleSelectionChange(rows) {
      this.localAccount.name = rows.map(item => item.name)
      this.localAccount.code = rows.map(item => item.code)
    },
    gettingPublicAccountType () {
      this.loading = true
      API.publicAccountType.list()
        .then(response => {
          this.loading = false
          this.labels = response.data
          this.initializeData()
        })
        .catch(_ => {
          this.loading = false
        })
    },
    initializeData () {
      const defaultAccount = {
        id:         null,
        code:       null,
        system_id:  null,
        account:    null,
        name:       null,
        remark:     null,
        type_id:    this.labels[0].id,
        valid_date: null
      }

      if (!this.account || this.mode === 'create') {
        this.localAccount = this.$lodash.clone(defaultAccount)
        this.localAccount.system_id = this.systemId
        return
      }
      if (this.mode === 'edit') {
        this.localAccount = this.$lodash.clone(this.account)
        this.localAccount.account = null
        this.selectUsedUserData = this.account.select_used_user
        this.selectUserData = this.account.select_user
        this.handleUserChange()
        this.handleUsedUserChange()
      }
      if (this.mode === 'ledger') {
        this.localAccount = this.$lodash.clone(this.account)
        this.localAccount.account = null
        this.localAccount.code = [this.account.account_code]
        this.localAccount.name = this.account.account_name
        this.localAccount.system_id = this.systemId
      }
    },
    handleCommit () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.mode === 'edit')   return this.updateAccount()
          if (this.mode === 'create') return this.createAccount()
          if (this.mode === 'ledger') return this.createAccount()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
    },
    getAllAccounts () {
      // 只有添加账号才需要请求所有功能账号
      if (this.mode !== 'create') return
      if (Number(this.systemId) === 0) return
      this.$axios.get(`api/systems/${this.systemId}/public_accounts/all`)
        .then(response => {
          this.allAccounts = response.data
        })
        .catch(error => {
          if (error.status === 404) Message.notFoundQuarter()
        })
    },
    createAccount () {
      this.$axios.post(`api/systems/${this.systemId}/public_accounts`, {
        public_account: this.localAccount
      })
        .then(response => {
          this.$message.success('功能账号创建成功')
          this.initializeData()
          this.visible = false
          this.$emit('update', response.data.data)
          this.$EventBus.$emit("account:change");
        })
        .catch(() => {})
    },
    updateAccount () {
      this.loading = true
      this.$axios.put(`api/systems/${this.systemId}/public_accounts/${this.localAccount.id}`, {
        public_account: this.localAccount
      })
        .then(response => {
          setTimeout(() => {
            this.loading = false
            this.$message.success('功能账号更新成功')
            this.visible = false
            this.$emit('update', response.data)
          }, 2000)
        })
        .catch(() => {
          this.loading = false
        })
    },
    loadUserTree () {
      this.userTree = this.$store.state.user_tree
    },
    handleUserChange () {
      if (this.isSyncSelect) return

      this.localAccount.user_id = this.selectUserData[this.selectUserData.length - 1]
    },
    handleUsedUserChange () {
      if (this.isSyncSelect) return

      const used_user_ids = []
      this.selectUsedUserData.forEach((data, _) => {
        used_user_ids.push(data[data.length - 1])
      })
      this.localAccount.used_user_ids = used_user_ids
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";
  .form-item{
    width: 100%;
  }
  .comment{
    font-size: 10px;
    line-height: 30px;
    color: $account-status-false;
  }
  .custom-form-item{
    margin-bottom: 0px;
  }
</style>

