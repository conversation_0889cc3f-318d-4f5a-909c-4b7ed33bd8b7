<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :title="`权限设置 - ${user.name} (${user.email})`"
    width="700px"
    append-to-body
    @close="handleClose"
  >
    <el-tabs
      v-model="activeName"
    >
      <el-tab-pane
        label="用户角色"
        name="first"
      >
        <set-admin-roles
          ref="adminRolesSet"
          :admin="user"
          :flag="flag"
          @roles="roles"
        />
      </el-tab-pane>
      <el-tab-pane
        label="业务系统权限"
        name="second"
      >
        <set-admin-business-systems
          ref="adminSystemsSet"
          :admin="user"
          :flag="flag"
          @systems="systems"
        />
      </el-tab-pane>
      <el-tab-pane
        label="查询范围权限"
        name="third"
      >
        <set-admin-departments
          ref="adminDepartmentsSet"
          :admin="user"
          :flag="flag"
          @area="area"
        />
      </el-tab-pane>
    </el-tabs>
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-button
          size="small"
          type="primary"
          @click="handleUpdate"
        >
          保  存
        </el-button>
        <el-button
          size="small"
          @click="handleCloseAll"
        >
          关  闭
        </el-button>
      </el-col>
    </el-row>
  </el-dialog>
</template>
<script>
import SetAdminRoles           from './SetAdminRoles'
import SetAdminBusinessSystems from './SetAdminBusinessSystems'
import SetAdminDepartments     from './SetAdminDepartments'
import API from '@/api'
export default {
  components: {
    SetAdminRoles,
    SetAdminBusinessSystems,
    SetAdminDepartments
  },
  props:      {
    user: {
      type:     Object,
      required: true
    }
  },
  data () {
    return {
      dialogVisible: false,
      flag: 3,
      adminId: null,
      systemsData: {},
      areaData: {},
      adminRoleIds: [],
      activeName: 'first'
    }
  },
  methods: {
    handleClose () {
      this.$emit('update')
      this.dialogVisible = false
    },
    roles (e, f) {
      this.adminId = e
      this.adminRoleIds = f
    },
    systems (e) {
      this.systemsData = e
    },
    area (e) {
      this.areaData = e
    },
    handleUpdate () {
      this.loading = true
      this.flag = Math.random()
      this.roles()
      setTimeout(() => {
        API.adminPermissions
          .updateAdminRoles(this.adminId, { role_ids: this.adminRoleIds })
          .then(() => {
            this.flag = Math.random()
            this.$message.success('成功更新该用户的角色')
          })
          .catch(() => {
            this.flag = Math.random()
          })
        API.adminPermissions
          .updateAdminPermissions(this.adminId, this.systemsData, this.areaData)
          .then(() => {
            this.$message.success('成功更新该用户的业务系统权限和查询范围权限')
          })
          .catch(() => {
          })
        this.dialogVisible = false
      }, 1000)
    },
    handleCloseAll () {
      this.$confirm(
        '未提交的表单数据将不会保存，且无法恢复，是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.dialogVisible = false
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
