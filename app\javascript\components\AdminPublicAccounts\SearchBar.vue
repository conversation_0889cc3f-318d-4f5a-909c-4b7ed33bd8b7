<template>
  <el-form
    :inline="true"
    class="search-box"
  >
    <el-form-item>
      <el-input
        v-model="search.name"
        :debounce="2000"
        placeholder="功能账号名称"
        style="width:200px;"
        size="small"
        @keyup.enter.native="handleChange"
      />
    </el-form-item>
    <el-form-item>
      <el-input
        v-model="search.code"
        :debounce="2000"
        placeholder="账号编码"
        size="small"
        style="width:200px;"
      />
    </el-form-item>

    <el-form-item>
      <el-input
        v-model="search.remark"
        :debounce="2000"
        placeholder="备注"
        size="small"
        style="width:200px;"
      />
    </el-form-item>

    <el-form-item>
      <el-select
        v-model="search.status"
        placeholder="过滤状态"
        size="small"
        style="width:170px;"
      >
        <el-option
          :value="false"
          label="过期"
        />
        <el-option
          :value="true"
          label="有效"
        />
        <el-option
          :value="null"
          label="全部"
        />
      </el-select>
    </el-form-item>
    <el-form-item
      class="search-box"
    >
      <el-date-picker
        v-if="showAdvancedSearch"
        size="small"
        v-model="search.date"
        type="daterange"
        unlink-panels
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期">
      </el-date-picker>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        size="small"
        @click="handleChange"
      >
        检索
      </el-button>

      <el-button
        size="small"
        @click="handleReset"
      >
        重置
      </el-button>
      <el-button
        size="small"
        style="margin-left:3px"
        @click="changeShowAdvancedSearch"
      >
        更多
        <i
          v-if="showAdvancedSearch"
          class="el-icon-arrow-up"
        />
        <i
          v-else
          class="el-icon-arrow-down"
        />
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  data () {
    return {
      showAdvancedSearch: false,
      searchDefault: {
        code: null, // 只显示未关联账号
        name: null,
        remark: null,
        status: null,
        date: null
      },
      search: {}
    }
  },
  created () {
    this.filterReset()
  },
  methods: {
    handleChange () {
      this.$emit('change', this.search)
    },
    handleReset () {
      this.filterReset()
      this.$emit('change', this.search)
    },
    filterReset () {
      this.search = this.$lodash.clone(this.searchDefault)
    },
    changeShowAdvancedSearch () {
      this.showAdvancedSearch = !this.showAdvancedSearch
    }
  }
}
</script>
