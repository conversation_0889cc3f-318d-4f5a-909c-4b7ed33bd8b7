<script>
export default {
  data () {
    return {
      resourceType: 'accounts',
      resourceMode:  'both' // disable 仅停用， both 同时支持停用和启用
    }
  },
  methods:  {
    disableButtonName (record) {
      if (this.resourceMode === 'disable') return '停用'

      if (record.account_state === '1') {
        return '停用'
      } else {
        return '启用'
      }
    },
    disableAction (record) {
      if (this.resourceMode === 'disable') return 'disable'

      if (record.account_state === '1') {
        return 'disable'
      } else {
        return '1'
      }
    },
    handleDisableResource (record) {
      const message = `确认${this.disableButtonName(record)}该用户吗?`
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.operationReviewDisableResource(record)
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })

    },
    operationReviewDisableResource (record) {
      this.$operationReview(this.systemId)
        .then(() => {
          this.disableResource(record)
        })
        .catch(() => {})
    },
    resourceRefresh () {
      // 在资源内中实现
    },
    disableResource (record) {
      const userName     = record.account_name
      const accountState = this.disableAction(record)
      const actionSuffix = accountState == "disable" ? "disable" : "enable"
      const actionName   = this.disableButtonName(record)

      this.$axios.post(`/admin_api/edit_api/${this.systemId}/accounts/${userName}/${actionSuffix}`, {
        account: {
            username: userName,
            system_id: this.systemId,
        }
      })
        .then(response => {
          this.$message.success(`用户已${actionName}`)
          this.resourceRefresh()
        })
        .catch(() => {})
    }
  }
}
</script>
