# 共享目录配置文件
class ShareSettingController < ApplicationController
  # TODO: 缺少权限控制

  def share_directory_upload_config
    config_file_path = "#{Setting.data_path['template']}/#{Setting.data_path['share_config_name']}"
    `rm -rf #{config_file_path}`
    user_file = ShareConfigUploader.new
    user_file.store!(params[:file])
    set_share_config
    json_respond(success: true, errors: [])
    # TODO: 补充审计日志
  rescue CarrierWave::IntegrityError => e
    # TODO: 补充审计日志
    json_custom_respond(415, success: false, error_message: e.message)
  end

  def share_directory_download_config
    set_share_config
    config_file_path = "#{Setting.data_path['template']}/#{Setting.data_path['share_config_name']}"
    send_file_compatible_with_msie config_file_path
  end

  private

  def set_share_config
    book = RubyXL::Parser.parse set_config_file
    config = {}

    remark_array = []
    sheet = book.worksheets[0]
    sheet[1..-1].each do |row|
      arr = row.cells.map{ |x| x ? x.value.to_s : '' }
      remark_array << arr if arr[0] != ''
    end

    config[:share_remark] = remark_array

    pass_array = []
    sheet = book.worksheets[1]
    sheet[1..-1].each do |row|
      arr = row.cells.map{ |x| x ? x.value.to_s : '' }
      pass_array << arr if arr[0] != ''
    end

    config[:share_pass] = pass_array

    store_share_remark(config)
  end

  def set_config_file
    remark_file_path = "#{Setting.data_path['template']}/share_config.xlsx"
    if !File::exist?(remark_file_path)
      export_excel = Axlsx::Package.new
      title_style = export_excel.workbook.styles.add_style({ sz: 12, border: Axlsx::STYLE_THIN_BORDER })
      export_excel.workbook.add_worksheet(:name => '目录说明') do |sheet|
        sheet.add_row ['目录地址','目录说明','目录负责人','目录类型'], style: title_style
      end

      export_excel.workbook.add_worksheet(:name => '忽略目录') do |sheet|
        sheet.add_row ['目录地址','包括当前目录','目录类型'], style: title_style
      end
      export_excel.serialize(remark_file_path)
    end
    remark_file_path
  end

  def share_remark
    @share_remark ||= share_remark_config[:share_remark]
  end

  def share_pass
    @share_pass ||= share_remark_config[:share_pass]
  end

  def share_remark_config
    @share_remark_config ||= YAML.load_file(share_remark_file)
  end

  def share_remark_file
    import = Pathname.new(Setting.data_path['import'])
    import.mkdir unless import.exist?
    base_url = import.join('share_directory')
    base_url.mkdir unless base_url.exist?
    base_url.join('share_remark.yml')
  end

  def store_share_remark(config_data)
    File.open(share_remark_file, 'wb') do |f|
      f.puts config_data.to_yaml
    end
  end

end
