# frozen_string_literal: true

# 后台账号管理审计日志
class BlackEmailAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '全局邮箱黑名单管理'
    super
  end

  def create
    set_account_category
    @operation = '邮箱黑名单'
    @comment   = "创建了黑名单邮箱 ：「#{params}」"
    create_audit_log
  end

  def update
    set_account_category
    @operation = '邮箱黑名单'
    @comment   = "更新了黑名单邮箱 ：「#{params[0]}」为「#{params[1]}」"
    create_audit_log
  end

  def destroy
    set_account_category
    @operation = '删除邮箱黑名单'
    @comment   = "删除了邮箱黑名单：「#{params}」"
    create_audit_log
  end

  private

  def set_account_category
    @operation_category = '邮箱黑名单管理'
  end
end
