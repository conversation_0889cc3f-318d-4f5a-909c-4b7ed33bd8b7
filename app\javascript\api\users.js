import axios from '@/settings/axios'
import { parseFileName, downloadBlob   } from './tool'
import API                               from '@/api'

export const searchUsers = (params = {}) => {
  return axios.get(`/api/users/search_users`, { params: params })
}

export const accountsExport = (params) => {
  return axios.get(`/api/users/${params.user_id}/quarters/${params.quarter_id}/accounts_export`,
    { responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'accountsExport.rar')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

export const accountsListExport = (params) => {
  return axios.get(`/api/users/${params.user_id}/quarters/${params.quarter_id}/accounts_list_export`,
    { responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'accountsListExport.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
// 可授权人员名单导出Excel
export const authorizer = (params) => {
  return axios.get(`${params}`,
    { responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'authorizer.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
// 导出员工数据文件
export const users = (params) => {
  return axios.get(`${params}`,
    { responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'users.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
// 投资交易系统临时授权查询下载文件
export const downloadExportExcel = (params) => {
  return axios.get(`${params}`,
    { responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'downloadExportExcel.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
// 业务系统账号检索批量导出
export const userBatchExport = (params) => {
  return axios.get(`${params}`,
    { responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'userBatchExport.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
// 共享目录配文件导出Excel
export const downloadConfig = (params) => {
  return axios.get('/api/share_directory_download_config',
    { responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'downloadConfig.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
export const globalBase = (params) => {
  return axios.get(`/api/reports/global_base_report?quarter_id=${params}`,
    { responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'globalBase.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}
// 请选择需要进行数据比对的时间点击下载
export const allExportDataThan = (quarterId, otherQuarterId, byDepartment) => {
  const url = `/api/download_all_export_data`
  const theParams = { quarter_id: quarterId, other_quarter_id: otherQuarterId, by_department: byDepartment }
  return API.downloadRecords.downloadAsync(url, theParams)
  // return axios.get(`${params}`,
  //   { responseType: 'blob' }
  // )
  //   .then(response => {
  //     const fileName = parseFileName(response, 'allExportDataThan.xlsx')
  //     downloadBlob(response.data, fileName)
  //   })
  //   .catch((error) => {
  //     throw error
  //   })
}
export const accountListDifferences = (params) => {
  return axios.post('/api/users/account_list_differences', params)
}
export const departmentTreeWithUsers = (headers = {}) => {
  return axios.get('/api/users_in_departments', { headers: headers })
}
export const getPositions = (headers = {}) => {
  return axios.get('/api/positions', { headers: headers })
}
// 告警示例列表导出Excel messageAlert
export const messageAlert = (params) => {
  return axios.post('admin_api/global_alerts/export_alerts',  params,
    { responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'messageAlert.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

// 导出用户excel
export const exportUsers = (params) => {
  return axios.get('api/users/export_users', { params: params, responseType: 'blob' }
  )
    .then(response => {
      const fileName = parseFileName(response, 'users.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}

// 岗位基线和员工角色对比
export const jobBaselineUserCompare = (params) => {
  return axios.get('/api/users/job_baseline_user_compare', { params: params })
}
