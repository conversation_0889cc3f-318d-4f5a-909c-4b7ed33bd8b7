<template>
  <div
    v-loading="loading"
    class="container"
  >
    <el-form
      ref="form"
      label-width="140px"
      :model="{rules: rules}"
    >
      <el-form-item
        label="共享目录配置文件"
      >
        <div class="form-item-container">
          <!-- eslint-disable vue/attribute-hyphenation -->
          <el-upload
            :headers="headers"
            :file-list="fileList"
            :on-success="handleSuccess"
            :on-error="handleError"
            :on-remove="handleRemove"
            :show-file-list="false"
            :action="uploadUrl"
          >
            <el-button size="small">导入 Excel</el-button>
          </el-upload>
          <!-- eslint-enable vue/attribute-hyphenation -->

          <el-button
            size="small"
            style="margin-left: 20px;margin-top: 2px"
            @click="usersExport()"
          >
            导出 Excel
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import API                      from '@/api'
export default {
  data () {
    const headers = API.tool.getToken()

    return {
      fileList: [],
      headers:  headers,
      loading:  false,
       rules:    [],
      exportUrl: API.tool.absoluteUrlFor('/api/share_directory_download_config'),
      uploadUrl: API.tool.absoluteUrlFor('/api/share_directory_upload_config')
    }
  },
  methods: {
    handleRemove (file, fileList) {
      this.$message.warning('该文件上传后不允许删除，如上传出错，请重新上传数据表。')
    },
    handlePreview (file) {
      console.log(file)
    },
    handleSuccess (response, file, fileList) {
      this.$message.success('可授配置文件已导入，将在下次数据导入时生效')
    },
    handleExceed (files, fileList) {
      console.log(files, fileList)
    },
    handleError (err, file, fileList) {
      const errMsg = JSON.parse(err.message)
      this.$message.error('文件上传失败，服务器返回：' + errMsg.error_message)
    },
    usersExport () {
      API.users.downloadConfig()
        .then(() => {})
        .catch(error => {
          error.data.text().then((res) => {
            this.$message.error(JSON.parse(res).error_message)
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.form-item-container {
  @include vertical_center_left;
  height: 40px;
}
</style>
