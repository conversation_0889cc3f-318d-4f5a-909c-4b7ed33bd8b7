<template>
  <div>
    <el-button
      size="small"
      @click="dialogVisible = true"
    >
      邮件发送测试
    </el-button>
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="700px"
      title="发送邮件测试"
    >
      <el-form
        :model='form'
        v-loading="loading"
        ref="mail_form"
        label-width="100px"
      >
        <el-form-item
          :rules="rules.email"
          prop="email"
          label="收件人邮箱"
        >
          <el-input
            v-model="form.email"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item
          v-show="error"
          prop="error"
          label="错误消息"
        >
          <div class="error">
            {{ error }}
          </div>
        </el-form-item>
        <el-form-item
          v-show="message"
          prop="message"
          label="调试消息"
        >
          <pre>{{ message }}</pre>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">
          关闭
        </el-button>
        <el-button
          type="primary"
          @click="sendTestMail"
        >
          发送
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  props: {
  },
  data () {
    return {
      dialogVisible: false,
      testMail:      null,
      error:         null,
      message:       null,
      loading:       false,
      form: {
        email:       null
      },
      rules: {
        email:  [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email',  message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  computed: {

  },
  methods: {
    sendTestMail () {
      this.$refs.mail_form.validate((valid) => {
        if(valid){
          this.loading = true
          this.$axios.post('admin_api/settings/notification/test_email', {
            email: this.form.email
          })
            .then(response => {
              this.loading = false
              this.error   = response.data.error
              this.message = response.data.message
              if (response.data.success) {
                this.$message.success('邮件已发送')
              } else {
                this.$message.error('邮件发送失败')
              }
            })
            .catch(() => {
              this.$message.error('邮件发送失败')
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .error {
    color: #E54D42;
    font-weight: bold;
  }

  .el-form-item .el-form-item {
    margin-bottom: 22px;
  }
</style>

