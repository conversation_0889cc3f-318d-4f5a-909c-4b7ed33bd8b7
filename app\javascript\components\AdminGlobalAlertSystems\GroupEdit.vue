<template>
  <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :title="title"
      width="500px"
      appendToBody
      @close="dialogVisible = false"
  >
    <el-form
        ref="groupForm"
        :model="groupForm"
        :rules="rules"
        labelWidth="120px"
        statusIcon
        class="new-form"
    >
      <el-form-item label="分组名称：" prop="name">
        <el-input v-model="groupForm.name"/>
      </el-form-item>
      <el-form-item label="选择告警：">
        <el-select
            v-model="groupForm.category_ids"
            placeholder="请选择"
            multiple
            style="width: 100%"
        >
          <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="顺序号：">
        <el-input-number
            :min=0
            :step=1
            :step-strictly="true"
            v-model="groupForm.order_number"
        />
      </el-form-item>
    </el-form>
    <span
        slot="footer"
        class="dialog-footer"
    >
      <el-button
          type="primary"
          @click="handleCommit"
      >
        {{ createMode ? '创建' : '更新' }}
      </el-button>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </span>
  </el-dialog>
</template>

<script>

export default {
  props: {
    group: {
      type: Object,
      required: true
    },
    categories: {
      type: Array,
      required: true
    },
    createMode: {
      type: Boolean,
      default: false
    },
    groupList: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      groupForm: {
        name: '',
        category_ids: [],
        order_number: null
      },
      rules: {
        name: [
          {required: true, message: '分组名称不能为空', trigger: 'blur'}
        ],
        category_ids: []
      }
    }
  },
  computed: {
    title() {
      if (this.createMode) return '创建告警分组'
      return '修改告警分组'
    }
  },
  watch:    {
    group () {
      this.groupInitialize()
    }
  },
  created() {
    this.groupInitialize()
  },
  methods: {
    groupInitialize() {
      if (this.createMode) {
        this.groupForm.name = ''
        this.groupForm.category_ids = []
        this.groupForm.order_number = null
      } else {
        this.groupForm.name = this.group.name
        this.groupForm.category_ids = this.group.category_ids
        this.groupForm.order_number = this.group.order_number
      }
    },
    handleCommit() {
      if (!this.groupForm.name) return this.$message.error('分组名称不能为空')
      const repeatGroupNames = this.validateRepeatGroupNames(this.groupForm.name, this.groupForm.category_ids)
      if (repeatGroupNames.length > 0) {
        this.formValid(this.groupForm.name, repeatGroupNames)
      } else {
        if (this.createMode) return this.createGroup()
        this.updateGroup()
      }
    },
    validateRepeatGroupNames(groupName, categoryIds) {
      const otherGroup    = this.groupList.filter(group => group.name !== groupName)
      const otherGroupIds = this.otherGroupCategories(otherGroup)
      return this.selectCategories(categoryIds.filter(v => otherGroupIds.includes(v)))
    },
    otherGroupCategories (otherGroup) {
      let arrIds = []
      otherGroup.forEach(function (group) {
        arrIds = arrIds.concat(group.category_ids)
      })
      return arrIds
    },
    selectCategories(categoryIds) {
      let categoryArr = []
      this.categories.forEach(function (category) {
        if (categoryIds.includes(category.id)) categoryArr = categoryArr.concat(category.name)
      })
      return categoryArr
    },
    formValid(groupName, repeatCategoryNames) {
      this.$confirm(`${repeatCategoryNames}目前已在其它分组，是否移动到「${groupName}」分组?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.createMode) return this.createGroup()
        this.updateGroup()
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    updateGroup() {
      const params = {
        id: this.group.id,
        name: this.groupForm.name,
        category_ids: this.groupForm.category_ids,
        order_number: this.groupForm.order_number || null
      }
      this.$axios.put(`/admin_api/global_alert_categories/group_update`, { group: params })
          .then(response => {
            this.$emit('update')
            this.$message.success(`更新${this.groupForm.name}成功`)
            this.dialogVisible = false
          })
          .catch(() => {
          })
    },
    createGroup() {
      if (!this.groupForm.name) return this.$message.error('分组名称不能为空')
      if (this.validateGroupName(this.groupForm.name)) return this.$message.error(`已经存在分组名称${this.groupForm.name}`)
      const params = {
        name: this.groupForm.name,
        category_ids: this.groupForm.category_ids,
        order_number: this.groupForm.order_number
      }
      this.$axios.post(`/admin_api/global_alert_categories/group_create`, { group: params })
          .then(response => {
            this.$emit('update')
            this.$message.success(`创建分组${this.groupForm.name}成功`)
            this.dialogVisible = false
          })
          .catch(() => {
          })
    },
    validateGroupName(groupName) {
      let repeatGroupName = false
      this.groupList.forEach(function (group) {
        if (group.name === groupName) {
          repeatGroupName = true
          return repeatGroupName
        }
      })
      return repeatGroupName
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.new-form {
  margin-right: 3em;
}

.new-input {
  width: 100%;
}
</style>
