<template>
  <span>
    <el-select
      :class="className"
      v-model="localSystemId"
      :placeholder="placeholder"
      :filterable="filterable"
      :disabled="disabled"
      :size="size"
      :multiple="multiple"
      clearable
    >
      <el-option
        v-for="item in systems"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>
  </span>
</template>

<script>
import API from '@/api'
export default {
  model: {
    prop: 'systemId',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '选择业务系统'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    systemId: {
      type: [String, Number, Array],
      required: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'normal'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      localSystemId: this.systemId,
      systems: [],
      className: 'el-select'
    }
  },
  watch: {
    systemId () {
      this.localSystemId = this.systemId
    },
    localSystemId () {
      this.$emit('change', this.localSystemId)
    }
  },
  created () {
    this.getSystems()
  },
  methods: {
    getSystems () {
      API.systems.list()
        .then(response => {
          this.systems = response.data
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
