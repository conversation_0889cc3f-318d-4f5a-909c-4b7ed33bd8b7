# frozen_string_literal: true

# 季度的控制器
class QuartersController < ApplicationController
  before_action :authenticate_admin!
  before_action :set_quarter, except: %i[list index create history_difference create_excel last]
  before_action :authenticate_policy!, except: %i[list index show systems_in_departments last]
  before_action -> { check_dependent_task_status(:departments_counter_task) }, only: [:systems_in_departments]
  before_action :generate_search_sql, only: :import_status

  def list
    @quarters = Quarter.all.map(&:output)
    json_respond @quarters.reverse
  end

  def index
    if params[:ids].present?
      @quarters = Quarter
                    .includes(:business_system_import_statuses)
                    .where(id: params[:ids])
                    .map(&:info_detail)
    else
      @quarters = Quarter
                    .includes(:business_system_import_statuses)
                    .order(id: :desc)
                    .page(params[:page])
                    .per(params[:per_page])
                    .map(&:info_detail)
    end
    json_respond(count: Quarter.count, quarters: @quarters)
  end

  def show
    json_respond @quarter.output
  end

  def create
    return_data = create_quarter
    if Setting.import_from_importer&.[]('enable') && return_data[:success]
      ImportTasksJob.perform_later(return_data[:data][:id])
    end
    json_respond return_data
  end

  def create_excel
    json_respond create_quarter
  end

  def update
    if @quarter.update(name: params[:name])
      audit_log! [@quarter, '成功']
      json_respond(success: true, error_message: nil)
    else
      audit_log! [@quarter, '失败']
      json_respond(success: false, error_message: @quarter.errors.full_messages.join('; '))
    end
  end

  def destroy
    audit_log! @quarter.name
    @quarter.update(status: 'removing')

    DestroyQuarterDatasJob.perform_later(params[:quarter_id])
    Ledger.destroy_not_exist_account!

    json_respond(success: true, error_message: nil)
  rescue StandardError => e
    @quarter.update(status: 'pending')

    json_respond(success: false, error_message: e)
  end

  def systems_in_departments
    json_respond Department.systems_has_accounts(@quarter.id, current_admin)
  end

  def import_status
    page = params[:page] || 1
    per_page = params[:per_page] || 999999 # 如果不传就是全部
    if @sql.present?
      import_statuses = @quarter
                          .business_system_import_statuses
                          .where(@sql)
      bs_ids = import_statuses.pluck(:business_system_id)
      # 未导入要在这里判断，不能通过sql实现
      if @json['status']&.include?(9999)
        not_found_bs_ids = get_not_found_bs_ids(@quarter)
        if @operation == 'OR'
          bs_ids |= not_found_bs_ids
        else
          bs_ids &= not_found_bs_ids
        end
      end
      options = { id: bs_ids }
    else
      options = nil
    end
    business_systems = BusinessSystem
                         .inservice
                         .where(options)
                         .page(page)
                         .per(per_page)
    task_statuses = AfterImport.enable_non_system_tasks(@quarter).map { |task| task.output.merge(loading: false) }
    systems_statuses = @quarter.system_with_user_import_status(business_systems, page)
    # 这里再过滤一次是因为员工导入系统需要过滤
    systems_statuses = systems_statuses.select do |x|
      x[:type] == 'user' && x[:name].include?(@json['name'].to_s) || x[:type] != 'user'
    end if @json['name'].present?
    systems_statuses = systems_statuses.select do |x|
      status_value = QuarterBusinessSystemImportStatus.statuses[x[:status]]
      if x[:type] == 'user'
        if @operation == 'AND' && @json['system_task_status'].blank? || @operation == 'OR'
          @json['status'].include?(status_value)
        end
      else
        x[:type] != 'user'
      end
    end if @json['status'].present?

    systems_statuses.map { |x| x.merge(loading: false) }
    # 如果包含员工数据同步，总数需要加一，因为员工数据是分页后插入的
    business_systems.define_singleton_method(:total_count) do
      super() + 1
    end if @quarter.user_import_status_show.present?
    total = business_systems.total_count
    json_respond systems: systems_statuses, tasks: task_statuses, total: total
  end

  def rakes_restart
    return json_respond(success: false, error_message: '系统数据未导入成功，不能执行任务') unless validate_system_import_statuses

    AfterImportTasksJob.perform_later(@quarter.id)

    audit_log! '全部任务已加入任务执行队列'
    json_respond(success: true, message: '全部任务已加入任务执行队列')
  rescue StandardError => e
    audit_log! '任务重新执行失败'
    json_respond(success: false, error_message: '任务重新执行失败')
  end

  def rake_restart
    return json_respond(success: false, error_message: '系统数据未导入成功，不能执行任务') unless validate_system_import_statuses

    task_key = params[:task_key]
    task     = @quarter.find_task(task_key.to_sym)
    begin
      # 1.判断前置任务
      task.check_dependent_tasks_status
      # 2.判断当前任务执行状态
      if task.status == 'running'
        audit_log! "「#{task.name}」已在任务执行队列，不能重复执行"
        json_respond(success: false, error_message: "#{task.name}已在任务执行队列，不能重复执行")
      else
        # 找到任务状态改为running
        task.status_record.update(status: :running)
        # 执行任务，later不能直接传task, now可以，所以再找一遍
        AfterImportTaskJob.perform_later(@quarter.id, task_key)
        audit_log! "「#{task.name}」已加入任务执行队列"
        json_respond(success: true, message: "#{task.name}已加入任务执行队列")
      end
    rescue StandardError => e
      if e.class == AfterImport::PreviousTaskNotCompleted
        before_task = AfterImport.enable_tasks(Quarter.last).find { |rake| rake.key == e.message.to_sym }.name
        audit_log! "「#{before_task}」尚未执行完成，不能执行该任务"
        json_respond(success: false, error_message: "#{before_task}尚未执行完成，不能执行该任务")
      else
        audit_log! "「#{task.name}」执行失败"
        json_respond(success: false, error_message: "#{task.name}执行失败")
      end
    end
  end

  # 重新执行系统依赖任务
  def rake_system_restart
    return json_respond(success: false, error_message: '系统数据未导入成功，不能执行任务') unless validate_system_import_status

    task_key = params[:task_key]
    bs       = BusinessSystem.find_by(id: params[:bs_id])
    task     = @quarter.find_task(task_key.to_sym, bs)
    begin
      # 1.判断前置任务
      task.check_dependent_tasks_status
      # 2.判断当前任务执行状态
      if task.status == 'running'
        audit_log! "#{bs.name}「#{task.name}」已在任务执行队列，不能重复执行"
        json_respond(success: false, error_message: "#{task.name}已在任务执行队列，不能重复执行")
      else
        # 找到任务状态改为running
        task.status_record.update(status: :running)
        # 执行任务，later不能直接传task, now可以，所以再找一遍
        AfterImportSystemTaskJob.perform_later(@quarter.id, task_key, bs.id)
        audit_log! "#{bs.name}「#{task.name}」已加入任务执行队列"
        json_respond(success: true, message: "#{task.name}已加入任务执行队列")
      end
    rescue StandardError => e
      if e.class == AfterImport::PreviousTaskNotCompleted
        before_task = AfterImport.enable_system_tasks(Quarter.last, [bs]).find { |rake| rake.key == e.message.to_sym }.name
        audit_log! "#{bs.name}「#{before_task}」尚未执行完成，不能执行该任务"
        json_respond(success: false, error_message: "#{before_task}尚未执行完成，不能执行该任务")
      else
        audit_log! "#{bs.name}「#{task.name}」执行失败"
        json_respond(success: false, error_message: "#{task.name}执行失败")
      end
    end
  end

  # 重新执行指定系统的所有系统依赖任务
  def rakes_system_restart
    return json_respond(success: false, error_message: '系统数据未导入成功，不能执行任务') unless validate_system_import_status

    bs = BusinessSystem.find(params[:bs_id])
    AfterImportSystemTasksJob.perform_later(@quarter.id, bs.id)
    msg = "「#{bs.name}」全部系统依赖任务已加入任务执行队列"
    audit_log! msg
    json_respond(success: true, message: msg)
  rescue StandardError => e
    audit_log! "「#{bs.name}」全部系统依赖任务执行失败，具体错误：#{e.message}"
    json_respond(success: false, error_message: "任务重新执行失败，具体错误：#{e.message}")
  end

  # 全部导入数据任务重新执行
  def imports_restart
    # return json_respond(success: false, error_message: '系统数据未导入成功，不能执行任务') unless validate_system_import_statuses

    if exist_task?('ImportTasksJob', [@quarter.id])
      log = '全部数据导入任务已在任务执行队列，不能重复执行'
      audit_log! log
      json_respond(success: false, error_message: log)
    else
      ImportTasksJob.perform_later(@quarter.id, true)
      log = '全部数据导入任务已加入任务执行队列，即将开始执行，请在状态选项中查看导入状态'
      audit_log! log
      json_respond(success: true, message: log)
    end
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    error_log = '数据导入任务重新执行失败'
    audit_log! "数据导入任务重新执行失败，#{e.message}"
    json_respond(success: false, error_message: error_log)
  end

  # 指定导入数据任务重新执行
  # bs_id 为空，执行导入员工
  def import_restart
    # return json_respond(success: false, error_message: '系统数据未导入成功，不能执行任务') unless validate_system_import_statuses

    bs = BusinessSystem.inservice.find_by(id: params[:bs_id])
    import_status = if params[:bs_id].present?
                      @quarter.business_system_import_statuses.find_by(business_system_id: bs.id)
                    else
                      @quarter.get_user_import_status
                    end
    task_name = bs ? bs.name : '员工'
    begin
      # 判断当前任务执行状态，如果有等待和执行中状态则不再添加任务
      # if import_status.present? && %w[pending processing].include?(import_status.status)
      if exist_task?('ImportTaskJob', [@quarter.id, bs&.id])
        log = "「#{task_name}」数据导入任务已在任务执行队列，不能重复执行"
        audit_log! log
        json_respond(success: false, error_message: log)
      else
        # 找到任务状态改为processing
        import_status.update(status: :pending, start_at: Time.now) if import_status.present?
        ImportTaskJob.perform_later(@quarter.id, bs&.id)
        log = "「#{task_name}」数据导入任务已加入执行队列"
        audit_log! log
        json_respond(success: true, message: log)
      end
    rescue StandardError => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }
      error_log = "「#{task_name}」数据导入任务执行失败"
      audit_log! error_log
      json_respond(success: false, error_message: error_log)
    end
  end

  def last
    quarter_task = QuarterTaskStatus
                     .success
                     .where(task: 'quarter_users_accounts_caches_task')
                     .order('quarter_id DESC')
                     .first
    quarter = quarter_task&.quarter
    json_respond quarter&.output || {}
  end

  private

  def create_quarter
    quarter = Quarter.new(name: params[:name])
    if quarter.save
      audit_log! [quarter.name, '成功']
      return_data = { success: true, data: { id: quarter.id, name: quarter.name } }
    else
      audit_log! [quarter.name, '失败']
      return_data = { success: false, error_message: '时间点名称不能为空' }
    end
    return_data
  end

  # 验证数据导入状态
  def validate_system_import_statuses
    system_status_records = @quarter.business_system_import_statuses
    business_systems      = BusinessSystem.inservice
    systems_statuses      = Quarter.system_import_state(system_status_records, business_systems)
    systems_statuses.all? { |status| status[:status] == 'success' }
  end

  # 验证数据导入状态
  def validate_system_import_status
    system_status_record = @quarter.business_system_import_statuses.find_by(business_system_id: params[:bs_id])
    system_status_record&.status == 'success'
  end

  def authenticate_policy!
    authorize Quarter
  end

  # 是否存在任务
  def exist_task?(job_clazz, args = [])
    exist_queue_task?(job_clazz, args) || exist_work_task?(job_clazz, args)
  end

  # sidekiq队列是否存在该任务
  def exist_queue_task?(job_clazz, args = [])
    queues = Sidekiq::Queue.all
    queues.any? do |queue|
      queue.any? do |job|
        obj = job.args.first
        next if obj.blank?

        obj['job_class'] == job_clazz && obj['arguments'] == args
      end
    end
  end

  # sidekiq执行中是否存在该任务
  def exist_work_task?(job_clazz, args = [])
    workers = Sidekiq::Workers.new
    workers.any? do |_process_id, _thread_id, job|
      obj = job['payload']['args'].first
      next if obj.blank?

      obj['job_class'] == job_clazz && obj['arguments'] == args
    end
  end

  # 生成系统数据导入SQL搜索语句
  def generate_search_sql
    @json = begin
      JSON.parse(params[:filter])
    rescue StandardError
      {}
    end
    @operation = @json['operation']&.upcase == 'OR' ? 'OR' : 'AND'
    sqls = []
    status_sqls = []
    sqls << "name like %#{@json['name']}%" if @json['name'].present?
    status_sqls << "(status IN (#{@json['status'].join(', ')}))" if @json['status'].present?
    status_sqls << "(system_task_status IN (#{@json['system_task_status'].join(', ')}))" if @json['system_task_status'].present?
    sqls << status_sqls.select(&:present?).join(" #{@operation} ")
    @sql = sqls.select(&:present?).join(' AND ')
  end

  def get_not_found_bs_ids(quarter)
    all_bs_ids = BusinessSystem.inservice.pluck(:id)
    all_bs_ids - quarter.business_system_import_statuses.pluck(:business_system_id)
  end
end
