# frozen_string_literal: true

module Overrides
  # 覆写 PasswordsController 修改数据及添加审计日志
  class PasswordsController < DeviseTokenAuth::PasswordsController
    before_action :password_decrypt

    protected

    def render_update_success
      audit_log! action: :audit_password_changed

      render json: {
        success: true,
        data:    resource_data,
        message: I18n.t('devise_token_auth.passwords.successfully_updated')
      }
    end

    private

    def password_decrypt
      params[:current_password] = aes_decrypt_data(params[:current_password]) if params[:current_password].present?
      params[:password] = aes_decrypt_data(params[:password]) if params[:password].present?
      params[:password_confirmation] = aes_decrypt_data(params[:password_confirmation]) if params[:password_confirmation].present?
    end
  end
end
