# frozen_string_literal: true

require 'net/ldap'
require 'yaml'
require 'devise_token_auth'

class ExternalAuthController < ApplicationController
  before_action :password_decrypt
  before_action :should_login!
  include ActAsExternalLogins

  class NotFoundUserError < StandardError
  end

  class NotFoundUserEmailError < StandardError
  end

  class UserAttrNotMatchError < StandardError
  end

  def authenticate
    # TODO: code 登录也走这里，审计日志会记录 ldap 方式的
    audit_log! username, action: :external_login_attempt, audit_log_class: SessionAuditLog

    # 验证用户名和密码是否空
    if username.blank? || password.blank?
      audit_failed_log(params_error)
      return json_custom_respond(401, error_message: params_error)
    end

    select_login_method
  end

  private

  def password_decrypt
    params[:password] = aes_decrypt_data(params[:password]) if params[:password]
  end

  def select_login_method
    # 外部 登录方式
    if username.in?(Setting.ldap&.[]('local_accounts').to_a) || username.in?(Setting.external_login&.[]('local_accounts').to_a)
      # 这里 username 就是 email
      @resource = Admin.find_by(email: username) || Admin.find_by(code: username) 
      authenticate_local
    elsif Setting.frontendSettings&.[]('authMethod') == 'external'
      # 这里 username 就是 code
      case Setting.external_login['adapter']
      when 'efunds'
        code = SsoConnector::EfundsSso.get_login_name(username, password)
      end
      if code
        @resource = Admin.find_by(code: code)
        pass_all_authenticate
      else
        no_user_log
      end

    # code 登录方式
    elsif Setting.frontendSettings&.[]('authMethod') == 'code'
      # 这里 username 就是 code
      @resource = Admin.find_by(code: username)
      return authenticate_local

    # ldap 登录方式
    elsif Setting.frontendSettings&.[]('authMethod') == 'ldap'
      authenticate_ldap
    else
      raise 'Configuration not found'
    end
  end

  def authenticate_ldap
    if Setting.ldap['not_change_user']
      ldap_username = username
    else
      ldap_username = username + '@' + Setting.ldap['base'].split(/,* *dc=/).delete_if { |x| x == '' }.join('.')
    end
    # 施罗德域控名称前面要添加corp
    ldap_uname = ldap_username
    domain = Setting.ldap['domain']
    ldap_uname = "#{domain}\\#{ldap_username}" if domain.present?

    is_tls = Setting.ldap['is_tls'] || false
    if is_tls
      ldap = Net::LDAP.new(
        encryption: :simple_tls,
        auth:       { method: :simple, username: ldap_uname, password: password }
      )
    else
      ldap = Net::LDAP.new
    end
    ldap.host = Setting.ldap['host']
    ldap.port = Setting.ldap['port']
    ldap.base = Setting.ldap['base']

    ldap.auth(ldap_uname, password) if !is_tls

    # 账号密码错误
    return render_ldap_internal_error(ldap.get_operation_result) unless ldap.bind

    @resource = verify_local_resource(ldap)

    # 禁用账号的处理
    return disabled_log('ldap') if @resource.disabled_at

    # 锁定账号的处理
    return locked_log('ldap') if @resource.access_locked?

    # 当用户未登录过时，激活账号
    unless @resource.confirmed_at
      @resource.confirmed_at = Time.now
      @resource.save
    end
    # 正常登录流程
    pass_all_authenticate

  rescue NotFoundUserError => _e
    no_ldap_user_log
  rescue NotFoundUserEmailError => _e
    no_user_email_log
  rescue UserAttrNotMatchError => e
    user_attr_not_match_log(e.message)
  rescue Net::LDAP::Error => e
    ldap_connect_error_log(e.message)
  end

  def verify_local_resource(ldap)
    verify_field = Setting.ldap['verify_field']
    verify_field ||= 'email'

    # 获取用户 email
    filter = Net::LDAP::Filter.eq('samaccountname', username)
    users  = ldap.search(base: ldap.base, filter: filter)
    user   = users.first
    # 找不到域用户报错
    raise NotFoundUserError if users.empty?

    # 找不到用户邮箱报错

    case verify_field
    when 'email'
      verify_resource_email(user)
    when 'code'
      verify_resource_code(user)
    else
      raise "not support ldap verify_field #{verify_field}"
    end
  end

  def verify_resource_code(user)
    raise NotFoundUserEmailError unless user.attribute_names.include? :samaccountname

    code = user.samaccountname.first&.downcase
    # this variable must named @resource for add auth header
    @resource = Admin.find_by(code: code)
    # 找不到系统用户或邮箱与系统账号不匹配报错
    raise UserAttrNotMatchError, "code: #{code}" unless @resource

    @resource
  end

  # 验证用户 email
  def verify_resource_email(user)
    raise NotFoundUserEmailError unless user.attribute_names.include? :mail

    email = user.mail.first&.downcase
    # this variable must named @resource for add auth header
    @resource = Admin.find_by(email: email)
    # 找不到系统用户或邮箱与系统账号不匹配报错
    raise UserAttrNotMatchError, "email: #{email}" unless @resource

    @resource
  end

  def username
    # 此处 email 对应的是 ldap 用户名
    params[:email]
  end

  def password
    params[:password]
  end

  def pass_all_authenticate
    login_logger.tagged(username) do
      login_logger.debug { 'login success' }
    end

    audit_log! action: :login_success, audit_log_class: SessionAuditLog

    @token = @resource.create_token
    @resource.save
    sign_in(:user, @resource, store: false, bypass: false)

    render_create_success
  end

  def resource_data(opts = {})
    response_data         = opts[:resource_json] || @resource.as_json
    response_data['type'] = @resource.class.name.parameterize if json_api?
    response_data
  end

  def json_api?
    return false unless defined?(ActiveModel::Serializer)

    if ActiveModel::Serializer.respond_to?(:setup)
      return ActiveModel::Serializer.setup do |config|
        config.adapter == :json_api
      end
    end

    ActiveModelSerializers.config.adapter == :json_api
  end

  def render_ldap_internal_error(ldap_response)
    ldap_error_codes = {
      1793 => 'windows 域错误消息：用户账户已到期',
      1326 => 'windows 域错误消息：未知的用户名或错误密码',
      1327 => 'windows 域错误消息：用户账户限制',
      1328 => 'windows 域错误消息：违反账户登录时间限制',
      1329 => 'windows 域错误消息：不允许用户登录到此计算机',
      1330 => 'windows 域错误消息：账户密码已过期',
      1331 => 'windows 域错误消息：当前账户已禁用',
      1239 => 'windows 域错误消息：试图在这个账户未被授权的时间内登录',
      1396 => 'windows 域错误消息：登录失败: 该目标账户名称不正确',
      1907 => 'windows 域错误消息：在第一次登录之前，必须更改用户密码',
      1909 => 'windows 域错误消息：引用的账户当前已锁定，且可能无法登录',
      2239 => 'windows 域错误消息：此用户账户已过期',
      2242 => 'windows 域错误消息：此用户的密码已经过期',
      4506 => 'windows 域错误消息：登录的用户数量上限'
    }

    error_code = ldap_response.error_message.match(/data (.*)\,/)[1]
    message    = ldap_error_codes[error_code.hex]

    message ||= "未知的 windows 域错误代码 #{error_code}"

    audit_failed_log message
    login_logger.tagged('ldap', username) do
      login_logger.error { "login failed - code: #{ldap_response.code}, error_code: #{error_code}, message: #{message}" }
    end
    json_custom_respond(401, error_message: message)
  end

  def no_user_log
    login_logger.tagged('local', username) do
      login_logger.error { 'no such user' }
    end

    audit_failed_log(user_not_match)
    json_custom_respond(401, error_message: user_not_match)
  end

  def disabled_log(type)
    login_logger.tagged("#{type}", username) do
      login_logger.error { 'user is disabled' }
    end
    audit_failed_log user_is_disabled
    json_custom_respond(401, error_message: user_is_disabled)
  end

  def locked_log(type)
    login_logger.tagged("#{type}", username) do
      login_logger.error { 'user is locked' }
    end
    audit_failed_log user_is_locked
    json_custom_respond(401, error_message: user_is_locked)
  end

  def repeated_log
    login_logger.tagged('local', username) do
      login_logger.error { 'password invalid' }
    end
    audit_failed_log params_error
    json_custom_respond(401, error_message: params_error)
  end

  def no_ldap_user_log
    login_logger.tagged('ldap', username) do
      login_logger.error { 'not found samaccountname' }
    end
    audit_failed_log ldap_not_found_user_error
    json_custom_respond(401, error_message: ldap_not_found_user_error)
  end

  def no_user_email_log
    login_logger.tagged('ldap', username) do
      login_logger.error { 'not found mail attr' }
    end
    audit_failed_log ldap_not_found_mail_error
    json_custom_respond(401, error_message: ldap_not_found_mail_error)
  end

  def user_attr_not_match_log(message)
    login_logger.tagged('ldap', username) do
      login_logger.error { "user_model not found #{message}" }
    end
    audit_failed_log user_not_match
    json_custom_respond(401, error_message: user_not_match)
  end

  def ldap_connect_error_log(message)
    login_logger.tagged('ldap', username) do
      login_logger.error { message }
    end
    audit_failed_log ldap_connect_error
    json_custom_respond(401, error_message: ldap_connect_error)
  end

  def params_error
    if Setting.frontendSettings&.[]('authMethod') == 'code'
      I18n.t('devise_token_auth.sessions.bad_code_credentials')
    else
      I18n.t('devise_token_auth.sessions.bad_credentials')
    end
  end

  def user_is_disabled
    I18n.t('devise_token_auth.sessions.user_is_disabled')
  end

  def user_is_locked
    I18n.t('devise_token_auth.sessions.not_confirmed')
  end

  def user_not_match
    I18n.t('devise_token_auth.sessions.user_not_match')
  end

  def ldap_not_found_user_error
    I18n.t('devise_token_auth.sessions.ldap_not_found_user')
  end

  def ldap_not_found_mail_error
    I18n.t('devise_token_auth.sessions.ldap_not_found_mail')
  end

  def ldap_connect_error
    I18n.t('devise_token_auth.sessions.ldap_connect_error')
  end
end
