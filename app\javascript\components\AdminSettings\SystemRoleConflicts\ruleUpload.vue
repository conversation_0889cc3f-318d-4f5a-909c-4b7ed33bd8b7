<template>
  <el-upload
    :action="uploadUrl"
    :headers="headers"
    :file-list="fileList"
    :show-file-list="false"
    :on-success="handleSuccess"
    :on-error="handleError"
    :before-upload="beforeFileUpload"
    :on-progress="handleProgress"
  >
    <el-button
      v-loading.fullscreen.lock="loading"
      element-loading-text="正在导入，请稍后……"
      :disabled="!$store.getters.hasPermission('system_role_conflicts.conflicts_admin')"
    >
      导入
    </el-button>
  </el-upload>
</template>

<script>
import API from '@/api'

export default {
  data () {
    const headers = API.tool.getToken()
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    headers['X-CSRF-Token'] = csrfToken

    return {
      headers:  headers,
      loading: false,
      fileList: []
    }
  },
  computed: {
    uploadUrl () {
      return API.tool.absoluteUrlFor(`/api/system_role_conflicts/upload`)
    }
  },
  methods: {
    handleSuccess (response, file, fileList) {
      this.loading = false
      if (response.fail_errors) {
        this.OpenNotify(response)
      }
      this.$message.success('已成功更新')
      this.$emit('update')
    },
    handleError (err, file, fileList) {
      this.loading = false
      const errorMsg = JSON.parse(err.message)
      this.$message.error(errorMsg.error_message)
    },
    beforeFileUpload (file) {
      const matches = file.name.match(/.*\.xlsx/i)
      // 上一行匹配成功返回匹配字符串，失败返回 null
      // 而结果只会判断 true or false，因此要做一下转换
      const isxlsx = matches !== null

      if (!isxlsx) {
        this.$message.error('只能够上传 xlsx 格式文件')
      }
      return isxlsx
    },
    handleProgress () {
      this.loading = true
    },
    OpenNotify (errors) {
      this.$notify({
        title: '警告',
        type: 'warning',
        message: errors.message,
        duration: 0
      })
    }
  }
}
</script>
