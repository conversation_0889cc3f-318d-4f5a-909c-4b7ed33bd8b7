<template>
  <div>
    <el-form-item
      label=""
    >
      <el-button
        size="small"
        :disabled="!$store.getters.hasPermission('app_settings.notification')"
        @click="createNewEmail"
      >
        添加
      </el-button>
    </el-form-item>
    <transition-group name="list-complete">
      <div
        v-for="(item, index) in emailSettings"
        :key="index"
        class="list-complete-item"
      >
        <el-form-item
          :prop="`emails.${index}.email`"
          :label="`邮箱 ${index + 1}`"
          :rules="rules.email"
          class="form-item-first-line"
        >
          <el-input
            v-model="emailSettings[index].email"
            size="small"
          />
          <el-button
            size="small"
            :disabled="!$store.getters.hasPermission('app_settings.notification')"
            @click="handleDeleteEmail(index)"
          >
            删除
          </el-button>
        </el-form-item>
        <el-form-item
          :prop="'emails.' + index + '.templates'"
          :rules="rules.templates"
          class="form-item-second-line"
        >
          <el-select
            v-model="emailSettings[index].templates"
            multiple
            size="small"
            :disabled="!$store.getters.hasPermission('app_settings.notification')"
            placeholder="请选择通知模板"
          >
            <el-option
              v-for="template in templates"
              :key="template.key"
              :value="template.key"
              :label="template.name"
            />
          </el-select>
        </el-form-item>
      </div>
    </transition-group>
  </div>
</template>

<script>

export default {
  props: {
    outerEmailSettings: {
      type: Array,
      default: () => [],
      required: true
    },
    outerTemplates: {
      type: Array,
      default: () => [],
      required: true
    }
  },
  data () {
    return {
      emailSettings: [],
      templates: [],
      rules: {
        email:  [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email',  message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        templates: [
          { required: true, message: '请选择通知模板', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    outerEmailSettings () {
      this.initializeEmailSettings()
    },
    outerTemplates () {
      this.initializeTemplates()
    }
  },
  computed: {

  },
  methods: {
    createNewEmail () {
      const itemData = { email: '', templates: [] }
      const length   = this.emailSettings.length
      this.$set(this.emailSettings, length, itemData)
    },
    handleDeleteEmail (index) {
      this.$delete(this.emailSettings, index)
    },
    initializeEmailSettings () {
      this.emailSettings = this.outerEmailSettings
    },
    initializeTemplates () {
      this.templates = this.outerTemplates
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .form-item-first-line {
    // MARK: 为了把表单验证的字露出来
    margin-bottom: 14px;

    .el-input {
      width: 200px;
    }
  }

  .form-item-second-line {
    margin-bottom: 25px;

    .el-select {
      width: 100%;
    }
  }

  .list-complete-item {
    transition: all 0.5s;
  }

  .error {
    color: #E54D42;
    font-weight: bold;
  }

</style>

