class UserTagsController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!
  before_action :set_tag, only: [:update, :disable]

  def index
    tags = Tag.all
    if params[:without_page].to_s == "true"
      json_respond tags.map(&:output)
    else
      if params[:name].present?
        tags = tags.where("name like ?", "%#{params[:name]}%")
      end
      tags = tags.page(params[:current_page] || 1).per(params[:per_page] || 15)
      output_data = {
        entities: tags.map(&:output),
        total_count: tags.total_count
      }
      json_respond output_data
    end
  end

  def create
    if Tag.find_by(name: tag_params[:name])
      json_respond(success: false, message: "名称已存在")
      return
    end

    tag = Tag.create(name: tag_params[:name], color: tag_params[:color], status: :enabled)
    if tag.valid?
      audit_log! [tag.name, tag.color]
      json_respond(success: true)
    else
      json_respond(success: false)
    end
  end

  def update
    if @tag.name != params[:name] && Tag.find_by(name: params[:name])
      json_respond(success: false, message: "名称已存在")
      return
    end

    if @tag.update(name: tag_params[:name], color: tag_params[:color])
      audit_log! [@tag.name, @tag.color]
      json_respond(success: true)
    else
      json_respond(success: false)
    end
  end

  def show
    json_respond @tag.full_output
  end

  # 启用与禁用
  def disable
    status = @tag.enabled? ? (:disabled) : (:enabled)
    if @tag.update(status: status)
      audit_log! [@tag.cn_status]
      json_respond(success: true)
    else
      json_respond(success: false)
    end
  end

  # 员工设置标签
  def user_set_tag
    user = User.find(params[:user_id])
    tags = Tag.where(id: params[:tag_ids])
    user.tags = tags
    audit_log! [user.name, tags.pluck(:name)]
    json_respond(success: true)
  end

  private

  def authenticate_policy!
    authorize Tag
  end

  def tag_params
    params.permit(:name, :color)
  end

  def set_tag
    @tag = Tag.find(params[:id])
  end
end
