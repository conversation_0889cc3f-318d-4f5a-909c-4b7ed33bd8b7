<template>
  <el-container>

    <el-aside width="220px">
      <el-scrollbar style="height: 100%">
        <el-collapse-transition>
          <span>
            <el-menu
              :defaultActive="defaultActiveIndex"
              @select="selectItem"
            >
              <div
                v-for="(sys, index) in systemData"
                :key="index"
              >
                <el-menu-item
                  :index="systemIndex(sys.system.id)"
                >
                  <div class="box">
                    <div class="left">{{ sys.system.name }}</div>
                    <div class="font-style">{{ sys.account_count }}</div>
                  </div>
                </el-menu-item>
              </div>
            </el-menu>
          </span>
        </el-collapse-transition>
      </el-scrollbar>
    </el-aside>

    <el-main>
      <div>
        <div
          v-loading="loading"
          class="account-container"
        >
          <system-basetype-items
            v-if="system !== null"
            :accounts="systemAccounts"
            :system="system"
            :system-mode="true"
            :total="count"
            :per-page="perPage"
            :page.sync="page"
            :display-selection="false"
            :display-sortable="false"
            @requestData="requestData"
          />
          <div
            v-if="systemData.length === 0"
            class="no-exist"
          >
            未查询到尚未关联的业务系统账号
          </div>
        </div>
      </div>
    </el-main>
  </el-container>
</template>
<script>
import SystemBasetypeItems from '@/components/SystemSummary/SystemSummaryItemsBaseTypeInSystemMode.vue'
import AccountDetail       from '@/components/AccountDetail'
import Styles              from '@/components/_variables.scss'

export default {
  components: {
    AccountDetail,
    SystemBasetypeItems
  },
  data () {
    return {
      loading:        false,
      data:           [],
      systemData:     [],
      page:           1,
      perPage:        15,
      systemAccounts: [],
      system:         null
    }
  },
  props: {
    query: {
      type: String,
      default: ''
    }
  },
  computed: {
    // 系统账号数量
    count () {
      const systemData = this.systemData.find(x => x.system.id === this.system.id)
      if (systemData === null) {
        return 0
      } else {
        return systemData.account_count
      }
    },
    storeVisible () {
      return this.$store.state.dialog_visible
    },
    currentQuarter () {
      return this.$store.state.current_quarter
    },
    defaultActiveIndex () {
      if (this.system !== null) {
        return this.systemIndex(this.system.id)
      }
      return ''
    }
  },
  watch: {
    storeVisible (val) {
      this.dialogVisible = val
    },
    query () {
      this.allAccountsSearchSystems()
    }
  },
  created () {
    // this.allAccountsSearchSystems()
  },
  methods: {
    systemIndex (system_id) {
      return `system-${system_id}`
    },
    // 获取关键词相关的系统和账号，用于初始化
    allAccountsSearchSystems () {
      this.loading = true
      const theParams = {
        q: this.query,
        quarter_id: this.currentQuarter.id,
        page: this.page,
        per_page: this.perPage
      }
      this.$axios.get('/api/systems/search_systems', { params: theParams })
        .then(response => {
          const systemId = response.data.system_id
          this.systemData = response.data.system_data
          this.system = this.getSystem(systemId)
          this.systemAccounts = response.data.system_accounts
          this.loading = false
        })
        .catch(error => {
          this.loading = false
          if (error.status !== 403) {
            console.log(error.data.error_message)
          }
        })
    },
    // 获取关键词相关的账号，用于分页和点击左侧系统
    SearchAccounts () {
      this.loading = true
      const theParams = {
        q: this.query,
        quarter_id: this.currentQuarter.id,
        page: this.page,
        per_page: this.perPage,
      }
      if (this.system === null) {
        this.loading = false
        return []
      }

      this.$axios.get(`/api/systems/${this.system.id}/search_accounts`, { params: theParams })
        .then(response => {
          this.systemAccounts = response.data.system_accounts
          this.loading = false
        })
    },
    // 点击左侧系统，获取关键词的账号
    selectItem (index) {
      let systemId = this.$lodash.parseInt(this.$lodash.last(index.split(/-/)))
      this.page = 1
      this.system = this.getSystem(systemId)
      this.SearchAccounts()
    },
    // 分页请求数据
    requestData () {
      this.SearchAccounts()
    },
    getSystem (systemId) {
      return this.systemData.find(x => x.system.id === systemId).system
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .el-aside{
    border-right: 1px solid #EBEEF5;
    height: calc(100vh -#{$header-height});

    .switch-placeholder{
      width: 100%;
      height: 40px;
    }
    .switch-container{
      position: fixed;
      width: 219px;
      height: 30px;
      bottom: 0;
      padding-top: 10px;
      border-top: 1px solid #EBEEF5;
      background-color: #fff;

      .el-switch{
        padding-left: 20px;
      }
    }
  }
  .container{
    width: 1000px;
    margin: auto;
  }
  .account-container{
    min-height: 50px;
  }
  .no-exist{
    margin-top: 30px;
    color: #A3A6AA;
    text-align: center;
  }

  .box {
    @include vertical_center_between;
  }
  .left{
    max-width:140px;
    overflow: hidden;
    text-overflow:ellipsis;
  }
  .font-style {
    font-size: 12px;
    color: #808080;
  }

</style>
