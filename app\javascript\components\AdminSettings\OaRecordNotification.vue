<template>
  <div>
    <el-form-item label="通知管理员：">
      <el-select
        v-model="oaRecordSettings.admin_ids"
        clearable
        multiple
        placeholder="请选择管理员"
        style="width: 250px;"
      >
        <el-option
          v-for="item in admins"
          :key="item.id"
          :label="item.name"
          :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="提前通知：">
      <el-input-number
        v-model="oaRecordSettings.days"
        :min=0
        :step=1
        :step-strictly="true"
      />
      天
    </el-form-item>
  </div>
</template>

<script>
import API from '@/api'

export default {
  props: {
    outerOaRecordSettings: {
      type: Object,
      default: () => {},
      required: true
    }
  },
  data () {
    return {
      admins: [],
      oaRecordSettings: {}
    }
  },
  watch: {
    outerOaRecordSettings () {
      this.initializeOaRecordSettings()
    }
  },
  created () {
    this.getAdmins()
  },
  methods: {
    initializeOaRecordSettings () {
      this.oaRecordSettings = this.outerOaRecordSettings
    },
    getAdmins () {
      API.adminAccounts.index()
        .then(response => {
          this.admins = response.data
        })
        .catch(() => {
        })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/components/variables";

  .error {
    color: #E54D42;
    font-weight: bold;
  }

</style>

