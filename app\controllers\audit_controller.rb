# frozen_string_literal: true

# 审计日志的控制器
class AuditController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!
  
  before_action :new_search_record_params, only: %i[create_search_record]
  before_action :search_record, only: %i[delete_search_record]

  def logs_in_section
    begin
      section_id = params[:section_id].to_i
      logs =
        AdminAuditSection
          .find(section_id)
          .admin_audit_logs
          .order(id: :desc)
          .map(&:output)

      json_respond(logs: logs, errors: [])
    rescue StandardError => e
      json_custom_respond(500, error_message: e.message)
    end
  end

  def logs_by_filter
    begin
      page = params[:page] || 1
    
      logs_after_filter =
        AdminAuditLog
          .includes(:admin)
          .start_filter(params[:filters])

      logs =
        logs_after_filter
          .order(operation_time: :desc, id: :desc)
          .page(page)
          .map(&:output)
      logs_count = logs_after_filter.count

      json_respond logs: logs, size: logs_count, errors: []
    rescue StandardError => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }
      json_custom_respond(500, error_message: e.message)
    end
  end

  def audit_section
    begin
      page = params[:page] || 1

      AdminAuditLog.create_section
      logs       =
        AdminAuditSection
          .includes(%i[admin admin_audit_logs])
          .order(operation_time: :desc, id: :desc)
          .page(page)
          .map(&:output)
      logs_count = AdminAuditSection.count

      json_respond logs: logs, size: logs_count, errors: []
    rescue StandardError => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }
      json_custom_respond(500, error_message: e.message)
    end
  end

  def export_audit_logs
    options = params.to_unsafe_h.symbolize_keys
    DownloadExport::AdminAuditLogExport.new(current_admin, options).async_export

    json_respond(status: true)
  rescue StandardError => e
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    audit_log! action: :export_audit_logs_faild
    json_custom_respond(500, error_message: e.message)
  end

  def create_search_record
    log_search_record = AuditLogsSearchRecord.new(new_search_record)
    if log_search_record.save
      audit_log! log_search_record
      json_respond log_search_record.output
    else
      json_custom_respond(:unprocessable_entity, error_message: log_search_record.errors.full_messages.join('; '))
    end
  end

  def audit_log_search_records
    json_respond(records: search_records = AuditLogsSearchRecord.order('id DESC').limit(10).map(&:output))
  end

  def delete_search_record
    @search_record.destroy
    audit_log! @search_record
  end

  private

  def authenticate_policy!
    authorize AdminAuditLog
  end

  # 用于设置存入AuditLogsSearchRecord的数据
  def new_search_record_params
    json_custom_respond(error_message: '筛选内容为空') if params[:filters].nil?
    @filters = params[:filters]
    @name    = params[:name]
  end

  def new_search_record
    search_record = {
      name:    @name,
      filters: @filters.map { |filter| filter.to_unsafe_h }.to_json,
      content: new_search_record_content.to_json
    }
  end

  def new_search_record_content
    content = []
    @filters.each do |filter|
      record = filter.to_unsafe_h
      zh_filter = I18n.t("mappings.audit_search_record.relation.#{record[:relation]}") +
                  I18n.t("mappings.audit_search_record.type.#{record[:type]}") +
                  new_search_record_condition(record[:type], record[:condition]) +
                  "「#{search_query(record[:type], record[:query])}」"
      content << zh_filter
    end
    content
  end

  def search_query(type, query)
    return query.to_time&.getlocal&.strftime("%Y-%m-%d %H:%M:%S") if type == 'operation_time'
    query
  end

  # 从i18n搜索condition
  def new_search_record_condition(type, condition)
    case type
    when 'operation_time'
      zh_condition = I18n.t("mappings.audit_search_record.time_condition.#{condition}")
    when 'agent', 'event_id'
      zh_condition = I18n.t("mappings.audit_search_record.digit_condition.#{condition}")
    else
      zh_condition = I18n.t("mappings.audit_search_record.common_condition.#{condition}")
    end
    zh_condition
  end

  def search_record
    @search_record = AuditLogsSearchRecord.find_by(id: params[:id])
    return @search_record if @search_record

    json_custom_respond(404, error_message: e.message.to_s)
  end
end
