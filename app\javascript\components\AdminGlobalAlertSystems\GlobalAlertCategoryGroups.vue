<template>
  <div class="container">
    <div class="tool-bar">
      <el-button
        size="small"
        @click="handleCreate"
        :disabled="!$store.getters.hasPermission('admin_global_alert_managers.group_update')"
      >
        创建分组
      </el-button>
    </div>
    <el-table
      :data="groupsByPage"
      border
    >
      <el-table-column
        prop="order_number"
        label="顺序号"
        width="100"
      />

      <el-table-column
        prop="name"
        label="分组名称"
        sortable
      />
      <el-table-column
        prop="category_names"
        label="告警名称"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.categories_names.length > 0">
            <ul>
              <li
                v-for="category_name in scope.row.categories_names"
                :key="category_name"
              >
                {{ category_name }}
              </li>
            </ul>
          </div>
          <div
            v-else
            style="padding-left: 25px"
          >
            -
          </div>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="160"
      >
        <template slot-scope="scope">
          <el-button
            size="small"
            @click="handleEdit(scope.row)"
            :disabled="!$store.getters.hasPermission('admin_global_alert_managers.group_update')"
          >
            设置
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDestroyConfirm(scope.row.id)"
            :disabled="!$store.getters.hasPermission('admin_global_alert_managers.group_delete')"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page.sync="currentPage"
      :page-size="pageSize"
      :total="groups.length"
      background
      layout="total, prev, pager, next, jumper"
      class="groups-pagination"
    />
    <group-edit
      ref="groupCreate"
      :group="{}"
      :groupList="groups"
      :categories="categories"
      create-mode
      @update="getGroups"
    />
    <group-edit
      ref="groupEdit"
      :group="currentGroup"
      :groupList="groups"
      :categories="categories"
      @update="getGroups"
    />
  </div>
</template>
<script>
import GroupEdit from './GroupEdit.vue'

export default {
  components: {
    GroupEdit
  },
  data () {
    return {
      systems:      [],
      groups:       [],
      currentGroup: {},
      currentPage:  1,
      pageSize:     15,
      categories:   []
    }
  },
  computed: {
    groupsByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.groups.slice(start, end)
    }
  },
  created () {
    this.getGroups()
    this.getCategories()
  },
  methods: {
    getGroups () {
      this.$axios.get('/admin_api/global_alert_categories/group_list')
        .then(response => {
          this.groups = response.data
        })
        .catch(() => {
        })
    },
    getCategories () {
      this.$axios.get('/admin_api/global_alert_categories')
        .then(response => {
          this.categories = response.data
        })
        .catch(() => {
        })
    },
    handleEdit (group) {
      this.currentGroup                  = group
      this.$refs.groupEdit.dialogVisible = true
    },
    handleCreate () {
      this.$refs.groupCreate.dialogVisible = true
    },
    handleDestroyConfirm (id) {
      this.$confirm('删除分组无法撤销, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      }).then(() => {
        this.destroyGroup(id)
      })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    destroyGroup (id) {
      this.$axios.post(`/admin_api/global_alert_categories/group_destroy`, { group: { id: id } })
        .then(response => {
          this.$message.success('已成功删除分组')
          this.getGroups()
        })
        .catch(() => {})
    }
  }
}

</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
}

.tool-bar {
  height:        50px;
  margin-bottom: 10px;
}

.el-table {
  width: 100%;
}

.groups-pagination {
  margin-top: 20px;
}
</style>
