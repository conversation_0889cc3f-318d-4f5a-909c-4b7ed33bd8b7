# Sidekiq 生产环境配置建议

## 当前脚本评估结果

### ✅ 优点
1. **环境检测完善** - 正确识别 Tomcat 环境
2. **避免重复启动** - PID 文件和全局变量检查
3. **日志记录详细** - 完整的启动和错误日志
4. **优雅关闭机制** - 超时强制关闭保护

### ⚠️ 改进点
1. **线程管理优化** - 使用更稳定的线程管理方式
2. **健康检查机制** - 添加自动监控和重启功能
3. **配置统一** - 避免与 `config/initializers/sidekiq.rb` 冲突
4. **内存管理** - 减少全局变量使用，改善内存泄漏风险

## 生产环境建议配置

### 1. 环境变量设置

```bash
# Redis 配置
export REDIS_URL="redis://your-redis-host:6379/0"
export REDIS_CONFIG_TYPE="username"  # 如果使用用户名认证
export REDIS_USERNAME="your-username"
export REDIS_PASSWORD="your-password"

# Sidekiq 配置
export SIDEKIQ_KILLER_ENABLE="true"
export SIDEKIQ_KILLER_MAX_RSS="2000"  # 2GB 内存限制
export SIDEKIQ_KILLER_GRACE_TIME="3600"  # 1小时优雅关闭时间

# Tomcat 环境标识
export CATALINA_HOME="/opt/tomcat"
export RAILS_ENV="production"
```

### 2. 监控和日志

#### 日志文件位置
- 启动日志: `log/sidekiq_initializer.log`
- Sidekiq 运行日志: `log/sidekiq.log`
- 健康检查日志: 包含在启动日志中

#### 监控指标
- Sidekiq 进程状态
- Redis 连接健康度
- 内存使用情况
- 队列积压情况

### 3. 性能优化建议

#### Redis 连接池配置
```yaml
# config/sidekiq.yml
production:
  :concurrency: 20  # 根据服务器性能调整
  :redis:
    :size: 25       # 连接池大小，建议比 concurrency 大 5
    :network_timeout: 5
```

#### JRuby 优化
```bash
# JVM 参数优化
export JRUBY_OPTS="-J-Xmx2g -J-Xms1g -J-XX:+UseG1GC"
```

### 4. 故障排除

#### 常见问题
1. **内存泄漏** - 启用 sidekiq-worker-killer
2. **Redis 连接超时** - 检查网络和 Redis 配置
3. **任务积压** - 增加 concurrency 或添加更多 worker

#### 日志检查命令
```bash
# 查看启动日志
tail -f log/sidekiq_initializer.log

# 查看 Sidekiq 运行日志
tail -f log/sidekiq.log

# 检查进程状态
ps aux | grep sidekiq
```

### 5. 部署检查清单

- [ ] Redis 服务正常运行
- [ ] 环境变量正确设置
- [ ] 日志目录权限正确
- [ ] 内存限制合理设置
- [ ] 网络连接稳定
- [ ] 备份和恢复策略就位

## 大量 Job 执行的稳定性保障

### 1. 并发控制
- 生产环境建议 concurrency: 20
- 根据服务器 CPU 核心数调整
- 监控内存使用，避免 OOM

### 2. 队列优先级
```yaml
:queues:
  - [critical, 2]  # 高优先级队列
  - default        # 默认队列
  - low           # 低优先级队列
```

### 3. 任务重试策略
```yaml
:max_retries: 1  # 避免无限重试
```

### 4. 内存保护
- 启用 sidekiq-worker-killer
- 设置合理的内存阈值
- 定期重启 worker 进程

## 结论

当前脚本经过改进后，**适合在生产环境使用**，能够稳定支持大量 Job 执行。主要改进包括：

1. ✅ 更稳定的线程管理
2. ✅ 自动健康检查和重启
3. ✅ 改进的关闭机制
4. ✅ 减少内存泄漏风险

建议在部署前进行充分测试，特别是在高负载情况下的稳定性测试。
