# frozen_string_literal: true

# 系统用户管理
class AppRoleAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '系统用户管理'
    @operation_category = '角色管理'
    super
  end

  def create
    @operation = '创建角色'
    @comment   = "创建了角色：「#{params}」"
    create_audit_log
  end

  def update
    @operation = '更新角色'
    @comment   = "更新了角色：「#{params[0]}」为「#{params[1]}」"
    create_audit_log
  end

  def destroy
    @operation = '删除角色'
    @comment   = "删除了角色：「#{params}」"
    create_audit_log
  end

  def update_role_alerts_permissions
    role = params[:role]
    permissions = params[:before_permissions]

    add_query_permissions = role.alert_categories_in_query.pluck(:name) - output_alert_category_names(permissions, 'query')
    reduce_query_permissions = output_alert_category_names(permissions, 'query') - role.alert_categories_in_query.pluck(:name)

    add_maintain_permissions = role.alert_categories_in_maintain.pluck(:name) - output_alert_category_names(permissions, 'maintain')
    reduce_maintain_permissions = output_alert_category_names(permissions, 'maintain') - role.alert_categories_in_maintain.pluck(:name)

    @operation = '更新角色告警权限'
    @comment   = "角色「#{role.name}」的告警权限更新: 
                  增加的查询权限：#{add_query_permissions.present? ? add_query_permissions.join('，') : '无'}; 减少的查询权限：#{reduce_query_permissions.present? ? reduce_query_permissions.join(',') : '无'};
                  增加的管理权限：#{add_maintain_permissions.present? ? add_maintain_permissions.join('，') : '无'}; 减少的管理权限：#{reduce_maintain_permissions.present? ? reduce_maintain_permissions.join(',') : '无'};"
    create_audit_log
  end

  def update_role_module_functions
    role = params[:role]
    # 增加的权限 更新后减更新前
    add_permissions = AppModuleFunction.where(id: (role.module_functions&.pluck(:id).to_a - params[:before_permissions].to_a)).pluck(:name)
    # 减少的权限 更新前减更新后
    reduce_permissions = AppModuleFunction.where(id: (params[:before_permissions].to_a - role.module_functions&.pluck(:id).to_a)).pluck(:name)
    
    @operation = '更新角色菜单权限'
    @comment   = "角色「#{role.name}」的菜单权限更新: 增加的权限：#{add_permissions.present? ? add_permissions.join(',') : '无'}; 减少的权限：#{reduce_permissions.present? ? reduce_permissions.join(',') : '无'}。"
    create_audit_log
  end

  def output_alert_category_names(permissions, type)
    permission_ids = permissions.select{ |permission| permission[type.to_sym] }.map{ |category| category[:category_id] }
    GlobalAlertCategory.where(id: permission_ids).pluck(:name)
  end
end