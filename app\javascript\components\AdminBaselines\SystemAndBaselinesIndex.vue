<template>
  <el-container>
    <el-aside width="180px">
      <el-scrollbar style="height: 920px;overflow-y:auto;">
        <el-collapse-transition>
          <sidebar-ledger-systems
            ref="systemSelect"
            v-loading="loading"
            :default-active="Number(activeIndex)"
            :systems="systems"
            @select="selectItem"
          />
        </el-collapse-transition>
      </el-scrollbar>
    </el-aside>
    <el-main>
      <baselines-index
        v-if="activeIndex"
        :system-id="Number(activeIndex)"
        style="height: 920px;"
      />
    </el-main>
  </el-container>

</template>

<script>
import BaselinesIndex       from './BaselinesIndex.vue'
import SidebarLedgerSystems from '@/components/SidebarLedgerSystems'
import API from '@/api'
export default {
  components: {
    SidebarLedgerSystems,
    BaselinesIndex
  },
  data () {
    return {
      activeIndex: null,
      systems:     [],
      loading:     false
    }
  },
  created () {
    this.permissionJudge()
  },
  computed: {
  },
  methods: {
    permissionJudge () {
      this.getSystems()
      if (! (this.$store.getters.hasPermission('system_baseline.accounts_query') ||
             this.$store.getters.hasPermission('system_baseline.query'))) {
        this.$message.error('您没有权限操作此功能')
      }
    },
    getSystems () {
      this.loading = true
      API.systems.maintainList()
        .then(response => {
          this.loading = false
          this.systems = response.data
        })
        .catch(() => {
          this.loading = false
        })
    },
    selectItem (systemId) {
      this.activeIndex = systemId
    }
  }

}
</script>

<style lang="scss" scoped>
.el-aside {
  border-right: 1px solid #EBEEF5;
}
.el-main{
  padding: 0 0 0 20px;
}
</style>
