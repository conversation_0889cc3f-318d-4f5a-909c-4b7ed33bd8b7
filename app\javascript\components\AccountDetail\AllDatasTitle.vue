<template>
  <div>
    <div class="title-container">
      <div class="title">账号权限列表</div>
      <div class="right">
        <account-permission-edit
          :systemId="systemId"
          :accountId="accountId"
          style="margin-right: 10px"
        />
        <account-baseline-create
          :systemId="systemId"
          :accountId="accountId"
        />
      </div>
    </div>
  </div>
</template>

<script>
import AccountPermissionEdit from '@/components/AccountPermissionEdit'
import AccountBaselineCreate from './AccountBaselineCreate.vue'
export default {
  components: {
    AccountBaselineCreate,
    AccountPermissionEdit
  },
  props: {
    accountId: {
      type: Number,
      required: true
    },
    systemId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
    }
  },
  computed: {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
  @import '~@/components/variables';

  .title-container{
    @include vertical_center_between;

    .title{
      font-size: 16px;
    }
  }
</style>
