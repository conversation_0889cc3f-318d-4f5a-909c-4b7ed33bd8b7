class PermissionEditableController < ApplicationController
  before_action :authenticate_admin!
  before_action :set_business_system
  before_action :set_business_system_api
  before_action :authenticate_policy, except: %i[show_account]
  before_action :replace_request_id
  before_action :set_class_for_business
  before_action :set_importer, only: %i[cms_user_excel_upload cms_user_excel_cancel cms_user_excel_submit]
  before_action :set_params, only: %i[cms_user_excel_submit]

  def cms_user_excel_upload
    user_file            = UserUploader.new
    user_file.custom_dir = 'zyfd_users/'

    begin
      user_file.store!(params[:file])
      audit_log! [@business_system.name, params[:file].original_filename, 0]
    rescue CarrierWave::IntegrityError => e
      audit_log! [@business_system.name, params[:file].original_filename, 1]
      json_custom_respond(415, success: false, error_message: e.message)
      return false
    end

    result = []
    begin
      if user_file.file.exists?
        user_import = @data_importer_class.new(current_admin, user_file.path)
        result      = user_import.check_excel
        audit_log! [@business_system.name, params[:file].original_filename, 2, user_import.to_audit_log_comment]
      end
    rescue DataImport::FileTypeError => e
      audit_log! [@business_system.name, params[:file].original_filename, 3, e.message]
      json_custom_respond(400, success: false, error_message: e.message)
      return false
    rescue DataImport::ParserNotFound => e
      audit_log! [@business_system.name, params[:file].original_filename, 3, e.message]
      json_custom_respond(400, success: false, error_message: e.message)
      return false
    rescue DataImport::ParserError => e
      audit_log! [@business_system.name, params[:file].original_filename, 3, e.message]
      json_custom_respond(400, success: false, error_message: e.message)
      return false
    rescue StandardError => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }
      audit_log! [@business_system.name, params[:file].original_filename, 3, e.message]
      json_custom_respond(500, success: false, error_message: e.message)
      return false
    end

    json_respond success:    true,
                 data:       result,
                 request_id: request.request_id,
                 file_name:  params[:file].original_filename
  end

  def cms_user_excel_cancel
    request.request_id = params[:request_id]
    audit_log! params[:file_name]
    json_respond success: true
  end

  def cms_user_excel_submit
    user_import  = @data_importer_class.new(current_admin)
    check_result = user_import.check account_excel_params
    if check_result.all? { |x| x[:status].values.all? }
      user_import.submit(request)
      json_respond success: true
    else
      json_custom_respond 457, data: check_result, error_message: '数据导入失败，请检查必填项'
    end
  rescue StandardError => e
    logger.error { e.backtrace.join("\n") }
    json_custom_respond 456, error_message: e.message
  end

  def search_cms_sync_accounts
    if Setting.data_config['lock_cms_account_sync']
      return json_custom_respond 456, error_message: I18n.t('aas.bank_cms.account_sync.lock_cms_account_sync')
    end

    json_respond BankCms::AccountSync.search(params[:code], params[:name])
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def search_sync_accounts
    all_result = @api.search_account(params, current_admin, request)

    result = \
      if @business_system.id == 303
        if params[:teller_id].present?
          all_result.select { |item| item[:teller_id] =~ /#{params[:teller_id]}/ }
        elsif params[:teller_name].present?
          all_result.select { |item| item[:teller_name] =~ /#{params[:teller_name]}/ }
        elsif params[:teller_seq].present?
          all_result.select { |item| item[:teller_name] =~ /#{params[:teller_seq]}/ }
        else
          all_result
        end
      elsif @business_system.id == 304
        if params[:account].present?
          all_result.select { |item| item[:account] =~ /#{params[:account]}/ }
        elsif params[:account_id].present?
          all_result.select { |item| item[:account_id] =~ /#{params[:account_id]}/ }
        elsif params[:user_name].present?
          all_result.select { |item| item[:user_name] =~ /#{params[:user_name]}/ }
        else
          all_result
        end
      else
        all_result
      end

    json_respond result
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def all_roles
    json_respond @api.all_roles(current_admin)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def create_role
    role_params = @api.role_params(params)
    json_respond @api.create_role(role_params, current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def update_role
    role_params = @api.role_params(params)
    json_respond @api.update_role(role_params, current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def destroy_role
    json_respond @api.destroy_role(params[:role_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def disable_role
    json_respond @api.disable_role(params[:role_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def enable_role
    json_respond @api.enable_role(params[:role_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def all_menus
    json_respond @api.all_menus(current_admin)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def role_menus
    json_respond @api.role_menus(params[:role_id], current_admin)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def update_role_menus
    json_respond @api.update_role_menus(params[:role_id], params[:menu_ids], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def all_organizations
    json_respond @api.all_organizations(current_admin, params[:parent_id])
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def all_organizations_tree
    json_respond @api.all_organizations_tree(current_admin)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def create_organization
    permit_params = @api.organization_params(params)
    json_respond @api.create_organization(permit_params, current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def update_organization
    permit_params = @api.organization_params(params)
    json_respond @api.update_organization(permit_params, current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def destroy_organization
    json_respond @api.destroy_organization(params[:org_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def disable_organization
    json_respond @api.disable_organization(params[:org_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def enable_organization
    json_respond @api.enable_organization(params[:org_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def all_areas
    json_respond @api.all_areas(current_admin)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def org_branches
    json_respond @api.org_branches(params[:org_id], current_admin)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def create_branch
    permit_params = @api.branch_params(params)
    json_respond @api.create_branch(permit_params, current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def update_branch
    permit_params = @api.branch_params(params)
    json_respond @api.update_branch(permit_params, current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def destroy_branch
    json_respond @api.destroy_branch(params[:org_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def disable_branch
    json_respond @api.disable_branch(params[:org_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def enable_branch
    json_respond @api.enable_branch(params[:org_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def org_accounts
    json_respond @api.org_accounts(params[:parent_id], current_admin)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def create_account
    permit_params = @api.account_params(params)
    json_respond @api.create_account(permit_params, current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def update_account
    permit_params = @api.account_params(params)
    json_respond @api.update_account(permit_params, current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def show_account
    json_respond @api.show_account(params[:account_id], current_admin)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def destroy_account
    json_respond @api.destroy_account(params[:account_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def disable_account
    @api.disable_account(params[:account_id], current_admin, request, params)

    @api.update_account_roles(params[:account_id], [], current_admin, request) if @business_system.id == 301

    json_respond success: true
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def enable_account
    json_respond @api.enable_account(params[:account_id], current_admin, request, params)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def reset_account_password
    json_respond @api.reset_account_password(params[:account_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def account_roles
    json_respond @api.account_roles(params[:account_id], current_admin, request)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def update_account_roles
    if @business_system.id == 301
      account = @api.show_account(params[:account_id], current_admin)

      return json_custom_respond 456, error_message: '用户在锁定、停用状态时不能修改角色' if account[:status_string] != '正常'
    end
    json_respond @api.update_account_roles(params[:account_id], params[:role_ids], current_admin, request, params)
  rescue @api_return_error => e
    json_custom_respond 456, error_message: e.message
  end

  def all_departments
    json_respond @api.all_departments
  end

  private

  def set_business_system_api
    @api = @business_system.api
  rescue StandardError => e
    logger.error(e.message)
    logger.error(e.backtrace.join("\n"))

    json_custom_respond(404, error_message: e.message)
  end

  def authenticate_policy
    authorize @business_system, policy_class: PermissionEditablePolicy
  end

  def permissions_params
    params.permit(permission_ids: [])
  end

  def account_excel_params
    permit_fields =
      [
        :org_id,
        :org_name,
        :user_name,
        :user_id,
        :account_phone,
        :id_number,
        :phone,
        :email,
        :system,
        :baseline_id,
        :baseline_name,
        :business_system_id,
        role_ids: []
      ]
    params.permit(accounts: permit_fields)
  end

  def replace_request_id
    request.request_id = params[:guid] if params[:guid].present?
  end

  def set_class_for_business
    @api_return_error = @business_system.api_return_error.safe_constantize
  end

  def set_importer
    @data_importer_class = DataImport::CustomerZyfd::AccountImporter
  end

  # 前端反洗钱的role_ids是单选，后端处理要统一为数组
  def set_params
    new_account_params = params[:accounts].map do |json|
      role_ids = json[:role_ids]
      json[:role_ids] = [role_ids] unless role_ids.is_a?(Array)
      json
    end
    params[:accounts] = new_account_params
  end
end
