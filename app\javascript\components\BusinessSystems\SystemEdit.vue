<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    title="修改系统信息"
    width="500px"
    appendToBody
    @close="dialogVisible = false"
  >
    <el-form
      ref="systemForm"
      :model="systemForm"
      :rules="rules"
      labelWidth="120px"
      statusIcon
      class="new-form"
    >
      <el-form-item label="系统名称：">
        <el-input v-model="systemForm.name" maxlength="255"/>
      </el-form-item>
      <el-form-item label="系统分组：">
        <el-select
          v-model="systemForm.business_system_category_id"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            :value="null"
            label="不选择分组"
          />
          <el-option
            v-for="category in categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="排序编号：">
        <el-input-number
          :min=0
          :step=1
          :step-strictly="true"
          v-model="systemForm.order_number"
        />
      </el-form-item>
    </el-form>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="primary"
        @click="updateSystem"
      >
        更新
      </el-button>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default{
  props: {
    system: {
      type: Object,
      required: true
    },
    categories: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      dialogVisible: false,
      systemForm: {
        name: '',
        business_system_category_id: null,
        order_number: null
      },
      rules: {
        name: [
          { required: true, message: '业务系统名称不能为空', trigger: 'blur' }
        ],
        business_system_category_id: []
      }
    }
  },
  watch: {
    system () {
      this.systemInitialize()
    }
  },
  created () {
    this.systemInitialize()
  },
  methods: {
    systemInitialize () {
      this.systemForm.name = this.system.name
      this.systemForm.business_system_category_id = this.system.category_id
      this.systemForm.order_number = this.system.order_number
    },
    updateSystem () {
      let params = {
        name: this.systemForm.name,
        business_system_category_id: this.systemForm.business_system_category_id,
        order_number: this.systemForm.order_number || null
      }
      this.$axios.put(`/api/systems/${this.system.id}`, { system: params })
        .then(response => {
          this.$emit('update')
          this.$message.success(`更新${this.systemForm.name}成功`)
          this.dialogVisible = false
        })
        .catch(() => {})
    }
  }

}

</script>

<style lang="scss" scoped>
  @import "~@/components/variables";
  .new-form{
    margin-right: 3em;
  }
  .new-input{
    width: 100%;
  }
</style>
