<template>
  <div class="container">
    <el-table
      :data="categoriesByPage"
      border
    >
      <el-table-column
        prop="order_number"
        width="100"
        label="顺序号"
      />
      <el-table-column
        prop="name"
        sortable
        label="告警名称"
      />
      <el-table-column
        sortable
        label="分组名称"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.group_name">
            {{ scope.row.group_name }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="max_deal_interval_duration"
        label="最大处理时限"
      >
      </el-table-column>
      <el-table-column
        prop="is_show"
        label="是否显示"
        sortable
        width="120"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.is_show">
            是
          </span>
          <span
            v-else
            style="color: #bbb"
          >
            否
          </span>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="160"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="handleEdit(scope.row)"
            :disabled="!$store.getters.hasPermission('admin_global_alert_managers.update')"
          >
            编辑
          </el-button>
          <el-button
            v-if="scope.row.is_show"
            size="mini"
            style="width: 65px;"
            @click="handleDisableConfirm(scope.row.id)"
            :disabled="!$store.getters.hasPermission('admin_global_alert_managers.disable')"
          >
            不显示
          </el-button>
          <el-button
            v-else
            size="mini"
            style="width: 65px;"
            @click="handleEnableConfirm(scope.row.id)"
            :disabled="!$store.getters.hasPermission('admin_global_alert_managers.disable')"
          >
            显示
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page.sync="currentPage"
      :page-size="pageSize"
      :total="categories.length"
      background
      layout="total, prev, pager, next, jumper"
      class="systems-pagination"
    />
    <category-edit
      ref="categoryEdit"
      :category="currentCategory"
      :groups="groups"
      @update="getCategories"
    />
  </div>
</template>

<script>
import CategoryEdit from './CategoryEdit.vue'

export default {
  components: {
    CategoryEdit
  },
  data () {
    return {
      categories:      [],
      groups:          [],
      currentCategory: {},
      currentPage:     1,
      pageSize:        15
    }
  },
  computed: {
    categoriesByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.categories.slice(start, end)
    }
  },
  created () {
    if (this.$store.getters.hasPermission('admin_global_alert_managers.query')) { this.getCategories() }
    this.getGroups()
  },
  methods: {
    getCategories () {
      this.$axios.get('/admin_api/global_alert_categories')
        .then(response => {
          this.categories = response.data
        })
        .catch(() => {})
    },
    getGroups () {
      this.$axios.get('/admin_api/global_alert_categories/group_list')
        .then(response => {
          this.groups = response.data
        })
        .catch(() => {})
    },
    handleEdit (alert_category) {
      this.currentCategory                  = alert_category
      this.$refs.categoryEdit.dialogVisible = true
    },
    handleDisableConfirm (id) {
      this.$axios.put(`/admin_api/global_alert_categories/${id}/disable`, { is_show: false })
        .then(response => {
          this.$message.success('操作成功')
          this.getCategories()
        })
        .catch(() => {})
    },
    handleEnableConfirm (id) {
      this.$axios.put(`/admin_api/global_alert_categories/${id}/disable`, { is_show: true })
        .then(response => {
          this.$message.success('操作成功')
          this.getCategories()
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
}

.el-table {
  width: 100%;
}

.systems-pagination {
  margin-top: 20px;
}
</style>
