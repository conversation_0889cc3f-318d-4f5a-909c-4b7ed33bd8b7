# frozen_string_literal: true

# 外部系统权限
class External::RolePermissionAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '外部系统角色管理'
    @operation_category = '外部系统角色管理'
    super
  end

  def create
    @operation  = '赋予外部系统权限'
    bs          = params[0]
    role        = params[1]
    permissions = params[2]
    return if permissions.blank?

    @comment    = "外部系统「#{bs&.name}」赋予外部系统角色「#{role.name}」权限「#{permissions.pluck(:name).join('、')}」成功"
    create_audit_log
  end
end
