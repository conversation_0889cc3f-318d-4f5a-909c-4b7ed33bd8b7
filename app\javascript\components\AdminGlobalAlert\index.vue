<template>
  <div class="container">
    <!-- <h1>查看告警实例</h1> -->
    <el-form
      v-if="!alertsShowFullScreen"
      style="display:flex;justify-content: end; margin:0px 20px -30px 0 ;"
    >
      <el-form-item>
        <el-date-picker
          v-model="dateVal"
          type="daterange"
          size="small"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :clearable="false"
          :picker-options="pickerOptions"
          @change="change"
        />
      </el-form-item>
    </el-form>
    <chart-tab
      v-loading="getLoading"
      :message-list="messageList"
      :date-val="dateVal"
      :all-alerts-count="allAlertsCount"
      :error-message="errorMessage"
      :processed-count="processedCount"
      @detailId="getAlertsCount"
      @statusIdVal="getAlertsCount"
      @fullScreen="handleFullScreen"
    />
  </div>
</template>
<script>
import ChartTab from './ChartTab.vue'
import API from '@/api'

export default {
  components: {
    ChartTab
  },
  data () {
    return {
      alertsShowFullScreen: false,
      dateVal:              [],
      messageList:          [],
      begin_time:           '',
      end_time:             '',
      allAlertsCount:       0,
      processedCount:       0,
      errorMessage:         '',
      getLoading:           false,
      pickerOptions:        {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end   = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end   = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一年',
          onClick (picker) {
            const end   = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {
    if (!this.$store.getters.hasPermission('global_alerts.query')) {
      this.$message.error('您没有权限进行该操作')
      return
    }
    this.resetDateRange()
    this.getAlertsCount()
  },
  methods: {
    resetDateRange () {
      const defaultQueryDate = this.$settings.globalAlert.admin_global_alert.default_query_date
      this.dateVal = API.globalAlerts.getDateRange(defaultQueryDate)
    },
    requestDate (startDate, endDate) {
      this.getLoading = true
      const params = { begin_time: startDate, end_time: endDate }
      API.globalAlerts.list(params)
        .then((res) => {
          this.messageList    = res.data.all_alerts
          this.allAlertsCount = res.data.entyties_count
          this.processedCount = res.data.processed_count
          this.errorMessage   = res.data.error_message
          this.getLoading     = false
        })
        .catch((err) => {
          this.getLoading   = false
          this.errorMessage = err.data.error_message
          console.error(err)
        })
    },
    getAlertsCount () {
      this.requestDate(this.dateVal[0], this.dateVal[1])
    },
    change (e) {
      this.dateVal = e
      this.requestDate(this.dateVal[0], this.dateVal[1])
    },
    handleFullScreen (e) {
      this.alertsShowFullScreen = e
    }
  }
}

</script>

<style lang="scss" scoped>
.container {
  padding-top:      20px;
  background-color: #EFF6FF;
}
</style>
