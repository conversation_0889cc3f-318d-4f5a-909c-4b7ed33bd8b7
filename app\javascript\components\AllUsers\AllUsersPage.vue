<template>
  <div style="height: 100vh;">
    <div class="allContainer" v-loading="loading">
      <search-user
        ref="search_user"
        v-if="displaySearchUser"
        :page="current_page"
        :query="query"
        @update="handleUpdate"
      />
      <div class="summary-container">
        <div class="summary-table">
          <el-table
            ref="expandstable"
            :data="users"
            :expand-row-keys="expands"
            :row-key="getRowKeys"
            height="100%"
            border
            style="width: 100%"
          >
            <el-table-column
              v-for="(item, index) in userInfoColumns"
              v-if="item.show_in_user_page"
              :key="index"
              :min-width="item.min_width"
              v-bind="item"
            >
              <template slot-scope="scope">
                <span v-if="item.prop === 'info.department'">
                  <span v-if="allowGetUsers()">
                    {{ scope.row.info.department }}
                  </span>
                  <el-button
                    v-else
                    type="text"
                    :title="scope.row.info.department"
                    @click="selectDepartment(scope.row.info.department_id)"
                  >
                    {{ scope.row.info.department }}
                  </el-button>
                </span>
                <span v-else>
                  {{ scope.row.info[item.prop.replace(/info./i,"")] }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
                min-width="170"
                label="员工标签"
            >
              <template slot-scope="scope">
                <div
                  class="tag-list"
                  v-for="item in scope.row.info.tags"
                >
                  <el-tag
                      :style="`color: ${item.color};background-color: ${generateRgba(item.color)};`"
                      size="small"
                  >
                    {{item.name}}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="isShowAllSystemInfo"
              label="系统账号详情"
              min-width="170"
              width="140"
            >
              <template
                slot-scope="scope"
                style="color: #ddd;"
              >
                <el-button
                  size="medium"
                  type="text"
                >
                  <system-accounts
                    :accounts="scope.row.accounts"
                    :allSystemInfoCount="allSystemInfoCount"
                  />
                </el-button>
              </template>
            </el-table-column>
            <el-table-column
              v-else
              label="系统账号详情"
              min-width="170"
            >
              <template slot-scope="scope">
                <div
                  v-for="x in scope.row.accounts"
                  :key="x.account.account_id"
                  class="account-span"
                >
                  <el-popover
                    :content="displayRoles(x.account.roles)"
                    placement="right-start"
                    title="账号所属组或角色"
                    width="200"
                    trigger="hover"
                  >
                    <el-button
                      slot="reference"
                      type="text"
                      class="system-button"
                      @click="accountDetail(x.account, x.system)"
                    >
                      <span :class="getAccountClass(x.account)">
                        {{ x.system.full_name }}
                      </span>
                    </el-button>
                  </el-popover>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="操作"
              width="130"
              header-align="center"
            >
              <template slot-scope="scope">
                <p>
                  <el-button
                    size="small"
                    @click="showUserDetail(scope.row.info.user_id)"
                  >
                    员工详情
                  </el-button>
                </p>
              </template>
            </el-table-column>
          </el-table>
        </div>
  
        <comment-bar>
          注意：{{ $t('common.notice.account_list') }}
        </comment-bar>

        <el-pagination
          :page-size="per_page"
          :total="count"
          @current-change="handleCurrentChange"
          :current-page.sync="current_page"
          :style="{marginTop: '20px'}"
          background
          layout="total, prev, pager, next, jumper"
        />
      </div>
      <user-detail
        :user-id="userId"
        :visible.sync="userDetailVisible"
        @setTag="handleTagChange"
        append-to-body
      />
    </div>
  </div>
</template>
<script>
import API                      from '@/api'
import UserHistory              from '@/components/UserHistory'
import CommentBar               from '@/components/common/CommentBar.vue'
import SearchUser               from '@/views/SearchUser.vue'
import ShowAllSystemInfo        from '@/views/ShowAllSystemInfo.vue'
import SystemAccounts           from '@/components/common/SystemAccounts.vue'
import HistoricalAccountCompare from '@/components/AllUsers/HistoricalAccountCompare.vue'
import UserDetail               from '@/components/UserDetail'
import { hexToRgb }             from '@/utils/color_util'
export default {
  components: {
    UserHistory,
    CommentBar,
    SearchUser,
    ShowAllSystemInfo,
    SystemAccounts,
    HistoricalAccountCompare,
    UserDetail
  },
  props: {
    query: {
      type: String,
      default: ''
    },
    displaySearchUser: {
      type: Boolean,
      default: true
    },
    jobId: {
      type: Number,
      default: () => null
    },
    quarter: {
      type:    Object,
      default: () => {}
    }
  },
  data () {
    return {
      loading:         false,
      users:           [],
      count:           0,
      current_page:    1,
      per_page:        10,
      activeNames:     [],
      expands: [],
      userId: 0,
      unFoldButton:    'el-icon-caret-right',
      foldStatus: true,
      userDetailVisible: false,
      selectUserId:    '',
      allSystemInfoCount: 0,
      isShowAllSystemInfo: this.$settings.allUsers.show_systems,
      getRowKeys: (row) => {
        return row.info.id
      }
    }
  },
  computed: {
    currentQuarter () {
      if (this.allowGetUsers ()) {
        return this.quarter
      }else{
        return this.$store.state.current_quarter
      }
    },
    userInfoColumns () {
      return JSON.parse(JSON.stringify(this.$settings.userInfoColumns))
    }
  },
  watch: {
    quarter () {
      this.getUsers()
    },
    jobId () {
      this.getUsers()
    }
  },
  created () {
    this.gettingAllSystemInfos()
    // 如果是从岗位基线点击过来，需要执行如下
    this.getUsers()
  },
  methods: {
    // 判断是否从岗位基线过来
    allowGetUsers () {
      return this.displaySearchUser === false && this.jobId !== null && !API.tool.isBlank(this.quarter)
    },
    handleCurrentChange (e) {
      this.current_page = e
      // 如果是从岗位基线点击过来，需要执行如下
      this.getUsers()
    },
    showUserDetail (userId) {
      this.userDetailVisible = true
      this.userId = userId
    },
    gettingAllSystemInfos () {
      this.$axios.get('/api/all_system_info/all_list')
        .then(response => {
          this.allSystemInfoCount = response.data.not_online_system_count
        })
        .catch(_e => {
        })
    },
    selectDepartment (departmentId) {
      this.$refs.search_user.local_department = departmentId
      this.$refs.search_user.dataUpdate()
    },
    accountDetail (account, system) {
      this.$emit('click', account, system)
    },
    displayRoles (roles) {
      if (roles.length === 0) {
        return '无'
      } else {
        return roles.map(x => x.name).join(' / ')
      }
    },
    // 获取搜索用户组件返回的数据
    handleUpdate (row) {
      if (this.$lodash.has(row, 'users')) this.users = row.users
      if (this.$lodash.has(row, 'count')) this.count = row.count
      if (this.$lodash.has(row, 'loading')) this.loading = row.loading
    },
    getUsers () {
      if (!this.allowGetUsers()){
        return
      }
      this.loading = true
      var params = {
        job_id:     this.jobId,
        quarter_id: this.currentQuarter.id,
        page:       this.current_page,
        per_page:   this.per_page,
      }
      this.$axios.get('/api/users/search', { params: params })
        .then(response => {
          this.loading = false
          this.users = response.data.users
          this.count = response.data.count
        })
        .catch(error => {
          this.loading = false
        })
    },
    exportUserAccounts () {
      const params = { user_id: this.userId, quarter_id: this.currentQuarter.id }
      API.users.accountsExport(params)
        .then(() => {})
        .catch(error => {
          error.data.text().then((res) => {
            this.$message.error(JSON.parse(res).error_message)
          })
        })
    },
    exportUserAccountsList () {
      const params = {
        user_id: this.userId,
        quarter_id: this.currentQuarter.id
      }
      API.users.accountsListExport(params)
        .then(() => {})
        .catch(error => {
          error.data.text().then((res) => {
            this.$message.error(JSON.parse(res).error_message)
          })
        })
    },
    getAccountClass (account) {
      let style = 'system-account'
      if (!account.status) {
        style = 'system-account-disable'
      } else if (account.is_blank_permission) {
        style = 'system-account-blank'
      }
      return style
    },
    handleTagChange() {
      this.$refs.search_user.dataUpdate()
    },
    generateRgba(color) {
      let [r, g, b] = hexToRgb(color)
      return `rgba(${r}, ${g}, ${b}, 0.1)`
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.allContainer { 
  height: 100vh;
  display: flex; 
  flex-direction: column;
}

.summary-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 260px);
}

.summary-table {
  flex: 1;
}

.container {
  margin: auto;
}

.title {
  margin-top: 1em;
}

.content {
  margin-bottom: 5em;
}

.account-span {
  margin: 0 1em 0 1em;
}

.show {
  font-size:     1.5em;
  text-align:    center;
  margin-bottom: 80%;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity .5s
}

.fade-enter, .fade-leave-to {
  opacity: 0
}

.system-button{
  text-align: left;
  white-space: normal;
}

.system-account-disable {
  color: #E54D42;
}

.system-account-blank {
  color: #909399;
}

.demo-table-expand {
  font-size: 0;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
}

.all-system-account {
  color: #909399;
  margin-top: 10px;
}

.normal-system-account {
  color: #409EFF;
  margin-top: 10px;
}

.disable-system-account {
  color: #F56C6C;
  margin-top: 10px;
}
.export-button {
  height: 25px;
}

.tag-list {
  margin-top: 5px;
}
</style>
