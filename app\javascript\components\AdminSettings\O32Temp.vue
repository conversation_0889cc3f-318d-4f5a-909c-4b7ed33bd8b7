<template>
  <div class="container">
    <authorizer-upload v-if="isAuthorizerCustomListEnable" />
    <el-divider v-if="isOperatorCheckEnable || isFundTypeCheckEnable" />
    <operator-edit v-if="isOperatorCheckEnable" />
    <el-divider v-if="isOperatorCheckEnable" />
    <department-edit v-if="isOperatorCheckEnable" />
    <el-divider v-if="isFundTypeCheckEnable" />
    <fund-type-ignore-accounts-edit v-if="isFundTypeCheckEnable" />
    <el-divider v-if="isDateCheckEnable" />
    <date-check-setting v-if="isDateCheckEnable" />
  </div>
</template>
<script>
import OperatorEdit               from '@/components/AdminSettings/O32Temp/OperatorEdit'
import AuthorizerUpload           from '@/components/AdminSettings/O32Temp/AuthorizerUpload'
import FundTypeIgnoreAccountsEdit from '@/components/AdminSettings/O32Temp/FundTypeIgnoreAccountsEdit'
import O32Settings                from '@/components/AdminSettings/O32Temp/O32Settings'
import DepartmentEdit             from '@/components/AdminSettings/O32Temp/DepartmentEdit'
import DateCheckSetting             from '@/components/AdminSettings/O32Temp/DateCheckSetting'

export default {
  components: {
    AuthorizerUpload,
    OperatorEdit,
    FundTypeIgnoreAccountsEdit,
    DepartmentEdit,
    DateCheckSetting
  },
  mixins: [O32Settings]
}
</script>
