import axios from "@/settings/axios";
import { parseFileName, downloadBlob } from "./tool";

export const index = (data) => {
  return axios.get("/api/fdfg_gzyss45_comparison", { params: data });
}

export const exportData = (data) => {
  return axios
    .get(`/api/fdfg_gzyss45_comparison/export`, {
      responseType: "blob",
      params: data,
    })
    .then((response) => {
      const fileName = parseFileName(response, "system-alignment-export.xlsx");
      downloadBlob(response.data, fileName);
    })
    .catch(() => {});
}
