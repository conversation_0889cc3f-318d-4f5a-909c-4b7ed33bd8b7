import axios from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'

// 不分页
export const list = (params = {}) => {
  return axios.get('/api/o32_option_baselines/list', { params: params })
}

// 分页
export const index = (params = {}) => {
  return axios.get('/api/o32_option_baselines', { params: params })
}

export const destroy = (id) => {
  return axios.delete(`/api/o32_option_baselines/${id}`, {})
}

export const update = (id, params = {}) => {
  return axios.put(`/api/o32_option_baselines/${id}`, params)
}

export const create = (params = {}) => {
  return axios.post('/api/o32_option_baselines', params)
}

export const o32Options = (baselineId, params = {}) => {
  return axios.get(`/api/o32_option_baselines/${baselineId}/o32_options`, { params: params })
}

export const searchCategories = (params = {}) => {
  return axios.get('/api/o32_option_baselines/search_categories', { params: params })
}

export const exportData = (baselineId) => {
  return axios.get(`/api/o32_option_baselines/${baselineId}/export`, { responseType: 'blob' })
    .then(response => {
      const fileName = parseFileName(response, 'o32_option_baseline.xlsx')
      downloadBlob(response.data, fileName)
    })
    .catch((error) => {
      throw error
    })
}