<script>
export default {
  data () {
    return {
      currentPage:  1, // 当前页码
      pageSize:     15, // 每页的数据条数
      tableLoading: false
    }
  },
  computed: {
    queryData () {
      return {
        fundName:         this.filter.fund_name,
        reportName:       this.filter.report_name,
        accountCode:      this.filter.account_code,
        accountName:      this.filter.account_name,
        dealStatus:       this.filter.statusFilter,
        recoverStatus:    this.filter.recoverStatusFilter,
        beginTime:        this.filter.systemTime[0],
        endTime:          this.filter.systemTime[1],
        bsIds:            this.filter.systemName.join(', '),
        remark:           this.filter.remark,
        column_1:         this.filter.column_1,
        column_2:         this.filter.column_2,
        column_3:         this.filter.column_3,
        column_4:         this.filter.column_4,
        column_5:         this.filter.column_5,
        column_6:         this.filter.column_6,
        column_7:         this.filter.column_7,
        column_8:         this.filter.column_8,
        column_9:         this.filter.column_9,
        column_10:        this.filter.column_10,
        column_user_code: this.filter.user_code,
        column_user_name: this.filter.user_name,
        departmentIds:    this.filter.departmentIds,
        sortProps:        this.sortProps
      }
    }
  },
  watch:    {
    currentPage () {
      this.handleQueryChange()
    },
    pageSize () {
      this.handleQueryChange()
    }
  },
  methods:  {
    handleQueryChange () {
      this.tableLoading = true
      this.$axios.get(`admin_api/global_alerts/category/${this.tableTitlaActive}`, {
        params: {
          per_page:       this.pageSize,
          page:           this.currentPage,
          begin_time:     this.queryData.beginTime,
          end_time:       this.queryData.endTime,
          account_code:   this.queryData.accountCode,
          account_name:   this.queryData.accountName,
          deal_status:    this.queryData.dealStatus,
          recover_status: this.queryData.recoverStatus,
          bs_ids:         this.queryData.bsIds,
          remark:         this.queryData.remark,
          fund_name:      this.queryData.fundName,
          report_name:    this.queryData.reportName,
          column_1:       this.queryData.column_1,
          column_2:       this.queryData.column_2,
          column_3:       this.queryData.column_3,
          column_4:       this.queryData.column_4,
          column_5:       this.queryData.column_5,
          column_6:       this.queryData.column_6,
          column_7:       this.queryData.column_7,
          column_8:       this.queryData.column_8,
          column_9:       this.queryData.column_9,
          column_10:      this.queryData.column_10,
          column_user_code: this.queryData.column_user_code,
          column_user_name: this.queryData.column_user_name,
          sort_props:       this.queryData.sortProps,
          department_ids: this.queryData.departmentIds
        }
      })
        .then((res) => {
          this.tableData    = res.data.entities
          this.total        = res.data.count
          this.tableLoading = false
        })
        .catch((err) => {
          this.tableLoading = false
          console.log(err)
        })
    }
  }
}
</script>
