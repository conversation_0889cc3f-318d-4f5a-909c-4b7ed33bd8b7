# frozen_string_literal: true

# 登录相关
module ActAsExternalLogins
  extend ActiveSupport::Concern

  private

  def authenticate_local
    # 找不到系统用户或邮箱与系统账号不匹配报错
    return no_user_log unless @resource

    # 禁用账号的处理
    return disabled_log('local') if @resource.disabled_at

    # 锁定账号的处理
    return locked_log('local') if @resource.access_locked?

    unless @resource.valid_password? password
      @resource.valid_for_authentication? { false } if is_a?(ExternalAuthController)

      return repeated_log
    end

    @resource.reset_failed_attempts!

    # 正常登录流程
    pass_all_authenticate
  end

  def login_logger
    @login_logger ||= LoginLogger.new
  end

  def audit_failed_log(message)
    audit_log! message, action: :login_failed_with_reason, audit_log_class: SessionAuditLog
  end

  def render_create_success
    render json: {
      success: true,
      data:    resource_data(resource_json: @resource.token_validation_response.merge(home_page: @resource.home_page))
    }
  end
end
