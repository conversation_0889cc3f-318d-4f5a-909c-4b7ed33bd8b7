<template>
  <!-- eslint-disable vue/attribute-hyphenation -->
  <el-dialog
    v-loading="loading"
    :visible.sync="localVisible"
    :close-on-click-modal="false"
    width="45%"
    top="8em"
    title="请选择要关联的员工"
    append-to-body
    @open="handleOpen"
    @close="handleClose"
  >
    <template>
      <el-cascader
        v-model="selectData"
        :options="userTree"
        filterable
        class="user-cascader"
        @change="handleChange"
      />
    </template>
    <template>
      <user-info
        v-if="userId && showUserDetail"
        ref="userDetail"
        :userId="userId"
        border
        style="margin-top: 2em"
        @get="getUserDetail"
      />
    </template>

    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="localVisible = false">取 消</el-button>
      <el-button
        type="primary"
        @click="batchCreateLink"
      >
        关联员工
      </el-button>
    </span>
  </el-dialog>
  <!-- eslint-enable vue/attribute-hyphenation -->
</template>

<script>
import UserInfo      from './UserInfo.vue'
import API           from '@/api'

export default {
  components: {
    UserInfo
  },
  props:      {
    visible:  {
      type:     Boolean,
      required: true
    },
    accountCodes:   {
      type:     Array,
      required: true
    },
    systemId: {
      type:     Number,
      required: true
    }
  },
  data () {
    return {
      userTree:      [],
      selectData:    [],
      localVisible:  false,
      userId:        null,
      loading:       false,
      showUserDetail: false,
    }
  },
  watch: {
    visible () {
      this.localVisible = this.visible
    },
    localVisible () {
      this.$emit('update:visible', this.localVisible)
    },
  },
  created () {
    this.localVisible = this.visible
    this.loadUserTree()
  },
  methods: {
    loadUserTree () {
      this.userTree = this.$store.state.user_tree
    },
    batchCreateLink() {
      if (this.selectData.length === 0) {
        this.$message.error('关联失败，您尚未选择需要关联的员工，请重新操作')
        return
      }
      this.loading = true
      let accountDatas = this.accountCodes.map(x => {
        return {
          account_code: x,
          user_id: this.userId
        }
      })
      this.$axios.post(`/admin_api/ledgers/bs/${this.systemId}/accounts/bulk`, accountDatas)
        .then(response => {
          this.loading = false
          if (response.status === 200) {
            this.localVisible = false
            this.$emit('dataUpdate')
            this.$message.success('批量关联成功')
            this.$EventBus.$emit('account:change')
          } else {
            this.$message.error('批量关联失败')
          }
        })
        .catch((error) => {
          this.loading = false
          console.error(error)
        })
    },
    handleChange () {
      this.userId = this.selectData[this.selectData.length - 1]
    },
    getUserDetail (payload) {
      if (payload.tree_ids) this.selectData = payload.tree_ids
    },
    handleOpen() {
      this.showUserDetail = true
    },
    handleClose() {
      this.showUserDetail = false
    }
  }
}
</script>

<style lang="scss" scoped>
.user-cascader {
  max-width: 650px;
  width:     100%;
}

.checkbox-container {
  margin-top: 20px;
}
</style>
