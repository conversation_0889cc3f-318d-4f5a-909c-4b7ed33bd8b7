<template>
  <div class="title-line">
    <div class="title">
      <span>{{ diffDisplayTitle }}</span>
      <span>
        <el-button
          v-if="diffMode != 'compareHistory'"
          type="text"
          size="small"
          @click="handleDiffTypeChange('compareHistory')"
        >
          {{ titles.compareHistory }}
        </el-button>
      </span>
      <span>
        <el-button
          v-if="diffMode != 'compareBaseline'"
          type="text"
          size="small"
          :disabled="!$store.getters.hasPermission('system_summary.diff_baseline')"
          @click="handleDiffTypeChange('compareBaseline')"
        >
          {{ titles.compareBaseline }}
        </el-button>
      </span>
      <span v-if="isShowOtherAccountPermissionCompare">
        <el-button
          v-if="diffMode != 'compareAccount'"
          type="text"
          size="small"
          :disabled="!$store.getters.hasPermission('system_summary.diff_account')"
          @click="handleDiffTypeChange('compareAccount')"
        >
          {{ titles.compareAccount }}
        </el-button>
      </span>
    </div>
    <div
      v-if="diffMode === 'compareBaseline' && bsName"
      class="baseline-name"
    >
      <el-tag>系统基线名称：{{ bsName }}</el-tag>
    </div>
    <div
      v-if="diffMode === 'compareBaseline' && bsName === ''"
      class="baseline-name"
    >
      <el-tag type="warning">该账号尚未关联系统基线数据</el-tag>
    </div>

    <quarter-select
      v-if="diffMode === 'compareHistory'"
      v-model="selectQuarter"
      default-select="second"
      @quarterChange="quarterChange"
    />
    <system-account-select
      v-if="diffMode === 'compareAccount'"
      v-model="selectAccount"
      :system-id="systemId"
      @change="handleChangeAccount"
    />
  </div>
</template>

<script>
import QuarterSelect       from '@/components/common/QuarterSelect.vue'
import SystemAccountSelect from '@/components/common/SystemAccountSelect.vue'

export default {
  components: {
    QuarterSelect,
    SystemAccountSelect
  },
  props:      {
    baselineName: {
      type:    String,
      default: ''
    },
    systemId:     {
      type:    [Number, String],
      default: ''
    },
    account:      {
      type:     Object,
      required: true
    }
  },
  data () {
    return {
      titles:           {
        compareHistory:  '历史权限比对信息',
        compareBaseline: '系统基线权限比对信息',
        compareAccount:  '其他账号对比信息'
      },
      selectQuarter:    0,
      quarters:         [],
      bsName:           this.baselineName,
      selectAccount:    this.account.account_code,
      compareWithOther: false,
      diffMode:         this.defaultDiffMode()
    }
  },
  watch: {
    account: {
      handler (_newV, _oldV) {
        if (this.account.system_baseline !== null) {
          this.bsName = this.account.system_baseline.name
          this.$emit('bsNameChange', this.bsName)
        }
      },
      deep: true
    },
    baselineName (val) {
      this.bsName = val
    }
  },
  computed: {
    isShowOtherAccountPermissionCompare () {
      return this.$settings.showOtherAccountPermissionCompare
    },
    diffDisplayTitle () {
      return this.titles[this.diffMode]
    }
  },
  created () {
    this.$emit('accountChange', this.selectAccount)
  },
  methods: {
    quarterChange (payload) {
      this.$emit('quarterChange', payload)
    },
    defaultDiffMode () {
      return this.$settings.accountDetail.show_diff_baseline_default ? 'compareBaseline' : 'compareHistory'
    },
    handleDiffTypeChange (val) {
      this.diffMode = val
      this.$emit('diffModeChange', this.diffMode)
    },
    handleChangeAccount (accountCode) {
      this.$emit('accountChange', accountCode)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/components/_variables';

.title-line {
  @include vertical_center_between;
  height: 40px;

  .title {
    font-size: 16px;
  }

}
</style>
