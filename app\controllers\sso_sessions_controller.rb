class SsoSessionsController < ApplicationController
  before_action :should_login!, only: [:sso_service]
  include ActAsSsoApis
  include ActAsExternalLogins

  skip_before_action :verify_authenticity_token, only: [:sso_service]

  # 本地登录
  def sso_service
    # 开发环境直接使用指定用户登录
    return login_in_development if Rails.env.development?
    begin
      attribute, login_name = get_resource_attribute
    rescue SsoConnector::LoginNameParseFailed => e
      return login_is_blank
    end

    audit_log! login_name, action: :sso_login_attempt, audit_log_class: SessionAuditLog

    found_resource(attribute, login_name)
  end

  # 判断跳转sso地址
  def connect
    return root_path if admin_signed_in?

    enable = Setting.frontendSettings&.[]('authMethod') == 'sso'
    return unless enable

    back_url  = "#{app_base_url}sso_sessions/raw_url"
    login_url = Setting.sso&.[]('login_url')

    if back_url.match? login_url
      redirect_to back_url
    else
      redirect_to "#{login_url}?targetredirecturl=#{CGI.escape(back_url)}"
    end
  end

  # 民生单点登录不支持软链，所以需要中转
  # 长盛单点登录带参数randKey
  def raw_url
    back_url = "#{app_base_url}sso_sessions/service"
    back_url = "#{back_url}?token=#{params[:randKey]}" if Setting.sso['adapter'] == 'csfund'
    back_url = "#{back_url}?token=#{params[:code]}" if Setting.sso['adapter'] == 'yiban'
    redirect_to back_url
  end

  private

  # app_base_url 结尾为 '/', 所有拼接 url 需要注意这一点
  def app_base_url
    Setting.app_base_url
  end

  def found_resource(attribute, value)
    @resource = Admin.find_by(attribute => value)

    if @resource.blank?
      login_logger.tagged('sso', value) do
        login_logger.error { "not found admin for code: #{value}" }
      end
      return resource_not_found
    end

    after_found_resource
  end

  def resource_data(opts = {})
    opts[:resource_json] || @resource.as_json
  end

  def login_in_development
    login_name = Setting.sso&.[]('login_in_development')
    @resource  = Admin.find_by(code: login_name)

    if @resource.blank?
      login_logger.tagged('sso', login_name) do
        login_logger.error { "not found admin for code: #{login_name}" }
      end
      return resource_not_found
    end

    after_found_resource
  end

  # 找到资源后的处理
  def after_found_resource
    # 禁用账号的处理
    if @resource.disabled_at
      login_logger.tagged('sso', @resource.code) do
        login_logger.error { 'user is disabled' }
      end
      return user_is_disabled
    end

    # 锁定账号的处理
    if @resource.access_locked?
      login_logger.tagged('sso', @resource.code) do
        login_logger.error { 'user is locked' }
      end
      return user_is_locked
    end

    # 当用户未登录过时，激活账号
    unless @resource.confirmed_at
      @resource.confirmed_at = Time.now
      @resource.save
    end

    login_logger.tagged(@resource.code) do
      login_logger.debug { 'login success' }
    end
    audit_log! action: :login_success, audit_log_class: SessionAuditLog

    @token = @resource.create_token
    sign_in(:user, @resource, store: false, bypass: false)
    render_create_success
  end

  def user_is_disabled
    msg = I18n.t('devise_token_auth.sessions.user_is_disabled')
    msg = "#{msg}，#{Setting.mail_content.to_s}" if Setting.mail_content
    audit_failed_log(msg)
    render json: { success: false, msg: msg }
  end

  def user_is_locked
    msg = I18n.t('devise_token_auth.sessions.not_confirmed')
    msg = "#{msg}，#{Setting.mail_content.to_s}" if Setting.mail_content
    audit_failed_log(msg)
    render json: { success: false, msg: msg }
  end

  def login_is_blank
    msg = I18n.t('errors.sso.login_is_blank')
    msg = "#{msg}，#{Setting.mail_content.to_s}" if Setting.mail_content
    audit_failed_log(msg)
    render json: { success: false, msg: msg }
  end

  def resource_not_found
    msg = I18n.t('errors.sso.resource_not_found')
    msg = "#{msg}，#{Setting.mail_content.to_s}" if Setting.mail_content
    audit_failed_log(msg)
    render json: { success: false, msg: msg }
  end
end
