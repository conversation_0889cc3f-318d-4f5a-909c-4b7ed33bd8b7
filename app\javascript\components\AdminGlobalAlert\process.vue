<template>
  <div>
    <el-dialog
      title="告警批量处理"
      :visible.sync="dialogVisible"
      width="40%"
      :before-close="handleClose"
    >
      <div>
        <div>
          <el-form
            ref="fromData"
            :model="fromData"
            label-width="120px"
            class="demo-ruleForm"
            :rules="rules"
          >
            <el-form-item
              label="处理状态"
              prop="deal_status"
            >
              <el-checkbox
                v-model="statusValue"
                :disabled="!maintain || closeStatusValue"
              >
                {{ statusValue == true ? '已处理' :'未处理' }}
              </el-checkbox>
            </el-form-item>
            <el-form-item
                label="关闭告警"
                prop="close_status"
            >
              <el-checkbox
                  v-model="closeStatusValue"
                  :disabled="!maintain || statusValue"
              >
                {{ closeStatusValue ? '已关闭' :'未关闭' }}
              </el-checkbox>
              <el-tooltip class="item" effect="dark" placement="right">
                <span><i class="el-icon-question"></i></span>
                <template slot="content">关闭告警，若问题未解决，会产生新的告警</template>
              </el-tooltip>
            </el-form-item>
            <el-form-item
                v-if="closeStatusValue"
                label="关闭原因"
                prop="close_reason"
                :rules="{
                    required: true, message: '请输入关闭原因', trigger: 'change'
                  }"
            >
              <el-col :span="12">
                <el-input
                    v-model="fromData.close_reason"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 6}"
                    placeholder="请输入关闭原因"
                    :disabled="!maintain"
                    style="width: 300px;"
                />
              </el-col>
            </el-form-item>
            <el-form-item
              v-if="!closeStatusValue"
              label="是否屏蔽告警"
              prop="enable"
            >
              <el-switch
                v-model="fromData.enable"
                :disabled="!maintain"
              />
            </el-form-item>
            <el-form-item
              v-if="fromData.enable==true && !closeStatusValue"
              label="解除屏蔽时间"
              prop="enable_at"
            >
              <el-col :span="12">
                <el-date-picker
                  v-model="fromData.enable_at"
                  type="date"
                  placeholder="选择日期"
                  :picker-options="pickerOptions"
                  :disabled="!maintain"
                />
              </el-col>
            </el-form-item>
            <el-form-item
              v-if="fromData.enable==true && !closeStatusValue"
              label="备注"
              prop="comment"
              :rules="{
                required: true, message: '请输入备注内容', trigger: 'change'
              }"
            >
              <el-col :span="12">
                <el-input
                  v-model="fromData.comment"
                  type="textarea"
                  :autosize="{ minRows: 4, maxRows: 6}"
                  placeholder="请输入备注内容"
                  style="width: 300px;"
                  :disabled="!maintain"
                />
              </el-col>
            </el-form-item>
            <template v-else>
              <el-form-item
                  label="备注"
                  prop="comment"
              >
                <el-col :span="12">
                  <el-input
                      v-model="fromData.comment"
                      type="textarea"
                      :autosize="{ minRows: 4, maxRows: 6}"
                      placeholder="请输入备注内容"
                      :disabled="!maintain"
                      style="width: 300px;"
                  />
                </el-col>
              </el-form-item>
            </template>
            <el-form-item
              label="附件"
            >
              <common-upload
                ref="commonUpload"
                @updateFormData="updateFormData"
                @removeFile="removeFile"
              >
              </common-upload>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          @click="cancelBtn"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          :disabled="!maintain"
          @click="confirmBtn('fromData')"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CommonUpload from "@/components/common/CommonUpload.vue";

export default {
  components: {
    CommonUpload
  },
  props: {
    dialogVisible: {
      type: Boolean,
      required: true
    },
    activeIds: {
      type: Array,
      default: () => {}
    },
    maintain:  {
      type:     Boolean,
      default:  false
    }
  },
  data () {
    return {
      fromData: {
        deal_status: '',
        updated_at: '',
        enable: false,
        enable_at: '',
        prev_quarter_id: 0,
        bs_id: 0,
        account_id: 0,
        comment: '',
        close_status: '',
        close_reason: '',
        uuid: ''
      },
      statusIds: [],
      statusValue: false,
      closeStatusValue: false,
      rules: {
        enable_at: [
          {  required: true, message: '请选择日期', trigger: 'change' }
        ]
      },
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now()
        }
      }
    }
  },
  watch: {
    fromDatas (newVal, oldVal) {
      this.fromData = newVal
      this.fromData.enable = !this.fromData.enable
      this.statusValue = this.fromData.deal_status
      this.closeStatusValue = this.fromData.close_status
    },
    activeIds (newOld, oldVal) {
      this.statusIds = newOld.map(e => e.id)
    },
    dialogVisible () {
      this.fromData.enable = false
      this.statusValue = false
      this.fromData.comment = ''
      this.fromData.enable_at = ''
      this.fromData.uuid = ''
      if (this.$refs.commonUpload) {
        this.$refs.commonUpload.attachments = []
      }
    },
    closeStatusValue(newVal, oldVal) {
      // 当勾选关闭告警时，要将屏蔽告警置为空
      if(newVal) {
        this.resetDealFormField()
      }
    }
  },
  created () {
  },
  mounted () {
  },
  methods: {
    cancelBtn () {
      this.$confirm('确认关闭告警批量处理？')
        .then(_ => {
          this.$emit('dialogAcive', false)
        })
        .catch(_ => {  })
    },
    handleClose (done) {
      this.$confirm('确认关闭告警批量处理？')
        .then(_ => {
          this.$emit('dialogAcive', false)
        })
        .catch(_ => {  })
    },
    // 将处理状态，屏蔽时间置为初始状态
    resetDealFormField() {
      this.fromData.enable = false
      this.statusValue = false
      this.fromData.enable_at = ''
    },
    confirmBtn (fromData) {
      this.$refs[fromData].validate((valid) => {
        if (valid) {
          this.$confirm('此操作将处理多条信息, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            if (this.closeStatusValue) {
              this.$confirm('您正在操作批量关闭告警，若问题未解决，会产生新的告警，是否继续？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.batchUpdate()
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消处理'
                })
              })
            } else {
              this.batchUpdate()
            }
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消处理'
            })
          })
        }
      })
    },
    batchUpdate() {
      this.$axios.put(`admin_api/global_alerts/batch_deal`,{
        global_alert_ids: this.statusIds,
        enable: !this.fromData.enable,
        enable_at: this.fromData.enable_at,
        deal_status: this.statusValue === false ? 0 : 1,
        comment: this.fromData.comment,
        close_status: this.closeStatusValue === false ? 0 : 1,
        close_reason: this.fromData.close_reason,
        common_file_uuid: this.fromData.uuid
      }).then(res => {
        this.$emit('statusIdVal', this.statusIds)
        this.$message({
          type: 'success',
          message: '处理成功!'
        }, this.$emit('dialogAcive', false))
      }).catch((err) => {
        console.log(err)
      })
    },
    updateFormData(data) {
      this.fromData.uuid = data.uuid
    },
    removeFile() {
      this.fromData.uuid = ''
    }
  }
}

</script>

  <style scoped lang="scss">
  </style>
