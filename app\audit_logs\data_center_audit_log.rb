class DataCenterAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '自定义业务系统管理'
    super
  end

  def create
    data_center_group_category
    @operation = '创建数据表'
    @comment   = "创建了数据表：「#{bundle_comment_data(params)}」"
    create_audit_log
  end

  def create_fail
    data_center_group_category
    @operation = '创建数据表'
    @comment   = "创建数据表失败：「#{params}」"
    create_audit_log
  end

  def update
    data_center_group_category
    @operation = '更新数据表'
    @comment   = "更新了数据表 ：「#{bundle_comment_data([params[0]])}」更新为 「#{bundle_comment_data([params[1]])}」"
    create_audit_log
  end

  def update_fail
    data_center_group_category
    @operation = '更新数据表'
    @comment   = "更新数据表失败 ：「#{params}」"
    create_audit_log
  end

  def destroy
    data_center_group_category
    @operation = '删除数据表'
    @comment   = "删除了数据表 ：「#{bundle_comment_data(params)}」"
    create_audit_log
  end

  private

  def data_center_group_category
    @operation_category = '数据表管理'
  end

  def bundle_comment_data(params)
    param_hash = params.first
    comment = ''
    comment += "表名称：#{param_hash[:name]}, 中文名称：#{param_hash[:cn_name]}, 表描述：#{param_hash[:desc]}, 数据库类型：#{param_hash[:db_type]}"
    if param_hash[:data_center_columns].present?
      param_hash[:data_center_columns].each_with_index do |column, index|
        comment += ", 字段#{index+1}：#{column[:name]}, 类型：#{column[:type_name]}"
      end
    end
    comment
  end
end
