# frozen_string_literal: true

# 登录审计
class SessionAuditLog < ApplicationAuditLog

  def initialize(user, request, params)
    @operation_module = '用户认证'
    super
  end

  def external_login_attempt
    set_login_category
    @operation = '「外部验证 方式」尝试登录'
    @comment   = "尝试使用账号「#{params}」登录系统"
    create_audit_log
  end

  def sso_login_attempt
    set_login_category
    @operation = '「SSO 方式」尝试登录'
    @comment   = "尝试使用员工编号「#{params}」登录系统"
    create_audit_log
  end

  def login_attempt
    set_login_category
    @operation = '尝试登录'
    @comment   = "尝试使用账号「#{params}」登录系统"
    create_audit_log
  end

  def login_success
    set_login_category
    @operation = '用户登录'
    @comment   = '登录成功'
    create_audit_log
  end

  def user_locked
    set_login_category
    @operation = '锁定用户'
    @comment   = '账号连续输入错误次数过多，已被锁定'
    create_audit_log
  end

  def login_failed
    set_login_category
    @operation = '用户登录'
    @comment   = "登录失败，这是第 #{params} 次尝试"
    create_audit_log
  end

  def login_failed_with_reason
    set_login_category
    @operation = '用户登录'
    @comment   = "登录失败, 原因是#{params}"
    create_audit_log
  end

  def password_expired
    set_login_category
    @operation = '密码过期'
    @comment   = "密码已过期，上次密码修改时间为 #{params.to_s}"
    create_audit_log
  end

  def password_change_requested
    set_login_category
    @operation = '密码过期'
    @comment   = '密码需要重置'
    create_audit_log
  end

  def logout
    @operation_category = '用户退出'
    @operation          = '用户退出'
    @comment            = '成功退出登录'
    create_audit_log
  end

  private

  def set_login_category
    @operation_category = '用户登录'
  end
end
