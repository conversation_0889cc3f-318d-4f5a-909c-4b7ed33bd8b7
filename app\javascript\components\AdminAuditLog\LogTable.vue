<template>
  <el-table
    :data="tableData"
    border
    stripe
  >
    <el-table-column
      property="id"
      label="操作序号"
      width="60"
    />
    <el-table-column
      property="event_id"
      label="事件 ID"
      width="85"
    />
    <el-table-column
      property="generate_type"
      label="日志类型"
      width="80"
    />
    <el-table-column
      property="name"
      label="操作人"
      width="80"
    />
    <el-table-column
      property="operation_module"
      label="功能模块"
      width="100"
    />
    <el-table-column
      property="operation_category"
      label="功能类别"
      width="100"
    />
    <el-table-column
      label="操作"
      width="120"
    >
      <template slot-scope="scope">
        <el-button
          v-if="scope.row.download_url"
          type="text"
          @click="handleDownloadUrl(scope.row.download_url, scope.row.job_id)"
        >
          {{ scope.row.operation }}
        </el-button>
        <div v-else> {{ scope.row.operation }}</div>
      </template>
    </el-table-column>
    <el-table-column
      property="comment"
      label="详细信息"
    />
    <el-table-column
      property="operation_time_p"
      label="操作时间"
      width="155"
    />
    <el-table-column
      property="ip_address"
      label="IP 地址"
      width="100"
    />
    <el-table-column
      property="agent_p"
      label="浏览器"
      width="100"
    />
  </el-table>
</template>

<script>
import API from '@/api'
import downloadFile from './downloadFile.vue'

export default {
  mixins: [downloadFile],
  props:      {
    tableData: {
      type:     Array,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
