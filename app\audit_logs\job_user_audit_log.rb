class JobUserAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '系统岗位管理'
    @operation_category = '岗位员工管理'
    super
  end

  def create
    @operation = '变更岗位员工'
    job = params[0]
    add_users = params[1]
    del_users = params[2]
    return unless add_users.present? || del_users.present?

    @comment = "变更了岗位员工：「#{job.name}」 编码：「#{job.code}」"
    @comment += " 新增员工：#{add_users.uniq.pluck(:name).join('、')}" if add_users.present?
    @comment += " 去掉员工：#{del_users.uniq.pluck(:name).join('、')}" if del_users.present?
    create_audit_log
  end
end
