<template>
  <span>
    <el-button
      :disabled="disabled"
      size="medium"
      @click="openDialog"
    >
      {{ buttonName }}
    </el-button>
    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-dialog
      :visible.sync="dialogVisible"
      title="账号历史变化"
      width="500px"
      append-to-body
    >
      <el-timeline v-loading="loading" v-infinite-scroll="loadMore" infinite-scroll-disabled="scrollDisabled">
        <history-task-status
          :quarterName="last_quarter"
          :status="history_task_status"
        />
        <div style="overflow:auto">
          <el-timeline-item
            v-for="(item, index) in historyDisplay"
            :key="index"
            :type="item.type"
            :icon="item.icon"
            :size="item.size"
            :timestamp="item.quarter"
          >
            <div v-if="item.importSuccessOrFail==='failed'">
              数据导入异常
            </div>
            <div v-else>
              <span>账号状态：{{ item.status }}</span>
              <span v-if="item.property_difference">
                <el-popover
                  placement="right"
                  title="名称变动"
                  :width="200"
                  trigger="hover"
                  :content="`变化前：${item.property_difference_values.from}，变化后：${item.property_difference_values.to}`"
                >
                  <template #reference>
                    <el-button class="m-2">名称变化</el-button>
                  </template>
                </el-popover>
              </span>
              <span v-else>
                <detail-popover
                  v-if="item.showDetailButton && item.lastQuarterId"
                  :systemId="systemId"
                  :accountId="item.accountId"
                  :otherQuarterId="item.lastQuarterId"
                  class="detail-button"
                />
              </span>
            </div>
          </el-timeline-item>
        </div>
        <el-divider v-if="currentPage != 1 && noMore">没有更多了</el-divider>
      </el-timeline>
      <comment-bar>
        * 注释：当节点图标显示为「<i class="el-icon-more" />」时，说明该节点与上一时间点对比，账号权限内容发生了变化。
      </comment-bar>

    </el-dialog>
    <!-- eslint-enable vue/attribute-hyphenation -->
  </span>
</template>

<script>
import DetailPopover from './DetailPopover.vue'
import CommentBar        from '@/components/common/CommentBar.vue'
import HistoryTaskStatus from '@/components/AccountHistory/HistoryTaskStatus'
import API from '@/api'
export default {
  components: {
    DetailPopover,
    CommentBar,
    HistoryTaskStatus
  },
  props: {
    systemId: {
      type: Number,
      required: true
    },
    accountId: {
      type: Number,
      required: true
    },
    buttonName: {
      type: String,
      default: '账号历史变化'
    }
  },
  data () {
    return {
      dialogVisible: false,
      account: {},
      history: [],
      loading: false,
      history_task_status: true,
      last_quarter: '',
      currentPage: 1,
      noMore: false
    }
  },
  computed: {
    disabled () {
      return !this.$store.getters.hasPermission('system_summary.account_history')
    },
    historyDisplay () {
      const statusDisplay = { enable: '正常', disable: '禁用', not_exist: '系统账号不存在' }
      const types = { enable: 'primary', disable: 'danger', not_exist: '' }
      const sizes = { enable: 'large', disable: 'large', not_exist: 'normal' }
      const icons = { true: 'el-icon-more', false: '', 'fail': 'el-icon-error' }
      return this.history.map(x => {
        if (x.status === null) {
          this.$message.error(`account (id: ${x.account_id}) status cannot be NULL`)
          x.status = true
        }
        return {
          quarter:       x.quarter,
          lastQuarterId: x.last_quarter_id,
          accountId:     x.account_id,
          status:        statusDisplay[x.status.toString()],
          type:          types[x.status.toString()],
          size:          sizes[x.status.toString()],
          icon:          icons[x.difference.toString()],
          showDetailButton: x.status !== 'not_exist',
          importSuccessOrFail: x.import_success_or_fail
        }
      })
    },
    scrollDisabled () {
      return this.loading || this.noMore
    }
  },
  methods: {
    getHistory () {
      this.loading = true
      this.history = []
      this.currentPage = 1
      this.noMore = false
      this.fetchData()
    },
    loadMore() {
      this.loading = true
      this.fetchData()
    },
    openDialog () {
      this.dialogVisible = true
      this.getHistory()
    },
    fetchData() {

      API.systemAccounts.history({system_id: this.systemId, account_id: this.accountId, page: this.currentPage})
        .then(response => {
          this.account.name = response.data.name
          this.history = this.history.concat(response.data.history)
          this.history_task_status = response.data.history_task_status
          this.last_quarter = response.data.last_quarter
          this.loading = false
          if(this.history.length == response.data.total_count) {
            this.noMore = true
          } else {
            this.currentPage = this.currentPage + 1
          }
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';

  .detail-button {
    margin-left: 6px;
    margin-top: -3px;
    height: 24px;
  }
</style>
