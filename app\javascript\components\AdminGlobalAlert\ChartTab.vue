<template>
  <div class="container">
    <transition name="el-zoom-in-top">
      <div
        v-show="!alertsShowFullScreen"
        class="cardLayout"
      >
        <div :class="{'chart-container': true}">
          <div class="total-pie-container">
            <total-pie-card
              :processed-count="processedCount"
              :all-alerts-count="allAlertsCount"
              :biggerStyle="alertCenterFlexStyle"
            />
          </div>
          <div
            class="alert-group-container"
          >
            <div
              v-for="(item, index) in alertGroups"
              style="margin-right: 20px;"
            >
              <alert-group-card
                :item="item"
                :current-type="currentType"
                :bigger-style="alertCenterFlexStyle"
                @changeCategory="changeCategory"
              />
            </div>
          </div>
          <div class="alert-group-container">
            <div
              v-for="(alerts, index) in noGroupAlerts"
              :key="index"
              :class="{'alerts-container': true, 'alerts-container-large': alertCenterFlexStyle}"
            >
              <div
                v-for="(alert, i) in alerts"
                :key="alert.type"
              >
                <alert-card
                  :alert="alert"
                  :is-active="currentType === alert.type"
                  :bigger-style="alertCenterFlexStyle"
                  @changeCategory="changeCategory"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
    <div class="table-container">
      <TableCard
        ref="tableCard"
        :tab-list="tabList"
        :maintain="maintain"
        :newTotal="total"
        :table-title="tableTitle"
        :system-select-list="systemSelectList"
        :diff-status-list="diffStatusList"
        :group-by-bs="groupByBs"
        :from-title="fromTitle"
        :date-val="dateVal"
        :disable-process="disableProcess"
        :error-message="errorMessage"
        :loading="getLoading"
        :search-schema="searchSchema"
        :table-schema="tableSchema"
        @detailId="detailId($event)"
        @statusIdVal="statusIdVal($event)"
        @fullScreen="handleFullScreen"
      />
    </div>

  </div>
</template>

<script>
import API from '@/api'
import TotalPieCard   from '@/components/AdminGlobalAlert/TotalPieCard.vue'
import TableCard      from './TableCard.vue'

import AlertGroupCard from '@/components/AdminGlobalAlert/AlertGroupCard.vue'
import AlertCard      from '@/components/AdminGlobalAlert/AlertCard.vue'

export default {
  components: {
    TotalPieCard,
    TableCard,
    AlertGroupCard,
    AlertCard
  },
  props:      {
    messageList:    {
      type:     Array,
      required: true
    },
    processedCount: {
      type:     Number,
      required: true
    },
    allAlertsCount: {
      type:     Number,
      required: true
    },
    dateVal:        {
      type:    Array,
      default: () => {
      }
    },
    errorMessage:   {
      type: String
      // default: () => {}
    }
  },
  data () {
    return {
      alertsShowFullScreen: false,
      currentType:          '',
      getLoading:           false,
      tabList:              [],
      tableTitle:           '',
      fromTitle:            '',
      systemSelectList:     [],
      diffStatusList:       [],
      disableProcess:       {},
      groupByBs:            [],
      searchSchema:         [],
      tableSchema:          [],
      total:                0,
      maintain:             false,
      alertCenterFlexStyle: this.$settings.alertCenterFlexStyle,
      statusFilter:         this.$store.state.global_alert_search_status,
      recoverStatusFilter:  this.$store.state.global_alert_search_recover_status
    }
  },
  computed: {
    noGroupAlerts () {
      const noGroup = this.messageList.find(x => x.group_name === '未分组')
      if (!noGroup) return []
      let category_alerts = this.$lodash.chunk(noGroup.category_alerts, 4)
      return category_alerts
    },
    alertGroups () {
      return this.messageList.filter(x => x.group_name !== '未分组')
    }
  },
  watch:    {
    messageList (newVal, old) {
      // 若已存在tableTile,直接刷新table的数据即可，不重新设置默认选中的告警分类
      if(this.tableTitle) {
        this.getAlertData(this.tableTitle)
        return
      }
      if (this.messageList.length === 0) return

      // const alertCategories = this.messageList[0].category_alerts
      const alerts = this.$lodash.flatten(this.messageList.map(x => x.category_alerts))
      if (alerts.length === 0) return

      this.changeCategory(this.getAlertCategory(alerts))
    },
  },
  created () {
  },
  methods: {
    detailId (e) {
      this.$emit('detailId', e)
    },
    statusIdVal (e) {
      this.$emit('statusIdVal', e)
    },
    getAlertData (alertType) {
      this.getLoading = true
      this.$axios.get(`admin_api/global_alerts/category/${alertType}?begin_time=${this.dateVal[0]}&end_time=${this.dateVal[1]}&deal_status=${this.statusFilter}&recover_status=${this.recoverStatusFilter}`)
        .then((res) => {
          // this.tabList          = res.data.entities
          this.searchSchema     = res.data.search_schema
          this.tableSchema      = res.data.table_schema
          this.maintain         = res.data.maintain
          this.disableProcess   = res.data
          this.systemSelectList = res.data.business_systems
          this.diffStatusList   = res.data.diff_status
          this.getLoading       = false
        })
        .catch((err) => {
          this.getLoading = false
          console.error(err)
        })
    },
    handleFullScreen (e) {
      this.alertsShowFullScreen = e
      this.$emit('fullScreen', e)
    },
    changeCategory (category) {
      this.tableTitle  = category.type
      this.fromTitle   = category.title
      this.total       = category.count
      this.currentType = category.type
      this.groupByBs   = category.group_by_bs
      this.$refs.tableCard.resetFilter()
      this.getAlertData(this.tableTitle)
    },
    // 获取告警类型
    getAlertCategory (alertCategories) {
      const paramCategory = this.$route.query.category
      let firstCategory
      // 如果category参数存在，通过参数赋值告警
      if (!API.tool.isBlank(paramCategory)) {
        firstCategory = alertCategories.filter(x => x.type === paramCategory)[0]
      }
      // 如果告警仍然为空，那就取第一个告警
      if (API.tool.isBlank(firstCategory)) {
        firstCategory = alertCategories[0]
      }
      return firstCategory
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/components/variables';

.container {
}

.chart-container {
  @include vertical_top_left_no_wrap;
  height:     420px;
  overflow-x: hidden;

  &:hover {
    overflow: auto;
  }

  .total-pie-container {
    padding: 0px 20px 0px 20px;
  }

  .alert-group-container {
    @include vertical_top_left_no_wrap;

    .alerts-container-large{
      width:   calc((100vw - 100px) / 3) !important;
    }
    .alerts-container {
      width:        340px;
      height:       400px;
      margin-right: 20px;
    }

    .group-card {
      margin-top: 0;
    }
  }

  .alert-no-group {

  }

}

.table-container {
  margin-top: 20px;
}


.boxFlex {
  display:         flex;
  justify-content: space-around;
  flex-wrap:       wrap;

  &:after {
    content: "";
    width:   45%;
  }
}


.group-card {
  width:         350px;
  margin-top:    20px;
  border-radius: 4px;
  padding:       15px;

  .group-name {
    font-size: 16px;
  }
}
</style>
