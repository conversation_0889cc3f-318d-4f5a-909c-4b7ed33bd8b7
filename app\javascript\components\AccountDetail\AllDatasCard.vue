<template>
  <el-card>
    <div
      v-if="showTitle"
      slot="header"
    >
      <all-datas-title
        :systemId="systemId"
        :accountId="accountId"
      />
    </div>
    <div v-if="isSingleDataSchema()">
      <base-table
        v-for="item in data_schema"
        v-loading="loading"
        :tableData="all_data[item.data_key]"
        :tableSchema="item.schema"
        filterable
        background
      />
    </div>
    <div v-else>
      <el-tabs
        v-loading="loading"
        v-model="activeName"
        type="border-card"
      >
        <el-tab-pane
          v-for="item in data_schema"
          :label="item.name"
          :name="item.data_key"
        >
          <base-table
            v-if="all_data[item.data_key]"
            :tableData="all_data[item.data_key]"
            :tableSchema="item.schema"
            :filterCustomMethod="item.filter_custom"
            filterable
            background
            border
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-card>
</template>

<script>
import BaseTableWithPagination from './TableWithPagination.vue'
import AllDatasTitle           from './AllDatasTitle.vue'
import API                     from '@/api'

export default {
  components: {
    AllDatasTitle,
    'base-table': BaseTableWithPagination
  },
  props:      {
    accountId: {
      type:     Number,
      required: true
    },
    systemId:  {
      type:     Number,
      required: true
    },
    showTitle: {
      type:    Boolean,
      default: true
    }
  },
  data () {
    return {
      all_data:    {},
      loading:     false,
      activeName:  '',
      data_schema: []
    }
  },
  watch: {
    account () {
      this.dataUpdate()
    }
  },
  created () {
    this.getSchema()
    this.dataUpdate()
  },
  methods: {
    dataUpdate (id) {
      this.loading = true
      API.systemAccounts.allDatas({ account_id: this.accountId, system_id: this.systemId })
        .then(response => {
          this.current_page = 1
          this.all_data     = response.data
          this.loading      = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    getSchema () {
      API.systems.accountSchema({ system_id: this.systemId })
        .then(response => {
          this.data_schema = response.data
          this.activeName  = this.data_schema[0].data_key
        })
        .catch(() => {})
    },
    // 判断返回的dataSchema是否是单个，用于展示
    isSingleDataSchema () {
      return this.data_schema.length == 1
    }
  }
}
</script>
