<template>
  <div v-loading="loading">
    <h3>部门设置（多选部门在临时授权中视作同一部门）</h3>
    <el-form
      ref="form"
      label-width="0px"
      :inline="true"
    >
      <el-form-item
        label=""
      >
        <div class="form-item-container">
          <department-select
            v-model="selectDepartments"
            multiple
          />
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          size="small"
          type="primary"
          @click="addOperatorDepartment()"
        >
          添加
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="departments"
      border
      style="width: 100%"
    >
      <el-table-column
        prop="names"
        label="部门"
      />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            @click="deleteDepartments(scope.row.names)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'
export default {
  components: {
    DepartmentSelect
  },
  data () {
    return {
      loading:          false,
      selectDepartments:    [],
      departments: []
    }
  },
  created () {
    this.getDepartments()
  },
  methods: {

    getDepartments () {
      this.loading = true
      this.$axios.get('/api/jiaoyi_temporary_permission/departments')
        .then(response => {
          this.departments = response.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    addOperatorDepartment () {
      if (!this.selectDepartments) return this.$message.error('请选择部门')
      const theParams = {
        department_ids: this.selectDepartments
      }
      this.$axios.post('/api/jiaoyi_temporary_permission/add_departments', theParams)
        .then(response => {
          this.$message.success('添加成功')
          this.getDepartments()
        })
        .catch(() => {
        })
    },
    deleteDepartments (departments) {
      const theParams = {
        departments: departments
      }
      this.$axios.post(`/api/jiaoyi_temporary_permission/delete_departments`, theParams)
        .then(response => {
          this.$message.success('删除成功')
          this.getDepartments()
        })
        .catch(() => {
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

.form-item-container {
  @include vertical_center_left;
  height: 40px;
}
</style>
