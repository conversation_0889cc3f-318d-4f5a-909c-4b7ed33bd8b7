<template>
  <div v-loading="loading">
    <el-table
      :data="tableDatas"
      style="width: 100%"
      @selection-change="selectAll"
      @sort-change="handleSortChange"
    >
      <el-table-column
        type="selection"
        width="55"
        :selectable="selectable"
      />
      <el-table-column
        v-for="schema in tableSchema"
        fixed
        :label="schema.label"
      >
        <template slot-scope="scope">
          <span v-if="schema.objtype == 'column'" :style="schema.style">{{ scope.row[schema.property] }}</span>
          <el-tag
            v-if="schema.objtype == 'account_status'"
            :type="scope.row[schema.property] == '正常' ? 'success' : 'primary'"
            disable-transitions
          >
            {{scope.row[schema.property]}}
          </el-tag>
          <span v-if="schema.objtype == 'data_json'" :style="schema.style">{{ scope.row.data_json[schema.property] }}</span>
          <span v-if="schema.objtype == 'permission'">
            <el-button type="text" @click="showPermissions(scope.row.data_json[schema.property])">查看详情</el-button>
          </span>
          <div v-if="schema.objtype == 'system_alignment'">
            <div v-show="scope.row.property === 'type_conflict'">
              <el-button
                slot="reference"
                type="text"
                class="unusual-button"
                @click="handleClickAbnormalButton('fundAbnormals', scope.row.fund_disclosure_o32_comparison_id, scope.row.exception_permissions.fund_types)"
              >
                {{ `基金类型冲突` }}
              </el-button>
            </div>
            <div v-show="scope.row.property === 'fund_abnormal'">
              <el-button
                slot="reference"
                type="text"
                class="unusual-button"
                @click="handleClickAbnormalButton('fundAbnormals', scope.row.fund_disclosure_o32_comparison_id, scope.row.exception_permissions.funds)"
              >
                {{ `基金异常` }}
              </el-button>
            </div>
            <div v-show="scope.row.property === 'role_abnormal'">
              <el-button
                slot="reference"
                type="text"
                class="unusual-button"
                @click="handleClickAbnormalButton('roleAbnormals', scope.row.fund_disclosure_o32_comparison_id, scope.row.exception_permissions.roles)"
              >
                {{ `角色异常` }}
              </el-button>
            </div>
            <div v-show="scope.row.property === 'date_abnormal'">
              <el-button
                slot="reference"
                type="text"
                class="unusual-button"
                @click="handleClickAbnormalButton('dateAbnormals', scope.row.fund_disclosure_o32_comparison_id, scope.row.exception_permissions.dates)"
              >
                {{ `期限匹配异常` }}
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="deal_status"
        label="处理状态"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.deal_status == 1 ? 'success' : 'primary'"
            disable-transitions
          >
            {{ scope.row.deal_status==1 ? '已处理' : '未处理' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="recover_status"
        label="恢复状态"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.recover_status == 1 ? 'success' : 'primary'"
            disable-transitions
          >
            {{ scope.row.recover_status==1 ? '已恢复' : '未恢复' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="enable"
        label="是否屏蔽告警"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.enable == true ? 'primary' : 'success'"
            disable-transitions
          >
            {{ scope.row.enable == 1 ? '否' : '是' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="created_at"
        label="首次告警时间"
        sortable="custom"
      />
      <el-table-column
        label="最新告警时间"
        prop="quarter_created_at"
        sortable="custom"
      >
        <template
          slot-scope="scope"
        >
          {{ scope.row.last_quarter_mes==null ? '-' : scope.row.last_quarter_mes.created_at }}
        </template>
      </el-table-column>
      <el-table-column
        prop="enable_at"
        label="解除屏蔽时间"
        width="120"
      >
        <template slot-scope="scope">
          {{ scope.row.enable_at==null ? '-' : scope.row.enable_at }}
        </template>
      </el-table-column>
      <el-table-column
        prop="over_deal"
        label="是否超出处理时限"
      >
      </el-table-column>
      <el-table-column
        prop="deal_at_latest"
        label="最晚处理时限"
      >
      </el-table-column>
      <el-table-column
        label="操作"
        prop="status"
      >
        <template slot-scope="scope">
          <el-button
            size="small"
            @click="handleEdit(scope.row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-drawer
      v-loading="drawerLoading"
      :title="`${fromTitle}:${fromData.account_name || ''}`"
      :visible.sync="drawerval"
      :direction="direction"
      destroy-on-close
      size="40%"
    >
      <el-descriptions
        :column="1"
        style="margin: 0 20px 20px 20px;"
        border
      >
        <el-descriptions-item
          v-for="schema in fromData.custom_alert.content_schema"
          v-if="fromData.custom_alert"
        >
          <template slot="label">
            {{ schema.label }}
          </template>
          <span v-if="schema.objtype == 'column'" :style="schema.style">{{ getProperty(fromData, schema) || '-' }}</span>
          <span v-if="schema.objtype == 'data_json'" :style="schema.style">{{ fromData.data_json[schema.property] }}</span>
          <span v-if="schema.objtype == 'permissions'">
            <el-button type="text" @click="showPermissions(fromData.data_json[schema.property])">查看详情</el-button>
          </span>
          <span v-if="schema.objtype == 'detail_popover'">
            <detail-popover
              button-size="normal"
              :diff-mode="schema.datas['diff_mode'] || 'compareHistory'"
              :system-id="fromData[schema.datas['system_id']]"
              :account-id="fromData[schema.datas['account_id']]"
              :other-quarter-id="fromData[schema.datas['other_quarter_id']]"
              :default-data-key="schema.datas['default_data_key']"
            />
          </span>
          <span
            v-if="schema.objtype == 'fund_permission_status'"
            :class="status_class(fromData.fund_permission_abnormal_status)"
          >
            {{ fromData.fund_permission_abnormal_status || '-' }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            处理状态
          </template>
          {{ fromData.deal_status == 1 ? "已处理" : "未处理" }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            恢复状态
          </template>
          {{ fromData.recover_status == 1 ? "已恢复" : "未恢复" }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            是否屏蔽告警
          </template>
          {{ fromData.enable == false ? "否" : "是" }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            告警发送时间
          </template>
          {{ fromData.created_at || '-' }}
        </el-descriptions-item>
      </el-descriptions>
      <hr>
      <froms
        :from-datas="fromDatas"
        :maintain="maintain"
        @detailId="detailId"
        @drawervals="drawervals"
        @handleEdit="handleEdit"
      />
      <hr>
      <message
        :table-data-details="tableDataDetails"
      />
    </el-drawer>

    <fund-abnormals
      ref="fundAbnormals"
      :permissions="abnormals.fundAbnormals"
      @commitAlert="dataUpdate"
    />
    <date-abnormals
      ref="dateAbnormals"
      :permissions="abnormals.dateAbnormals"
      @commitAlert="dataUpdate"
    />
    <role-abnormals-mutexable
      v-if="rolesCheckMode === 'mutexable'"
      ref="roleAbnormals"
      :permissions="abnormals.roleAbnormals"
    />
    <role-abnormals-sameable
      v-if="rolesCheckMode === 'sameable'"
      ref="roleAbnormals"
      :permissions="abnormals.roleAbnormals"
    />

    <el-dialog
      title="权限详情"
      :visible.sync="permissionDialog"
      width="60%"
    >
      <el-tabs
        v-model="permissionActive"
        type="border-card"
      >
        <el-tab-pane
          v-for="item in permissionSchema"
          :label="item.name"
          :name="item.data_key"
        >
          <base-table
            :table-data="permissionData[item.data_key]"
            :table-schema="item.schema"
            :filter-custom-method="item.filter_custom"
            filterable
            background
            border
          />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import DetailPopover    from '@/components/AccountHistory/DetailPopover.vue'
import BaseTableWithPagination from '@/components/AccountDetail/TableWithPagination.vue'
import message from './message.vue'
import froms from './froms.vue'
import FundAbnormals          from '@/components/SystemAlignment/FundAbnormals'
import DateAbnormals          from '@/components/SystemAlignment/DateAbnormals'
import RoleAbnormalsMutexable from '@/components/SystemAlignment/RoleAbnormalsMutexable'
import RoleAbnormalsSameable  from '@/components/SystemAlignment/RoleAbnormalsSameable'

export default {
  components: {
    message,
    froms,
    DetailPopover,
    'base-table': BaseTableWithPagination,
    FundAbnormals,
    DateAbnormals,
    RoleAbnormalsMutexable,
    RoleAbnormalsSameable
  },
  props: {
    tableData: {
      type: Array,
      //   required: true
      default: () => {}
    },
    fromTitle: {
      type: String,
      required: true
    },
    maintain:   {
      type:     Boolean,
      default:  false
    },
    loading:  {
      type:     Boolean,
      default:  false
    },
    tableSchema:  {
      type:     Array,
      default: () => {}
    }
  },
  computed: {
    rolesCheckMode () {
      return this.$settings.systemAlignment.roles_check_mode
    }
  },
  data () {
    return {
      tableDatas: this.tableData,
      newtableData: [],
      tableDataDetails: [],
      gridData: [],
      activeId: [],
      drawerval: false,
      innerVisible: false,
      drawerLoading: false,
      direction: 'rtl',
      fromDatas: {},
      fromData: {
        account_name: '',
        account_code: '',
        sub_bs_name: '',
        deal_status: '',
        updated_at: '',
        enable: '',
        enable_at: '',
        prev_quarter_id: 0,
        bs_id: 0,
        account_id: 0,
        comment: '',
        column_1: '',
        column_2: '',
        column_3: '',
        column_4: '',
        column_5: '',
        column_6: '',
        column_7: '',
        column_8: '',
        column_9: '',
        column_10: '',
        custom_alert: {
          content_schema: [],
          table_schema: []
        }
      },
      filterItems:     [
        'status',        // 处理状态
        'recoverStatus', // 恢复状态
        'systemName',
      ],
      messageType: [],
      BaselineMismatch: '',
      statusFilter: '',
      tabPane: '',
      enable: '',
      tableTitlaActive: '',
      statusValue: false,
      permissionActive: '',
      permissionData: {},
      permissionSchema: [],
      permissionDialog: false,
      rules: {
        enable_at: [
          {  required: true, message: '请选择日期', trigger: 'change' }
        ]
      },
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now()
        }
      },
      abnormals:       {
        fundAbnormals: [],
        roleAbnormals: [],
        dateAbnormals: []
      },
      sortProps: {}
    }
  },
  watch: {
    tableData (newVal, oldVal) {
      this.tableDatas = newVal
    },
    detailId (e) { },
    drawervals (e) {}
  },
  created () {
    this.$emit('handleFilterItems', this.filterItems)
  },
  methods: {
    getProperty (fromData, schema) {
      const property = fromData[schema.property]
      if (['system_alignment_diff'].indexOf(fromData.category_code) >= 0 && schema.property === 'remark') {
        return this.$t(`aas.system_alignments.abnormals.${property}`)
      } else {
        return property
      }
    },
    showPermissions(item) {
      this.permissionActive = item.schema[0].data_key
      this.permissionData = item.data
      this.permissionSchema = item.schema
      this.permissionDialog = true
    },
    selectable (row, index) {
      if (row.deal_status === 1) {
        return false
      } else {
        return true
      }
    },
    handleEdit (e) {
      this.drawerval = true
      this.drawerLoading = true
      this.$axios.get(`admin_api/global_alerts/${e.id}`).then((res) => {
        this.fromData = res.data
        this.fromData.deal_status = res.data.deal_status === false ? 0 : this.fromData.deal_status
        this.fromData.deal_status = res.data.deal_status !== 0
        // this.statusValue = this.fromData.deal_status
        this.fromDatas = this.fromData
        this.tableDataDetails = res.data.quarter_and_msg.reverse()
        this.drawerLoading = false
      }).catch((err) => {
        this.drawerLoading = false
        console.log(err)
      })
    },
    item (index, row) {
      if (row.deal_status === 0) {
        return false
      } else {
        return true
      }
    },
    detailId (e) {
      this.$emit('detailId', e)
    },
    drawervals (e) {
      this.drawerval = false
    },
    selectAll (e) {
      this.$emit('activeId', e)
    },
    handleClickAbnormalButton (dialog, recordId, abnormals) {
      this.closeAllDialogs()
      this.abnormals[dialog]           = abnormals
      this.recordId                    = recordId
      this.$refs[dialog].dialogVisible = true
      this.$refs[dialog].recordId      = recordId
    },
    closeAllDialogs () {
      this.$refs.fundAbnormals.dialogVisible = false
      this.$refs.roleAbnormals.dialogVisible = false
      this.$refs.dateAbnormals.dialogVisible = false
    },
    dataUpdate () {
      this.drawerLoading  = true
      const theParams = {
        quarter_id: this.fromData.quarter_id,
        query_user: this.fromData.user_name
      }
      API.systemAlignments.index(theParams)
        .then(response => {
          this.users     = response.data.users
          this.users[0].abnormal_statuses = [this.fromData.remark]
          this.count     = response.data.count
          this.all_roles = response.data.all_roles
          this.drawerLoading   = false
        })
        .catch(() => {
          this.drawerLoading = false
        })
    },
    status_class (status) {
      if (status === '正常') return []
      return ['unnormal_status']
    },
    handleSortChange(sortProps) {
      this.$emit("sort", sortProps)
    }
  }
}
</script>
<style lang="scss" scoped>
  .unnormal_status {
    color: #E54D42;
    font-weight: bold;
  }
</style>

