import axios from '@/settings/axios'

export const list = (dataCenterGroupId) => {
  return axios.get(`/api/data_center_groups/${dataCenterGroupId}/data_centers`)
}

export const create = (dataCenterGroupId, params) => {
  return axios.post(`/api/data_center_groups/${dataCenterGroupId}/data_centers`, params)
}

export const update = (dataCenterGroupId, dataCenterId, params) => {
  return axios.put(`/api/data_center_groups/${dataCenterGroupId}/data_centers/${dataCenterId}`, params)
}

export const destroy = (dataCenterGroupId, id) => {
  return axios.delete(`/api/data_center_groups/${dataCenterGroupId}/data_centers/${id}`)
}

export const detail = (dataCenterGroupId, dataCenterId) => {
  return axios.get(`/api/data_center_groups/${dataCenterGroupId}/data_centers/${dataCenterId}`)
}