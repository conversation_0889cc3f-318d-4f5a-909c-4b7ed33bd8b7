import axios from '@/settings/axios'

export const list = (params) => {
  return axios.get('api/public_account_types/', { params: params })
}

export const create = (params) => {
  return axios.post('api/public_account_types/', params )
}

export const update = (params) => {
  return axios.put(`api/public_account_types/${params.id}`, params )
}

export const destroy = (params) => {
  return axios.delete(`api/public_account_types/${params.id}`)
}
