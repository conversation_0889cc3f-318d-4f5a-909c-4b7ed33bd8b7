<template>
  <el-dialog
    :visible.sync="visible"
    :title="`${modeTitle}基线`"
    width="960px"
    @open="handleOpen"
    @close="handleClose"
    :before-close="handleBeforeClose"
    append-to-body
  >
    <div class="dialog-container">
      <!-- eslint-disable vue/attribute-hyphenation -->
      <el-form
        ref="localForm"
        :model="localBaseline"
        label-width="100px"
      >
        <el-form-item
          :rules="[{ required: true, message: '名称不能为空', trigger: 'blur' }]"
          prop="name"
          label="基线名称"
        >
          <el-input
            style="width: 660px"
            size="small"
            v-model="localBaseline.name" />
        </el-form-item>

        <el-form-item
          v-if="mode=='create'"
          prop="source_id"
          label="权限复制"
        >
          <el-select
            v-model="localBaseline.source_type"
            size="small"
            style="width: 180px"
          >
            <el-option
              value="baseline"
              label="系统基线"
            />
            <el-option
              value="account"
              label="账号权限"
            />
          </el-select>

          <el-select
            v-if="localBaseline.source_type === 'baseline'"
            v-model="localBaseline.source_id"
            :loading="loading"
            filterable
            size="small"
            style="width: 476px"
            @visible-change="loadingRemoteData"
          >
            <el-option
              v-for="item in baselines"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            />
          </el-select>
          <el-select
            v-if="localBaseline.source_type === 'account'"
            v-model="localBaseline.source_id"
            :loading="loading"
            filterable
            size="small"
            style="width: 240px"
            @visible-change="loadingRemoteData"
          >
            <el-option
              v-for="item in accounts"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <department-select
            v-model="localBaseline.department_id"
            :all-users="true"
            placeholder="选择部门"
            :checkStrictly="false"
            selectStyle="width: 660px"
            size="small"
            collapseTags
        />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
              type="textarea"
              v-model="localBaseline.comment"
              :autosize="{ minRows: 4, maxRows: 6 }"
              style="width: 660px;"
          />
        </el-form-item>
        <el-form-item
          label="告警条件"
        >
          <el-radio-group
            v-model="localBaseline.compare_rule"
          >
            <el-radio label="all_contrast">权限不一致</el-radio>
            <el-radio label="only_add">超出基线范围</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="localBaseline.source_type === 'account'"
          prop="linked"
          label=""
        >
          <el-checkbox v-model="localBaseline.linked">
            是否将账号与系统基线进行关联
          </el-checkbox>
        </el-form-item>


        <el-form-item
          v-show="localBaseline.source_id"
          v-if="!isSingleSchemaData()"
          label="告警维度"
        >
          <span
            v-for="(data, index) in localBaseline.output_schema_data"
          >
            <el-checkbox
              v-model="localBaseline.output_schema_data[index].is_notice"
              :label="data.name"
              :key="data.data_key"
              style="margin-right: 20px;"
            >
              {{data.name}}
            </el-checkbox>
          </span>
        </el-form-item>
        <el-form-item
            v-if="localBaseline.is_merge"
            label="来源基线"
        >
          <el-tag
              v-for="item in localBaseline.origin_baselines"
              :key="item.id"
              closable
              size="medium"
              effect="plain"
              style="margin-right: 10px;"
              @close="handleRemoveBaseline(item)"
          >
            {{item.name}}
          </el-tag>
        </el-form-item>
        <el-form-item
          v-show="localBaseline.source_id"
          label="权限内容"
        >
          <baseline-permissions-preview
            ref="preview"
            :mode="mode"
            :systemId="systemId"
            :sourceType="localBaseline.source_type"
            :sourceId="localBaseline.source_id"
            :baseline="baseline"
            :outputSchemaData="localBaseline.output_schema_data"
            @setPermissionChange="setPermissionChange"
            @setPermissionSchemaChange="setPermissionSchemaChange"
          />
        </el-form-item>

      </el-form>
      <comment-bar>
        注意: 如选择部门为非自己管理权限的部门，该基线将无法查看
      </comment-bar>
      <!-- eslint-enable vue/attribute-hyphenation -->
    </div>

    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="visible = false">关 闭</el-button>
      <el-button
        type="primary"
        @click="handleComfirm"
      >
        确 定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import BaselinePermissionsPreview from './BaselinePermissionsPreview.vue'
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'
import CommentBar               from '@/components/common/CommentBar.vue'
import API from '@/api'
export default {
  components: {
    BaselinePermissionsPreview,
    DepartmentSelect,
    CommentBar
  },
  props:      {
    systemId: {
      type:     Number,
      required: true
    },
    baseline: {
      type:    Object,
      default: () => {}
    },
    mode:     {
      type:    String,
      default: 'create'
    }
  },
  data () {
    return {
      visible:       false,
      loading:       false,
      accounts:      [],
      baselines:     [],
      permissionChanged: false,
      localBaseline: {
        id:          null,
        name:        null,
        source_type: 'baseline',
        source_id:   null,
        linked:      false,
        output_schema: [],
        output_schema_data: [],
        compare_rule:'all_contrast',
        department_id: '',
        comment: '',
        origin_baselines: [],
        is_merge:   false,
      }
    }
  },
  computed:   {
    modeTitle () {
      if (this.mode === 'create') return '创建'
      if (this.mode === 'update') return '修改'
    }
  },
  watch:      {
    'localBaseline.source_type' () {
      this.localBaseline.source_id = null
      this.localBaseline.linked    = false
    },
    systemId: {
      immediate: true,
      handler () {
        this.accounts = []
        this.baselines = []
      }
    }
  },
  methods:    {
    handleOpen () {
      if (this.mode === 'update') {
        this.localBaseline.source_type   = 'baseline'
        this.localBaseline.id            = this.baseline.id
        this.localBaseline.name          = this.baseline.name
        this.localBaseline.comment       = this.baseline.comment
        this.localBaseline.source_id     = this.baseline.id
        this.localBaseline.compare_rule  = this.baseline.compare_rule
        this.localBaseline.department_id = this.baseline.department_id
        this.localBaseline.output_schema_data = this.baseline.output_schema_data
        this.localBaseline.origin_baselines  = this.baseline.origin_baselines
        this.localBaseline.is_merge    = this.baseline.is_merge
        this.localBaseline.select_output_schema_data = this.filterOutputSchemaData(this.baseline.output_schema_data)
        this.$nextTick(() => {
          this.loadingRemoteData(true)
          this.$refs.preview.getPermissions()
        })
      }else if (this.mode === 'create'){
        this.getSystemSettings()
      }
    },
    filterOutputSchemaData(output_schema_data){
      return output_schema_data.filter(function(value, index, array){
        return value.is_notice;
      }).map(function(obj){
        return obj.name
      })
    },
    handleBeforeClose(done) {
      if(this.permissionChanged) {
        this.$confirm('未提交的基线修改将不会保存，是否继续?', '提示', {
          confirmButtonText: '确认',
          cancelButtonText:  '取消',
          type:              'warning'
        })
            .then(() => {
              done()
            })
            .catch(() => {
            })
      } else {
        done()
      }
    },
    handleClose(){
      // 关闭系统基线编辑框的时候要重新设置source_type为baseline
      // 因为当source_type为account的时候，下次打开会导致获取权限时的api报错误(source_id为空)
      this.localBaseline.source_type = 'baseline'
    },
    loadingRemoteData (openOrClose) {
      if (openOrClose &&
        this.localBaseline.source_type === 'baseline' &&
        this.baselines.length === 0
      ) {
        this.getBaselines()
      }
      if (openOrClose &&
        this.localBaseline.source_type === 'account' &&
        this.accounts.length === 0
      ) {
        this.getAccounts()
      }
    },
    getBaselines () {
      this.loading = true
      this.$axios.get(`/api/systems/${this.systemId}/baselines/all`)
        .then(response => {
          this.loading   = false
          this.baselines = response.data
        })
        .catch(() => {
          this.loading = false
        })
    },
    getAccounts () {
      this.loading = true
      this.$axios.get(`/api/systems/${this.systemId}/accounts_with_baseline/all`)
        .then(response => {
          this.loading  = false
          this.accounts = response.data.accounts
        })
        .catch(() => {
          this.loading = false
        })
    },
    updateBaseline () {
      let method = 'put'
      let url    = `/api/systems/${this.systemId}/baselines/${this.localBaseline.id}`

      if (this.mode === 'create') {
        method = 'post'
        url    = `/api/systems/${this.systemId}/baselines`
      }
      this.$axios({
        method: method,
        url:    url,
        data:   { baseline: this.localBaseline, permission_data: JSON.stringify(this.$refs.preview.permissionData) }
      })
        .then(response => {
          this.$emit('change')
          this.visible = false
          this.$refs.localForm.resetFields()
          this.permissionChanged = false
          this.$message.success(`${this.modeTitle}系统基线成功`)
        })
        .catch(() => {})
    },
    updateBaselineConfirm () {
      if (this.mode === 'create') {
        this.updateBaseline()
        return
      }

      this.$confirm('确认更新该系统基线吗？修改的系统基线会反馈到所有已关联的账号上', '提示', {
        confirmButtonText: '确定',
        cancelButtonText:  '取消',
        type:              'warning'
      })
        .then(() => {
          this.updateBaseline()
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    handleComfirm () {
      this.$refs.localForm.validate((valid) => {
        if (valid) {
          const data = this.localBaseline.output_schema_data.filter (x => x.is_notice)
          if (data.length === 0) {
            this.$message.error('请至少选择一个告警维度')
            return false
          }
          this.updateBaselineConfirm()
        } else {
          this.$message.error('请检查必填项')
          return false
        }
      })
      this.$axios.get(`/api/systems/${this.systemId}/baselines/all`)
        .then(response => {
          this.loading   = false
          this.baselines = response.data
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 点击添加系统基线按钮初始化维度通知选项
    getSystemSettings(){
      this.$axios.get(`/api/systems/${this.systemId}/settings`, { params: { with_role: 'true' } })
        .then(response => {
          let output_schema_data  = response.data.output_schema_data
          this.localBaseline.output_schema_data = output_schema_data
        })
        .catch(() => {})
    },
    isSingleSchemaData(){
      return this.localBaseline.output_schema_data.length == 1
    },
    setPermissionChange() {
      this.permissionChanged = true
    },
    handleRemoveBaseline(data) {
      this.$confirm('移除来源基线会更新当前基线，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.removeBaseline(data)
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    removeBaseline(data) {
      let theParams = {
        baseline_id: this.localBaseline.id,
        origin_baseline_id: data.id
      }
      this.loading = true
      API.systemBaselines.removeBaseline(this.systemId, theParams)
        .then(response => {
          this.loading = false
          if(response.data.success) {
            this.localBaseline.origin_baselines = response.data.origin_baselines
            this.$nextTick(() => {
              this.loadingRemoteData(true)
              this.$refs.preview.getPermissions()
            })
            this.$message.success('移除基线成功')
          } else {
            this.$message.error(response.data.message)
          }
        })
        .catch(error => {
          this.loading = false
          console.log(error)
        })
    },
    setPermissionSchemaChange (payload) {
      this.localBaseline.output_schema = payload
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/components/variables";

</style>
