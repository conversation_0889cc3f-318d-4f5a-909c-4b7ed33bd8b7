<template>
  <div>
    <el-upload
      ref="upload"
      :headers="headers"
      :action="`/admin_api/global_alerts/${alert.id}/attachments`"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-preview="handleDownload"
      :file-list="alert.attachments"
      :showFileList="true"
      class="upload-demo"
    >
      <el-button
        size="small"
        type="text"
        :disabled="!maintain"
      >
        上传附件
      </el-button>
    </el-upload>
  </div>
</template>
<script>
import API from '@/api'

export default {
  props: {
    alert: {
      type: Object,
      required: true
    },
    maintain:  {
      type:     Boolean,
      default:  false
    }
  },
  data () {
    const headers           = API.tool.getToken()
    return {
      headers: headers,
    }
  },
  methods: {
    handleSuccess (_response, _file, _fileList) {
      this.$message.success('文件上传成功!')
      this.$emit('updateFromData')
    },
    handleError (error) {
      console.log(error)
    },
    handleRemove (file) {
      const theParam = {
        alertId:      this.alert.id,
        attachmentId: file.id
      }
      API.globalAlerts.handleRemove(theParam)
        .then(() => {
          this.$emit('updateFromData')
        })
        .catch((err) => {
          console.log(err)
        })
    },
    handleDownload (file) {
      const theParam = {
        alertId:      this.alert.id,
        attachmentId: file.id
      }
      API.globalAlerts.handleDownload(theParam)
        .then(() => {})
        .catch(error => {
          error.data.text().then((res) => {
            this.$message.error(JSON.parse(res).error_message)
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
