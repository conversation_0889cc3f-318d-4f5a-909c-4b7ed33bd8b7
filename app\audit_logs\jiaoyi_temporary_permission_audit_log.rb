# frozen_string_literal: true

# 后台账号管理审计日志
class JiaoyiTemporaryPermissionAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '系统设置'
    super
  end

  def add_operator_account
    jiaoyi_category
    @operation = '投研中台账号添加'
    @comment   = "添加账号 #{params.name} (#{params.code})"
    create_audit_log
  end

  def delete_operator_account
    jiaoyi_category
    @operation = '投研中台账号移除'
    @comment   = "移除账号 #{params.name} (#{params.code})"
    create_audit_log
  end

  def add_date_check_account
    jiaoyi_category
    @operation = '临时授权超30天添加配置'
    @comment   = "配置信息 #{params}"
    create_audit_log
  end

  def delete_date_check_account
    jiaoyi_category
    @operation = '临时授权超30天移除配置'
    @comment   = "配置信息：#{params}"
    create_audit_log
  end

  def add_departments
    jiaoyi_category
    @operation = '部门配置'
    @comment   = "添加部门"
    create_audit_log
  end

  def delete_departments
    jiaoyi_category
    @operation = '部门配置'
    @comment   = "移除部门"
    create_audit_log
  end

  def upload_authorizer_excel
    jiaoyi_category
    @operation = '可授权人员名单上传'
    @comment   = "成功上传可授权人员名单，文件名称：#{params.original_filename}"
    create_audit_log
  end

  def upload_authorizer_excel_fail
    jiaoyi_category
    @operation = '可授权人员名单上传'
    @comment   = "上传可授权人员名单失败，文件名称：#{params[0].original_filename}，错误信息: #{params[1]}"
    create_audit_log
  end

  def add_fund_type_ignore_account
    jiaoyi_category
    @operation = '同时包含公募与专户产品账号忽略列表添加'
    @comment   = "添加账号 #{params.name} (#{params.code})"
    create_audit_log
  end

  def del_fund_type_ignore_account
    jiaoyi_category
    @operation = '同时包含公募与专户产品账号忽略列表移除'
    @comment   = "移除账号 #{params.name} (#{params.code})"
    create_audit_log
  end

  def download_authorizer_excel_success
    jiaoyi_category
    @operation = '可授权人员名单导出'
    @comment   = "「可授权人员名单」下载成功"
    create_audit_log
  end

  def download_authorizer_excel_faild
    jiaoyi_category
    @operation = '可授权人员名单导出'
    @comment   = "「可授权人员名单」下载失败"
    create_audit_log
  end

  def export_temporary_excel_success
    jiaoyi_category
    @operation = '投资交易系统临时授权导出'
    @comment   = "成功导出 「#{params[:start_date]} -- #{params[:stop_date]}」的「投资交易系统临时授权」数据"
    create_audit_log
  end

  def export_temporary_excel_faild
    jiaoyi_category
    @operation = '投资交易系统临时授权导出'
    @comment   = "导出 「#{params[:start_date]} -- #{params[:stop_date]}」的「投资交易系统临时授权」数据失败"
    create_audit_log
  end

  private

  def jiaoyi_category
    @operation_category = '投资交易系统临时授权功能设置'
  end
end
