# frozen_string_literal: true

# 系统设置审计日志
class AdminSettingAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '系统设置'
    super
  end

  def update_license_success
    @operation_category = '系统授权设置'
    @operation = '产品授权信息更新'
    @comment   = '成功更新了产品授权信息'
    create_audit_log
  end

  def update_license_failed
    @operation_category = '系统授权设置'
    @operation = '产品授权信息更新'
    @comment   = '更新产品授权信息失败'
    create_audit_log
  end

  def update_general_settings_success
    @operation_category = '通用设置'
    @operation = '通用设置更新'
    @comment   = "更新了通用设置为 #{params}"
    create_audit_log
  end

  def update_general_settings_failed
    @operation_category = '通用设置'
    @operation = '通用设置更新'
    @comment   = '更新了通用设置失败!'
    create_audit_log
  end

  def update_notification_settings
    @operation_category = '通知设置'
    @operation = '通知设置更新'
    @comment   = "更新了通知设置为 #{params}"
    create_audit_log
  end

  def update_leave_user_serious_notification
    @operation_category = '离职员工账号关闭告警升级设置'
    @operation = '离职员工账号关闭告警升级设置更新'
    @comment   = "更新了离职员工账号关闭告警升级设置为 #{params}"
    create_audit_log
  end

  def update_quarter_auto_clear
    @operation_category = '数据定期清理'
    @operation = '设置清理时间'
    @comment   = "更新了数据清理时间，#{params[0]}，#{params[1]}"
    create_audit_log
  end

  # 更新「重置密码通知设置」设置
  def update_send_reset_password
    @operation_category = '重置密码通知设置'
    @operation = '重置密码通知更新'
    @comment   = "更新了「重置密码通知」设置为 #{params}"
    create_audit_log
  end
end
