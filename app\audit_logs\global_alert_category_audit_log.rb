# frozen_string_literal: true

# 告警分组管理审计日志
class GlobalAlertCategoryAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '告警分组管理'
    super
  end

  def update
    set_operation_category
    @operation = '告警更新'
    @comment   = "更新#{params[0]}, 分组为：#{params[1]}"
    @comment += "，顺序号为：#{params[2]}" if params[2].present?
    @comment += "，最大处理时限为：#{params[3]}" if params[3].present?
    @comment += "，提前告警天数: #{params[4][:advance_days]}" if params[4][:advance_days].present?
    @comment += "，延迟告警天数: #{params[4][:delay_days]}" if params[4][:delay_days].present?
    setting_str = setting_text(params[5])
    @comment += "，设置: #{setting_str}" if setting_str.present?
    create_audit_log
  end

  def disable
    set_operation_category
    @operation = '告警更新'
    @comment   = "更新#{params[0]}, 是否显示：#{params[1]}"
    create_audit_log
  end

  def group_create
    set_operation_category
    @operation = '创建告警分组'
    @comment   = "创建 分组：#{params[0]}; 告警分类：#{params[1].join(', ')}"
    create_audit_log
  end

  def group_update
    set_operation_category
    @operation = '更新告警分组'
    @comment   = "更新 分组：#{params[0]}; 告警分类：#{params[1].join(', ')}"
    create_audit_log
  end

  def group_destroy
    set_operation_category
    @operation = '删除告警分组'
    @comment   = "删除 分组：#{params[0]}"
    create_audit_log
  end

  private

  def set_operation_category
    @operation_category = '告警分组管理'
  end

  def setting_text(global_alert_category)
    return unless global_alert_category && global_alert_category.settings.present?

    settings = global_alert_category.settings
    case global_alert_category.code
    when 'unusing_account'
      "超时告警阈值 #{UnusingAccountAlert.setting_text}"
    when 'business_account_has_admin_role'
      all_bs = BusinessSystem.all
      settings.select { |x| x[:role_codes].present? }.map do |x|
        bs = all_bs.find { |bs| bs.id == x[:system_id] }
        next if bs.nil?

        is_code = bs.role_class.column_names.include?('code')
        options = is_code ? { code: x[:role_codes] } : { name: x[:role_codes] }
        roles = bs.role_class.where(options.merge(quarter_id: Quarter.last&.id))
        next if roles.blank?

        role_name = roles.pluck(:name).map { |name| "「#{name}」" }.join('')
        "#{bs.name} 管理员角色 #{role_name}"
      end.compact.join('，')
    end
  end
end
