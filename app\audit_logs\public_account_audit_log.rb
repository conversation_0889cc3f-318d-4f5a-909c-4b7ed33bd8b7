# frozen_string_literal: true

# 功能账号日志
class PublicAccountAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '功能账号管理'
    super
  end

  def create
    @operation_category = '功能账号管理'
    @operation = '创建功能账号'
    @comment   =  "成功创建功能账号「#{params}」"
    create_audit_log
  end

  def update
    @operation_category = '功能账号管理'
    @operation = '修改功能账号'
    @comment   = "成功修改功能账号「#{params}」"
    create_audit_log
  end

  def destroy
    @operation_category = '功能账号管理'
    @operation = '删除功能账号'
    @comment   = "成功删除功能账号「#{params}」"
    create_audit_log
  end
end