<template>
  <span>
    <span v-show="status === 'not_start'" >
      <i class="el-icon-time" />
      尚未执行
    </span>
    <span v-show="status === 'running'">
      <i class="el-icon-loading" />
      正在执行
    </span>
    <span v-show="status === 'success'" >
      <i class="el-icon-success" />
      执行成功
    </span>
    <span v-show="status === 'failed'">
      <i class="el-icon-error" />
      执行失败
    </span>
  </span>
</template>

<script>
export default {
  props: {
    status: {
      type:     String,
      required: true
    }
  }
}
</script>

<style scoped>
  @import './status.scss';
</style>
