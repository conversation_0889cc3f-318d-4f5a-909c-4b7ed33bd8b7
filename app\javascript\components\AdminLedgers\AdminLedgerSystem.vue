<template>
  <div
    v-loading="loading"
    class="component-container"
  >
    <div class="toolbar">
      <div class="left">
        <search-box
          :systemId="systemId"
          @change="handleSearchChange"
        />
      </div>
      <div class="right">
        <div class="toolbarRight">
          <div class="left">
            <el-button
              type="primary"
              size="small"
              @click="handleBatch"
            >
              批量关联
            </el-button>
          </div>
          <div>
            <ai-matcher
              :systemId="systemId"
              @dataUpdate="dataUpdate"
            />
          </div>
        </div>
      </div>
    </div>
    <main-table
      ref="ledgerTable"
      :systemId="systemId"
      :systems="systems"
      :search="search"
    />
    <user-batch-link-form
      :visible.sync="dialogVisible"
      :systemId="systemId"
      :accountCodes="accountCodes"
      @dataUpdate="dataUpdate"
    />
  </div>
</template>

<script>
import MainTable from './MainTable.vue'
import SearchBox from './SearchBox.vue'
import AiMatcher from './AIMatcher.vue'
import UserBatchLinkForm from './UserBatchLinkForm.vue'

export default {
  components: {
    MainTable, SearchBox, AiMatcher, UserBatchLinkForm
  },
  props: {
    systemId: {
      type: Number,
      required: true
    },
    systems: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      loading: false,
      search: {
        onlyShowNoLinkAccount: false, // 只显示未关联账号
        property: 'account_code',
        value: null,
        account_status: null
      },
      dialogVisible: false,
      accountCodes: []
    }
  },
  methods: {
    handleSearchChange (payload) {
      this.search = payload
    },
    dataUpdate () {
      this.$refs.ledgerTable.getLedgerData()
    },
    handleBatch() {
      let selectData = this.$refs.ledgerTable.selectData
      if(selectData.length > 0) {
        this.accountCodes = selectData.map(x => x.account_code)
        this.dialogVisible = true
      } else {
        this.$message.error('请选择账号关联')
      }
    },
  }
}
</script>
<style lang="scss" scoped>
  @import '~@/components/variables';

  .component-container{
    .toolbar{
      @include vertical_top_between;
      margin-bottom: 20px;
      width: 100%;

      .right{
        margin-top: 4px;
      }
    }
    .toolbarRight {
      @include vertical_center;

      .left {
        margin-right: 10px;
      }
    }
  }
</style>
