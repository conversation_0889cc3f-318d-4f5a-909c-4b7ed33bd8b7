<template>
  <div>
    <admin-roles-item
      v-if="handle == 'show'"
      @ToForm="updateHandle"
    />
    <role-permission-set
      v-else
      :role-id="roleId"
      :module-function-ids="moduleFunctionIds"
      :alert-permissions="alertPermissions"
      :role-name="roleName"
      @update="updateRoles"
    />
  </div>
</template>
<script>
import AdminRolesItem from './AdminRolesItem'
import RolePermissionSet from './RolePermissionSet'

export default {
  components: {
    AdminRolesItem,
    RolePermissionSet
  },
  data () {
    return {
      handle: 'show',
      roleId: '',
      moduleFunctionIds: [],
      alertPermissions: []
    }
  },
  methods: {
    updateHandle (data) {
      this.handle = data.handle
      this.roleId = data.roleId
      this.roleName = data.roleName
      this.moduleFunctionIds = data.moduleFunctionIds
      this.alertPermissions = data.alertPermissions
    },
    updateRoles () {
      this.handle = 'show'
    }
  }
}
</script>
