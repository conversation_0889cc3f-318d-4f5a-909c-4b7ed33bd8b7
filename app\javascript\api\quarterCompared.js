import axios                           from '@/settings/axios'
import { parseFileName, downloadBlob } from './tool'
import API                             from '@/api'

export const comparedStatus          = (params) => {
  const url = `/api/quarter_compared/${params.quarterId}/compared_status/${params.comparedQuarterId}/bs/${params.systemId}`
  return axios.get(url)
}
export const createCompare           = (params) => {
  const url = `/api/quarter_compared/${params.quarterId}/compared_status/${params.comparedQuarterId}`
  return axios.post(url, { business_system_ids: params.businessSystemIds })
}
export const systemComparedDetail    = (params) => {
  const url = `/api/quarter_compared/${params.quarterId}/compared_detail/${params.comparedQuarterId}/bs/${params.systemId}`
  console.log(params)
  return axios.post(url, {
    page:      params.page,
    page_size: params.pageSize,
    filter:    JSON.stringify(params.filter) 
  })
}
export const systemComparedHistory   = (params) => {
  const query = {
    page:      params.page,
    page_size: params.pageSize,
    filter:    JSON.stringify(params.filter) 
  }
  return axios.post(`/api/quarter_compared/bs/${params.systemId}/history_difference`, query)
}
export const comparedDestroy         = (params) => {
  const url = `/api/quarter_compared/${params.quarterId}/compared_status/${params.comparedQuarterId}/bs/${params.systemId}`
  return axios.delete(url)
}

export const differenceExport        = (params) => {
  const url = `api/quarter_compared/${params.quarterId}/compared_detail/${params.comparedQuarterId}/bs/${params.systemId}/export`
  const theParams = { filter: params.filter }
  return API.downloadRecords.downloadAsync(url, theParams)
  // return axios.get(url, {
  //   responseType: 'blob',
  //   params:       { filter: params.filter }
  // })
  //   .then(response => {
  //     const fileName = parseFileName(response, 'quarter-difference.xlsx')
  //     downloadBlob(response.data, fileName)
  //   })
  //   .catch(() => {})
}
export const historyDifferenceExport = (params) => {
  const url = `api/quarter_compared/${params.quarterId}/compared_detail/${params.comparedQuarterId}/bs/${params.systemId}/history_export`
  const theParams = { filter: params.filter }
  return API.downloadRecords.downloadAsync(url, theParams)
  // return axios.get(url, {
  //   // responseType: 'blob',
  //   params:       { filter: params.filter }
  // })
  //   .then(response => {
  //     const fileName = parseFileName(response, 'quarter-difference-history.xlsx')
  //     downloadBlob(response.data, fileName)
  //   })
  //   .catch(() => {})
}
