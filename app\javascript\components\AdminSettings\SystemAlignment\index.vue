<template>
  <el-tabs v-model="tab">
    <el-tab-pane
      v-for="item in tabs"
      :key="item"
      :name="item"
      :label="tabTitle(item)"
    >
    </el-tab-pane>

    <roles-check v-if="tab === 'role_abnormal'" />
    <funds-check v-if="tab === 'fund_abnormal'" />
    <dates-check v-if="tab === 'date_abnormal'" />
    <fund-status-check v-if="tab === 'fund_status_abnormal' && showFundStatusAbnormal"/>
  </el-tabs>
</template>

<script>
import RolesCheck from '@/components/AdminSettings/SystemAlignment/RolesCheck'
import FundsCheck from '@/components/AdminSettings/SystemAlignment/FundsCheck'
import DatesCheck from '@/components/AdminSettings/SystemAlignment/DatesCheck'
import FundStatusCheck from '@/components/AdminSettings/SystemAlignment/FundStatusCheck'

export default {
  components: {
    FundStatus<PERSON><PERSON><PERSON>,
    <PERSON>s<PERSON><PERSON><PERSON>,
    <PERSON>s<PERSON><PERSON><PERSON>,
    DatesCheck
  },
  data () {
    return {
      tab: 'role_abnormal'
    }
  },
  computed: {
    showFundStatusAbnormal() {
      return this.$store.getters.hasPermission('app_settings.fund_status_compare_rule_settings')
    },
    tabs() {
      let tabs = ['role_abnormal', 'fund_abnormal', 'date_abnormal']
      if(this.showFundStatusAbnormal) {
        tabs.push('fund_status_abnormal')
      }
      return tabs
    }
  },
  methods: {
    tabTitle (key) {
      const i18nKey = `aas.system_alignments.abnormals.${key}`
      return `${this.$t(i18nKey)}设置`
    }
  }
}
</script>
