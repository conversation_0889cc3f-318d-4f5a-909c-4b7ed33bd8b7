# frozen_string_literal: true

# HomeController
class HomeController < ApplicationController
  def index
    # root
  end

  def app_status
    setting = UserDefinedSetting.appStatusAllowIp
    return json_custom_respond(400, error_message: "没有访问权限") if setting&.[]("enable") && !setting&.[]("allow_ip").to_a.include?(request.ip)

    json_respond(params[:help] == 'true' ? AppStatus.docs : AppStatus.check)
  end

  def aas_info
    json_respond AccountAuditSystemInformation.aas_info
  end

  def close_alert
    if current_admin.update(service_expire_alert: params[:service_expire_alert])
      audit_log! current_admin
      json_respond current_admin.output
    else
      json_custom_respond(:unprocessable_entity, error_message: current_admin.errors.full_messages.join('; '))
    end
  end

  def aas_security
    data = {
      password_min_length: AppSecurity.password_length.min,
      password_max_length: AppSecurity.password_length.max,
      password_complexity: AppSecurity.password_complexity
    }
    json_respond data
  end

  def upload_common_file
    common_file = CommonFile.new(
      admin_id: current_admin.id,
      uuid:     SecureRandom.uuid,
    )
    common_file.attachment = params[:file]
    begin
      common_file.save!

      json_custom_respond(200, common_file.output)
    rescue CarrierWave::IntegrityError => e
      json_custom_respond(415, success: false, error_message: e.message)
    end
  end

  def app_settings
    settings = {
      appName:                    Setting.application_name,
      importFromImporter:         Setting.import_from_importer,
      createSystemSettings:       Setting.create_system,
      globalAlertSettings:        Setting.global_alert,
      ssoSettings:                Setting.sso,
      jiaoyiTemporarySettings:    Setting.jiaoyi_temporary,
      permissionEditableSettings: Setting.permission_editable,
      systemAlignmentSettings:    UserDefinedSetting.system_alignment,
      pcCompareGuzhiSettings:     Setting.pc_compare_guzhi,
      pcCompareO32Settings:       Setting.pc_compare_o32,
      fdfgCompareGzyss45Settings: UserDefinedSetting.fdfg_compare_gzyss45,
      fundPermissionAbnormals:    UserDefinedSetting.fund_permission_abnormals,
      positionRoleCheck:          Setting.position_role_check,
      fundEmailsCheck:            Setting.fund_emails_check,
      systemRoleConflicts:        Setting.system_role_conflicts,
      quarterMode:                UserDefinedSetting.quarterMode,
      userInfoColumns:            UserDefinedSetting.userInfoColumns,
      accountDetail:              UserDefinedSetting.accountDetail,
      allUsers:                   UserDefinedSetting.allUsers,
      userDetail:                 UserDefinedSetting.userDetail,
      sendResetPasswordSettings:  UserDefinedSetting.send_reset_password,
      jobBaselineSettings:        Setting.job_baseline,
      oaRecordSettings:           Setting.oa_record,
      i18n:                       I18n.t('.').except(:faker),
      frontendSettings:           Setting.frontendSettings.merge({
                                                                   customerId: Setting.customer_id,
                                                                   apiBaseUrl: ENV['RAILS_RELATIVE_URL_ROOT']
                                                                 })
    }
    json_respond settings
  end
end
