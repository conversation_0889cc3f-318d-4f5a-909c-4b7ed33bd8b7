import axios from '@/settings/axios'

export const list = () => {
  return axios.get('/admin_api/data_sources')
}

export const checkSql = (dataSourceId, sql) => {
  return axios.post(`/admin_api/data_sources/${dataSourceId}/check_sql`, { sql: sql })
}

export const getSqlResult = (dataSourceId, sql) => {
  const promise = new Promise(function(resolve, reject){
    if (dataSourceId == null) {
      resolve({ status: 'failure', error_message: '请选择数据源' })
    }
    if (sql == null || sql === '') {
      resolve({ status: 'failure', error_message: '请填写SQL' })
    }
    if(dataSourceId != null && sql != null){
      checkSql(dataSourceId, sql)
        .then(response => {
          resolve(response.data)
        })
        .catch(() => {
        })
    }
  })
  return promise
}
