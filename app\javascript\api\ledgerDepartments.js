import axios from '@/settings/axios'

export const index = (params = {}) => {
  return axios.get('/api/ledger_departments', { params: params })
}

export const detail = (id) => {
  return axios.get(`/api/ledger_departments/${id}`)
}

export const update = (id, params = {}) => {
  return axios.put(`/api/ledger_departments/${id}`, params)
}

export const create = (params = {}) => {
  return axios.post(`/api/ledger_departments`, params)
}

export const destroy = (id) => {
  return axios.delete(`/api/ledger_departments/${id}`)
}

export const systems = (params = {}) => {
  return axios.get('/api/ledger_departments/systems', {})
}

export const departments = (business_system_id) => {
  const params = { business_system_id: business_system_id }
  return axios.get('/api/ledger_departments/departments', { params: params })
}