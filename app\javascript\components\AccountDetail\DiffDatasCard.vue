<template>
  <el-card>
    <div
      v-if="showTitle"
      slot="header"
    >
      <diff-datas-title
        :baseline-name="baselineName"
        :system-id="systemId"
        :account="account"
        @quarterChange="otherQuarterChange"
        @accountChange="otherAccountChange"
        @diffModeChange="handleDiffModeChange"
        @bsNameChange="handleBsNameChange"
      />
    </div>
    <el-tabs
      v-model="activeName"
      v-loading="loading"
      type="border-card"
    >
      <el-tab-pane
        v-if="diffMode !== 'compareBaseline'"
        name="roles"
      >
        <span slot="label" v-if="checkDiffData(roleDiff)"> 账号角色*</span>
        <span slot="label" v-else> 账号角色</span>
        <diff-roles-table
          :account-id="accountId"
          :system-id="systemId"
          :show="diffMode !== 'compareBaseline'"
          :other-quarter-id="realOtherQuarterId"
          :compare-name="compareName"
          :other-account-code="otherAccount"
          :diff-mode="diffMode"
          :account="account"
          :role-diff="roleDiff"
          :role-schema="roleSchema"
          @changeRoleDiff="changeRoleDiff"
        />
      </el-tab-pane>

      <template
        v-for="item in dataSchema"
      >
        <el-tab-pane
          v-if="diff_datas[item.data_key]"
          :key="item.data_key"
          :name="item.data_key"
        >
          <span slot="label" v-if="checkDiffData(diff_datas[item.data_key])"> {{ item.name }}*</span>
          <span slot="label" v-else> {{ item.name }}</span>
          <diff-datas-table
            :diff-datas="diff_datas[item.data_key]"
            :data-schema="item.schema"
            :compare-name="compareName"
            :diff-mode="diffMode"
            :other-account-code="otherAccount"
            :system-id="systemId"
            :baseline-name="baselineName"
            :compare-rule="compareRule"
            :get-response="getResponse"
          />
        </el-tab-pane>
      </template>
    </el-tabs>
  </el-card>
</template>

<script>
import DiffDatasTable from './DiffDatasTable.vue'
import DiffDatasTitle from './DiffDatasTitle.vue'
import DiffRolesTable from './DiffRolesTable.vue'
import API            from '@/api'

export default {
  components: {
    DiffDatasTitle,
    DiffDatasTable,
    DiffRolesTable
  },
  props:      {
    accountId:        {
      type:     Number,
      required: true
    },
    systemId:         {
      type:     Number,
      required: true
    },
    showTitle:        {
      type:    Boolean,
      default: true
    },
    otherQuarterId:   {
      type:    Number,
      default: 0
    },
    // 是否启用 外部传入的 otherQuarterId 而不是从 title 中获取 otherQuarter
    enableOtherQuarterId: {
      type:    Boolean,
      default: false
    },
    defaultDiffMode: {
      type:    String,
      default: ''
    },
    defaultDataKey: {
      type:    String,
      default: ''
    },
    account:          {
      type:    Object,
      default: () => {
        return {
          account_code: ''
        }
      }
    }
  },
  data () {
    return {
      activeName:   'roles',
      diff_datas:   {},
      dataSchema:   [],
      roleSchema:   [],
      roleDiff:     {
        add_permissions:    [],
        reduce_permissions: []
      },
      loading:      false,
      otherQuarter: { id: 0, name: '不存在导入数据' },
      baselineName: '',
      otherAccount: '',
      diffMode:     this.handleDefaultDiffMode(),
      compareRule:  'all_contrast',
      getResponse:  false
    }
  },
  computed: {
    realOtherQuarterId () {
      if (this.enableOtherQuarterId) {
        return this.otherQuarterId
      } else {
        return this.otherQuarter.id
      }
    },
    compareName () {
      if (this.diffMode === 'compareBaseline') return this.baselineName
      return this.otherQuarter.name
    },
    baselinePermissionShow () {
      return this.$settings.baselinePermissionShow || false
    }
  },
  watch:    {
    accountId (val) {
      this.diffDataRequest()
    },
    // 这里如果直接判断 otherQuarter，由于结果是一个 object，即使数据一致，内存地址不会相同，不会被认为是同一数据，会导致重复的数据刷新
    'otherQuarter.id': {
      handler (newValue, oldValue) {
        if (newValue === oldValue) return

        this.diffDataRequest()
      },
      deep: true
    },
    otherAccount (val) {
      this.diffDataRequest()
    }
  },
  created () {
    this.diffDataRequest()
  },
  methods: {
    checkDiffData (data) {
      return data.add_permissions.length > 0 || data.reduce_permissions.length > 0
    },
    changeRoleDiff (roleDiff) {
      this.roleDiff = roleDiff
    },
    handleBsNameChange (payload) {
      this.diffDataRequest()
    },
    otherQuarterChange (payload) {
      this.otherQuarter = payload
      this.$emit('otherQuarterChange', payload)
    },
    otherAccountChange (payload) {
      this.otherAccount = payload
    },
    // 通过 otherQuarterId 获取对比季度信息
    getOtherQuarter () {
      if (this.otherQuarterId === 0) return
      if (this.diffMode !== 'compareHistory') return

      API.quarters.info(this.otherQuarterId)
        .then(response => {
          this.otherQuarter = response.data
        })
        .catch(() => {})
    },
    // 历史差异
    getDiffDatasWithQuarter (otherQuarterId) {
      if (otherQuarterId === 0) return

      this.loading    = true
      const theParams = {
        other_quarter_id: otherQuarterId,
        account_id:       this.accountId,
        system_id:        this.systemId
      }
      API.systemAccounts.diffDatas(theParams)
        .then(response => {
          this.diff_datas = response.data.diff_datas
          this.dataSchema = response.data.output_schema
          this.loading    = false
          if (API.tool.isBlank(this.defaultDataKey)){
            this.activeName = this.dataSchema[0].data_key
          } else {
            this.activeName = this.defaultDataKey
          }
          this.getResponse = true
        })
        .catch(() => {
          this.loading = false
          this.getResponse = true
        })
    },
    // 账号差异
    getDiffDatasWithAccount (otherAccount) {
      this.loading    = true
      const theParams = {
        other_account_code: otherAccount,
        account_id:         this.accountId,
        system_id:          this.systemId
      }
      API.systemAccounts.compareWithAccount(theParams)
        .then(response => {
          this.diff_datas = response.data.diff_datas
          this.dataSchema = response.data.output_schema
          this.loading    = false
          if (API.tool.isBlank(this.defaultDataKey)){
            this.activeName = this.dataSchema[0].data_key
          } else {
            this.activeName = this.defaultDataKey
          }
          this.getResponse = true
        })
        .catch(() => {
          this.loading = false
          this.getResponse = true
        })
    },
    // 系统基线差异
    getDiffDataWithBaseline () {
      this.loading = true
      this.$axios.get(`/api/systems/${this.systemId}/accounts/${this.accountId}/diff_baseline`)
        .then(response => {
          this.diff_datas   = response.data.diff_datas
          this.baselineName = response.data.name
          this.compareRule  = response.data.compare_rule
          this.dataSchema   = response.data.output_schema
          if(this.baselinePermissionShow) {
            this.dataSchema = this.filterSchemaWithBaselineNotice(response.data.output_schema_data)
          } else {
            this.$set(this.dataSchema, this.replaceSchemaWithBaselineNotice(response.data.output_schema_data))
          }

          // 系统基线模式时，tab 默认显示为第一个维度
          this.activeName = this.dataSchema[0].data_key
          this.loading    = false
          this.getResponse = true
        })
        .catch(() => {
          this.loading = false
          this.getResponse = true
        })
    },
    handleDefaultDiffMode () {
      if (this.defaultDiffMode === 'compareBaseline') return 'compareBaseline'
      if (!this.defaultDiffMode && this.$settings.accountDetail.show_diff_baseline_default) return 'compareBaseline'
      return 'compareHistory'
    },
    // payload 为外部传入
    handleDiffModeChange (payload) {
      this.diffMode = payload
      this.diffDataRequest()
    },
    diffDataRequest () {
      this.getResponse = false
      switch (this.diffMode) {
        case 'compareAccount':
          return this.getDiffDatasWithAccount(this.otherAccount)
        case 'compareBaseline':
          return this.getDiffDataWithBaseline()
        default:
          this.getOtherQuarter()
          return this.getDiffDatasWithQuarter(this.realOtherQuarterId)
      }
    },
    filterSchemaWithBaselineNotice (outputSchemaData) {
      const dataKeys = outputSchemaData.filter(x => !x.is_notice).map(x => x.data_key)
      return this.dataSchema.filter(x => !dataKeys.includes(x.data_key))
    },

    // 根据返回数据添加不告警提示,用于替换dataSchema
    replaceSchemaWithBaselineNotice (outputSchemaData) {
      const dataKeys = outputSchemaData.filter(x => !x.is_notice).map(x => x.data_key)
      return this.dataSchema.map(x => {
        if (dataKeys.indexOf(x.data_key) > -1 && x.name.indexOf('不告警') < 0) {
          x.name = x.name + '【不告警】'
        }
        return x
      })
    }
  }
}
</script>
