# frozen_string_literal: true

# 系统后台账号（管理员）控制器
class PublicAccountTypesController < ApplicationController
  before_action :authenticate_admin!
  before_action :authenticate_policy!, except: %i[index]
  before_action :params_type, only: %i[update destroy]

  def index
    json_respond PublicAccountType.all.map(&:output)
  end

  def create
    @type = PublicAccountType.new(
      key:   params[:key],
      label: params[:label]
    )

    if @type.save
      audit_log! @type
      json_respond @type
    else
      json_custom_respond(400, error_message: @type.errors.full_messages.join('; '))
    end
  end

  def update
    if @type.update(update_params)
      audit_log! @type
      json_respond @type
    else
      json_custom_respond(:unprocessable_entity, error_message: @type.errors.full_messages.join('; '))
    end
  end

  def destroy
    audit_log! @type
    @type.destroy

    json_respond_no_content
  end

  private

  def params_type
    @type = PublicAccountType.find_by(id: params[:id])
  end

  def authenticate_policy!
    authorize nil, policy_class: PublicAccountTypesPolicy
  end

  def update_params
    params.permit(:label, settings: [systems: [:id, :name, ignore_account_codes: []]])
  end
end
