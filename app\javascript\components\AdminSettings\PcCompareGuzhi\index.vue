<template>
  <el-tabs v-model="tab">
    <el-tab-pane
      v-for="item in tabs"
      :key="item"
      :name="item"
      :label="tabTitle(item)"
    >
    </el-tab-pane>

    <funds-check v-if="tab === 'fund_abnormal'" />
  </el-tabs>
</template>

<script>
import FundsCheck from '@/components/AdminSettings/PcCompareGuzhi/FundsCheck'
export default {
  components: {
    FundsCheck
  },
  data () {
    return {
      tab: 'fund_abnormal',
      tabs: ['fund_abnormal']
    }
  },
  methods: {
    tabTitle (key) {
      const i18nKey = `aas.pc_o32_comparison.abnormals.${key}`
      return `${this.$t(i18nKey)}设置`
    }
  }
}
</script>
