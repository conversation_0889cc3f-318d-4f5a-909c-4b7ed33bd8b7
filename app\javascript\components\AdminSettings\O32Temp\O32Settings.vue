<script>
export default {
  computed: {
    isAuthorizerCustomListEnable () {
      const authorizerCustomList = this.$settings.jiaoyiTemporary.authorizer_custom_list
      return authorizerCustomList ? authorizerCustomList.enable : false
    },
    isFundTypeCheckEnable () {
      const fundTypeCheck = this.$settings.jiaoyiTemporary.fund_type_check
      return fundTypeCheck ? fundTypeCheck.enable : false
    },
    isOperatorCheckEnable () {
      const operatorCheck = this.$settings.jiaoyiTemporary.operator_check
      return operatorCheck ? operatorCheck.enable : false
    },
    isDateCheckEnable () {
      const fundDateCheck = this.$settings.jiaoyiTemporary.fund_date_check
      return fundDateCheck ? fundDateCheck.enable : false
    },
    isO32SettingEnable () {
      return this.isAuthorizerCustomListEnable || this.isFundTypeCheckEnable || this.isOperatorCheckEnable
    }
  }
}
</script>
