class JobAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module   = '系统岗位管理'
    @operation_category = '系统岗位'
    super
  end

  def create
    @operation = '创建岗位'
    @comment = "创建了岗位：「#{params.name}」 编码：「#{params.code}」 ID：「#{params.id}」"
    @comment += " 所属部门：#{params.department&.name}" if params.department.present?
    create_audit_log
  end

  def create_fail
    @operation = '创建岗位'
    error_message = params.errors.full_messages.join('; ')
    @comment = "创建岗位失败：「#{params.name}」 编码：「#{params.code}」 错误信息：#{error_message}"
    create_audit_log
  end

  def update
    @operation = '更新岗位'
    job = params[0]
    before_job = params[1]
    after_job = params[2]
    str = update_job_detail(before_job, after_job)
    @comment = "更新了岗位： ID：「#{job.id}」 名称：「#{job.name}」 编码：「#{job.code}」"
    @comment += "详情：#{str}" if str.present?
    create_audit_log
  end

  def update_fail
    @operation = '更新岗位'
    error_message = params.errors.full_messages.join('; ')
    @comment = "更新岗位失败： ID：「#{params.id}」 名称：「#{params.name}」 编码：「#{params.code}」 错误信息：#{error_message}"
    create_audit_log
  end

  def destroy
    @operation = '删除岗位'
    @comment   = "删除了岗位： ID：「#{params.id}」 名称：「#{params.name}」"
    create_audit_log
  end

  protected

  def update_job_detail(before_job, after_job)
    str = ''
    str += "编码：「#{before_job[:code]}」改为「#{after_job[:code]}」" if after_job[:code] != before_job[:code]
    str += "名称：「#{before_job[:name]}」改为「#{after_job[:name]}」" if after_job[:name] != before_job[:name]
    if after_job[:department_id] != before_job[:department_id]
      str += "部门：「#{before_job[:department_name]}」改为「#{after_job[:department_name]}」"
    end
    if after_job[:parent_id] != before_job[:parent_id]
      str += "上级岗位：「#{before_job[:parent_name]}」改为「#{after_job[:parent_name]}」"
    end
    str
  end
end
