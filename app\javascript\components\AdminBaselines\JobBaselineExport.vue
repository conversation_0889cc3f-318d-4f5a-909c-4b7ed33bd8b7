<template>
  <el-button
    v-loading="loading"
    type="primary"
    size="small"
    :disabled="!$store.getters.hasPermission('job_baseline.export')"
    @click="exportBaseline"
  >
    导出岗位基线
  </el-button>
</template>

<script>
import API from '@/api'
export default {
  props: {
    systemId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      loading: false
    }
  },
  methods: {
    exportBaseline () {
      this.loading = true
      API.jobBaselines.exportFile()
        .then(() => this.loading = false)
        .catch(error => {
          this.loading = false
          if (error.status === 400) {
            error.data.text().then((res) => {
              this.errorNotify(res)
            })
          }
        })
    },
    errorNotify (error) {
      this.$notify.error({
        title: '错误',
        message: JSON.parse(error).error_message,
        duration: 0
      })
    }
  }
}
</script>
<style lang="scss">
  .el-notification__content p {
    width: 260px;
    text-align: left;
    word-break:break-all;
    word-wrap:break-word;
  }
</style>
