<template>
  <span>
    <el-dropdown-item
      v-loading="loadingSelect"
      type="primary"
      :disabled="!$store.getters.hasPermission('system_baseline.export')"
      @click.native="exportSelectBaseline"
    >
      导出所选系统基线
    </el-dropdown-item>

    <el-dropdown-item
      v-loading="loading"
      type="primary"
      :disabled="!$store.getters.hasPermission('system_baseline.export')"
      @click.native="exportBaseline"
    >
      导出全部筛选系统基线
    </el-dropdown-item>
  </span>
</template>

<script>
import API from '@/api'
export default {
  props: {
    systemId: {
      type: Number,
      required: true
    },
    filter: {
      type: Object,
      required: true
    },
    baselineIds: {
      type: Array,
      required: false
    }
  },
  data () {
    return {
      loading: false,
      loadingSelect: false
    }
  },
  methods: {
    exportBaseline () {
      this.loading = true
      API.systemBaselines.exportBaseline(this.systemId, this.filter)
        .then(() => this.loading = false)
        .catch(error => {
          this.loading = false
          console.log(error)
          if (error.status === 400) {
            error.data.text().then((res) => {
              this.errorNotify(res)
            })
          }
        })
    },
    exportSelectBaseline () {
      if (this.baselineIds.length === 0) {
        const h = this.$createElement
        this.$notify.error({
          title:   '错误',
          message: h('i', { style: 'color: teal' }, '您未选取任何数据。')
        })
      } else {
        this.loadingSelect = true
        API.systemBaselines.exportSelectBaseline(this.systemId, this.baselineIds)
          .then(() => this.loadingSelect = false)
          .catch(error => {
            this.loadingSelect = false
            console.log(error)
            if (error.status === 400) {
              error.data.text().then((res) => {
                this.errorNotify(res)
              })
            }
          })
      }
    },
    errorNotify (error) {
      this.$notify.error({
        title: '错误',
        message: JSON.parse(error).error_message,
        duration: 0
      })
    }
  }
}
</script>
<style lang="scss">
  .el-notification__content p {
    width: 260px;
    text-align: left;
    word-break:break-all;
    word-wrap:break-word;
  }
</style>
