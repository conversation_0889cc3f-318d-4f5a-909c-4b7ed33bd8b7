<template>
  <div class="summary-container">
    <!-- eslint-disable vue/attribute-hyphenation -->
    <el-table
      v-loading="loading"
      ref="multipleTable"
      :data="currentPageData"
      :row-class-name="tableRowClassName"
      size='small'
      height="100%"
      @sort-change="changeSort"
      :default-sort="{prop: 'account_code', order: 'ascending'}"
      border
    >
      <el-table-column
        type="selection"
        width="55"
      />
      <el-table-column
        property="account_code"
        label="账号编码"
        sortable="custom"
      >
        <template slot-scope="{row}">
          {{ row.account_code || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        property="account_name"
        label="账号名称"
        sortable="custom"
      >
        <template slot-scope="{row}">
          {{ row.account_name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        property="account_status"
        label="账号状态"
        width="70"
      >
        <template slot-scope="{row}">
          {{ row.account_status || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        v-for="(item, index) in userInfoColumns"
        :key="index"
        v-bind="item"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.user_info[item.prop.replace(/info./i,"")] }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        property="is_public"
        label="功能账号"
        width="70"
      >
        <template slot-scope="scope">
          <span v-show="scope.row.is_public === 0">-</span>

          <span v-show="scope.row.is_public === 1">有效</span>
          <span
            v-show="scope.row.is_public === 2"
            style="color: red;font-weight: bold;"
          >
            过期
          </span>
        </template>
      </el-table-column>
      <el-table-column
        property="public_account_type"
        label="功能账号类型"
        width="70"
      >
      </el-table-column>
      <el-table-column
        v-if="$store.getters.hasPermission('ledgers.disable')"
        property="ignore_matcher"
        label="禁用匹配"
        width="70"
      >
        <template slot-scope="scope">
          <ignore-matcher
            :ledger="scope.row"
            :systemId="systemId"
            @update="getLedgerData"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="155"
      >
        <template slot-scope="scope">
          <el-button-group>
            <el-button
              title="编辑关联"
              icon="el-icon-edit"
              size="small"
              :disabled="!hasChangeUserPermission()"
              @click="handleChangeUser(scope.row)"
            />
            <el-button
              v-if="scope.row.is_public == 0"
              :disabled="!hasAddPublicAccountPermission()"
              title="添加至功能账号"
              icon="el-icon-folder-add"
              size="small"
              @click="handleAddPublicAccount(scope.row)"
            />
            <el-button
              v-else
              :disabled="!hasRemovePublicAccountPermission()"
              title="解除绑定功能账号"
              icon="el-icon-folder-remove"
              size="small"
              @click="handleRemovePublicAccount(scope.row)"
            />
            <el-button
              :disabled="!hasDeleteUserPermission(scope.row)"
              title="取消关联"
              icon="el-icon-delete"
              size="small"
              @click="handleDeleteUser(scope.row)"
            />
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-size="pageSize"
      :total="paginationLength"
      :current-page.sync="currentPage"
      background
      layout="total, prev, pager, next, jumper"
      class="pagination"
      @current-change="getLedgerData"
    />
    <user-change
      :visible.sync="dialogVisible"
      :ledger="ledgerWillChangeUser"
      :systemId="systemId"
      @update="getLedgerData"
    />
    <user-delete
      :visible.sync="deleteDialogVisible"
      :ledger="ledgerWillDeleteUser"
      :systemId="systemId"
      @update="getLedgerData"
    />
    <public-account-form
      ref="publicAccount"
      :account="publicAccount"
      :systemId="systemId"
      :systems="systems"
      mode="ledger"
      @update="getLedgerData"
    />
    <!-- eslint-enable vue/attribute-hyphenation -->
  </div>
</template>
<script>
import UserInfo          from './UserInfo.vue'
import UserChange        from './UserChange.vue'
import IgnoreMatcher     from './IgnoreMatcher.vue'
import UserDelete        from '@/components/AdminLedgers/UserDelete'
import PublicAccountForm from '@/components/AdminPublicAccounts/AccountForm.vue'
import * as Message      from '@/utils/general_messages'

export default {
  components: {
    UserInfo,
    UserChange,
    UserDelete,
    PublicAccountForm,
    IgnoreMatcher
  },
  props:      {
    systemId: {
      type:     Number,
      required: true
    },
    systems:  {
      type:    Array,
      default: () => []
    },
    search:   {
      type:     Object,
      required: true
    }
  },
  data () {
    return {
      loading:              false,
      dialogVisible:        false,
      currentPageData:      [],
      currentPage:          1,
      pageSize:             15,
      paginationLength:     0,
      ledgerWillChangeUser: {},
      publicAccount:        null,
      prop:                 'account_code',
      order:                'ASC',
      deleteDialogVisible:  false,
      ledgerWillDeleteUser: {}
    }
  },
  computed: {
    userInfoColumns () {
      const infoColumns = JSON.parse(JSON.stringify(this.$settings.userInfoColumns))
      return infoColumns.filter((item) => item.show_in_account_page)
    },
    selectData () {
      return this.$refs.multipleTable.selection
    },
    // 所有操作权限的系统
    adminMaintainSystemIds () {
      return this.$store.state.adminPrivileges.maintain_system.map(x => x.id)
    }
  },
  watch: {
    search: {
      handler: function () {
        this.currentPage = 1
        this.getLedgerData()
      },
      deep:    true
    }
  },
  created () {
    this.getLedgerData()
  },
  methods: {
    // 拥有操作系统权限
    hasMaintainSystemPermission() {
      return this.adminMaintainSystemIds.indexOf(this.systemId) >= 0
    },
    hasChangeUserPermission () {
      return this.$store.getters.hasPermission('ledgers.edit') && this.hasMaintainSystemPermission()
    },
    hasAddPublicAccountPermission () {
      return this.$store.getters.hasPermission('public_accounts.edit') && this.hasMaintainSystemPermission()
    },
    hasRemovePublicAccountPermission () {
      return this.$store.getters.hasPermission('public_accounts.edit') && this.hasMaintainSystemPermission()
    },
    hasDeleteUserPermission (row) {
      return row.user_name && this.$store.getters.hasPermission('ledgers.edit') && this.hasMaintainSystemPermission()
    },
    changeSort (val) {
      this.prop  = val.prop
      this.order = val.order === 'ascending' ? 'ASC' : 'DESC'
      this.getLedgerData()
    },
    tableRowClassName ({ row, rowIndex }) {
      if (row.is_public === 1) {
        return 'warning-row'
      }
      return ''
    },
    getLedgerData () {
      let url    = `/admin_api/ledgers/bs/${this.systemId}/accounts`
      let params = {
        page:     this.currentPage,
        per_page: this.pageSize,
        filter:   this.search,
        prop:     this.prop,
        order:    this.order
      }

      this.loading = true
      this.$axios.get(url, { params: params })
        .then(response => {
          this.loading          = false
          this.currentPageData  = response.data.data
          this.paginationLength = response.data.count
        })
        .catch(error => {
          this.loading = false
          if (error.status === 404) Message.notFoundQuarter()
        })
    },
    handleChangeUser (row) {
      this.dialogVisible        = true
      this.ledgerWillChangeUser = row
    },
    handleDeleteUser (row) {
      this.deleteDialogVisible  = true
      this.ledgerWillDeleteUser = row
    },
    handleAddPublicAccount (row) {
      this.publicAccount               = row
      this.$refs.publicAccount.visible = true
    },
    handleRemovePublicAccount(row) {
      this.$confirm('此操作将取消该账号与功能账号关联，无法恢复，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          this.removePublicAccount(row.public_account.id)
        })
        .catch(() => {
          this.$message.info('已取消删除操作')
        })
    },
    removePublicAccount(id) {
     this.$axios.delete(`api/systems/${this.systemId}/public_accounts/${id}`)
      .then(response => {
        this.$message.success('功能账号已删除')
        this.getLedgerData()
      })
      .catch(() => {})
    },
    updateRow (row) {
      let index = this.currentPageData.findIndex(x => x.account_code === row.account_code)
      this.currentPageData.splice(index, 1, row)
    }
  }
}
</script>
<style lang="scss" scoped>

.summary-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 245px);
  min-height: 600px;
}

.pagination {
  margin-top: 2em;
}

.account-no-link {
  height:      60px;
  font-size:   14px;
  color:       #a9a7b1;
  /* line-height: 60px; 会导致 "该账号尚未关联员工" 置于表格下方 和基线的不同，基线得有*/
  text-align:  center;
}

/* 修复 el-table expand 和 fixed 混用导致的样式问题 */
/deep/ .el-table__body-wrapper {
  .el-table__expanded-cell {
    z-index: 100;
    padding: 0;
  }
}

/deep/ .el-table__fixed, /deep/ .el-table__fixed-right {
  .el-table__expanded-cell {
    visibility: hidden;
    padding:    0;
  }
}

.expand {
  padding:          20px;
  background-color: #fff; //盖住 fixed 产生的阴影
}
</style>

<style>
.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}
</style>
