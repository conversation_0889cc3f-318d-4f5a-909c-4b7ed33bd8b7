# frozen_string_literal: true

# 系统用户管理审计日志
class AdminAccountAuditLog < ApplicationAuditLog
  def initialize(user, request, params)
    @operation_module = '系统用户管理'
    @operation_category = '用户管理'
    super
  end

  def create
    @operation = '创建用户'
    @comment   = "创建了后台管理用户 登录名：「#{params.uid}」 姓名：「#{params.name}」"
    create_audit_log
  end

  def update
    @operation                    = '修改用户'
    name_change                   = params.old_admin.name != params.new_admin.name
    mobile_change                 = params.old_admin.mobile != params.new_admin.mobile
    allow_notification_change     = params.old_admin.allow_notification != params.new_admin.allow_notification
    allow_sms_notification_change = params.old_admin.allow_sms_notification != params.new_admin.allow_sms_notification
    bund_user_change              = params.old_admin.user_id != params.new_admin.user_id
    default_home_page_change      = params.old_admin.default_home_page != params.new_admin.default_home_page

    if name_change || mobile_change || allow_notification_change || allow_sms_notification_change || bund_user_change || default_home_page_change
      @comment = "用户「#{params.new_admin.uid}」的"
    end
    @comment                      += "姓名「#{params.old_admin.name}」=>「#{params.new_admin.name}」 " if name_change
    @comment                      += "手机号 「#{params.old_admin.mobile}」 => 「#{params.new_admin.mobile}」" if mobile_change
    if allow_notification_change
      @comment                    += "接收邮件通知 「#{params.old_admin.allow_notification ? '是' : '否'}」 => 「#{params.new_admin.allow_notification ? '是' : '否'}」"
    end
    if allow_sms_notification_change
      @comment                    += "接收短信通知 「#{params.old_admin.allow_sms_notification ? '是' : '否'}」 => 「#{params.new_admin.allow_sms_notification ? '是' : '否'}」"
    end
    @comment += "绑定员工 「#{params&.old_admin&.user&.name}」=>「#{params&.new_admin&.user&.name}」" if bund_user_change
    if default_home_page_change
      old_default_home_page_name = AppModule.find_by(router: params.old_admin.default_home_page)&.name
      new_default_home_page_name = AppModule.find_by(router: params.new_admin.default_home_page)&.name
      @comment += "默认首页 「#{old_default_home_page_name}」=> 「#{new_default_home_page_name}」"
    end

    create_audit_log if @comment.present?
  end

  def destroy
    @operation = '删除用户'
    @comment   = "删除了登录名：「#{params.uid}」 姓名：「#{params.name}」的后台管理用户"
    create_audit_log
  end

  def unlock
    @operation = '解锁用户'
    @comment   = "用户「#{params.uid}」解锁成功"
    create_audit_log
  end

  def disable
    @operation = '禁用用户'
    @comment   = "禁用用户「#{params.uid}」"
    create_audit_log
  end

  def enable
    @operation = '启用用户'
    @comment   = "重新启用用户「#{params.uid}」"
    create_audit_log
  end

  def change_password
    @operation = '修改密码'
    @comment   = "用户「#{params.uid}」登录密码修改成功"
    create_audit_log
  end

  def update_roles
    @operation = '修改角色'
    @comment   = "修改用户「#{params.uid}」的角色为：#{admin_roles}"

    create_audit_log
  end

  def update_admin_permissions
    update_admin_systems_permissions
    update_admin_departments_permissions
  end

  def set_operator_permission
    @operation = '业务系统账号绑定'
    @comment   = "绑定了「#{params.name}」的业务系统账号"

    create_audit_log
  end

  private

  def admin_roles
    params.roles.present? ? params.roles.map(&:name).join('，') : '无'
  end

  def system_in_query
    params[0].only_business_systems_in_query.present? ? params[0].only_business_systems_in_query.pluck(:name).join('，') : '无'
  end

  def system_in_maintain
    params[0].only_business_systems_in_maintain.present? ? params[0].only_business_systems_in_maintain.pluck(:name).join('，') : '无'
  end

  def department_in_query(admin)
    admin.departments_in_query.present? ? admin.departments_in_query.pluck(:name).join('，') : '无'
  end

  # 查询权限的系统分类
  def system_category_in_query
    admin = params[0]
    admin.business_system_categories_in_query.present? ? admin.business_system_categories_in_query.pluck(:name).join('，') : '无'
  end

  # 操作权限的系统分类
  def system_category_in_maintain
    admin = params[0]
    admin.business_system_categories_in_maintain.present? ? admin.business_system_categories_in_maintain.pluck(:name).join('，') : '无'
  end

  def update_admin_systems_permissions
    @operation = '修改系统权限'
    @comment   = "修改用户「#{params[0].uid}」的系统分类权限为：查询权限: #{system_category_in_query}; 操作权限: #{system_category_in_maintain}；系统权限为：查询权限: #{system_in_query}; 操作权限: #{system_in_maintain}"
    create_audit_log
  end

  def update_admin_departments_permissions
    admin = params[0]
    all_departments_query = params[1]
    @operation = '修改部门权限'
    if all_departments_query.to_s == 'true'
      @comment   = "修改用户「#{admin.uid}」的部门权限为「所有部门权限」"
    else
      @comment   = "修改用户「#{admin.uid}」的部门权限为：#{department_in_query(admin)}"
    end

    create_audit_log
  end
end
