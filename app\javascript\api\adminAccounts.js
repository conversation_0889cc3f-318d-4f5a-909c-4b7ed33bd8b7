import axios from '@/settings/axios'

export const index = () => {
  return axios.get(`/admin_api/admin_accounts`)
}

export const disable = (id) => {
  return axios.post(`/admin_api/admin_accounts/${id}/disable`)
}

export const enable = (id) => {
  return axios.post(`/admin_api/admin_accounts/${id}/enable`)
}
export const create = (params) => {
  const admin = {
    email:                  params.email,
    mobile:                 params.mobile,
    name:                   params.name,
    code:                   params.code,
    password:               params.password,
    password_confirmation:  params.password_confirmation,
    allow_notification:     params.allow_notification,
    allow_sms_notification: params.allow_sms_notification,
    user_id:                params.user_id
  }
  return axios.post('/admin_api/admin_accounts', { admin: admin })
}
