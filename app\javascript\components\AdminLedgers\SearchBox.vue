<template>
  <div>
    <el-form
      :inline="true"
      class="search-box"
      style="margin-bottom: 0.5em;"
    >
      <el-form-item>
        <el-checkbox
          v-model="search.onlyShowNoLinkAccount"
          @change="handleChange"
          size='small'
        >
          只显示未关联账号
        </el-checkbox>
      </el-form-item>

      <el-form-item>
        <el-select
          v-model="search.property"
          placeholder="检索条件"
          size='small'
          style="width:120px;"
        >
          <el-option
            label="账号编码"
            value="account_code"
          />
          <el-option
            label="账号名称"
            value="account_name"
          />
          <el-option
            label="员工编码"
            value="user_code"
          />
          <el-option
            label="员工名称"
            value="user_name"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="search.value"
          placeholder="输入搜索内容"
          size='small'
          style="width:125px;"
          @keyup.enter.native="handleChange"
          clearable
        />
      </el-form-item>

      <el-form-item>
        <el-select
          v-model="search.account_status"
          placeholder="账号状态"
          size='small'
          style="width:110px;"
          clearable
        >
          <el-option
            label="全部状态"
            value=""
          />
          <el-option
            :label="getAccountStatusString(true)"
            value="true"
          />
          <el-option
            :label="getAccountStatusString(false)"
            value="false"
          />
          <el-option
            label="已删除"
            value="delete"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="search.public_account_status"
          placeholder="是否包含功能账号"
          size="small"
          style="width: 150px;"
          clearable
        >
        <el-option
          label="全部"
          value=""
        />
        <el-option
          label="是"
          value="true"
        />
        <el-option
          label="否"
          value="false"
        />

        </el-select>
      </el-form-item>


      <el-form-item>
        <el-button
          type="primary"
          @click="handleChange"
          size='small'
        >
          检索
        </el-button>

        <el-button
          size="small"
          style="margin-left:3px"
          @click="changeShowAdvancedSearch"
        >
          更多
          <i
            v-if="showAdvancedSearch"
            class="el-icon-arrow-up"
          />
          <i
            v-else
            class="el-icon-arrow-down"
          />
        </el-button>
      </el-form-item>

      <div
        v-if="showAdvancedSearch"
        class="toolbar"
        style="margin-bottom: 0.5em;"
      >
        <div class="search-box">
          <el-form-item>
            <el-select
              v-show="showFilter('info.inservice')"
              v-model="search.user_status"
              placeholder="员工状态"
              size="small"
              clearable
              width="120px"
            >
              <el-option
                key="true"
                :label="getUserInserviceString(true)"
                value="true"
              />
              <el-option
                key="false"
                :label="getUserInserviceString(false)"
                value="false"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-select
              v-model="search.role_ids"
              placeholder="系统角色"
              size="small"
              clearable
              filterable
              multiple
            >
              <el-option
                v-for="x in systemRoles"
                :key="x.id"
                :label="x.name"
                :value="x.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <department-select
              v-show="showFilter('info.department')"
              v-model="search.department_ids"
              placeholder="选择部门"
              :department-id="[]"
              size="small"
              select-style="width: 220px; margin-right:0px;"
              :checkStrictly="true"
              multiple
              collapseTags
              filterable
            />
          </el-form-item>

          <el-form-item>
            <el-autocomplete
              v-show="showFilter('info.position')"
              v-model="search.position"
              :fetch-suggestions="positionSuggestions"
              placeholder="员工岗位"
              prefix-icon="el-icon-search"
              clearable
              class="position-input"
              popper-class="el-autocomplete-suggestion"
              :popper-append-to-body="false"
              size="small"
              @keyup.enter.native="runSearch"
              @clear="clearSearchPosition"
            />
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import DepartmentSelect from '@/components/common/DepartmentSelect.vue'

export default {
  components: {
    DepartmentSelect,
  },
  props: {
    systemId: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      showAdvancedSearch: false,
      search: {
        onlyShowNoLinkAccount: false, // 只显示未关联账号
        property: 'account_code',
        value: null,
        account_status: null,
        public_account_status: null,
        user_status:     '',
        role_ids:        [],
        department_ids:  [],
        position:        '',
      },
      positions: [],
      systemRoles: []
    }
  },
  created () {
    this.positions = this.$store.state.positions
    this.getSystemRoles()
  },
  methods: {
    handleChange () {
      // 使用 clone 防止引用相同变量，
      // 以防 input 内的输入变化会引发外部的 watch
      this.$emit('change', this.$lodash.clone(this.search))
    },
    changeShowAdvancedSearch () {
      this.showAdvancedSearch = !this.showAdvancedSearch
    },
    positionSuggestions (string, callback) {
      const positions   = this.positions.map(x => { return { value: x } })
      let escapeString = this.$lodash.escapeRegExp(string)
      const suggestions = string ? positions.filter(x => x.value.match(new RegExp(escapeString))) : positions
      callback(suggestions)
    },
    showFilter (conditions) {
      const filter = this.$settings.userInfoColumns.find(x => x.prop === conditions)
      return filter && filter.show_in_account_page
    },
    clearSearchPosition () {
      this.search.position = ''
    },
    getSystemRoles () {
      if (this.systemId === 0 || this.systemId === null) return

      const params = { system_id: this.systemId }
      this.$axios.get('/api/roles', { params: params })
          .then(response => {
            this.systemRoles = response.data.roles
          })
          .catch(() => {
            this.systemRoles = []
          })
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-form-item{
    margin-bottom: 0;
  }
</style>
