# frozen_string_literal: true

class SystemBaselinesController < ApplicationController
  before_action :authenticate_admin!, except: [:diff_account]
  before_action :authenticate_policy!, except: %i[import list revoke_bulk]
  before_action :set_business_system
  before_action :set_system_account, only: %i[diff_account found_baseline_by_account]
  before_action :set_account_baseline, only: [:link, :ignore_match]
  before_action :set_baseline, only: %i[show update destroy delete_permission
                                        update_permission create_permission baseline_with_notice_permission]
  before_action :check_permission, only: %i[create_permission update_permission]
  before_action :system_baselines, only: %i[baseline_index export_baselines export_select_baselines export_accounts export_select_accounts]

  def export_baselines
    audit_log! @business_system
    exporter = DataExport::SystemBaselineExportBaseline.new(params[:system_id].to_i, @baselines.pluck(:id))
    begin
      baselines_data = exporter.export
    rescue StandardError => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }
      return json_custom_respond 400, error_message: e.message
    end
    send_data(
      baselines_data,
      filename: URI.encode_www_form_component("#{@business_system.name}系统基线导出.xlsx".gsub(/\s+/, '')),
      type:     'application/octet-stream;charset=utf-8'
    )
  end

  def export_select_baselines
    audit_log! [@business_system, params[:baseline_ids]]
    exporter = DataExport::SystemBaselineExportBaseline.new(params[:system_id].to_i, @baselines.pluck(:id))
    begin
      baselines_data = exporter.export
    rescue StandardError => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }
      return json_custom_respond 400, error_message: e.message
    end
    send_data(
      baselines_data,
      filename: URI.encode_www_form_component("#{@business_system.name}系统基线导出.xlsx".gsub(/\s+/, '')),
      type:     'application/octet-stream;charset=utf-8'
    )
  end

  def export_accounts
    audit_log! @business_system
    exporter = DataExport::SystemBaselineExportAccount.new(params[:system_id].to_i, @baselines.pluck(:id))
    begin
      baselines_data = exporter.export
    rescue StandardError => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }
      return json_custom_respond 400, error_message: e.message
    end
    send_data(
      baselines_data,
      filename: URI.encode_www_form_component("#{@business_system.name}系统基线账号列表导出.xlsx".gsub(/\s+/, '')),
      type:     'application/octet-stream;charset=utf-8'
    )
  end

  def export_select_accounts
    audit_log! [@business_system, params[:baseline_ids]]
    exporter = DataExport::SystemBaselineExportAccount.new(params[:system_id].to_i, @baselines.pluck(:id))
    begin
      baselines_data = exporter.export
    rescue StandardError => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }
      return json_custom_respond 400, error_message: e.message
    end
    send_data(
      baselines_data,
      filename: URI.encode_www_form_component("#{@business_system.name}系统基线账号列表导出.xlsx".gsub(/\s+/, '')),
      type:     'application/octet-stream;charset=utf-8'
    )
  end

  def import_validation
    # 导入的系统基线本地存储，二次调用接口会用到
    path = "#{Rails.root}/data/import/baselines/"
    FileUtils.mkdir_p(path) unless File.exist?(path)
    file = File.open("#{path}/baseline_import.txt", 'w+')
    # 这里要存一下params[:file].read，否则下面方法调用读取不到，只能一次.read
    import_data = params[:file].read
    file.syswrite(import_data)
    file.close

    importer = DataImport::SystemBaselineImport.new(import_data, @business_system.id)
    importer.validates
  rescue DataImport::ValidateFailure => e
    audit_log! @business_system, action: :import_failed
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    json_custom_respond 400, error_message: e.message
  rescue DataImport::WarningFailure => e
    # audit_log! @business_system, action: :import_failed
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    json_custom_respond 456, error_message: e.message
  end

  # 这个是系统基线导入的二次调用的接口 执行完 import_validation 之后才会执行，所以 import_validation 加验证就可以
  def import
    # 二次调用，读取本地存储的二进制文件流内容
    file        = File.open("#{Rails.root}/data/import/baselines/baseline_import.txt", 'r+')
    import_data = file.read
    file.close

    importer = DataImport::SystemBaselineImport.new(import_data, @business_system.id, current_admin.id)
    importer.import
    audit_log! @business_system, action: :import_success
    json_respond success: true
  rescue DataImport::ValidateFailure => e
    audit_log! @business_system, action: :import_failed
    logger.error { e.message }
    logger.error { e.backtrace.join("\n") }
    json_custom_respond 400, error_message: e.message
  end

  # baselines 获取 baselines 只在后台管理系统基线管理用
  def baseline_index
    params_page     = params[:page] || 1
    params_per_page = params[:per_page] || 25
    sort_name = params[:sort_name]
    sort_type = params[:sort_type] == 'descending' ? 'desc' : 'asc'
    return_data = if sort_name.present?
                    sort_name = Tool::Config.convert_gbk_sql(sort_name) if sort_name == 'name'
                    @baselines.order("#{sort_name} #{sort_type}")
                  else
                    @baselines.order_pinyin
                  end
    output_data = return_data.page(params_page).per(params_per_page).map(&:output)
    json_respond data: output_data, count: @baselines.count
  end

  # 多个页面的接口
  def list
    baselines = @business_system.baselines.includes(:department)
    if params[:is_merge].to_s.present?
      baselines = if params[:is_merge].to_s == 'true'
                    baselines.where(is_merge: true)
                  else
                    baselines.where(is_merge: false)
                  end
    end
    json_respond baselines.order_pinyin.map(&:output)
  end

  # accounts 已经关联系统基线的账号 只在后台管理系统基线管理用
  def account_index
    params_page     = params[:page] || 1
    params_per_page = params[:per_page] || 25

    filter = params[:filter] ? JSON.parse(params[:filter], symbolize_names: true) : {}

    show_baseline_links     = filter[:showBaselineLinks]
    search_field            = filter[:property]
    query                   = filter[:value]
    status_filter           = filter[:account_status]
    roles_filter            = filter[:roles]
    departments_filter      = current_admin.filter_departments(filter[:departments])
    search_result =
      SystemAccountsBaseline.search(
        business_system:  @business_system,
        search_field:     search_field,
        query:            query,
        show_baseline_links:     show_baseline_links,
        status_filter:           status_filter,
        departments_filter:      departments_filter,
        roles_filter:            roles_filter
      )

    can_auto_match = Setting.account_match_system_baseline&.[]('enable') || false

    return_data =
      search_result
        .order_by_code
        .page(params_page)
        .per(params_per_page)
        .map(&:output)

    json_respond data: return_data, can_auto_match: can_auto_match, count: search_result.count, system_roles: @business_system.system_roles
  end

  # 账号忽略自动关联系统基线
  def ignore_match
    need_ignore_match = params[:ignore_match].to_s == 'true'
    @account_baseline.update(ignore_match: need_ignore_match)
    json_respond success: true
  end

  # 需要关联系统基线的账号接口 只在后台管理系统基线管理用
  def all_accounts
    show_baseline_links = params[:show_baseline_links]

    accounts = SystemAccountsBaseline.all_accounts_output(@business_system.id, show_baseline_links)

    last_quarter_id = Quarter.last&.id
    roles           = set_role_names(@business_system.role_class.where(quarter_id: last_quarter_id)).uniq

    json_respond(accounts: accounts, roles: roles, departments: Department.all)
  rescue QuarterNotFound => _e
    json_custom_respond(404, {})
  end

  # 变更系统基线
  def link
    baseline = SystemBaseline.find_by(id: params[:baseline_id])
    account_baselines =  SystemAccountsBaseline.where(
        business_system_id: @business_system.id,
        account_code_field: @business_system.account_unique_field,
        account_code_value: params[:account_codes]
      )

    # 这里不能用update_all，需要触发accounts_count，该字段使用了counter_cache
    account_baselines.each { |x| x.update(baseline_id: params[:baseline_id], follow_job: params[:follow_job]) }

    audit_params = {
      business_system: @business_system,
      baseline:        baseline,
      accounts:        account_baselines.map { |account| "#{account&.output[:account_name]}(#{account&.account_code_value})" },
      follow_job:      params[:follow_job]
    }

    audit_log! audit_params, action: :link
    json_respond(success: true)
  end

  # 批量解除关联
  def revoke_bulk
    accounts =
      SystemAccountsBaseline.where(
        business_system_id: @business_system.id,
        account_code_value: params[:account_codes]
      )
    accounts.update_all(baseline_id: nil, follow_job: false)
    audit_params = {
      business_system: @business_system,
      accounts:        accounts.map { |account| "#{account&.output[:account_name]}(#{account&.account_code_value})" }
    }
    audit_log! audit_params, action: :revoke_bulk
    json_respond(success: true)
  end

  # 系统基线查看 仅后台管理系统基线管理用
  def show
    json_respond @baseline.output_detail
  end

  # 只展示notice的告警维度和对应的权限(目前只有鹏华基金使用)
  def baseline_with_notice_permission
    output_detail = @baseline.output_detail
    if Setting.frontendSettings&.[]('baselinePermissionShow')
      notice_data_keys = output_detail[:output_schema_data].select { |item| item[:is_notice] }.pluck(:data_key)
      output_detail[:output_datas] = output_detail[:output_datas].select { |key, _| notice_data_keys.include?(key.to_s) }
      output_detail[:output_schema] = output_detail[:output_schema].select { |item| notice_data_keys.include?(item[:data_key].to_s) }
    end
    json_respond output_detail
  end

  # 系统基线创建 仅 账号详情 & 后台管理系统基线管理
  def create
    @baseline = SystemBaseline.new(baseline_params.slice(:name, :compare_rule, :output_schema, :output_schema_data, :department_id, :comment)
                                                  .merge(business_system_id: @business_system.id))
    if @baseline.save
      audit_log! @baseline
    else
      json_custom_respond(400, error_message: @baseline.errors.full_messages.join('; '))
      return false
    end
    # 没有 source_id 参数就给空权限直接返回了
    unless baseline_params[:source_id]
      output_schema = BusinessSystem.compat_with_old_schema(@business_system.account_class.output_schema)
      output_datas = @business_system.output_datas_for_baseline
      if @business_system.role_class
        output_schema = BusinessSystem.add_role_schema(output_schema, @business_system)
        output_datas = output_datas.merge({ role: [] })
      end
      output_schema = SystemBaseline.init_schema_diff(output_schema)
      @baseline.update(
        output_schema: output_schema,
        output_datas:  output_datas
      )
      @baseline.generate_history(current_admin.id)
      json_respond(success: true)
      return true
    end

    case baseline_params[:source_type]
    when 'baseline'
      clone_from_baseline(baseline_params[:source_id])
    when 'account'
      clone_from_account(baseline_params[:source_id])
    end
  end

  # 合并系统基线
  def merge_baselines
    result = SystemBaselineServices::MergeBaselines.new(params[:system_id], merge_baselines_params).execute
    if result[:is_success]
      audit_log! [params[:system_id], merge_baselines_params]
      json_respond success: true
    else
      json_respond success: false, message: result[:message]
    end
  end

  # 从合并的系统基线中， 移除某个系统基线
  def remove_baseline
    result = SystemBaselineServices::RemoveBaseline.new(params[:baseline_id], params[:origin_baseline_id]).execute
    if result[:is_success]
      audit_log! [params[:baseline_id], params[:origin_baseline_id]]
      json_respond success: true, origin_baselines: result[:data]
    else
      json_respond success: false, message: result[:message]
    end
  end

  # 系统基线更新 仅 账号详情 & 后台管理系统基线管理
  def update
    data_hash = baseline_params.slice(:name, :compare_rule, :output_schema, :output_schema_data, :department_id, :comment)
    if params[:permission_data].present? && JSON.parse(params[:permission_data]).present?
      data_hash.merge!({ output_datas: JSON.parse(params[:permission_data]).deep_symbolize_keys })
    end
    unless @baseline.update(data_hash)
      json_custom_respond(400, error_message: @baseline.errors.full_messages.join('; '))
      return
    end

    unless baseline_params[:source_id]
      @baseline.generate_history(current_admin.id)
      json_respond @baseline.output
      return true
    end

    case baseline_params[:source_type]
    when 'baseline'
      clone_from_baseline(baseline_params[:source_id], 'update')
    when 'account'
      clone_from_account(baseline_params[:source_id], 'update')
    end
  end

  # 提供给客户批量更新系统基线
  def update_bulk
    qc_ids      = params[:qc_ids]
    qc_records  = QuarterComparedAccountDifference.where(id: qc_ids)
    account_ids = qc_records.map(&:account_id)
    accounts    = @business_system.account_class.where(id: account_ids)
    baselines   = accounts.map(&:baseline)

    baseline_ids = baselines.compact.map(&:id)

    # 检查是否修改了同一个系统基线
    if baseline_ids.size > baseline_ids.uniq.size
      json_custom_respond 400, error_message: '不能同时更新同一个系统基线'
      return false
    end

    update_status = false
    accounts.each_with_index do |account, index|
      update_status = true if update_from_account(baselines[index], account, qc_records[index])
    end

    if update_status
      json_respond success: true
    else
      json_respond success: false
    end
  end

  # 从账号更新系统基线（目前暂时只用于告警中心的快捷操作）
  def update_with_account
    account_id = params[:account_id]
    account = @business_system.account_class.find_by(id: account_id)
    is_success = update_from_account(account&.baseline, account)
    if is_success
      json_respond success: true
    else
      json_respond success: false
    end
  end

  def delete_permission
    permission = JSON.parse(params[:permission], symbolize_names: true)

    @baseline.output_datas[params[:data_key].to_sym] -= [permission]
    @baseline.save

    audit_log! [@baseline, params]
    json_respond success: true
  end

  def create_permission
    permission = JSON.parse(params[:permission].to_json, symbolize_names: true)

    @baseline.output_datas[params[:data_key].to_sym] |= [permission]
    @baseline.save

    audit_log! [@baseline, params]
    json_respond success: true
  end

  def update_permission
    old_permission = JSON.parse(params[:old_permission].to_json, symbolize_names: true)
    new_permission = JSON.parse(params[:permission].to_json, symbolize_names: true)
    permission     = @baseline.output_datas[params[:data_key].to_sym].find { |x| x == old_permission }
    permission.each { |k, _v| permission[k] = new_permission[k] }
    @baseline.save

    audit_log! [@baseline, params]
    json_respond success: true
  end

  def destroy
    @baseline.destroy
    audit_log! @baseline

    json_respond_no_content
  end

  # 与系统基线比较权限
  # 场景: output_schema_data用于两个地方：
  # 1) 用户详情的系统基线权限比对信息的菜单提示 不告警
  # 2) 当前时间权限差异情况 权限发生变化提示 不告警
  def diff_account
    account_baseline_link =
      SystemAccountsBaseline.find_by(
        business_system_id: @business_system.id,
        account_code_field: @business_system.account_unique_field,
        account_code_value: @account.send(@business_system.account_unique_field)
      )

    # @type [SystemBaseline]
    baseline = account_baseline_link&.baseline

    if baseline
      if baseline.business_system_id == @business_system.id
        json_respond(
          name:               baseline.name,
          compare_rule:       baseline.compare_rule,
          diff_datas:         BusinessSystem.compat_with_old_diff_data(baseline.diff(@account)),
          output_schema_data: baseline.output_schema_data,
          output_schema:      baseline.output_schema
        )
      else
        json_custom_respond(400, error_message: '系统基线关联异常，请重新设置该账号基线')
      end
    else
      # 当账号未关联 baseline 时，返回名称为空, diff_datas 返回默认数据
      json_respond(
        name:               '',
        diff_datas:         BusinessSystem.compat_with_old_diff_data(@business_system.diff_datas_default),
        output_schema_data: [],
        output_schema:      BusinessSystem.compat_with_old_schema(@business_system.account_class.output_schema)
      )
    end
  end

  # 判断账号没有没有 baseline, 有的话返回 id 和名称 加了查询权限
  def found_baseline_by_account
    account_baseline_link =
      SystemAccountsBaseline.find_by(
        business_system_id: @business_system.id,
        account_code_field: @business_system.account_unique_field,
        account_code_value: @account.send(@business_system.account_unique_field)
      )
    baseline              = account_baseline_link&.baseline

    if baseline
      output_schema_data = baseline.calculate_output_schema_data
      json_respond(is_found: true, id: baseline.id, name: baseline.name,
                   compare_rule: baseline.compare_rule, department_id: baseline.department_id,
                   comment: baseline.comment, output_schema_data: output_schema_data,
                   output_schema: baseline.output_schema)
    else
      output_schema = BusinessSystem.compat_with_old_schema(@business_system.account_class.output_schema)
      output_schema = SystemBaseline.init_schema_diff(output_schema)
      output_schema_data = SystemBaseline.new(
        business_system_id: @business_system.id,
        output_schema:      @business_system.account_class.output_schema
      ).calculate_output_schema_data
      json_respond(is_found: false, id: nil, name: nil, compare_rule: nil, department_id: nil,
                   comment: nil, output_schema_data: output_schema_data,
                   output_schema: output_schema)
    end
  end

  def histories
    system_baseline = SystemBaseline.find(params[:baseline_id])
    baseline_histories = system_baseline.system_baseline_histories.map(&:simple_hash)
    json_respond(baseline_histories: baseline_histories)
  end

  def history_detail
    history = SystemBaselineHistory.find(params[:history_id])
    json_respond(history: history.detail)
  end

  def restore_from_history
    system_baseline = SystemBaseline.find(params[:baseline_id])
    history = SystemBaselineHistory.find(params[:history_id])
    system_baseline.restore_from(history)
    system_baseline.generate_history(current_admin.id)
    audit_log! [system_baseline.business_system&.name, params[:baseline_id], params[:history_id]]
    json_respond(success: true)
  end

  # 对比不同系统基线的差异
  def diff_history_baseline
    system_baseline = SystemBaseline.find(params[:baseline_id])
    diff_datas = if params[:diff_mode] == 'diff_history'
                   system_baseline.diff_history_baseline(params[:diff_id])
                 else
                   system_baseline.diff_other_baseline(params[:diff_id])
                 end
    json_respond(diff_datas)
  end

  private

  def system_baselines
    if action_name == 'baseline_index'
      @filter = params[:filter] ? JSON.parse(params[:filter], symbolize_names: true) : {}
    else
      @filter = params
    end
    params_only_no_accounts = @filter[:showNoAccountLinks].is_a?(String) ? @filter[:showNoAccountLinks] == 'true' : @filter[:showNoAccountLinks]
    search_field            = @filter[:property]
    query                   = @filter[:value]
    baseline_ids            = @filter[:baseline_ids]

    # 若有筛选部门，则展示的系统基线的按(有权限的部门 & 传过来的部门ids)筛选
    # 若没有筛选部门，则展示的系统基线按(有权限的部门并上部门为空的)
    @department_ids = (current_admin.departments_in_query | current_admin.parent_departments_in_query).pluck(:id).uniq
    if @filter[:department_ids].to_a.map(&:to_i).present?
      @department_ids = @department_ids & @filter[:department_ids].to_a.map(&:to_i)
    else
      @department_ids << nil
    end

    @baselines =
      SystemBaseline.search(
        business_system:  @business_system,
        search_field:     search_field,
        query:            query,
        only_no_accounts: params_only_no_accounts,
        department_ids:   @department_ids,
        baseline_ids:     baseline_ids
      )
  end

  def check_permission
    check_status = false
    data_schema = @baseline.output_schema.find { |x| x[:data_key] == params[:data_key] }
    if data_schema
      data_schema[:schema].each do |schema|
        check_status = true if params[:permission][schema[:property].to_s].present?
      end
    end
    json_custom_respond(400, error_message: '权限不能全部为空') unless check_status
  end

  def authenticate_policy!
    authorize SystemBaseline
  end

  def set_role_names(roles)
    role_names = []
    if roles.first.respond_to? 'name_display'
      roles.each do |role|
        role_names << role.name_display
      end
    else
      roles.each do |role|
        role_names << role.name
      end
    end
    role_names
  end

  def link_account_baseline(account, follow_job = true)
    link =
      SystemAccountsBaseline.find_or_create_by(
        business_system_id: @business_system.id,
        account_code_field: @business_system.account_unique_field,
        account_code_value: account.send(@business_system.account_unique_field)
      )
    link.update(baseline_id: @baseline.id, follow_job: follow_job)
    link
  end

  def clone_from_baseline(baseline_id, mode = 'create')
    clone_from_baseline = SystemBaseline.find_by(id: baseline_id)

    unless clone_from_baseline
      json_custom_respond(404, error_message: 'not found baseline')
      return false
    end

    if baseline_id == @baseline.id
      @baseline.generate_history(current_admin.id)
      audit_log! @baseline, action: :update if @baseline.previous_changes.present?
      return json_respond(success: true)
    end

    @baseline.output_schema = BusinessSystem.compat_with_old_schema(clone_from_baseline.output_schema) if @baseline.output_schema.blank?
    @baseline.output_datas  = clone_from_baseline.output_datas

    if @baseline.save
      mode_display = mode == 'update' ? '更新' : '创建'
      audit_params = {
        baseline:     @baseline,
        mode_display: mode_display,
        source:       clone_from_baseline
      }
      @baseline.generate_history(current_admin.id)
      audit_log! audit_params, action: :clone_from_baseline

      json_respond(success: true)
    else
      json_custom_respond(400, error_message: @baseline.errors.full_messages.join('; '))
    end
  end

  def clone_from_account(account_id, mode = 'create')
    account = @business_system.account_class.find_by(id: account_id)

    unless account
      json_custom_respond(404, error_message: 'not found account')
      return false
    end

    if @baseline.clone_accounts(@business_system, [account], baseline_params[:output_schema_data]).save
      mode_display = mode == 'update' ? '更新' : '创建'

      audit_params = {
        baseline:     @baseline,
        mode_display: mode_display,
        source:       account
      }
      @baseline.generate_history(current_admin.id)
      audit_log! audit_params, action: :clone_from_account

      if baseline_params[:linked]
        link = link_account_baseline(account, baseline_params[:follow_job])
        account_name = link.accounts.last&.name
        audit_params = {
          business_system: link.business_system,
          baseline:        link.baseline,
          accounts:        ["#{account_name}(#{link.account_code_value})"]
        }
        audit_log! audit_params, action: :link
      end
      json_respond(success: true)
    else
      json_custom_respond(400, error_message: @baseline.errors.full_messages.join('; '))
    end
  end

  # 用于批量更新系统基线使用
  def update_from_account(baseline, account, qc_record = nil)
    return false unless baseline
    return false unless account

    old_output_schema_data = baseline.output_schema_data
    baseline.output_schema = BusinessSystem.compat_with_old_schema(account.class.output_schema)
    baseline.output_datas  = BusinessSystem.compat_with_old_data(account.display_output_all_datas)

    business_system = account.the_system
    if business_system.role_class
      baseline.output_schema       = BusinessSystem.add_role_schema(baseline.output_schema, business_system)
      baseline.output_datas[:role] = account.roles_for_diff
    end
    baseline.output_schema = SystemBaseline.init_schema_diff(baseline.output_schema)
    baseline.output_schema_data = baseline.calculate_output_schema_data
    baseline.output_schema_data.each do |item|
      old_data = old_output_schema_data.find { |old_item| old_item[:data_key] == item[:data_key] }
      item[:is_notice] = old_data[:is_notice] if old_data.present?
    end
    return false unless baseline.save

    qc_record&.update(baseline_update_flag: true)

    audit_params = {
      baseline:     baseline,
      mode_display: '更新',
      source:       account
    }
    audit_log! audit_params, action: :clone_from_account
    true
  end

  def set_account_baseline
    @account_baseline =
      SystemAccountsBaseline.find_or_create_by(
        business_system_id: @business_system.id,
        account_code_field: @business_system.account_unique_field,
        account_code_value: params[:account_code]
      )
  rescue StandardError => e
    json_custom_respond(404, error_message: e.message)
  end

  def set_baseline
    @baseline = SystemBaseline.find(params[:baseline_id])
  rescue StandardError => e
    json_custom_respond(404, error_message: e.message)
  end

  def baseline_params
    params.require(:baseline).permit(
      :name,
      :source_type,
      :source_id,
      :linked,
      :compare_rule,
      :department_id,
      :comment,
      :follow_job,
      output_schema_data: %i[data_key name is_notice],
      output_schema: [:data_key, :name, schema: %i[label property is_diff]]
    )
  end

  def merge_baselines_params
    params.require(:baseline).permit(:name, :compare_rule, :department_id, :comment,
                                     :merge_type, :baseline_id, :move_account_bind, origin_baseline_ids: [])
  end
end
