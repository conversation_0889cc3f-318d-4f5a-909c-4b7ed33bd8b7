<template>
  <div
    v-loading="loading"
    class="pa-container"
  >
    <div class="toolbar">
      <div class="left">
        <search-bar
          @change="handleSearch"
        />
      </div>
      <div class="right">
        <el-form
          :inline="true"
          class="search-box"
        >
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              :disabled="!hasEditPermission()"
              @click="handleCreate"
            >
              添加功能账号
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table
      :data="dataByPage"
      border
    >
      <el-table-column
        property="name"
        label="功能账号名称"
      />
      <el-table-column
        property="code"
        label="系统账号编码"
      />
      <el-table-column
        property="label"
        label="功能账号标记"
      />
      <el-table-column
        property="user_name"
        label="责任人"
      />
      <el-table-column
        property="used_user_names"
        label="使用人"
      />
      <el-table-column
        property="valid_date"
        label="有效期"
      />
      <el-table-column
        property="status"
        label="状态"
      >
        <template slot-scope="scope">
          <span v-show="scope.row.status === true">有效</span>
          <span
            v-show="scope.row.status === false"
            style="color: red;font-weight: bold;"
          >
            过期
          </span>
        </template>
      </el-table-column>
      <el-table-column
        property="remark"
        label="备注"
      />
      <el-table-column
        fixed="right"
        label="操作"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            :disabled="!hasEditPermission()"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>

          <el-button
            size="mini"
            type="danger"
            :disabled="!hasEditPermission()"
            @click="accountDeleteConfirm(scope.row)"
          >
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-size="pageSize"
      :total="dataBySearch.length"
      :current-page.sync="currentPage"
      background
      layout="total, prev, pager, next, jumper"
      class="pagination"
    />
    <account-form
      ref="accountEdit"
      :account="currentAccount"
      :system-id="systemId"
      :systems="systems"
      @update="getAccounts"
    />
    <account-form
      ref="accountCreate"
      :account="currentAccount"
      :system-id="Number(systemId)"
      :systems="systems"
      mode="create"
      @update="getAccounts"
    />
  </div>
</template>

<script>
import SearchBar from './SearchBar.vue'
import AccountForm from './AccountForm.vue'

export default {
  components: {
    SearchBar,
    AccountForm
  },
  props: {
    systemId: {
      type: Number,
      required: true
    },
    systems: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      publicAccounts: [],
      loading: false,
      pageSize: 15,
      currentPage: 1,
      currentAccount: null,
      filter: {
        code: null,
        name: null,
        remark: null,
        status: null,
        date: null
      }
    }
  },
  computed: {
    dataBySearch () {
      let searchData = this.publicAccounts

      if (this.filter.code) {
        let filterCode = this.$lodash.escapeRegExp(this.filter.code)
        searchData = searchData.filter(x => x.code.match(new RegExp(filterCode)))
      }

      if (this.filter.name) {
        let filterName = this.$lodash.escapeRegExp(this.filter.name)
        searchData = searchData.filter(x => x.name.match(new RegExp(filterName)))
      }

      if (this.filter.remark) {
        let filterRemark = this.$lodash.escapeRegExp(this.filter.remark)
        searchData = searchData.filter(x => x.remark && x.remark.match(new RegExp(filterRemark)))
      }

      if (this.filter.status === false || this.filter.status === true) {
        searchData = searchData.filter(x => x.status === this.filter.status)
      }

      if (this.filter.status === true) {
        searchData = searchData.filter(x => x.status === true)
      }

      if (this.filter.date) {
        searchData = searchData.filter(x => (this.filter.date[0] <= x.valid_date && this.filter.date[1] >= x.valid_date))
      }

      return searchData
    },
    dataByPage () {
      const start = (this.currentPage - 1) * this.pageSize
      const end   = this.currentPage * this.pageSize
      return this.dataBySearch.slice(start, end)
    },
    // 所有操作权限的系统
    adminMaintainSystemIds () {
      return this.$store.state.adminPrivileges.maintain_system.map(x => x.id)
    }
  },
  created () {
    this.getAccounts()
  },
  methods: {
    // 拥有操作系统权限
    hasMaintainSystemPermission() {
      return this.adminMaintainSystemIds.indexOf(this.systemId) >= 0
    },
    hasEditPermission () {
      return this.$store.getters.hasPermission('public_accounts.edit') && this.hasMaintainSystemPermission()
    },
    accountDeleteConfirm (row) {
      this.$confirm(`将从功能账号列表里移除账号「${row.name}」, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.accountDelete(row.id)
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    accountDelete (id) {
      this.$axios.delete(`api/systems/${this.systemId}/public_accounts/${id}`)
        .then(response => {
          this.$message.success('功能账号已删除')
          this.getAccounts()
        })
        .catch(() => {})
    },
    getAccounts () {
      if (Number(this.system_id) === 0) return
      this.loading = true
      this.$axios.get(`api/systems/${this.systemId}/public_accounts`)
        .then(response => {
          this.publicAccounts = response.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleCreate () {
      this.currentAccount = null
      this.$refs.accountCreate.mode = 'create'
      this.$refs.accountCreate.visible = true
    },
    handleEdit (row) {
      this.currentAccount = row
      this.$refs.accountEdit.visible = true
    },
    handleSearch (payload) {
      this.filter = payload
      this.currentPage = 1
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '~@/components/variables';
  .pa-container{
    background-color: white;
    min-width: 600px;

    .toolbar{
      @include vertical_top_between;
      margin-bottom: 0px;
    }
    .pagination{
      margin-top: 20px;
    }
  }
</style>
