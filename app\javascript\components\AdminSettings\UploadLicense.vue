<template>
  <div>
    <el-upload
      ref="upload"
      :headers="headers"
      :action="`/admin_api/settings/update_license`"
      :on-success="handleSuccess"
      :on-error="handleError"
      :show-file-list="false"
    >
      <el-button
        size="small"
        type="primary"
        class="upload-license"
      >
        更新产品授权
      </el-button>
    </el-upload>
  </div>
</template>
<script>
import API from '@/api'

export default {
  props: {
  },
  data () {
    const headers = API.tool.getToken()

    return {
      headers: headers
    }
  },
  methods: {
    handleSuccess (_response, _file, _fileList) {
      this.$message.success('产品License已更新，需要手动重启系统')
      this.$emit('update')
    },
    handleError (error) {
      this.$message.error('文件上传失败，服务器返回：' + JSON.parse(error.message).error_message)
    }
  }
}
</script>
<style lang="scss" scoped>
.upload-license {
  margin: 22px 0px 0px 0px;
}
</style>
